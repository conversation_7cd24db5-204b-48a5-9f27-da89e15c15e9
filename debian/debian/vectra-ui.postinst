#!/bin/bash
set -exuo pipefail

EXISTING_VERSION="$2"
PYTHON_RUNTIME="/usr/local/bin/pyvui"

if [[ "$(curl -s http://localhost:5010/api/flag/platform/cloud_bridge | jq .data.value)" == "true" ]]; then
    CLOUD_BRIDGE=true
else
    CLOUD_BRIDGE=false
fi

# Reload new service definitions
systemctl daemon-reload

chmod 0440 /etc/sudoers.d/www-data

# ensure tunnel account exists
if getent passwd tunnel >/dev/null 2>&1; then
    echo "tunnel user exists"
else
    echo "tunnel user does not exist, creating now"
    useradd -d /home/<USER>/bin/false tunnel
fi
mkdir -p /home/<USER>/.ssh/
chmod 0700 /home/<USER>/.ssh
touch /home/<USER>/.ssh/authorized_keys
chmod 0600 /home/<USER>/.ssh/authorized_keys
chown -R tunnel:tunnel /home/<USER>

# ensure the vectra_procs group exists and add www-data to it
if [ -z "$(grep vectra_procs /etc/group)" ]; then
    groupadd vectra_procs
fi
usermod -a -G vectra_procs www-data

# make sure that /opt/protected/diagnostics exists and give it the right permissions
if [ ! -d /opt/protected ]; then
    echo "/opt/protected does not exist, creating it now"
    mkdir /opt/protected
fi

if [ ! -d /opt/protected/diagnostics ]; then
    echo "/opt/protected/diagnostics does not exist, creating it now"
    mkdir /opt/protected/diagnostics
fi

if [ ! -d /opt/protected/status_report ]; then
    echo "/opt/protected/status_report does not exist, creating it now"
    mkdir /opt/protected/status_report
fi

chown root:vectra_procs /opt/protected
chown root:vectra_procs /opt/protected/diagnostics
chown root:vectra_procs /opt/protected/status_report
chmod ug+wx /opt/protected
chmod ug+wx /opt/protected/diagnostics
chmod ug+wx /opt/protected/status_report

# make sure www-data can create in svg and img folders
chown root:vectra_procs /opt/public/static/media/community/img
chown root:vectra_procs /opt/public/static/media/community/svg
chmod ug+wx /opt/public/static/media/community/img
chmod ug+wx /opt/public/static/media/community/svg

chown -R root:vectra_procs /opt/public/static/media/client
chmod -R u=rw,go=r,a+X /opt/public/static/media/client

chown root:vectra_procs /opt/tracevector/bin/offline_upgrade/offline_upgrade_install.sh
chown root:vectra_procs /opt/tracevector/bin/offline_upgrade/offline_upgrade_chain_install.sh
chown root:vectra_procs /opt/tracevector/bin/offline_upgrade/offline_upgrade_verify.sh
chmod ug+x /opt/tracevector/bin/offline_upgrade/offline_upgrade_install.sh
chmod ug+x /opt/tracevector/bin/offline_upgrade/offline_upgrade_chain_install.sh
chmod ug+x /opt/tracevector/bin/offline_upgrade/offline_upgrade_verify.sh
chmod ug+x /opt/tracevector/bin/get_lock_info.sh

chmod +x /opt/tracevector/bin/initialize_sql.sh

/opt/tracevector/bin/get_lock_info.sh || true

if ! systemctl is-active mysql.service; then
  systemctl start mysql.service
fi

for i in {1..30}; do # Attempts ping every 10 seconds for 5 minutes.
    if [ $i -eq 30 ]; then
        echo "mysqladmin ping command failed, max retries reached"
        exit 1
    fi
    mysqladmin ping &>/dev/null && break || sleep 10
done

/opt/tracevector/bin/initialize_sql.sh

# Give vadmin read access
mysql -e "GRANT SELECT ON tvui.* TO vadmin@localhost IDENTIFIED VIA unix_socket;"

# Port access needs to be up for mysql by now
for i in {1..30}; do # Attempts ping every 10 seconds for 5 minutes.
    if [ $i -eq 30 ]; then
        echo "mysqladmin ping command failed, max retries reached"
        exit 1
    fi
    mysqladmin -h 127.0.0.1 ping &>/dev/null && break || sleep 10
done

rm -f /data/containers/vectra-vui-latest.xz || true

# Python Scripts to Keep
# As a reminder, If scripts apply to SaasUI Vui, please add scripts execution to ./debian/opt/tracevector/bin/cloud/cloud_db_setup.sh
#
${PYTHON_RUNTIME} /opt/tracevector/bin/rbac_setup.py  # APP-2118: Create/Clean up RBAC Permissions and Roles
${PYTHON_RUNTIME} /opt/tracevector/bin/cognito_user.py || true # APP-5785: Create Cognito user and use proxy VectraUser model instead of User
${PYTHON_RUNTIME} /opt/tracevector/bin/sql_schema_check.py # APP-9697: Generate a metric/stat for SQL schema setting
# Generate Predefined Groups
${PYTHON_RUNTIME} /opt/tracevector/bin/predefined_groups/generate_groups.py --current_version "$EXISTING_VERSION" # APP-8277: Generate HostGroups and Domain Groups from a JSON file.
# Generate Triage Templates
${PYTHON_RUNTIME} /opt/tracevector/bin/triage_templates/generate_triage_templates.py # APP-8277: Generate Triage Templates from a JSON file.
# Turn off vadmin if in cloud
${PYTHON_RUNTIME} /opt/tracevector/bin/disable_vadmin_on_cloud.py
# Generate Dynamic Detection Schemas
${PYTHON_RUNTIME} /opt/tracevector/bin/dynamic_detections/generate_detection_schemas.py # APP-10555
# Generate Dynamic EDR Schemas
${PYTHON_RUNTIME} /opt/tracevector/bin/dynamic_edrs/generate_edr_schemas.py # APP-13021
# CAT-516: [KEEP] Clear auto-triage context on system update
${PYTHON_RUNTIME} /opt/tracevector/bin/bulk_autotriage_scripts/reset_autotriage_job_contexts.py
# CAT-3234 Manage Endpoint Blocklist records in tvui_setting
${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_endpoint_blocklist.py
# CS-9302: [KEEP] set entity state to inactive
${PYTHON_RUNTIME} /opt/tracevector/bin/entity_state_fix.py
# BRIDGE-1722 (fixed by adding in BRIDGE-2001): Generate AI Triage - Distillation Smart Rules
${PYTHON_RUNTIME} /opt/tracevector/bin/distillation_scripts/generate_distillation_smart_rules.py
# End of Python Scripts to Keep

# One-Time or Temporary migration scripts
# Please add these scripts in order of releases
#
# 5.3
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/cleanup_kerberos_events.py || true  # APP-10158: [Optionally KEEP] quick cleanup of kerberos events
# 6.10
${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_inactive_accounts.py || true #  CS-5118: [Optionally KEEP] Migrate inactive accounts to be rescored after bug caused them to be in a limbo state
# 7.8
timeout 30m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_tvui_setting.py || true # SAASAPPS-3041 Transition from tvui_setting to feature flag service.
# 8.2
timeout 30m ${PYTHON_RUNTIME} /opt/tracevector/bin/search_and_fix_deprecated_hosts.py || true # PROD-303 Handle Hosts who are out of sync with couch and could be marked for deletion
# 8.5
timeout 30m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_account_alarmed.py || true # BRIDGE-576 Migrate alarmed data from Account to LinkedAccount
# 8.6
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_stream_config_add_metadata_type.py "suricata" || true # PLAT-14443 Migrate stream config to add default for new metadata type
# 8.9
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_aws_suspect_public_ebs_access_category.py || true # BRIDGE-1184 Migrate aws_suspect_public_ebs_access category
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_azurecp.py || true # BRIDGE-1191 Migrate detections with azurecp as data source type to azure-cp
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_info_detections_to_scored.py || true # BRIDGE-1166 Migrate info dets to scored
# 8.10
timeout 2m /opt/tracevector/bin/fix_account_reconciliation_type.sh || true # CS-9609 Move reconciliation type manual with no mappings to automatic if mapping and AD integration are enabled
# 8.11
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_azure_detection_ip_field.py || true # BRIDGE-1367 Migrate azure detection ip fields from src_ip to flex_json.caller_ip_address
#9.1
timeout 15m ${PYTHON_RUNTIME} /opt/tracevector/bin/migrate_sw_o365_dllHijack_category.py || true #  # BRIDGE-1483 Migrate sw_o365_dllHijack_category detection category

# End of One-Time migration scripts

# Generate security token for vui internal endpoints
/bin/bash /usr/local/bin/create_secrets.sh -s vui_internal_token -g vectra_procs -u root

# make sure dead files are removed
for dead_file in \
    /opt/tracevector/logs/vectra-ui.log* \
    /etc/uwsgi/apps-available/account-service.ini \
    /etc/uwsgi/apps-available/detection-detail-service.ini \
    /etc/uwsgi/apps-enabled/detection-detail-service.ini
do
    rm -f "$dead_file"
done

# Remove old cloud-bridge directory
if [ -d /etc/uwsgi/cloud-bridge ];then
    rm -rf /etc/uwsgi/cloud-bridge
fi

# Setup site unique cert / key package
/opt/tracevector/bin/initialize_vectraui_cert2.sh

chown -R www-data:vectra_procs /opt/tracevector/logs

rm -f /etc/init/nginx.override
rm -f /etc/nginx/sites-enabled/default

chmod 644 /etc/cron.d/generate_ptr
chmod 644 /etc/cron.d/dump_database
chmod 644 /etc/logrotate.d/uwsgi

# Use 2048 bits for DH params vs the default 1024
if [ ! -f /etc/ssl/certs/dhparam.pem ];then
    rm -f /tmp/.opensslrand
    export RANDFILE=/tmp/.opensslrand
    /usr/bin/openssl dhparam -out /etc/ssl/certs/dhparam.pem 2048
fi

chmod 744 /etc/ssl/certs/dhparam.pem

# Clean up large internal nginx logs that were populated before logrotate was fixed in PLAT-3706.
find /var/log/nginx/ -name "vui-4080*" -size +1G -delete || true

# Enable tvui.conf as long as it isn't blocked to becoming enabled
if [[ "$CLOUD_BRIDGE" == "true" ]]; then
    NGINX_TVUI_CONF=/etc/nginx/sites-available/tvui-cloud-bridge.conf
else
    NGINX_TVUI_CONF=/etc/nginx/sites-available/tvui.conf
fi
if [ ! -f /etc/nginx/sites-enabled/disabled.conf ] && [ ! -f /opt/vectra/embryo_origin ]; then
    ln -sf $NGINX_TVUI_CONF /etc/nginx/sites-enabled/tvui.conf
fi

/usr/sbin/service nginx restart

bg_systemd_services="\
    admin_pw_set.service \
    alarm_cool_off.service \
    campaigns_service.service \
    customer_health_agent.service \
    detection_processing.service \
    host_collection_scoring.service \
    mysql_ensure_users.service \
    py_blish.service \
    recall_tz_sync.service \
    sensor_detection_service.service \
    shell_knocker_context_couch_to_sql.service \
    sql_es_replicator.service \
    uwsgi.service \
    uwsgi_cloud_bridge.service \
    vui_services_agent.service \
    lc39_reachable_region_cache.service \
    vectra-ui-task-celery.service \
    vectra-ui-task-celerybeat.service \
    vectra-ui-task-celeryflower.service"

systemctl daemon-reload
systemctl disable ${bg_systemd_services} || true
systemctl enable ${bg_systemd_services}
systemctl restart ${bg_systemd_services} || true


# Attempt to wait and start UWSGI; previously failed to start service
if [[ "$CLOUD_BRIDGE" == "true" ]]; then
    /usr/sbin/service uwsgi_cloud_bridge stop || true
else
    /usr/sbin/service uwsgi stop || true
fi
lsof_ret=0
retry_count=0
while [ $lsof_ret -ne 1 ]
do
    retry_count=$((retry_count+1))
    if [ "$retry_count" -gt 10 ]; then
        # Note that this will kill other non-vui uwsgi services on the appliance as well
        # including cloud_proxy, DAM, and a number of other vui related apps.
        echo "ERROR: uwsgi appears to still be running.  There may be an orphaned worker.  killall uwsgi!!"
        killall uwsgi
        sleep 2
        break
    fi
    sleep 1
    lsof -i tcp@localhost:8001 && lsof_ret=$? || lsof_ret=$?
done

# Setup VUI service containers
## stop old containers
service vui-uwsgi stop || true
podman container stop vectra-vui-brain || true
## cleanup old container, if --rm is not passed as the exec command
podman container rm --force vectra-vui-brain || true
## cleanup old image
podman image rm vectra-vui-brain:latest || true
## load new image (disabled for now)
# unxz -c /data/containers/vectra-vui-latest.xz | podman image load

vui_uwsgi_systemd_services="\
    vui-uwsgi-internal.service \
    vui-uwsgi-replicator.service \
    vui-uwsgi-entity-receiver.service \
    vui-uwsgi.service"

systemctl daemon-reload
systemctl disable ${vui_uwsgi_systemd_services} || true
systemctl enable ${vui_uwsgi_systemd_services}
systemctl restart ${vui_uwsgi_systemd_services} || true

# Activate systemd timers
vui_systemd_timers="\
    EULA_to_cloud.timer \
    ad_metric_bulk.timer \
    alarm_cool_off.timer \
    autotriage.timer \
    autotriage_ensure_disablement.timer \
    autotriage_expire_targets.timer \
    bulk_account_reconciliation.timer \
    campaigns_cleanup.timer\
    cantina_forwarder.timer \
    cantina_pusher.timer \
    cloud_stats_push.timer \
    daily_digest.timer \
    email_agent.timer \
    es_health_check_comparison.timer \
    fix_sensor_pairing.timer \
    hero_report.timer \
    hero_report_daily.timer \
    host_detection_research_push.timer \
    lc39_reachable_region_cache.timer \
    licensing_metrics.timer \
    lockdown_monitor.timer \
    migrate_vectra_bad_hosts_defender.timer \
    notification_agent.timer \
    prune_kerberos_events.timer \
    push_score_data.timer \
    reevaluate_all_smart_rules.timer \
    remote_support_heartbeat.timer \
    reporting_agent.timer \
    selective_pcap_downloaded_manager.timer \
    selective_pcap_deleted_manager.timer \
    selective_pcap_scheduled_manager.timer \
    selective_pcap_stopped_manager.timer \
    sensor_detection_service.timer \
    sftp_downloader.timer \
    sql_health_service.timer \
    sql_log_reaper.timer \
    sql_reaper.timer \
    threat_intel_purge.timer \
    update_account_t_c_scores.timer \
    host_sessions_sync.timer \
    sync_data_sources.timer \
    refresh_cached_views.timer \
    cleanup_data_sources.timer \
    build_learner_state_on_prem.timer"

systemctl daemon-reload
systemctl reenable ${vui_systemd_timers}

vui_deprecated_systemd_units="\
    migrate_vectra_bad_hosts.timer \
    bulk_autotriage_metrics.timer \
    vui-uwsgi-account-service.service \
    lc39_updater_secrets.timer \
    vui-uwsgi-detection-detail.service \
    host_couch_listener.service \
    host_sessions_couch_listener.service \
    sync_flags_to_ui_db.timer \
    update_account_privilege_scores.timer \
    update_account_privilege_scores.service \
    update_probable_owners.timer"

systemctl disable ${vui_deprecated_systemd_units} || true
systemctl stop ${vui_deprecated_systemd_units} || true

# Stop and start nginx so new upstart configs are picked up
/usr/sbin/service nginx stop
/usr/sbin/service nginx start
# Start uwsgi with upstart
if [[ "$CLOUD_BRIDGE" == "true" ]]; then
    /usr/sbin/service uwsgi_cloud_bridge start || true
else
    /usr/sbin/service uwsgi start || true
fi

chown root:vectra_procs /etc/timezone
chmod 664 /etc/timezone

# check if keys are in /opt/vectra/keys
if [ ! -f /opt/vectra/keys/rsa.key ]; then
    if [ -f /root/rsa.key ]; then
        cp /root/rsa.key /opt/vectra/keys/rsa.key
    fi
fi
if [ -f /opt/vectra/keys/rsa.key ]; then
    chown root:vectra_procs /opt/vectra/keys/rsa.key
    chmod 0440 /opt/vectra/keys/rsa.key
fi
if [ ! -f /opt/vectra/keys/rsa.key.pub ]; then
    if [ -f /root/rsa.key.pub ]; then
        cp /root/rsa.key.pub /opt/vectra/keys/rsa.key.pub
    fi
fi
if [ -f /opt/vectra/keys/rsa.key.pub ]; then
    chown root:vectra_procs /opt/vectra/keys/rsa.key.pub
    chmod 0440 /opt/vectra/keys/rsa.key.pub
fi
if [ ! -f /opt/vectra/keys/aes.key ]; then
    ${PYTHON_RUNTIME} /opt/tracevector/bin/generate_aeskey.py
    chown root:vectra_procs /opt/vectra/keys/aes.key
    chmod 0440 /opt/vectra/keys/aes.key

    # Migrate plaintext smtp password to encrypted form using new aes.key
    ${PYTHON_RUNTIME} /opt/tracevector/bin/smtp_password_migration.py
else
    # Ensure that this key has the correct permissions even if it currently exists
    chown root:vectra_procs /opt/vectra/keys/aes.key
    chmod 0440 /opt/vectra/keys/aes.key
fi
if [ -f /opt/vectra/keys/sensor_aws.rsa.key.pub ]; then
    # sensors now use sensor registration token, remove default registration key
    rm -f /opt/vectra/keys/sensor_aws.rsa.key.pub
fi

if [ -f /opt/vectra/keys/cloud_encrypt.key.pub ]; then
    chown root:vectra_procs /opt/vectra/keys/cloud_encrypt.key.pub
    chmod 0440 /opt/vectra/keys/cloud_encrypt.key.pub
fi

# Remove old senso/stream update files
rm -rf /opt/updater/apt

# Set correct permissions for sudoers file
chmod 0440 /etc/sudoers.d/www-data

# Enforce safe permissions for my.cnf file
if [ -f /etc/mysql/my.cnf ];then
    chown root:root /etc/mysql/my.cnf
    chmod 644 /etc/mysql/my.cnf
fi

# Check for syslog directories and create if not found
SYSLOG_DIRS=("/etc/syslog-ng" "/etc/syslog-ng/backup")
CERT_DIRS=("ca.d" 'cert.d' 'key.d' 'conf.d')
for DIR in "${SYSLOG_DIRS[@]}" ; do
    for CERT in "${CERT_DIRS[@]}" ; do
        mkdir -p "$DIR/$CERT"
        chown -R root:vectra_procs "$DIR/$CERT"
        chmod -R g+w "$DIR/$CERT"
    done
done

for fname in \
    /etc/syslog-ng/conf.d/colossus.conf /etc/syslog-ng/conf.d/colossus_1.conf /etc/syslog-ng/conf.d/colossus_2.conf /etc/syslog-ng/conf.d/colossus_logstash.conf
do
    touch "$fname"
    chgrp vectra_procs "$fname"
    chmod g+w "$fname"
done

if [ ! -f /etc/vectra-vpn/state/enable ]; then
        mkdir -p /etc/vectra-vpn
        mkdir -p /etc/vectra-vpn/state
        # vpn state/enable file needs to be writable / owned by ui
        chown www-data:www-data /etc/vectra-vpn/state
        echo "enable=off" > /etc/vectra-vpn/state/enable
        chown www-data:www-data /etc/vectra-vpn/state/enable
fi

# Add write ability to vectra_procs and set umask to automatically do this for future files
if [ -d /opt/tracevector/logs ]; then
    chmod -f 664 /opt/tracevector/logs/* || true
    umask 0002 /opt/tracevector/logs
fi

# DC-737 Make sure the couchdb detector doc matches the current SQL state
${PYTHON_RUNTIME} /opt/tracevector/bin/fix_detector_config.py || true

# Allow vadmin/adm group to read django logs
mkdir -p /var/log/uwsgi/app --mode 0755
chmod 0755 /var/log/uwsgi
chmod 0640 /var/log/uwsgi/app/vui-uwsgi*.log || true
chgrp adm /var/log/uwsgi/app
if [ "$(ls /var/log/uwsgi/app/* 2>/dev/null)" ]; then
    chgrp adm /var/log/uwsgi/app/*
fi
chmod g+s /var/log/uwsgi/app

mkdir -p -m 755 /opt/vectra/sensor-reports/
chown www-data:www-data /opt/vectra/sensor-reports/

mkdir -p -m 644 /data/vui/selective_pcap/downloading
chown www-data:www-data /data/vui/selective_pcap
chown www-data:www-data /data/vui/selective_pcap/downloading
chmod 755 /data/vui/selective_pcap/downloading

# Sync offline_update_enabled feature flag with manual_update_enabled key from tvui_setting
tvui_pw=`cat /etc/secrets/mysql_tvui_writer`
manual_update_enabled=$(mysql -u "tvui_writer" -p$tvui_pw -h "127.0.0.1" -D "tvui" -BNe "SELECT value FROM tvui_setting WHERE tvui_setting.group='version' AND tvui_setting.key='manual_update_enabled'")
if [ -z "$manual_update_enabled" ]; then
    echo "'manual_update_enabled' key in tvui_settings database not present. Skipping platform/offline_update_enabled feature flag sync."
else
    echo "Syncing platform/offline_update_enabled feature flag with 'manual_update_enabled' key in tvui_settings database for migration."
    if [ "$manual_update_enabled" = "on" ]; then
        curl -X PUT -d '{"value":true}' 127.0.0.1:5010/api/flag/platform/offline_update_enabled
        data=true
    else
        curl -X PUT -d '{"value":false}' 127.0.0.1:5010/api/flag/platform/offline_update_enabled
        data=false
    fi

    response=`curl 127.0.0.1:5010/api/flag/platform/offline_update_enabled | jq '.data | .value'`

    if [ "$response" != "$data" ]; then
        echo "ERROR: feature flag 'platform/offline_update_enabled' not set properly."
    fi

    response=$(mysql -u "tvui_writer" -p$tvui_pw -h "127.0.0.1" -D "tvui" -e "DELETE FROM tvui_setting WHERE tvui_setting.group='version' AND tvui_setting.key='manual_update_enabled'" 2>&1)

    if [ -z "$response" ]; then
        echo "'manual_update_enabled' key in tvui_settings database deleted"
    else
        echo "Error while trying to delete 'manual_update_enabled' key from tvui_settings database:"
        echo $response
    fi
fi
