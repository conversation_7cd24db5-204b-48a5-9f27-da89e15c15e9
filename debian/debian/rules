#!/usr/bin/make -f
PYTHON_VENV = vectra-vui-py38-202007 (>= 0.21.87)
DH_INITLIB = dh_installsystemd

SUBSTVARS = -Vdist:PreDepends="$(PYTHON_VENV)"

override_dh_usrlocal:

%:
	dh $@

override_dh_auto_build:
	dh_auto_build

override_dh_install:
	dh_install

override_dh_gencontrol:
	dh_gencontrol -- $(SUBSTVARS)

override_dh_installinit:
	$(DH_INITLIB) --name campaigns_service
	$(DH_INITLIB) --name cleanup_data_sources
	$(DH_INITLIB) --name cloud_stats_push
	$(DH_INITLIB) --name detection_processing
	$(DH_INITLIB) --name daily_digest
	$(DH_INITLIB) --name hero_report
	$(DH_INITLIB) --name hero_report_daily
	$(DH_INITLIB) --name alarm_cool_off
	$(DH_INITLIB) --name host_collection_scoring
	$(DH_INITLIB) --name host_detection_research_push
	$(DH_INITLIB) --name host_sessions_sync
	$(DH_INITLIB) --name notification_agent
	$(DH_INITLIB) --name email_agent
	$(DH_INITLIB) --name es_health_check_comparison
	$(DH_INITLIB) --name offline_install
	$(DH_INITLIB) --name py_blish
	$(DH_INITLIB) --name reporting_agent
	$(DH_INITLIB) --name reevaluate_all_smart_rules
	$(DH_INITLIB) --name shell_knocker_context_couch_to_sql
	$(DH_INITLIB) --name sql_es_replicator
	$(DH_INITLIB) --name threat_intel_purge
	$(DH_INITLIB) --name EULA_to_cloud
	$(DH_INITLIB) --name sql_log_reaper
	$(DH_INITLIB) --name sql_reaper
	$(DH_INITLIB) --name sql_health_service
	$(DH_INITLIB) --name sftp_downloader
	$(DH_INITLIB) --name vui_services_agent
	$(DH_INITLIB) --name mysql_ensure_users
	$(DH_INITLIB) --name update_account_t_c_scores
	$(DH_INITLIB) --name push_score_data
	$(DH_INITLIB) --name campaigns_cleanup
	$(DH_INITLIB) --name prune_kerberos_events
	$(DH_INITLIB) --name recall_tz_sync
	$(DH_INITLIB) --name admin_pw_set
	$(DH_INITLIB) --name licensing_metrics
	$(DH_INITLIB) --name lockdown_monitor
	$(DH_INITLIB) --name fix_sensor_pairing
	$(DH_INITLIB) --name customer_health_agent
	$(DH_INITLIB) --name uwsgi
	$(DH_INITLIB) --name uwsgi_cloud_bridge
	$(DH_INITLIB) --name vui-uwsgi
	$(DH_INITLIB) --name vui-uwsgi-internal
	$(DH_INITLIB) --name vui-uwsgi-replicator
	$(DH_INITLIB) --name vui-uwsgi-entity-receiver
	$(DH_INITLIB) --name bulk_account_reconciliation
	$(DH_INITLIB) --name migrate_vectra_bad_hosts_defender
	$(DH_INITLIB) --name migrate_vectra_bad_hosts_defender.timer
	$(DH_INITLIB) --name autotriage
	$(DH_INITLIB) --name autotriage_ensure_disablement
	$(DH_INITLIB) --name autotriage_expire_targets
	$(DH_INITLIB) --name ad_metric_bulk
	$(DH_INITLIB) --name remote_support_heartbeat
	$(DH_INITLIB) --name selective_pcap_downloaded_manager
	$(DH_INITLIB) --name selective_pcap_downloaded_manager.timer
	$(DH_INITLIB) --name selective_pcap_scheduled_manager
	$(DH_INITLIB) --name selective_pcap_scheduled_manager.timer
	$(DH_INITLIB) --name selective_pcap_stopped_manager
	$(DH_INITLIB) --name selective_pcap_stopped_manager.timer
	$(DH_INITLIB) --name selective_pcap_deleted_manager
	$(DH_INITLIB) --name selective_pcap_deleted_manager.timer
	$(DH_INITLIB) --name cantina_forwarder
	$(DH_INITLIB) --name cantina_pusher
	$(DH_INITLIB) --name sensor_detection_service
	$(DH_INITLIB) --name lc39_reachable_region_cache
	$(DH_INITLIB) --name lc39_reachable_region_cache.timer
	$(DH_INITLIB) --name sync_data_sources
	$(DH_INITLIB) --name sync_data_sources.timer
	$(DH_INITLIB) --name refresh_cached_views
	$(DH_INITLIB) --name refresh_cached_views.timer
	$(DH_INITLIB) --name vectra-ui-task-celery
	$(DH_INITLIB) --name vectra-ui-task-celerybeat
	$(DH_INITLIB) --name vectra-ui-task-celeryflower
	$(DH_INITLIB) --name build_learner_state_on_prem
