# Copyright (c) 2015 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

"""Detection Processing Class

   - Pull new detection DETAILS
   - Run O365 detections through the distillation framework
   - Metadata processing (add fields/modify fields)
   - Evaluate Smart Rules
   - Bucket details to detection 'parents'
   - Detection Scoring for affected detections
   - Host Scored for the affected identified Hosts
   - Syslog Notification

SAAS Metrics:
    - vui_new_detections
"""

import hashlib
import json
import time
from collections import defaultdict
from functools import lru_cache

import django.db
from django.db.models import Q
from django.forms.models import model_to_dict
from django.utils import timezone
from pure_utils import log_utils, syslog_utils, net_utils, iter_utils, event_utils
from typing import List, Dict, Tuple, Generator

from base_tvui.lib_mitre import MITRE_FRAMEWORK_MAPPINGS_LABELS
from base_tvui.account_type import AccountType
from base_tvui import lib_syslog, lib_reaper, lib_tv
from base_tvui.feature_flipper import conditions, flag_enabled, Flags
from base_tvui.lib_cloud_metrics import Metrics, MetricName
from base_tvui.smart_rules import smart_rule_nme_evaluation, smart_rule_dd_comparison, smart_rule_utils
from detection_processing_v2 import (
    detection_scoring,
    detection_bucketing,
    detection_detail_manager,
    detection_processing_utils,
    detection_scoring_utils,
)
from detection_processing_v2.detection_bucketing import VNAME_CAT_MAP
from distillation.common import (
    DataSourceType,
    NETWORK_DISTILL_DETECTION_TYPES,
    NO_DISTILL_DETECTION_TYPES,
    is_distillation_enabled,
    DISTILLATION_DATA_SOURCE_TYPES,
)
from distillation.distillation_framework.distiller import run_distiller
from tvui import models
from tvui.detections.detection_types import DetectionType
from tvui.detections.dynamic_detections.dynamic_processors import DetectionEventDetailProcessor
from tvui.helpers import DETECTION_METADATA

# Setting log defaults
logger = log_utils.get_vectra_logger(__name__)

MAX_DETAILS_PER_BUCKET = 100


@lru_cache
def _load_detail_schemas():
    """
    Load and cache all detection detail schemas.
    """
    return {
        detection_schema.type: detection_schema.schema.get('detailProcessor') for detection_schema in models.DetectionSchema.objects.all()
    }


@lru_cache
def _load_syslogProcessor():
    return {
        detection_schema.type: detection_schema.schema.get('syslogProcessor') for detection_schema in models.DetectionSchema.objects.all()
    }


class Processing(object):
    """Processing: Process new detections coming into MySQL"""

    def __init__(self):
        """Initialize Processing Class"""
        logger.info('Initialize...')

        # Detection Detail Manager Class
        self._dd_manager = detection_detail_manager.DetectionDetailManager()

        # Detections and Detection Details Tables
        self._detection_table = 'tvui_detection'
        self._detection_details_table = 'tvui_detection_details'
        self._host_session_table = 'tvui_host_session'

        # Smart Rules Evaluation Class
        self._smart_rule_evaluation = smart_rule_nme_evaluation.SmartRuleNmeEvaluation()

        # Detection Scoring Class
        self._detection_scoring = detection_scoring.DetectionScoring(couch_retry=True)

        # Initialize vars
        self.timer = 10

    def run(self):
        """Main run loop for processing new detections coming into MySQL"""
        logger.info('Running...')
        logger.info('One time pruning...')
        self._one_time_pruning()
        while True:
            self.process_new_detections()
            time.sleep(self.timer)

    def run_once(self):
        self._one_time_pruning()
        self.process_new_detections()

    def _split_detections_for_distillation(self, dd_generator: List) -> Tuple[List[Dict], Dict[str, List[Dict]]]:
        """Split detection details into distillation enabled and not enabled types, where enabled types
        are separated by data source type."""

        not_enabled_dd = []
        enabled_dd_by_source = {}
        for data_source_type in DISTILLATION_DATA_SOURCE_TYPES:
            if is_distillation_enabled(data_source_type):
                enabled_dd_by_source[data_source_type] = []

        if len(enabled_dd_by_source) > 0:
            for dd in dd_generator:
                detail_type = dd["type"]
                detail_source_type = DataSourceType.type_to_source_type(detail_type)
                if detail_source_type in enabled_dd_by_source and detail_type not in NO_DISTILL_DETECTION_TYPES:
                    enabled_dd_by_source[detail_source_type].append(dd)
                else:
                    not_enabled_dd.append(dd)
        else:
            not_enabled_dd = dd_generator

        return not_enabled_dd, enabled_dd_by_source

    def _run_distillation(self, data_source_type: DataSourceType, distiller_dd: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        Take in list of details and run distillation, returning two lists:
        distilled details and non-distilled details.
        """
        distilled_dd, non_distilled_dd = [], distiller_dd
        if is_distillation_enabled(data_source_type):
            if distiller_dd:
                logger.info(f'Running Distillation Framework on {len(distiller_dd)} {data_source_type} detection details...')
                distilled_dd, non_distilled_dd = run_distiller(detection_details=distiller_dd, data_source_type=data_source_type)
            else:
                Metrics.count(MetricName.DISTILLATION_DETAIL_HISTORY_COUNT, 0, labels={"data_source_type": data_source_type})
        return distilled_dd, non_distilled_dd

    def process_new_detections(self, limit=2000):
        """Processing new detection details coming into MySQL"""
        logger.info('Start processing new detections')
        processing_start_time = time.time() * 1000
        # Load up any NEW detection details
        # Note: The Detection Details Manager produces a generator
        dd_list = list(self._dd_manager.load_detection_details(realtime=True, limit=limit))
        total_dd = len(dd_list)
        logger.info('Loaded new details to process')

        logger.info(f'Processing {total_dd} New Detections...')
        Metrics.count(MetricName.DET_PROC_NEW_DETECTIONS, total_dd)

        # track when number of detections is maxed out
        if limit == total_dd:
            Metrics.increment(MetricName.DET_PROC_MAX_NEW_DETECTIONS)

        logger.debug('Metadata Processing...')
        self._meta_data_processing(dd_list)

        # separate out distillable detections to run through distillation
        dd_list, distillable_dd_by_source = self._split_detections_for_distillation(dd_list)

        # run distillation
        total_distilled_dd = []
        for data_source_type, distillable_dd in distillable_dd_by_source.items():
            distilled_dd, non_distilled_dd = self._run_distillation(data_source_type, distillable_dd)
            dd_list.extend(non_distilled_dd)
            total_distilled_dd.extend(distilled_dd)

        logger.debug('Matching SmartRules...')
        matches = self._smart_rule_evaluation.evaluate_all(dd_list)
        smart_rule_utils.update_rule_timestamps(smart_rule_dd_comparison.compute_match_changes(details=dd_list, matches=matches))
        smart_rule_dd_comparison.apply_matches_to_details(details=dd_list, matches=matches)

        # Add back in detections that were already distilled by the Distillation Framework
        dd_list.extend(total_distilled_dd)

        logger.debug('Bucketing...')
        affected_ids = detection_bucketing.bucket_detection_details(dd_list)

        self.register_new_host_roles(dd_list)

        logger.debug('Cleaning old detections...')
        reaper = lib_reaper.Reaper()
        reaper._cleanup_smuggler_detections()

        logger.debug('Pruning Detection Details...')
        for detection_id in affected_ids:
            try:
                self._prune_detection_details(detection_id)
            except Exception:
                logger.exception('Failed to prune details for detection {}'.format(detection_id))

        affected_ids += detection_scoring_utils.find_detections_that_need_scoring()
        affected_ids = list(set(affected_ids))

        reason_map = detection_scoring_utils.map_host_score_reasons(affected_ids)
        detection_scoring_utils.full_rescore(
            detection_ids=affected_ids,
            detection_scoring=self._detection_scoring,
            host_score_reason=reason_map,
            account_score_reason='detection',
        )

        if flag_enabled(Flags.settings_syslog):
            logger.debug('Syslog...')
            # force cache refresh of syslog formats
            syslog_utils.get_syslog_formats(refresh=True)
            for det_detail in dd_list:
                notify_syslog(det_detail)

        if flag_enabled(Flags.detection_events):
            logger.info(f'Processing {len(dd_list)} detection event details...')
            self._process_event_details(dd_list)
            if len(dd_list) > 0:
                Metrics.increment(MetricName.SUCCESSFUL_DETECTIONS_PROCESSED)

        processing_end_time = time.time() * 1000
        Metrics.timing(
            name=MetricName.DET_PROC_TIME,
            value=(processing_end_time - processing_start_time),
            labels={'isDistillation': any(is_distillation_enabled(source_type) for source_type in DISTILLATION_DATA_SOURCE_TYPES)},
        )

        if conditions.is_cloud():
            self._post_latency_metrics(dd_list)

        logger.info('End processing new detections')
        return total_dd

    def _post_latency_metrics(self, dd_list):
        """Calculate and record pipeline latency metrics of details after processing"""

        timing_fields = {
            'COUCH_2_PUB': ('date_couch', 'date_publish'),
            'PUB_2_DEP': ('date_publish', 'date_s3'),
            'DEP_2_DETAIL': ('date_s3', 'date_created'),
            'DETAIL_2_BUCKET': ('date_created', 'date_first_bucket'),
            'COUCH_2_BUCKET': ('date_couch', 'date_first_bucket'),
            'LAST_TIMESTAMP_2_BUCKET': ('last_timestamp', 'date_first_bucket'),
        }

        # Some of these things only apply to network data sources ('sensor_luid'? 'date_couch'?)
        # what about aws, o365? for now, let's track a rough count of details missing timestamps
        details_missing_timestamps_by_datasource = defaultdict(set)

        for detail in dd_list:
            try:
                data_source = ''
                category = ''

                # logic copied to match: detection_bucketing.py:_DetectionBucketing._make_new_bucket
                if detail['type'].startswith('cm_'):
                    category = detail['category']
                    data_source = models.DataSourceType.NETWORK
                elif detail['type'] in VNAME_CAT_MAP:
                    category = VNAME_CAT_MAP[detail['type']][1]
                    data_source = VNAME_CAT_MAP[detail['type']][2]
                else:
                    category = 'Unknown'
                    data_source = 'Unknown'

                metric_labels = {
                    'entity_type': 'host' if detail['is_host_detail'] else 'account',
                    'detail_type': detail['type'],
                    'data_source': data_source,
                }

                for metric_name, (attr_t_start, attr_t_end) in timing_fields.items():
                    if not ((t_start := detail.get(attr_t_start)) and (t_end := detail.get(attr_t_end))):
                        details_missing_timestamps_by_datasource[data_source].add(detail['id'])
                        logger.info(
                            'detail %s (couch %s) missing timing metric %s', detail['id'], detail.get('couch_note_id', '-'), metric_name
                        )
                        continue

                    latency = (t_end - t_start).total_seconds()
                    name = getattr(MetricName, f'DETECTION_PIPELINE_LAG__{metric_name}{"__INFO" if category == "info" else ""}')
                    Metrics.timing(name, value=latency, labels=metric_labels)

            except Exception:
                logger.exception('Unable to calculate pipeline latency metrics for detection detail')

        try:
            for data_source, details in details_missing_timestamps_by_datasource.items():
                Metrics.count(MetricName.DETECTION_PIPELINE_DETAILS_MISSING_TIMES, value=len(details), labels={'data_source': data_source})
        except Exception:
            logger.exception('Unable to count detection details missing pipeline timings')

    def _get_type_from_spa_detection(self, det_detail):
        if not det_detail['type'].startswith('spa_'):
            return None
        detection_type_name = DETECTION_METADATA.get(det_detail['type'], {}).get('vname', '')
        detection_name = det_detail.get('flex_json.metadata.name', '')
        return f"{detection_type_name}: {detection_name}"

    def _get_mitre_from_spa_detection_detail(self, det_detail):
        if not det_detail['type'].startswith('spa_'):
            return None
        mitre_list = det_detail.get('flex_json.metadata.mitre', [])
        return [item.get('mitre_id') for item in mitre_list if 'mitre_id' in item and item.get('mitre_id')]

    def register_new_host_roles(self, detail_list):
        try:
            for detail in detail_list:
                if detail.get('type') == 'si_new_host_role':
                    curr_time = detail.get('last_timestamp') or timezone.now()
                    infected_host_id = detail.get('host_id')
                    note_role_name = detail.get("flex_json.role_display_name")
                    if note_role_name and infected_host_id:
                        models.HostRole.objects.update_or_create(
                            host_id=infected_host_id,
                            role_name=note_role_name,
                            defaults={"date_last_seen": curr_time, "source": "detection_event"},
                        )
        except Exception as excpt:
            logger.exception("Error when attempting to register new host role - {excpt}")

    def _create_detection_event(self, det_detail: dict, detection_id: int, data: dict, entity_type: str) -> models.DetectionEvent:
        """Detection event creation helper"""
        return models.DetectionEvent(
            version=lib_tv.get_version_number(),
            type=det_detail['type'],
            entity_type=entity_type,
            detection_detail_id=det_detail['id'],
            detection_id=detection_id,
            category=DETECTION_METADATA[det_detail['type']].get('category'),
            threat=det_detail.get('flex_json.threat_score') or 0,
            certainty=det_detail.get('flex_json.certainty_score') or 0,
            triage_id=det_detail['smart_rule_id'],
            host_session_id=det_detail['host_session_id'],
            ip_address=det_detail['src_ip'],
            account_id=det_detail['account_id'],
            account_uid=det_detail['account_uid'],
            timestamp=det_detail['date_created'],
            data=data,
            src_host_session_luid=det_detail['src_session_luid'],
            detection_vname=self._get_type_from_spa_detection(det_detail),
            mitre_list=self._get_mitre_from_spa_detection_detail(det_detail),
        )

    def _process_event_details(self, det_details, chunk_size=50):
        detection_events = []
        det_detail_objs = models.detection_detail.objects.filter(id__in={det_detail['id'] for det_detail in det_details})
        det_detail_objs_map = {det_detail_obj.id: det_detail_obj for det_detail_obj in det_detail_objs}
        for det_detail in det_details:
            detail_schema = _load_detail_schemas().get(det_detail['type'], {})
            syslog_schema = _load_syslogProcessor().get(det_detail['type'])

            if (det_detail_obj := det_detail_objs_map.get(det_detail['id'])) is None:
                logger.warning(
                    f"Detection detail with id {det_detail['id']} does not exist. "
                    f"Detection Type: {det_detail['type']}, "
                    f"Account Detection ID: {det_detail['account_detection_id']}, "
                    f"Host Detection ID: {det_detail['host_detection_id']}."
                )
                continue

            detection_id = det_detail_obj.host_detection.id if det_detail['is_host_detail'] else det_detail_obj.account_detection.id
            if flag_enabled(Flags.v3_3_detection_event_details) and (
                det_detail['type'] in detection_processing_utils.V3_3_DETAIL_DETECTION_TYPES or detail_schema == {}
            ):
                data = detection_processing_utils.process_v3_3_detection_event_data(det_detail, syslog_schema)
            else:
                data = DetectionEventDetailProcessor(detail_schema, model_to_dict(det_detail_obj)).process_detail()

            detection_events.append(
                self._create_detection_event(det_detail, detection_id, data, 'host' if det_detail['is_host_detail'] else 'account')
            )

            # add second event with opposite entity type for special case PAA type detections with both account and host information
            if det_detail['type'].startswith('papi') and det_detail['account_id'] and det_detail['session_luid']:
                detection_events.append(
                    self._create_detection_event(det_detail, detection_id, data, 'account' if det_detail['is_host_detail'] else 'host')
                )

        for chunk in iter_utils.chunker(chunk_size, detection_events):
            models.DetectionEvent.objects.bulk_create(chunk)

    def _meta_data_processing(self, dd_list):
        """Add any additional meta data that the detail might need"""
        for detail in dd_list:
            # Internal Spreading: compute payload hash and place into reason field
            if detail['type'] == 'internal_spreading':
                if detail['description']:
                    # Update both the internal detail and the one in the database
                    logger.info('Saving payload hash for detail %d', detail['id'])
                    payload = str(detail['host_detection_id']) + detail['description']
                    payload_hash = hashlib.md5(payload.encode('utf-8')).hexdigest()[:6]
                    detail['reason'] = payload_hash

                    # Update database
                    query = 'UPDATE tvui_detection_detail SET reason="%s" where id=%d' % (payload_hash, detail['id'])
                    cursor = django.db.connection.cursor()
                    cursor.execute(query)

            # Port Sweep: decompresss delta IP ranges into CIDR and place into description field
            elif detail['type'] in ['port_sweep', 'out_port_sweep']:
                # Convert and Update database
                try:
                    delta_encoding = json.loads(detail['description'])
                    cidr_list = net_utils.delta_to_cidr(delta_encoding)
                    detail['description'] = ', '.join(cidr_list)
                    query = 'UPDATE tvui_detection_detail SET description="%s" where id=%d' % (detail['description'], detail['id'])
                    cursor = django.db.connection.cursor()
                    cursor.execute(query)
                except ValueError:
                    logger.error('JSON decode fail on: %s', detail['description'])

        # Commit any MySQL changes
        django.db.connection.commit()

    def _prune_detection_details(self, det_id):
        """
        Prunes detection details from the provided detection bucket.
        This is a two step process:
           - First, cut the foreign key links from "old" details to the provided bucket.
           - Then, delete any of those "old" details that are not pointing to another bucket.
        """
        logger.info('Pruning Details from Detection [ %d ]...', det_id)

        # Figure out if the detection is host-based or account-based
        host_based = True

        try:
            det = models.detection.pure.get(id=det_id)
        except models.detection.DoesNotExist:
            logger.warning('Detection [ %d ] does not exists, unable to prune', det_id)
            return

        if det.account_id is not None:
            host_based = False

        # We only want to keep MAX_DETAILS_PER_BUCKET details
        prune_older_than = None
        try:
            if host_based:
                detail_filter = Q(host_detection_id=det_id)
            else:
                detail_filter = Q(account_detection_id=det_id)
            prune_older_than = models.detection_detail.pure.filter(detail_filter).order_by('-id')[MAX_DETAILS_PER_BUCKET]
        except IndexError:
            # Not enough details
            return

        details_to_prune = models.detection_detail.pure.filter(detail_filter, id__lte=prune_older_than.id)
        detail_ids_to_prune = list(details_to_prune.values_list('id', flat=True))

        # Cut ties to the from the details to the bucket. The details may or may not still point to another bucket.
        if host_based:
            details_to_prune.update(is_host_detail=False, host_detection=None)
        else:
            details_to_prune.update(is_account_detail=False, account_detection=None)

        # Delete any details that are no longer used
        count_deleted, _ = models.detection_detail.pure.filter(
            id__in=detail_ids_to_prune, is_host_detail=False, is_account_detail=False
        ).delete()

        if conditions.is_cloud():
            Metrics.count(MetricName.DETAILS_PRUNED_DUE_TO_BUCKET_LIMIT, count_deleted, labels={'detection_type': det.type})
        else:
            event_utils.bundle_cloud_log(
                {
                    MetricName.DETAILS_PRUNED_DUE_TO_BUCKET_LIMIT: count_deleted,
                    'detection_type': det.type,
                },
                'details_prune_due_to_bucket_limit',
            )
        logger.info('Deleted %d details from detection [ %d ]', count_deleted, det_id)

    def _one_time_pruning(self, chunk_size=1000):
        """Prune detections that have a lot of details"""
        cursor = django.db.connection.cursor()
        query = """
            SELECT host_detection_id as detection_id, count(*) as count
            FROM tvui_detection_detail
            WHERE host_detection_id is not null
            GROUP BY host_detection_id

            UNION ALL

            SELECT account_detection_id as detection_id, count(*) as count
            FROM  tvui_detection_detail
            WHERE account_detection_id is not null
            GROUP BY account_detection_id
        """
        cursor.execute(query)

        rows = cursor.fetchmany(size=chunk_size)
        while rows:
            for row in rows:
                detection_id, count = row

                if count > MAX_DETAILS_PER_BUCKET:
                    try:
                        self._prune_detection_details(detection_id)
                    except Exception:
                        logger.exception('Failed to prune details for detection {}'.format(row.get('detection_id')))

            rows = cursor.fetchmany(size=chunk_size)

        cursor.close()


def notify_syslog(det_detail):
    """Notify syslog given detection details.

    `det_detail` should be what is returned from
    `detection_detail_manager.DetectionDetailManager.load_detection_details()`

    detection details with state=whitelist are ignored.
    """
    state = det_detail.get('state')
    if state is None or state != "whitelist":
        det_detail['mitre'] = []

        if det_detail['type'] in MITRE_FRAMEWORK_MAPPINGS_LABELS:
            det_detail['mitre'] = MITRE_FRAMEWORK_MAPPINGS_LABELS[det_detail['type']]

        elif det_detail.get('type') and det_detail['type'].startswith('spa_'):
            mitre_list = [item['mitre_id'] for item in det_detail.get('flex_json.metadata.mitre', [])]
            det_detail['mitre'] = mitre_list

        if det_detail.get('host_detection_id'):
            # Pull the detection and host data
            det_data = detection_processing_utils.get_syslog_detection_and_host_info(det_detail)
            # TODO colossus side changes and testing for syslog
            lib_syslog.syslog_detection_detail(det_detail, det_data, is_host_det=True)
        if det_detail.get('account_detection_id'):
            # Pull the detection and host data
            det_data = detection_processing_utils.get_syslog_detection_and_account_info(det_detail)
            lib_syslog.syslog_detection_detail(det_detail, det_data, is_host_det=False)
