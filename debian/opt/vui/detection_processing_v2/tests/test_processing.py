# Copyright (c) 2019 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
import json
import random
import uuid
import copy
from unittest.mock import ANY, call, patch
from decimal import Decimal
from datetime import timedelta

from base_tvui.feature_flipper.base import SimpleFlag
from base_tvui.feature_flipper.conditions import Envs
from base_tvui.lib_cloud_metrics import MetricName
from detection_processing_v2 import processing
from detection_processing_v2.processing import MAX_DETAILS_PER_BUCKET, _load_detail_schemas
from detection_processing_v2.detection_processing_utils import process_v3_3_detection_event_data, process_v3_3_additional_details
from distillation.common import DataSourceType, DISTILLATION_DATA_SOURCE_TYPES
from django.test import TransactionTestCase
from django.utils import timezone
from tvui.detections.detection_types import DetectionType
from tvui.models import host, host_session, detection_detail, Account, detection, smart_rule, setting, DetectionEvent, NetworkSensor
from base_tvui import lib_account, account_type
from base_tvui.feature_flipper import Flags
import vui_tests.base_tvui.smart_rules.testing_utils as testing_utils
from vui_tests.tvui.detections.dynamic_detections.test_dynamic_utils import generate_schema_by_type
from tvui import models


class ProcessingTest(TransactionTestCase):
    def setUp(self):
        self.now = timezone.now()
        self.sensors = {}
        self.hosts = {}
        self.host_sessions = {}
        self.detection_details = {}
        self.dns = {}

        self.metrics_lib = patch("detection_processing_v2.processing.Metrics")
        self.mock_metrics_lib = self.metrics_lib.start()
        self.get_scoring_ranges_patch = patch('base_tvui.couch_utils.get_named_document')
        self.get_scoring_ranges_mock = self.get_scoring_ranges_patch.start()
        self.get_scoring_ranges_patch.return_value = '[1,100]'

        self.version_patch = patch('base_tvui.lib_tv.get_version_number', return_value="2022.0.0")
        self.version_patch.start()
        self.addCleanup(self.version_patch.stop)

        self.syslog_patch = patch("pure_utils.syslog_utils.syslog_detection_detail", autospec=True)
        self.syslog_mock = self.syslog_patch.start()

        self.mock_hs_open_patch = patch('host_scoring_v2.host_scoring.score_host')
        self.mock_hs_open_mock = self.mock_hs_open_patch.start()
        self.mock_hs_open_patch.return_value = True

        self.mock_as_open_patch = patch('detection_processing_v2.detection_scoring_utils.lib_account.rescore_accounts')
        self.mock_as_open_mock = self.mock_as_open_patch.start()
        self.mock_as_open_patch.return_value = True

        self.mock_get_hostname_and_default_ip_patch = patch(
            'base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('***************', '***************')
        )
        self.mock_get_hostname_and_default_ip_patch.start()

        self.processing = processing.Processing()

        self.base_host_session_detection_detail_data = {
            'count': 1,
            'dst_dns': 'static-**************-tataidc.co.in',
            'dst_geo': 'IN',
            'dst_geo_lat': Decimal('20.983300'),
            'dst_geo_lon': Decimal('77.583300'),
            'proto': 'tcp',
            'is_host_detail': True,
            'is_account_detail': False,
            'first_timestamp': self.now - timedelta(days=6),
            'last_timestamp': self.now - timedelta(days=1),
            'type': DetectionType.REVERSE_RAT,
            'date_couch': self.now - timedelta(minutes=6),
            'date_publish': self.now - timedelta(minutes=4),
            'date_s3': self.now - timedelta(minutes=3),
        }

        _load_detail_schemas.cache_clear()

    def tearDown(self):
        """Tears down patches"""
        self.get_scoring_ranges_patch.stop()
        self.syslog_patch.stop()
        self.mock_hs_open_patch.stop()
        self.mock_as_open_patch.stop()
        self.mock_get_hostname_and_default_ip_patch.stop()
        self.metrics_lib.stop()

    def simulated_run(self, chunk_size=1000):
        self.processing._one_time_pruning(chunk_size)
        self.processing.process_new_detections()

    def get_default_sensor_luid(self, sensor_luid):
        if not sensor_luid and len(self.sensors) > 0:
            sensor_luid = list(self.sensors.keys())[0]
        elif not sensor_luid:
            sensor_luid = str(uuid.uuid4())

        return sensor_luid

    def get_or_create_default_host_session(self, host_id=None):
        if not host_id:
            new_host = self.get_or_create_default_host()
            host_id = new_host.id

        new_hs = self.create_host_session(host_id)

    def create_sensor(self, sensor_luid=None):
        if not sensor_luid:
            sensor_luid = str(uuid.uuid4())

        new_sensor = NetworkSensor.objects.create(
            alias='test-sensor',
            serial_number='S1234567890',
            status='paired',
            luid=sensor_luid,
            mode='sensor',
            ip_address='************',
            product_name='S2',
            last_seen=self.now,
            details={'update_count': 1, 'location': 'US'},
        )

        self.sensors[new_sensor.luid] = new_sensor

        return new_sensor

    def create_host(self, sensor_luid=None):
        if not sensor_luid:
            new_sensor = self.create_sensor()
            sensor_luid = new_sensor.luid

        new_host = host.objects.create(
            name='test_src_host_{}'.format(random.randint(0, 10000)),
            key_asset=1,
            state='active',
            last_source='*********',
            t_score=random.randint(0, 99),
            c_score=random.randint(0, 99),
            sensor_luid=sensor_luid,
            last_detection_timestamp=self.now,
        )

        new_host.host_sessions = {}
        self.hosts[new_host.id] = new_host

        return new_host

    def create_host_session(self, host_id=None, session_luid=None):
        if not session_luid:
            session_luid = str(uuid.uuid4())

        if not host_id:
            new_host = self.create_host()
            host_id = new_host.id

        new_hs = host_session.objects.create(
            host=self.hosts[host_id],
            ip_address='*********',
            session_luid=session_luid,
            sensor_luid=self.hosts[host_id].sensor_luid,
            start=self.now - timedelta(days=3),
            end=self.now - timedelta(days=2),
        )

        self.host_sessions[new_hs.id] = new_hs
        self.hosts[host_id].host_sessions[new_hs.id] = new_hs

        return new_hs

    def create_account(self):
        return Account.objects.create(
            uid='testAccount_{}@fake.net'.format(str(uuid.uuid4())),
            account_type=Account.TYPE_KERBEROS,
            state='active',
            t_score=random.randint(0, 99),
            c_score=random.randint(0, 99),
            last_seen=self.now,
            first_seen=self.now,
        )

    def create_detection_detail(
        self,
        host_detection=True,
        account_detection=False,
        src_host_sess=None,
        dst_host_sess=None,
        account=None,
        detection_type=None,
        flex_json=None,
        description=None,
    ):
        if not account:
            account = self.create_account()

        if not src_host_sess:
            src_host_sess = self.create_host_session()

        if not dst_host_sess:
            dst_host_sess = self.create_host_session()

        dd_data = copy.deepcopy(self.base_host_session_detection_detail_data)

        if detection_type:
            dd_data['type'] = detection_type
        dd_data['last_timestamp'] = self.now - timedelta(days=1)
        dd_data['first_timestamp'] = self.now - timedelta(days=2)
        dd_data['src_session_luid'] = src_host_sess.session_luid
        dd_data['src_ip'] = (src_host_sess.ip_address,)
        dd_data['sensor_luid'] = src_host_sess.sensor_luid
        dd_data['dst_session_luid'] = (dst_host_sess.session_luid,)
        dd_data['account_id'] = account.id
        dd_data['account_uid'] = account.uid
        dd_data['is_host_detail'] = host_detection
        dd_data['is_account_detail'] = account_detection
        if flex_json:
            dd_data['flex_json'] = flex_json
        if description:
            dd_data["description"] = description

        new_dd = detection_detail.objects.create(**dd_data)
        self.detection_details[new_dd.id] = new_dd

        return new_dd

    def test_missing_detections_on_prune(self):
        try:
            self.processing._prune_detection_details(-1)
        except Exception:
            self.fail("Failed to handle missing detection ids")

    def test_notify_syslog(self):
        for test_detail in [
            ({"type": None, "state": "whitelist"}, 0),
            ({"type": None, "state": "active", "host_detection_id": 1}, 1),
            ({"type": None, "state": None, "account_detection_id": 1}, 1),
            ({"type": None, "state": None, "account_detection_id": 1, "host_detection_id": 1}, 2),
        ]:
            det_detail, call_count = test_detail
        processing.notify_syslog(det_detail)
        self.assertEqual(self.syslog_mock.call_count, call_count)
        self.syslog_mock.reset_mock()

    def test_notify_syslog_mitre(self):
        for test_detail in [
            ({"type": 'internal_icmp_tunnel_c2s', "state": "active", "host_detection_id": 1}, 1),
        ]:
            det_detail, call_count = test_detail
            processing.notify_syslog(det_detail)
            self.assertEqual(self.syslog_mock.call_count, call_count)
            self.assertEqual(det_detail.get('mitre', []), ['T1008', 'T1095', 'T1570'])
            self.syslog_mock.reset_mock()

    @patch("detection_processing_v2.processing.notify_syslog", autospec=True)
    def test_basic_processing(self, syslog_mock):
        # Create a few host only detection_details
        num_detection_details = 10
        for x in range(num_detection_details):
            self.create_detection_detail()

        self.simulated_run()

        # We should have 10 syslogs for the new detections
        self.assertEqual(syslog_mock.call_count, num_detection_details)

        # Pull the details
        dets = detection_detail.objects.all()
        for det in dets:
            self.assertTrue(det.host_detection_id)
            self.assertFalse(det.account_detection_id)

        # Add more details that are both
        num_detection_details = 10
        for x in range(num_detection_details):
            self.create_detection_detail(account_detection=True, host_detection=True)

        self.simulated_run()

        # Now 20 syslogs
        self.assertEqual(syslog_mock.call_count, num_detection_details * 2)

        # Add 5 account only detection details
        num_account_detections = 5
        for x in range(num_account_detections):
            self.create_detection_detail(account_detection=True, host_detection=False)

        self.simulated_run()

        # Still 20 details
        self.assertEqual(syslog_mock.call_count, num_detection_details * 2 + num_account_detections)

    def test_processing_si_new_host_role(self):
        # test that si new host role details generate entries in the tvui_host_role table
        test_host = models.host.objects.create(name='test-host-a', state='active', host_luid='ylcdi8i7')
        test_host_session = models.host_session.objects.create(
            host=test_host, session_luid='abcEFG', ip_address='10.0.0.0', start=self.now - timedelta(days=3), end=None
        )
        test_detail = self.create_detection_detail(
            detection_type='si_new_host_role', src_host_sess=test_host_session, flex_json={"role_display_name": "DNS Server"}
        )
        self.simulated_run()
        host_roles = list(models.HostRole.objects.all())
        self.assertEqual(len(host_roles), 1)
        host_role = host_roles[0]
        self.assertEqual(host_role.host_id, test_host.id)
        self.assertEqual(host_role.role_name, test_detail.flex_json.get('role_display_name'))
        self.assertEqual(host_role.source, 'detection_event')
        dets = list(models.detection.objects.all())
        self.assertEqual(len(dets), 1)

    def test_processing_si_new_host_role_no_flex_json(self):
        # test that si new host role details without a flex json field still generate a detection and cause no errors
        test_host = models.host.objects.create(name='test-host-a', state='active', host_luid='ylcdi8i7')
        test_host_session = models.host_session.objects.create(
            host=test_host, session_luid='abcEFG', ip_address='10.0.0.0', start=self.now - timedelta(days=3), end=None
        )
        self.create_detection_detail(detection_type='si_new_host_role', src_host_sess=test_host_session)
        self.simulated_run()
        host_roles = list(models.HostRole.objects.all())
        self.assertEqual(len(host_roles), 0)
        dets = list(models.detection.objects.all())
        self.assertEqual(len(dets), 1)

    def test_processing_port_sweep(self):
        # test that port_sweep details correctly combine cidr ranges
        test_host = models.host.objects.create(name='test-host-a', state='active', host_luid='ylcdi8i7')
        test_host_session = models.host_session.objects.create(
            host=test_host, session_luid='abcEFG', ip_address='10.0.0.0', start=self.now - timedelta(days=3), end=None
        )
        self.create_detection_detail(detection_type='port_sweep', src_host_sess=test_host_session, description='[1, 2, 3]')
        self.simulated_run()
        details = list(models.detection_detail.objects.all())
        self.assertEqual(len(details), 1)
        self.assertEqual(details[0].description, "0.0.0.0/27")

    @patch("base_tvui.feature_flipper.conditions.is_cloud", return_value=True)
    def test_ntc_detection_pipeline_metrics(self, *args):
        """Test calculation of detection pipeline timing metrics"""
        self.test_basic_processing()

        expected_labels = {'entity_type': 'host', 'detail_type': 'reverse_rat', 'data_source': 'network'}

        # note: some assertions below use mock.ANY because they are dependent on test execution time and could be flakey
        # the values in comments were observed when writing the test
        expected_metrics_calls = [
            call('vui_det_pipe_lag__couch_2_pub', value=120.0, labels=expected_labels),
            call('vui_det_pipe_lag__pub_2_dep', value=60.0, labels=expected_labels),
            call('vui_det_pipe_lag__dep_2_detail', value=ANY, labels=expected_labels),  # ~ 180.0
            call('vui_det_pipe_lag__detail_2_bucket', value=ANY, labels=expected_labels),  # ~ 0.0
            call('vui_det_pipe_lag__couch_2_bucket', value=ANY, labels=expected_labels),  # ~ 360.0
            call('vui_det_pipe_lag__last_timestamp_2_bucket', value=ANY, labels=expected_labels),  # ~ 360.0
        ]

        self.mock_metrics_lib.timing.assert_has_calls(expected_metrics_calls)

    @patch("detection_processing_v2.detection_scoring_utils.lib_account.rescore_accounts", autospec=True)
    @patch("detection_processing_v2.processing.detection_scoring.DetectionScoring._get_detection_score", autospec=True)
    @patch("detection_processing_v2.processing.detection_bucketing.bucket_detection_details", autospec=True)
    def test_account_bucket(self, mock_bucket_detection_details, mock_get_detection_score, mock_rescore_accounts):
        # Create detections
        accounts = [
            {'uid': '<EMAIL>', 'account_type': account_type.AccountType.KERBEROS, 'first_seen': self.now, 'last_seen': self.now},
            {'uid': '<EMAIL>', 'account_type': account_type.AccountType.KERBEROS, 'first_seen': self.now, 'last_seen': self.now},
            {'uid': '<EMAIL>', 'account_type': account_type.AccountType.KERBEROS, 'first_seen': self.now, 'last_seen': self.now},
        ]

        acc_uid_to_id_map = lib_account.create_or_update_accounts(accounts)

        det1 = detection.objects.create(
            type='papi_breach',
            type_vname='PAPI Breach',
            category='LATERAL MOVEMENT',
            src_ip='0.0.0.0',
            state='active',
            t_score=78,
            c_score=42,
            last_timestamp=self.now,
            account_id=acc_uid_to_id_map['<EMAIL>'],
        )

        det2 = detection.objects.create(
            type='papi_breach',
            type_vname='PAPI Breach',
            category='LATERAL MOVEMENT',
            src_ip='0.0.0.0',
            state='active',
            t_score=35,
            c_score=93,
            last_timestamp=self.now,
            account_id=acc_uid_to_id_map['<EMAIL>'],
        )

        det3 = detection.objects.create(
            type='papi_breach',
            type_vname='PAPI Breach',
            category='LATERAL MOVEMENT',
            src_ip='0.0.0.0',
            state='active',
            t_score=12,
            c_score=3,
            last_timestamp=self.now,
            account_id=acc_uid_to_id_map['<EMAIL>'],
        )

        dd = {
            'type': 'papi_breach',
            'account_detection': det1,
            'first_timestamp': (self.now - timedelta(hours=1)).replace(microsecond=0),
            'last_timestamp': self.now.replace(microsecond=0),
            'src_ip': '0.0.0.0',
            'count': 1,
            'couch_note_id': 'c-note',
            'is_host_detail': False,
            'is_account_detail': True,
            'account_uid': '<EMAIL>',
            'account_id': acc_uid_to_id_map['<EMAIL>'],
            'flex1': 'priv_mismatch',
            'flex2': 'fake_service_name',
            'flex3': "{'host': [], 'account': [], 'service': []})",
            'flex4': "{'host': 1, 'account': 4, 'service': 10})",
        }

        dd2 = {
            'type': 'papi_breach',
            'account_detection': det2,
            'first_timestamp': (self.now - timedelta(hours=1)).replace(microsecond=0),
            'last_timestamp': self.now.replace(microsecond=0),
            'src_ip': '0.0.0.0',
            'count': 1,
            'couch_note_id': 'c-note2',
            'is_host_detail': False,
            'is_account_detail': True,
            'account_uid': '<EMAIL>',
            'account_id': acc_uid_to_id_map['<EMAIL>'],
            'flex1': 'priv_mismatch',
            'flex2': 'fake_service_name',
            'flex3': "{'host': [], 'account': [], 'service': []})",
            'flex4': "{'host': 1, 'account': 4, 'service': 10})",
        }

        dd3 = {
            'type': 'papi_breach',
            'account_detection': det3,
            'first_timestamp': (self.now - timedelta(hours=1)).replace(microsecond=0),
            'last_timestamp': self.now.replace(microsecond=0),
            'src_ip': '0.0.0.0',
            'count': 1,
            'couch_note_id': 'c-note3',
            'is_host_detail': False,
            'is_account_detail': True,
            'account_uid': '<EMAIL>',
            'account_id': acc_uid_to_id_map['<EMAIL>'],
            'flex1': 'priv_mismatch',
            'flex2': 'fake_service_name',
            'flex3': "{'host': [], 'account': [], 'service': []})",
            'flex4': "{'host': 1, 'account': 4, 'service': 10})",
        }

        detection_detail.objects.create(**dd)
        detection_detail.objects.create(**dd2)
        detection_detail.objects.create(**dd3)

        mock_bucket_detection_details.return_value = [det1.id, det2.id, det3.id]

        detection_scores = {}
        detection_scores[det1.id] = {'threat': 7, 'certainty': 8}
        detection_scores[det2.id] = {'threat': 9, 'certainty': 10}
        detection_scores[det3.id] = {'threat': 11, 'certainty': 12}

        def mocked_get_detection_score(self, det):
            return detection_scores[det['id']]

        mock_get_detection_score.side_effect = mocked_get_detection_score

        def mocked_rescore_accounts(account_ids, reason=''):
            req_acc_ids = list(acc_uid_to_id_map.values())

            for acc_id in req_acc_ids:
                if acc_id not in account_ids:
                    raise Exception('Missing account id: {}'.format(acc_id))

        mock_rescore_accounts.side_effect = mocked_rescore_accounts

        processing_instance = processing.Processing()
        processing_instance.process_new_detections()

        # Make sure detections scores were set as expected
        for det_id in detection_scores:
            det = detection.objects.get(id=det_id)
            self.assertEqual(det.c_score, detection_scores[det_id]['certainty'])
            self.assertEqual(det.t_score, detection_scores[det_id]['threat'])

        # TODO: Make sure call to lib_account.rescore_accounts is correct

    @patch("detection_processing_v2.processing.notify_syslog", autospec=True)
    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    def test_no_syslog_in_cloud(self, syslog_mock):
        for x in range(10):
            self.create_detection_detail()

        self.simulated_run()
        self.assertEqual(syslog_mock.call_count, 0)

    def test_processing_run_once(self):
        with patch.object(self.processing, '_one_time_pruning') as mock_one_time_pruning, patch.object(
            self.processing, 'process_new_detections'
        ) as mock_process_new_detections:
            self.processing.run_once()
            mock_one_time_pruning.assert_called_once_with()
            mock_process_new_detections.assert_called_once_with()

    def test_simple_host_pruning(self):
        # Create a lot of details all in the same bucket
        src_host_sess = self.create_host_session()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10
        details = []
        for x in range(num_detection_details):
            details.append(self.create_detection_detail(src_host_sess=src_host_sess))

        # Run processing
        self.simulated_run()

        # There should be a host-based bucket with all the details, nothing gets pruned yet
        self.assertEqual(detection.objects.count(), 1)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), num_detection_details)

        # Create more details, the bucket will now need to be pruned
        for x in range(num_detection_details):
            details.append(self.create_detection_detail(src_host_sess=src_host_sess))

        # Run processing
        self.simulated_run()

        # There should be a host-based bucket with all the details, pruned down to MAX_DETAILS_PER_BUCKET
        self.assertEqual(detection.objects.count(), 1)
        bucket = detection.objects.all()[0]
        self.assertEqual(bucket.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # The bucket should have the most recent details (older ones get pruned)
        start_idx = len(details) - MAX_DETAILS_PER_BUCKET
        expected_details = details[start_idx:]
        for detail in expected_details:
            detail.refresh_from_db()
        self.assertEqual(list(bucket.detection_detail_entire_set.order_by('id')), expected_details)

    def test_simple_account_pruning(self):
        # Create a lot of details all in the same bucket
        account = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10
        for x in range(num_detection_details):
            self.create_detection_detail(account=account, host_detection=False, account_detection=True)

        # Run processing
        self.simulated_run()

        # There should be an account-based bucket with all the details, nothing gets pruned yet
        self.assertEqual(detection.objects.count(), 1)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), num_detection_details)

        # Create more details, the bucket will now need to be pruned
        for x in range(num_detection_details):
            self.create_detection_detail(account=account, host_detection=False, account_detection=True)

        # Run processing
        self.simulated_run()

        # There should be an account-based bucket with all the details, pruned down to MAX_DETAILS_PER_BUCKET
        self.assertEqual(detection.objects.count(), 1)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

    def test_mixed_host_account_pruning(self):
        # Create some details that point to both host and account buckets
        src_host_sess = self.create_host_session()
        account = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10

        for x in range(num_detection_details):
            self.create_detection_detail(account=account, src_host_sess=src_host_sess, host_detection=True, account_detection=True)

        # Run processing
        self.simulated_run()

        # There should be a host-based and an account-based bucket with all the details, nothing gets pruned yet
        self.assertEqual(detection.objects.count(), 2)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), num_detection_details)
        self.assertEqual(detection.objects.all()[1].detection_detail_entire_set.count(), num_detection_details)

        # Add more details to both buckets to trigger the need for pruning
        for x in range(num_detection_details):
            self.create_detection_detail(account=account, src_host_sess=src_host_sess, host_detection=True, account_detection=True)

        # Run processing
        self.simulated_run()

        # There should be a host-based and an accout-based bucket with all the details, pruned down to MAX_DETAILS_PER_BUCKET
        self.assertEqual(detection.objects.count(), 2)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)
        self.assertEqual(detection.objects.all()[1].detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

    def test_host_details_prevent_account_pruning(self):
        # Create some details that point to both host and account buckets
        src_host_sess = self.create_host_session()
        account = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10

        host_details = []
        for x in range(num_detection_details):
            host_details.append(
                self.create_detection_detail(account=account, src_host_sess=src_host_sess, host_detection=True, account_detection=True)
            )

        # Run processing
        self.simulated_run()

        # There should be a host-based and an account-based bucket with all the details, nothing gets pruned yet
        self.assertEqual(detection.objects.count(), 2)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), num_detection_details)
        self.assertEqual(detection.objects.all()[1].detection_detail_entire_set.count(), num_detection_details)

        # Now create more account-only details
        for x in range(num_detection_details):
            self.create_detection_detail(account=account, src_host_sess=src_host_sess, host_detection=False, account_detection=True)

        # Run processing
        self.simulated_run()

        # There should still be one host-based and one accout-based bucket
        host_dets = list(detection.objects.filter(host_session__isnull=False))
        self.assertEqual(len(host_dets), 1)

        account_dets = list(detection.objects.filter(account__isnull=False))
        self.assertEqual(len(account_dets), 1)

        # The account bucket should get pruned down to MAX_DETAILS_PER_BUCKET details.
        self.assertEqual(account_dets[0].detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # But the host bucket should still just have the original details: the host details got unlinked from the account bucket, but not deleted
        self.assertEqual(host_dets[0].detection_detail_entire_set.count(), num_detection_details)

        # Create another round of account-only details
        for x in range(num_detection_details):
            self.create_detection_detail(account=account, src_host_sess=src_host_sess, host_detection=False, account_detection=True)

        # Run processing
        self.simulated_run()

        # The account bucket should get pruned back to MAX_DETAILS_PER_BUCKET details again.
        self.assertEqual(account_dets[0].detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # The host-details still cant be deleted entirely
        self.assertEqual(host_dets[0].detection_detail_entire_set.count(), num_detection_details)

    def test_prune_dns_table(self):
        # Create some dns details that need pruning
        src_host_sess = self.create_host_session()
        num_detection_details = MAX_DETAILS_PER_BUCKET + 10

        for x in range(num_detection_details):
            self.create_detection_detail(
                src_host_sess=src_host_sess, host_detection=True, account_detection=False, detection_type='cnc_dga'
            )

        # Run processing
        self.simulated_run()

        # Make sure pruning worked
        self.assertEqual(detection.objects.count(), 1)
        self.assertEqual(detection.objects.all()[0].detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

    def test_one_time_pruning_chunking(self):
        # Create some details that point to both host and account buckets
        src_host_sess1 = self.create_host_session()
        account1 = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10
        for x in range(num_detection_details):
            self.create_detection_detail(account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True)

        # Run processing
        self.simulated_run()

        # There be one host-based and one account-based bucket
        host_dets = list(detection.objects.filter(host_session__isnull=False))
        self.assertEqual(len(host_dets), 1)

        account_dets = list(detection.objects.filter(account__isnull=False))
        self.assertEqual(len(account_dets), 1)

        # Manually put a bunch more details into the buckets
        host_det = host_dets[0]
        account_det = account_dets[0]
        num_details_to_be_deleted = 10
        num_detection_details = MAX_DETAILS_PER_BUCKET + num_details_to_be_deleted
        detail_ids_to_delete = []
        for x in range(num_detection_details):
            detail = self.create_detection_detail(
                account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True
            )
            # should prune the oldest details (details with lowest ids)
            if x < num_details_to_be_deleted:
                detail_ids_to_delete.append(detail.id)
            detail.host_detection = host_det
            detail.account_detection = account_det
            detail.save()

        # There should now be too many details in the buckets
        host_det.refresh_from_db()
        self.assertGreater(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertGreater(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # Run processing to kickoff one time pruning
        self.simulated_run(1)

        # There should now be only MAX_DETAILS_PER_BUCKET details in each bucket
        host_det.refresh_from_db()
        self.assertEqual(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertEqual(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # make sure pruned details were deleted
        self.assertEqual(0, detection_detail.pure.filter(id__in=detail_ids_to_delete).count())

    def test_one_time_pruning_needed(self):
        # Create some details that point to both host and account buckets
        src_host_sess1 = self.create_host_session()
        account1 = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10
        for x in range(num_detection_details):
            self.create_detection_detail(account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True)

        # Run processing
        self.simulated_run()

        # There be one host-based and one account-based bucket
        host_dets = list(detection.objects.filter(host_session__isnull=False))
        self.assertEqual(len(host_dets), 1)

        account_dets = list(detection.objects.filter(account__isnull=False))
        self.assertEqual(len(account_dets), 1)

        # Manually put a bunch more details into the buckets
        host_det = host_dets[0]
        account_det = account_dets[0]
        num_details_to_be_deleted = 10
        num_detection_details = MAX_DETAILS_PER_BUCKET + num_details_to_be_deleted
        detail_ids_to_delete = []
        for x in range(num_detection_details):
            detail = self.create_detection_detail(
                account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True
            )
            # should prune the oldest details (details with lowest ids)
            if x < num_details_to_be_deleted:
                detail_ids_to_delete.append(detail.id)
            detail.host_detection = host_det
            detail.account_detection = account_det
            detail.save()

        # There should now be too many details in the buckets
        host_det.refresh_from_db()
        self.assertGreater(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertGreater(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # Run processing to kickoff one time pruning
        self.simulated_run()

        # There should now be only MAX_DETAILS_PER_BUCKET details in each bucket
        host_det.refresh_from_db()
        self.assertEqual(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertEqual(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # make sure pruned details were deleted
        self.assertEqual(0, detection_detail.pure.filter(id__in=detail_ids_to_delete).count())

    def test_one_time_pruning_needed_with_whitelist(self):
        # Create a bunch of detections
        src_host_sess1 = self.create_host_session()
        account1 = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET + 10
        for x in range(num_detection_details):
            self.create_detection_detail(account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True)

        # Bucket
        self.processing.process_new_detections()

        # There be one host-based and one accout-based bucket
        host_dets = list(detection.pure.filter(host_session__isnull=False))
        self.assertEqual(len(host_dets), 1)
        host_det = host_dets[0]

        account_dets = list(detection.pure.filter(account__isnull=False))
        self.assertEqual(len(account_dets), 1)
        account_det = account_dets[0]

        # There should now be max details
        host_det.refresh_from_db()
        self.assertEqual(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertEqual(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # Add more details
        num_details_to_be_deleted = 10
        num_detection_details = MAX_DETAILS_PER_BUCKET + num_details_to_be_deleted
        detail_ids_to_delete = []
        for x in range(num_detection_details):
            detail = self.create_detection_detail(
                account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True
            )
            # should prune the oldest details (details with lowest ids)
            if x < num_details_to_be_deleted:
                detail_ids_to_delete.append(detail.id)
            detail.host_detection = host_det
            detail.account_detection = account_det
            detail.save()

        # Set them all to whitelist
        detection.pure.all().update(state='whitelist')

        # There should now be too many details in the buckets
        host_det = detection.pure.get(id=host_det.id)
        self.assertGreater(len(detection_detail.pure.filter(host_detection_id=host_det.id)), MAX_DETAILS_PER_BUCKET)

        account_det = detection.pure.get(id=account_det.id)
        self.assertGreater(len(detection_detail.pure.filter(account_detection_id=account_det.id)), MAX_DETAILS_PER_BUCKET)

        # Run pruning
        self.processing._one_time_pruning()

        # There should now be only MAX_DETAILS_PER_BUCKET details in each bucket
        host_det = detection.pure.get(id=host_det.id)
        self.assertEqual(len(detection_detail.pure.filter(host_detection_id=host_det.id)), MAX_DETAILS_PER_BUCKET)

        account_det = detection.pure.get(id=account_det.id)
        self.assertEqual(len(detection_detail.pure.filter(account_detection_id=account_det.id)), MAX_DETAILS_PER_BUCKET)

        # make sure pruned details were deleted
        self.assertEqual(0, detection_detail.pure.filter(id__in=detail_ids_to_delete).count())

    def test_one_time_pruning_needed_missing_detection(self):
        # Create some details that point to both host and account buckets
        src_host_sess1 = self.create_host_session()
        account1 = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10
        for x in range(num_detection_details):
            self.create_detection_detail(account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True)

        # Run processing
        self.simulated_run()

        # There be one host-based and one accout-based bucket
        host_dets = list(detection.objects.filter(host_session__isnull=False))
        self.assertEqual(len(host_dets), 1)

        account_dets = list(detection.objects.filter(account__isnull=False))
        self.assertEqual(len(account_dets), 1)

        # Manually put a bunch more details into the buckets
        host_det = host_dets[0]
        account_det = account_dets[0]
        num_details_to_be_deleted = 10
        num_detection_details = MAX_DETAILS_PER_BUCKET + num_details_to_be_deleted
        detail_ids_to_delete = []
        for x in range(num_detection_details):
            detail = self.create_detection_detail(
                account=account1, src_host_sess=src_host_sess1, host_detection=True, account_detection=True
            )
            # should prune the oldest details (details with lowest ids)
            if x < num_details_to_be_deleted:
                detail_ids_to_delete.append(detail.id)
            detail.host_detection = host_det
            detail.account_detection = account_det
            detail.save()

        # There should now be too many details in the buckets
        host_det.refresh_from_db()
        self.assertGreater(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertGreater(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # Patch in a detection_id that doesnt exist and the two existing ones
        fake_response = [
            {'detection_id': host_det.id, 'count': 125},
            {'detection_id': account_det.id, 'count': 125},
            {'detection_id': 99181, 'count': 125},
        ]
        with patch.object(processing.detection_processing_utils, 'dict_fetchall', return_value=fake_response) as mock_dict_fetch:
            # Prune
            self.processing._one_time_pruning()

        # Then Process
        self.processing.process_new_detections()

        # Verify everything still worked as expected
        host_det.refresh_from_db()
        self.assertEqual(host_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        account_det.refresh_from_db()
        self.assertEqual(account_det.detection_detail_entire_set.count(), MAX_DETAILS_PER_BUCKET)

        # make sure pruned details were deleted
        self.assertEqual(0, detection_detail.pure.filter(id__in=detail_ids_to_delete).count())

    @patch("detection_processing_v2.processing.notify_syslog", autospec=True)
    def test_processing_triaged_details(self, syslog_mock):
        # Create a single account to use
        new_account = self.create_account()
        new_hs = self.create_host_session()

        # Create an initial smart rule
        flex2_service_name = 'service1'
        created_sr = smart_rule.objects.create(
            smart_category='test PAPI triage rule',
            description='not a threat',
            is_whitelist=False,
            category='LATERAL MOVEMENT',
            type='papi_unusual_admin_console',
            type_vname='Privilege Anomaly: Unusual Host',
            last_timestamp=None,
            priority=smart_rule.objects.count() + 1,
            family=smart_rule.FAMILY_CUSTOMER,
            context=None,
            conditions=testing_utils.get_smart_rule_conditions(
                additional_conditions=testing_utils.get_direct_condition_dict(field='flex2', values=[flex2_service_name])
            ),
        )

        # Create a few host only detection_details
        num_detection_details = 4
        detection_types = [DetectionType.PAPI_BREACH, DetectionType.PAPI_ROGUE_ADMIN, DetectionType.PAPI_UNUSUAL_ADMIN_CONSOLE]

        # Creat the account only details
        for detection_type in detection_types:
            for x in range(num_detection_details):
                dd_data = {}
                dd_data['last_timestamp'] = self.now - timedelta(days=1)
                dd_data['first_timestamp'] = self.now - timedelta(days=x + 2)
                dd_data['type'] = detection_type
                dd_data['is_host_detail'] = False
                dd_data['is_account_detail'] = True
                dd_data['account_id'] = new_account.id
                dd_data['account_uid'] = new_account.uid
                dd_data['src_ip'] = new_hs.ip_address
                dd_data['src_session_luid'] = new_hs.session_luid
                dd_data['flex2'] = 'service{}'.format(x)

                detection_detail.objects.create(**dd_data)

        # sanity check -- rule should have no timestamp beforehand
        self.assertIsNone(smart_rule.objects.get(id=created_sr.id).last_timestamp)

        self.simulated_run()

        # 4 detections (all account based). 3 for each det type and 1 for the one with the smart rule
        all_detections = detection.objects.all()
        self.assertEqual(len(detection_types) + 1, len(all_detections))
        self.assertTrue(all(det.entity_type == detection.EntityType.account for det in all_detections))

        # Verify detections have t and c scores except for triaged one
        for det in all_detections:
            if det.smart_rule:
                self.assertEqual(det.t_score, 0)
                self.assertEqual(det.c_score, 0)
            else:
                self.assertTrue(det.t_score > 0)
                self.assertTrue(det.c_score > 0)

        # Only one detail on the triaged det
        triaged_det = detection.objects.get(smart_rule__isnull=False)
        triaged_details = detection_detail.pure.filter(account_detection_id=triaged_det.id)
        self.assertEqual(len(triaged_details), 1)

        # timestamp should have been updated since rule matched detail
        self.assertIsNotNone(smart_rule.objects.get(id=created_sr.id).last_timestamp)

        # Add one more detail to be triaged
        dd_data = {}
        dd_data['last_timestamp'] = self.now - timedelta(days=1)
        dd_data['first_timestamp'] = self.now - timedelta(days=x + 2)
        dd_data['type'] = DetectionType.PAPI_UNUSUAL_ADMIN_CONSOLE
        dd_data['is_host_detail'] = False
        dd_data['is_account_detail'] = True
        dd_data['account_id'] = new_account.id
        dd_data['account_uid'] = new_account.uid
        dd_data['src_ip'] = new_hs.ip_address
        dd_data['src_session_luid'] = new_hs.session_luid
        dd_data['flex2'] = flex2_service_name

        detection_detail.objects.create(**dd_data)
        self.simulated_run()

        # Still 4 detections (all account based). 3 for each det type and 1 for the one with the smart rule
        all_detections = detection.objects.all()
        self.assertEqual(len(detection_types) + 1, len(all_detections))
        self.assertTrue(all(det.entity_type == detection.EntityType.account for det in all_detections))

        # Verify detections have t and c scores except for triaged one
        for det in all_detections:
            if det.smart_rule:
                self.assertEqual(det.t_score, 0)
                self.assertEqual(det.c_score, 0)
            else:
                self.assertTrue(det.t_score > 0)
                self.assertTrue(det.c_score > 0)

        # Only one detail on the triaged det
        triaged_det = detection.objects.get(smart_rule__isnull=False)
        triaged_details = detection_detail.pure.filter(account_detection_id=triaged_det.id)
        self.assertEqual(len(triaged_details), 2)

    @patch("detection_processing_v2.processing.is_distillation_enabled", side_effect=lambda x: x == DataSourceType.O365)
    @patch("detection_processing_v2.processing.run_distiller")
    @patch("detection_processing_v2.processing.detection_bucketing.bucket_detection_details")
    @patch("detection_processing_v2.processing.smart_rule_utils.update_rule_timestamps")
    @patch("detection_processing_v2.processing.smart_rule_dd_comparison")
    def test_processing_o365_distillation(
        self, mocked_dd_comparison, mocked_rule_timestamps, mocked_bucket, mocked_distillation, mocked_flag_enabled
    ):
        host_sess = self.create_host_session()
        account = self.create_account()

        # Create a bunch of o365 and AWS detections
        params = {"account": account, "src_host_sess": host_sess, "host_detection": True, "account_detection": True}
        self.create_detection_detail(**params, detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE)
        self.create_detection_detail(**params, detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE)
        self.create_detection_detail(**params, detection_type=DetectionType.O365_SUSPICIOUS_MAILBOX_RULE)
        self.create_detection_detail(**params, detection_type=DetectionType.O365_SUSPICIOUS_MAILBOX_RULE)
        self.create_detection_detail(**params, detection_type=DetectionType.SW_O365_ACCOUNT_BRUTE_FORCE)
        self.create_detection_detail(**params, detection_type=DetectionType.AWS_CRYPTOMINING)
        self.create_detection_detail(**params, detection_type=DetectionType.AWS_ATTACK_TOOLS)

        # Mock the run_distillation to return half detection details as distilled and half as not distilled
        def distill_some(detection_details, data_source_type):
            distilled = [dd for dd in detection_details if dd["type"] == DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE]
            non_distilled = [dd for dd in detection_details if dd["type"] != DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE]
            return distilled, non_distilled

        # mocked_distillation = MagicMock
        mocked_distillation.side_effect = distill_some

        with patch.object(self.processing, '_meta_data_processing'), patch.object(self.processing, '_smart_rule_evaluation'):
            self.processing.process_new_detections()

        # Check that run_distillation was called only with O365 detections
        mocked_distillation.assert_called_once()
        name, args, kwargs = mocked_distillation.mock_calls[0]
        assert len(kwargs["detection_details"]) == 5
        for arg in kwargs["detection_details"]:
            assert arg["type"].startswith("o365") or arg["type"].startswith("sw_o365")

        # Check that all detections are bucketed
        name, args, kwargs = mocked_bucket.mock_calls[0]
        assert len(args[0]) == 7, [dd["type"] for dd in args[0]]

    @patch("detection_processing_v2.processing.run_distiller")
    @patch("detection_processing_v2.processing.detection_bucketing.bucket_detection_details")
    @patch("detection_processing_v2.processing.is_distillation_enabled", side_effect=lambda x: x == DataSourceType.O365)
    def test_processing_types_not_distillable(self, mocked_flag_enabled, mocked_bucket, mocked_distillation):
        """Make sure detections not distillable still get passed through the pipeline as normal."""
        host_sess = self.create_host_session()
        account = self.create_account()

        # Create some network detections and first check that they are indeed not distillable
        # Then assert distillation was not called, but bucketing was (ensuring pipeline was successfully invoked)
        det_type = DetectionType.HIDDEN_TUNNEL_CNC

        params = {"account": account, "src_host_sess": host_sess, "host_detection": True, "account_detection": True}
        self.create_detection_detail(**params, detection_type=det_type)
        self.create_detection_detail(**params, detection_type=det_type)

        with patch.object(self.processing, '_meta_data_processing'), patch.object(self.processing, '_smart_rule_evaluation'):
            self.processing.process_new_detections()

        mocked_distillation.assert_not_called()
        mocked_bucket.assert_called_once()

    @patch("detection_processing_v2.processing.run_distiller")
    @patch("detection_processing_v2.processing.detection_bucketing.bucket_detection_details")
    @patch("detection_processing_v2.processing.is_distillation_enabled", side_effect=lambda x: x == DataSourceType.O365)
    def test_processing_o365_distillation_no_details(self, mocked_flag_enabled, mocked_bucket, mocked_distillation):
        with patch.object(self.processing, '_meta_data_processing'), patch.object(self.processing, '_smart_rule_evaluation'):
            self.processing.process_new_detections()

        # Check that distillation was not called, but bucketing was (ensuring pipeline was successfully invoked)
        mocked_distillation.assert_not_called()
        mocked_bucket.assert_called_once()

    def test_processing_host_details_with_accounts(self):
        # Create several host-based details that have accounts set but are not account details.
        num_detection_details = 25
        for x in range(num_detection_details):
            new_hs = self.create_host_session()
            new_account = self.create_account()
            self.create_detection_detail(src_host_sess=new_hs, account=new_account, host_detection=True, account_detection=False)
            self.create_detection_detail(src_host_sess=new_hs, account=new_account, host_detection=True, account_detection=False)

        self.simulated_run()

        # We should have 50 syslogs for the new detections
        self.assertEqual(self.syslog_mock.call_count, num_detection_details * 2)

        # We have num_detection_details buckets total
        self.assertEqual(len(list(detection.objects.all())), num_detection_details)

        # We have num_detection_details host scoring call
        self.assertEqual(self.mock_hs_open_mock.call_count, num_detection_details)

        # No Account Scoring Calls
        self.assertEqual(self.mock_as_open_mock.call_count, 0)

        # Pull the details
        dets = detection_detail.objects.all()
        for det in dets:
            self.assertTrue(det.host_detection_id)
            self.assertFalse(det.account_detection_id)

    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    def test_processing_checkpoint(self):
        setting.objects.create(value='on', group='feature', key='detection_events')
        generate_schema_by_type(DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE)
        initial_count = DetectionEvent.objects.count()

        num_detection_details = 5
        for x in range(num_detection_details):
            new_hs = self.create_host_session()
            new_account = self.create_account()
            self.create_detection_detail(
                src_host_sess=new_hs,
                account=new_account,
                host_detection=True,
                account_detection=False,
                detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE,
            )
            self.create_detection_detail(
                src_host_sess=new_hs,
                account=new_account,
                host_detection=False,
                account_detection=True,
                detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE,
            )

        self.processing.process_new_detections()
        self.assertEqual(DetectionEvent.objects.count(), initial_count + num_detection_details * 2)

    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    @patch('detection_processing_v2.processing.models.detection_detail.objects.filter')
    @patch('detection_processing_v2.processing.logger.warning')
    def test_processing_checkpoint_logs(self, mock_warning, mock_filter):
        setting.objects.create(value='on', group='feature', key='detection_events')
        generate_schema_by_type(DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE)

        num_detection_details = 1
        for x in range(num_detection_details):
            new_hs = self.create_host_session()
            new_account = self.create_account()
            self.create_detection_detail(
                src_host_sess=new_hs,
                account=new_account,
                host_detection=True,
                account_detection=False,
                detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE,
            )
            self.create_detection_detail(
                src_host_sess=new_hs,
                account=new_account,
                host_detection=False,
                account_detection=True,
                detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE,
            )

        mock_filter.return_value = detection_detail.objects.none()
        self.processing.process_new_detections()
        self.assertTrue(mock_warning.called)

    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    def test_process_v3_3_detection_event_data(self):
        setting.objects.create(value='on', group='feature', key='v3_3_detection_detail_events')
        generate_schema_by_type(DetectionType.BINARYLOADER)
        generate_schema_by_type(DetectionType.REVERSE_RAT)
        generate_schema_by_type(DetectionType.SHELL_KNOCKER_C2S)

        details = []
        details.append(
            self.create_detection_detail(
                detection_type=DetectionType.BINARYLOADER,
            )
        )
        details.append(
            self.create_detection_detail(
                detection_type=DetectionType.REVERSE_RAT,
            )
        )
        details.append(
            self.create_detection_detail(
                detection_type=DetectionType.SHELL_KNOCKER_C2S,
            )
        )
        for dd in details:
            detail = process_v3_3_detection_event_data(dd, syslog_schema={})
            self.assertIsNotNone(detail)
            self.assertIn("dst_host", detail)
            self.assertIn("src_host", detail)
            self.assertIn("bytes_sent", detail)
            self.assertIn("additional_details", detail)
            self.assertIn("bytes_received", detail)
            self.assertIn("event_name", detail)
            self.assertIn("event_id", detail)
            self.assertIn("account_id", detail)
            self.assertIn("src_account", detail)
            self.assertIn("dst_account", detail)
            self.assertIn("first_timestamp", detail)
            self.assertIn("last_timestamp", detail)
            self.assertIn("sensor", detail)
            self.assertIn("count", detail)
            self.assertIn("dst_port", detail)

    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    def test_process_v3_3_detection_event_data_additional_details(self):
        setting.objects.create(value='on', group='feature', key='v3_3_detection_detail_events')
        port_scan_syslog = {
            'fields': {
                'ports': [{'handler': 'load_json', 'field': 'flex1'}, {'handler': 'unique_list'}],
                'scans': 'count',
                'successes': 'count_pos',
            }
        }
        detection = {
            'type': 'port_scan',
            'flex1': '2-3,6-11',
            'flex2': 'False',
            'flex3': None,
            'flex4': None,
            'flex5': None,
            'flex6': None,
            'flex_json.is_smallscan': True,
        }
        additional_details = process_v3_3_additional_details(detection, {}, port_scan_syslog)
        self.assertIn("ports", additional_details)
        self.assertIn("other_2", additional_details)
        self.assertEqual(additional_details["ports"], "2-3,6-11")
        self.assertEqual(additional_details["other_2"], 'False')

    def test_processing_spa_detail(self):
        new_hs = self.create_host_session()
        mitre_id = "T1071"

        detection_detail.objects.create(
            type='spa_dns_info',
            src_ip=new_hs.ip_address,
            last_timestamp=self.now,
            first_timestamp=self.now,
            src_session_luid=new_hs.session_luid,
            flex_json=json.dumps(
                {
                    'alert_type': 'fake_det',
                    'threat_score': 75,
                    'certainty_score': 80,
                    'metadata': {
                        'name': 'Suspect DNS Activity',
                        'description': 'This is a description of the suspicious activity',
                        'mitre': [{"mitre_id": mitre_id, "name": "Standard Application Layer Protocol", "framework": "tactics"}],
                        'archetype_bucket': '',
                    },
                }
            ),
        )

        self.simulated_run()
        self.assertEqual(self.syslog_mock.call_count, 1)
        actual_call_args = self.syslog_mock.call_args[1]
        self.assertEqual(actual_call_args['det_detail']['mitre'], [mitre_id])

    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    def test_dup_entity_papi_detection_events(self):
        """Ensure that papi detections create 2 detection events for each entity type"""
        # enable detection events
        setting.objects.create(value='on', group='feature', key='detection_events')
        detection_type = 'papi_admin_peer_console'
        new_hs = self.create_host_session()
        mitre_id = "T1071"
        account = self.create_account()

        detection_detail.objects.create(
            type=detection_type,
            src_ip='*******',
            account_id=account.id,
            last_timestamp=self.now,
            first_timestamp=self.now,
            src_session_luid=new_hs.session_luid,
            flex_json=json.dumps(
                {
                    'alert_type': 'fake_det',
                    'threat_score': 75,
                    'certainty_score': 80,
                    'metadata': {
                        'name': 'Test PAPI detection',
                        'description': 'This is a description of the papi activity',
                        'mitre': [{"mitre_id": mitre_id, "name": "Standard Application Layer Protocol", "framework": "tactics"}],
                        'archetype_bucket': '',
                    },
                }
            ),
        )
        self.simulated_run()
        self.assertTrue(len(DetectionEvent.objects.filter(type=detection_type)), 2)
        self.assertTrue(DetectionEvent.objects.filter(type=detection_type).filter(entity_type='account'))
        self.assertTrue(DetectionEvent.objects.filter(type=detection_type).filter(entity_type='host'))

    @patch('base_tvui.feature_flipper.conditions.ENV', new=Envs.cloud)
    def test_metrics_on_num_pruned_details(self):
        # Put it 10 details under bucketing limit
        account = self.create_account()
        num_detection_details = MAX_DETAILS_PER_BUCKET - 10
        for x in range(num_detection_details):
            self.create_detection_detail(
                account=account, host_detection=False, account_detection=True, detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE
            )
        self.simulated_run()

        # no metric
        any_pruning_metric_call = call(MetricName.DETAILS_PRUNED_DUE_TO_BUCKET_LIMIT, ANY, labels=ANY)
        self.assertTrue(any_pruning_metric_call not in self.mock_metrics_lib.count.call_args_list)

        # Add details to cause it to exceed bucket limit slightly
        num_detection_details = 15
        for x in range(num_detection_details):
            self.create_detection_detail(
                account=account, host_detection=False, account_detection=True, detection_type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE
            )
        self.simulated_run()

        # metric gets recorded now
        prune_5_metric_call = call(
            MetricName.DETAILS_PRUNED_DUE_TO_BUCKET_LIMIT, 5, labels={'detection_type': DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE}
        )
        self.mock_metrics_lib.count.assert_has_calls([prune_5_metric_call])
