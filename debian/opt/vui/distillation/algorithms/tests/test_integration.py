# Copyright (c) Vectra AI - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import logging
import warnings
from contextlib import contextmanager
from datetime import datetime, timezone
from unittest.mock import patch

from base_tvui.feature_flipper import conditions, Flags, flag_enabled
from base_tvui.schema_utils import SchemaType, load_schemas_from_disk
from detection_processing_v2 import processing
from distillation.algorithms._c2_domains import FQ_DOMAIN_LIST, L2_DOMAIN_LIST
from distillation.algorithms.tests import generate
from distillation.common import DistillationReason, DataSourceType, get_distillation_detection_types
from distillation.distillation_framework import learner
from tvui.detections.detection_types import DetectionType
from tvui.detections.dynamic_detections import dynamic_utils
from tvui.models import detection_detail, smart_rule
from vui_tests.helpers import patch_registry
from vui_tests.vui_testcase import VuiTestCase

# Set to False to see more logs in console. Useful when debugging tests.
SILENT = True


@contextmanager
def silent_console():
    try:
        with warnings.catch_warnings():
            if SILENT:
                logging.disable(logging.WARN)
                warnings.filterwarnings("ignore", message=r".* received a naive datetime")
            yield
    finally:
        logging.disable(0)


def get_detection_details():
    return list(detection_detail.objects.all().select_related("account_detection"))


def split_details(details, entity_type: str = "account"):
    distilled = []
    not_distilled = []
    for detail in details:
        if getattr(getattr(detail, f"{entity_type}_detection"), "smart_rule_id"):
            distilled.append(detail)
        else:
            not_distilled.append(detail)
    return distilled, not_distilled


def generate_detection_schemas():
    for schema in load_schemas_from_disk(schema_type=SchemaType.DETECTION):
        dynamic_utils.generate_detection_schema(schema, schema.get('type'))


def load_smart_rules():
    """Create smart rule ids for distillation and return mapping of distillation
    level/detection type to smart rule id.
    """
    detection_types = get_distillation_detection_types()
    distill_reasons = [e.value for e in DistillationReason]
    smart_rule_dict = {}
    for reason in distill_reasons:
        for detection_type in detection_types:
            smart_rule_, _ = smart_rule.distillation.get_or_create(
                type=detection_type,
                distillation_algorithm=reason,
                defaults={'family': smart_rule.FAMILY_DISTILLATION, 'smart_category': 'AI-Filtered'},
            )
            smart_rule_dict[(detection_type, reason)] = smart_rule_.id
    return smart_rule_dict


@patch_registry(o365_distillation=True, settings_syslog=False)
class O365DistillationTest(VuiTestCase):
    def setUp(self):
        self.now = datetime.now(timezone.utc)
        self.p.add_patch('db_commit', patch('django.db.connection.commit', autospec=True))
        self.p.add_patch('db_close', patch('django.db.connection.close', autospec=True))
        self.p.add_patch(
            'enable_triage', patch('base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled', return_value=True)
        )

        with silent_console():
            generate_detection_schemas()
            self.smart_rules = load_smart_rules()
            self.process = processing.Processing().process_new_detections

    def test_historical(self):
        """Test that the Learner successfully triggers historical distillation,
        by generating noisy detections of type 'sw_o365_disableMFA' such that
        'today', or the day the learner state is built, would be the day that
        'sw_o365_disableMFA' flips from not noisy to noisy.
        """
        det_type = DetectionType.SW_O365_DISABLE_MFA
        with silent_console():
            generate.state_for_historical(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.O365)

        details = get_detection_details()
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 107)
        self.assertEqual(len(not_distilled), 0)

        distill_reason = DistillationReason.DETECTION_CO_OCCURRENCE.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertTrue(all(det.account_detection.smart_rule_id == smart_rule_id for det in distilled))

    @patch_registry(entity_history_distillation_learned_state=True)
    def test_entity_history(self):
        """Test entity history distillation, by generating many historical
        detections on an entity of type 'sw_o365_redundantAccess', and
        later pushing detections of the same type on that entity.

        Three detections are pushed at that later stage. One with no context,
        which should trigger 'entity history - detection type' distillation,
        one with context, which should trigger 'entity history - detection type',
        and one with an account id not seen before, which should not be distilled.
        """
        # Ensure we are using new entity history distillation (with learned state)
        self.assertTrue(flag_enabled(Flags.entity_history_distillation_learned_state))

        det_type = DetectionType.SW_O365_REDUNDANT_ACCESS
        with silent_console():
            generate.state_for_entity_history(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.O365)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_entity_history(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        for det in distilled:
            if "context" in det.account.uid:
                distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            else:
                distill_reason = DistillationReason.ENTITY_HISTORY_DETECTION.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(det.account_detection.smart_rule_id, smart_rule_id)

    @patch_registry(entity_history_distillation_learned_state=False)
    def test_old_entity_history(self):
        """Test entity history distillation, by generating many historical
        detections on an entity of type 'sw_o365_redundantAccess', and
        later pushing detections of the same type on that entity.

        Three detections are pushed at that later stage. One with no context,
        which should trigger 'entity history - detection type' distillation,
        one with context, which should trigger 'entity history - detection type',
        and one with an account id not seen before, which should not be distilled.
        """
        # Ensure we are not using new entity history distillation (ie, we compute state manually)
        self.assertFalse(flag_enabled(Flags.entity_history_distillation_learned_state))

        det_type = DetectionType.SW_O365_REDUNDANT_ACCESS
        with silent_console():
            generate.state_for_entity_history(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.O365)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_entity_history(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        for det in distilled:
            if "context" in det.account.uid:
                distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            else:
                distill_reason = DistillationReason.ENTITY_HISTORY_DETECTION.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(det.account_detection.smart_rule_id, smart_rule_id)

    def test_cooccurrence(self):
        """Test detection cooccurrence distillation, by generating historical noisy
        detections of type 'sw_o365_eDiscoverySearch', building a learner state from
        those detections, and later pushing detections of the same type.

        Two detections are pushed at that later stage. One of type
        'sw_o365_eDiscoverySearch', which should trigger distillation, and
        one of a different type, which should not trigger distillation.
        """
        det_type = DetectionType.SW_O365_E_DISCOVERY_SEARCH
        other_det_type = DetectionType.SW_O365_E_DISCOVERY_EXFIL
        with silent_console():
            generate.state_for_cooccurrence(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.O365)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_cooccurrence(self.now, det_type, other_det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.DETECTION_CO_OCCURRENCE.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)

    def test_common_context(self):
        """Test common context distillation, by generating many historical
        detections of type 'sw_o365_3rdPartyApp' with certain context fields,
        building a learner state from those detections, and later pushing
        detections of the same type.

        Two detections are pushed at that later stage. One with the same
        context fields (stored as common in the learner state), which
        should trigger distillation, and one with unseen context fields,
        which should not trigger distillation.
        """
        det_type = DetectionType.SW_O365_3RD_PARTY_APP
        other_det_type = DetectionType.SW_O365_ATTACK_TOOL_RULER
        with silent_console():
            generate.state_for_common_context(self.now, det_type, other_det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.O365)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_common_context(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.COMMON_CONTEXT.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]

        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)

    def test_common_context_tuple_context(self):
        """Test common context distillation, by generating many historical
        detections of type 'o365_suspicious_factor_registration' with certain context fields,
        building a learner state from those detections, and later pushing
        detections of the same type.

        Two detections are pushed at that later stage. One with the same
        context fields (stored as common in the learner state), which
        should trigger distillation, and one with unseen context fields,
        which should not trigger distillation.
        """
        det_type = DetectionType.O365_SUSPICIOUS_FACTOR_REGISTRATION
        # det_type = DetectionType.O365_SUSPICIOUS_POWER_AUTOMATE
        other_det_type = DetectionType.SW_O365_ATTACK_TOOL_RULER
        with silent_console():
            generate.state_for_common_context(self.now, det_type, other_det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.O365)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_common_context(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.COMMON_CONTEXT.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]

        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)


@patch_registry(aws_distillation=True, settings_syslog=False)
class AWSDistillationTest(VuiTestCase):
    def setUp(self):
        self.now = datetime.now(timezone.utc)
        self.p.add_patch('db_commit', patch('django.db.connection.commit', autospec=True))
        self.p.add_patch('db_close', patch('django.db.connection.close', autospec=True))
        self.p.add_patch(
            'enable_triage', patch('base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled', return_value=True)
        )

        with silent_console():
            generate_detection_schemas()
            self.smart_rules = load_smart_rules()
            self.process = processing.Processing().process_new_detections

    def test_historical(self):
        """Test that the Learner successfully triggers historical distillation,
        by generating noisy detections of type 'aws_ec2_discovery' such that
        'today', or the day the learner state is built, would be the day that
        'aws_ec2_discovery' flips from not noisy to noisy.
        """
        det_type = DetectionType.AWS_EC2_DISCOVERY
        with silent_console():
            generate.state_for_historical(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AWS)

        details = get_detection_details()
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 107)
        self.assertEqual(len(not_distilled), 0)

        distill_reason = DistillationReason.DETECTION_CO_OCCURRENCE.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertTrue(all(det.account_detection.smart_rule_id == smart_rule_id for det in distilled))

    @patch_registry(entity_history_distillation_learned_state=True)
    def test_entity_history(self):
        """Test entity history distillation, by generating many historical
        detections on an entity of type 'aws_lambda_hijacking', and
        later pushing detections of the same type on that entity.

        Three detections are pushed at that later stage. One with no context,
        which should trigger 'entity history - detection type' distillation,
        one with context, which should trigger 'entity history - detection type',
        and one with an account id not seen before, which should not be distilled.
        """
        # Ensure we are using new entity history distillation (with learned state)
        self.assertTrue(flag_enabled(Flags.entity_history_distillation_learned_state))

        det_type = DetectionType.AWS_LAMBDA_HIJACKING
        with silent_console():
            generate.state_for_entity_history(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AWS)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_entity_history(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        for det in distilled:
            if "context" in det.account.uid:
                distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            else:
                distill_reason = DistillationReason.ENTITY_HISTORY_DETECTION.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(det.account_detection.smart_rule_id, smart_rule_id)

    @patch_registry(entity_history_distillation_learned_state=False)
    def test_old_entity_history(self):
        """Test entity history distillation, by generating many historical
        detections on an entity of type 'aws_lambda_hijacking', and
        later pushing detections of the same type on that entity.

        Three detections are pushed at that later stage. One with no context,
        which should trigger 'entity history - detection type' distillation,
        one with context, which should trigger 'entity history - detection type',
        and one with an account id not seen before, which should not be distilled.
        """
        # Ensure we are not using new entity history distillation (ie, we compute state manually)
        self.assertFalse(flag_enabled(Flags.entity_history_distillation_learned_state))

        det_type = DetectionType.AWS_LAMBDA_HIJACKING
        with silent_console():
            generate.state_for_entity_history(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AWS)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_entity_history(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        for det in distilled:
            if "context" in det.account.uid:
                distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            else:
                distill_reason = DistillationReason.ENTITY_HISTORY_DETECTION.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(det.account_detection.smart_rule_id, smart_rule_id)

    def test_cooccurrence(self):
        """Test detection cooccurrence distillation, by generating historical noisy
        detections of type 'aws_ec2_discovery', building a learner state from
        those detections, and later pushing detections of the same type.

        Two detections are pushed at that later stage. One of type
        'aws_ec2_discovery', which should trigger distillation, and
        one of a different type, which should not trigger distillation.
        """
        det_type = DetectionType.AWS_EC2_DISCOVERY
        other_det_type = DetectionType.AWS_LOGGING_DISABLED
        with silent_console():
            generate.state_for_cooccurrence(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AWS)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_cooccurrence(self.now, det_type, other_det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.DETECTION_CO_OCCURRENCE.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)

    def test_common_context(self):
        """Test common context distillation, by generating many historical
        detections of type 'aws_credential_access_param_store' with certain context fields,
        building a learner state from those detections, and later pushing
        detections of the same type.

        Two detections are pushed at that later stage. One with the same
        context fields (stored as common in the learner state), which
        should trigger distillation, and one with unseen context fields,
        which should not trigger distillation.
        """
        det_type = DetectionType.AWS_CREDENTIAL_ACCESS_PARAM_STORE
        other_det_type = DetectionType.AWS_EC2_DISCOVERY
        with silent_console():
            generate.state_for_common_context(self.now, det_type, other_det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AWS)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_common_context(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.COMMON_CONTEXT.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)


@patch_registry(azure_distillation=True, settings_syslog=False)
class AzureDistillationTest(VuiTestCase):
    def setUp(self):
        self.now = datetime.now(timezone.utc)
        self.p.add_patch('db_commit', patch('django.db.connection.commit', autospec=True))
        self.p.add_patch('db_close', patch('django.db.connection.close', autospec=True))
        self.p.add_patch(
            'enable_triage', patch('base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled', return_value=True)
        )

        with silent_console():
            generate_detection_schemas()
            self.smart_rules = load_smart_rules()
            self.process = processing.Processing().process_new_detections

    def test_historical(self):
        """Test that the Learner successfully triggers historical distillation."""
        det_type = DetectionType.AZURE_APPSERVICE_ACTIVITY
        with silent_console():
            generate.state_for_historical(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AZURE)

        details = get_detection_details()
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 107)
        self.assertEqual(len(not_distilled), 0)

        distill_reason = DistillationReason.DETECTION_CO_OCCURRENCE.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertTrue(all(det.account_detection.smart_rule_id == smart_rule_id for det in distilled))

    @patch_registry(entity_history_distillation_learned_state=True)
    def test_entity_history(self):
        """Test entity history distillation."""
        # Ensure we are using new entity history distillation (with learned state)
        self.assertTrue(flag_enabled(Flags.entity_history_distillation_learned_state))

        det_type = DetectionType.AZURE_APPSERVICE_ACTIVITY
        with silent_console():
            generate.state_for_entity_history(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AZURE)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_entity_history(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        for det in distilled:
            if "context" in det.account.uid:
                distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            else:
                distill_reason = DistillationReason.ENTITY_HISTORY_DETECTION.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(det.account_detection.smart_rule_id, smart_rule_id)

    @patch_registry(entity_history_distillation_learned_state=False)
    def test_old_entity_history(self):
        """Test entity history distillation."""
        # Ensure we are not using new entity history distillation (ie, we compute state manually)
        self.assertFalse(flag_enabled(Flags.entity_history_distillation_learned_state))

        det_type = DetectionType.AZURE_APPSERVICE_ACTIVITY
        with silent_console():
            generate.state_for_entity_history(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AZURE)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_entity_history(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        for det in distilled:
            if "context" in det.account.uid:
                distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            else:
                distill_reason = DistillationReason.ENTITY_HISTORY_DETECTION.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(det.account_detection.smart_rule_id, smart_rule_id)

    def test_cooccurrence(self):
        """Test detection cooccurrence distillation."""
        det_type = DetectionType.AZURE_SUSPICIOUS_DISK_DOWNLOAD
        other_det_type = DetectionType.AZURE_ST_PUBLIC_ACCESS_ENABLED
        with silent_console():
            generate.state_for_cooccurrence(self.now, det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AZURE)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_cooccurrence(self.now, det_type, other_det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.DETECTION_CO_OCCURRENCE.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)

    def test_common_context(self):
        """Test common context distillation."""
        det_type = DetectionType.AZURE_SLOT_ACTIVITY
        other_det_type = DetectionType.AZURE_CRYPTOMINING
        with silent_console():
            generate.state_for_common_context(self.now, det_type, other_det_type)
            self.process()
            learner.build_learner_state(data_source_type=DataSourceType.AZURE)
            learner_ids = {d.id for d in get_detection_details()}

            generate.detections_for_common_context(self.now, det_type)
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details)

        # tests
        self.assertEqual(len(distilled), 1)
        self.assertEqual(len(not_distilled), 1)
        self.assertTrue("no_distill" in not_distilled[0].account.uid)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.COMMON_CONTEXT.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertEqual(distilled[0].account_detection.smart_rule_id, smart_rule_id)


@patch_registry(network_distillation=True, c2_distillation=True, entity_history_distillation_learned_state=True, settings_syslog=False)
class NetworkDistillationTest(VuiTestCase):
    def setUp(self):
        self.now = datetime.now(timezone.utc)
        self.p.add_patch('db_commit', patch('django.db.connection.commit', autospec=True))
        self.p.add_patch('db_close', patch('django.db.connection.close', autospec=True))
        self.p.add_patch('score_host', patch('host_scoring_v2.host_scoring.score_host', autospec=True))
        self.p.add_patch(
            'get_detection_score',
            patch("detection_processing_v2.processing.detection_scoring.DetectionScoring._get_detection_score", autospec=True),
        )
        self.p.add_patch(
            'enable_triage', patch('base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled', return_value=True)
        )

        with silent_console():
            generate_detection_schemas()
            self.smart_rules = load_smart_rules()
            self.process = processing.Processing().process_new_detections

    def set_up_host_sessions(self):
        return {
            "distill": generate.create_host_session(self.now),
            "no_distill": generate.create_host_session(self.now),
        }

    def run_network_entity_history_tests(self, det_type: str, det_triageable_context: dict, should_distill: bool = True):
        host_sessions = self.set_up_host_sessions()
        with silent_console():
            generate.state_for_entity_history_network(
                self.now, det_type, num_days=50, src_hs=host_sessions["distill"], overrides=det_triageable_context
            )
            generate.state_for_entity_history_network(self.now, det_type, num_days=2, src_hs=host_sessions["no_distill"])
            self.process()

            learner.build_learner_state(data_source_type=DataSourceType.NETWORK)
            learner_ids = {d.id for d in get_detection_details()}

            det_count = 0
            det_count += generate.detections_for_entity_history_network(
                self.now, det_type, src_hs=host_sessions["distill"], overrides=det_triageable_context
            )
            det_count += generate.detections_for_entity_history_network(self.now, det_type, src_hs=host_sessions["no_distill"])
            self.process()

        details = [d for d in get_detection_details() if d.id not in learner_ids]
        distilled, not_distilled = split_details(details, entity_type="host")

        # tests
        num_distilled = 1 if should_distill else 0
        num_not_distilled = det_count - num_distilled
        self.assertEqual(len(distilled), num_distilled)
        self.assertEqual(len(not_distilled), num_not_distilled)
        if should_distill:
            self.assertEqual(host_sessions["distill"].ip_address, distilled[0].host_detection.src_ip)
            self.assertEqual(host_sessions["no_distill"].ip_address, not_distilled[0].host_detection.src_ip)

            # check if detail was distilled by correct level
            distill_reason = DistillationReason.ENTITY_HISTORY_CONTEXT.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(distilled[0].host_detection.smart_rule_id, smart_rule_id)
            self.assertEqual(distilled[0].distilled_context, det_triageable_context)
        else:
            src_ips = [host_detection.src_ip for host_detection in not_distilled]
            self.assertTrue(host_sessions["distill"].ip_address in src_ips)
            self.assertTrue(host_sessions["no_distill"].ip_address in src_ips)

    def run_network_c2_tests(self, det_type: str, should_distill: bool = True):
        if should_distill:
            test_l2_domain = f"test.{next(iter(L2_DOMAIN_LIST))}"
            test_fq_domain = next(iter(FQ_DOMAIN_LIST))
        else:
            test_l2_domain = "evil1234.com"
            test_fq_domain = "this.domain.is.bad.co.uk"
        with silent_console():
            det_count = 0
            det_count += generate.detections_for_c2_network(self.now, det_type, [test_fq_domain])
            det_count += generate.detections_for_c2_network(self.now, det_type, [test_l2_domain])
            self.process()

        details = get_detection_details()
        distilled, not_distilled = split_details(details, entity_type="host")

        # tests
        num_distilled = 2 if should_distill else 0
        num_not_distilled = det_count - num_distilled
        self.assertEqual(len(distilled), num_distilled)
        self.assertEqual(len(not_distilled), num_not_distilled)

        if should_distill:
            distilled_contexts = [distilled[0].distilled_context, distilled[1].distilled_context]
            self.assertTrue({"dst_dns": [test_l2_domain]} in distilled_contexts)
            self.assertTrue({"dst_dns": [test_fq_domain]} in distilled_contexts)

            # check if detail was distilled by correct level
            distill_reason = DistillationReason.C2_DOMAIN_LIST.value
            smart_rule_id = self.smart_rules[(det_type, distill_reason)]
            self.assertEqual(distilled[0].host_detection.smart_rule_id, smart_rule_id)
            self.assertEqual(distilled[1].host_detection.smart_rule_id, smart_rule_id)

    def test_entity_history_darknet(self):
        """Test entity history distillation for darknet. We should trigger when dst_port is 1, but not when it is
        the default value (80). Note that dst_port is the triageable_context field for darknet detections."""
        det_type = DetectionType.DARKNET
        det_triageable_context = generate.DEFAULT_OVERRIDES[det_type]
        self.run_network_entity_history_tests(det_type, det_triageable_context)

    def test_entity_history_kerberoasting_cipher_downgrade(self):
        """Test entity history distillation for kerberoasting_cipher_downgrade."""
        det_type = DetectionType.KERBEROASTING_CIPHER_DOWNGRADE
        det_triageable_context = generate.DEFAULT_OVERRIDES[det_type]
        self.run_network_entity_history_tests(det_type, det_triageable_context)

    def test_entity_history_ldap_recon(self):
        """Test entity history distillation for ldap_recon."""
        det_type = DetectionType.LDAP_RECON
        det_triageable_context = generate.DEFAULT_OVERRIDES[det_type]
        self.run_network_entity_history_tests(det_type, det_triageable_context)

    def test_entity_history_rdp_recon(self):
        """Test entity history distillation for rdp_recon."""
        det_type = DetectionType.RDP_RECON
        det_triageable_context = generate.DEFAULT_OVERRIDES[det_type]
        self.run_network_entity_history_tests(det_type, det_triageable_context)

    def test_entity_history_rpc_recon_1to1(self):
        """Test entity history distillation for kerberoasting_cipher_downgrade."""
        det_type = DetectionType.RPC_RECON_1TO1
        det_triageable_context = generate.DEFAULT_OVERRIDES[det_type]
        self.run_network_entity_history_tests(det_type, det_triageable_context)

    def test_entity_history_frontwatch_no_distill(self):
        """Test entity history distillation for frontwatch, which should not be distilled."""
        det_type = DetectionType.FRONTWATCH
        det_triageable_context = generate.DEFAULT_OVERRIDES.get(det_type)
        self.run_network_entity_history_tests(det_type, det_triageable_context, should_distill=False)

    def test_c2_frontwatch_cloud(self):
        """Test c2 distillation for frontwatch, which should be distilled. Frontwatch can have multiple dst_dns values."""
        det_type = DetectionType.FRONTWATCH
        test_l2_domain_1 = f"test.{next(iter(L2_DOMAIN_LIST))}"
        test_l2_domain_2 = f"test.{next(iter(L2_DOMAIN_LIST))}"
        test_fq_domain_1 = next(iter(FQ_DOMAIN_LIST))
        test_fq_domain_2 = next(iter(FQ_DOMAIN_LIST))
        with silent_console():
            det_count = 0
            det_count += generate.detections_for_c2_network(self.now, det_type, [test_fq_domain_1, test_fq_domain_2])
            det_count += generate.detections_for_c2_network(self.now, det_type, [test_l2_domain_1, test_l2_domain_2])
            self.process()

        details = get_detection_details()
        distilled, not_distilled = split_details(details, entity_type="host")

        # tests
        self.assertEqual(len(distilled), 2)
        self.assertEqual(len(not_distilled), 0)

        distilled_contexts = [distilled[0].distilled_context, distilled[1].distilled_context]
        self.assertTrue({"dst_dns": [test_l2_domain_1, test_l2_domain_2]} in distilled_contexts)
        self.assertTrue({"dst_dns": [test_fq_domain_1, test_fq_domain_2]} in distilled_contexts)

        # check if detail was distilled by correct level
        distill_reason = DistillationReason.C2_DOMAIN_LIST.value
        smart_rule_id = self.smart_rules[(det_type, distill_reason)]
        self.assertEqual(distilled[0].host_detection.smart_rule_id, smart_rule_id)
        self.assertEqual(distilled[1].host_detection.smart_rule_id, smart_rule_id)

    def test_c2_hidden_dns_tunnel_cnc(self):
        """Test c2 distillation for hidden_dns_tunnel_cnc, which should be distilled."""
        det_type = DetectionType.HIDDEN_DNS_TUNNEL_CNC
        self.run_network_c2_tests(det_type)

    def test_c2_hidden_binaryloader(self):
        """Test c2 distillation for binaryloader, which should be distilled."""
        det_type = DetectionType.BINARYLOADER
        self.run_network_c2_tests(det_type)

    def test_c2_reverse_rat_cloud(self):
        """Test c2 distillation for reverse_rat, which should be distilled."""
        det_type = DetectionType.REVERSE_RAT
        self.run_network_c2_tests(det_type)

    def test_c2_reverse_rat_no_distill(self):
        """Test c2 distillation for reverse_rat with malicious domain, which should not be distilled."""
        det_type = DetectionType.REVERSE_RAT
        self.run_network_c2_tests(det_type, should_distill=False)
