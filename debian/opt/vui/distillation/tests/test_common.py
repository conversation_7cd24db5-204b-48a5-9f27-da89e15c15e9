from unittest import mock

from distillation.common import (
    is_distillation_enabled,
    DataSourceType,
    DISTILLATION_DATA_SOURCE_TYPES,
    get_distillation_flag,
)

from vui_tests.vui_testcase import VuiTestCase


class TestCommon(VuiTestCase):
    @mock.patch("distillation.common.flag_enabled")
    @mock.patch("base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled")
    def test_is_distillation_enabled_true(self, mock_auto_triage, mock_flag_enabled):
        mock_auto_triage.return_value = True
        mock_flag_enabled.return_value = True
        for ds_type in DISTILLATION_DATA_SOURCE_TYPES:
            assert is_distillation_enabled(ds_type) is True

    @mock.patch("distillation.common.flag_enabled")
    @mock.patch("base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled")
    def test_is_distillation_enabled_false_auto_triage(self, mock_auto_triage, mock_flag_enabled):
        mock_auto_triage.return_value = False
        mock_flag_enabled.return_value = True
        for ds_type in DISTILLATION_DATA_SOURCE_TYPES:
            assert is_distillation_enabled(ds_type) is False

    @mock.patch("distillation.common.flag_enabled")
    @mock.patch("base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled")
    def test_is_distillation_enabled_false_flag(self, mock_auto_triage, mock_flag_enabled):
        mock_auto_triage.return_value = True
        mock_flag_enabled.return_value = False
        for ds_type in DISTILLATION_DATA_SOURCE_TYPES:
            assert is_distillation_enabled(ds_type) is False

    @mock.patch("distillation.common.flag_enabled")
    @mock.patch("base_tvui.smart_rules.auto_triage.lib_autotriage.is_auto_triage_fully_enabled")
    def test_is_distillation_enabled_unsupported_type(self, mock_auto_triage, mock_flag_enabled):
        mock_auto_triage.return_value = True
        mock_flag_enabled.return_value = True
        unsupported_type = DataSourceType.UNSUPPORTED
        assert unsupported_type not in DISTILLATION_DATA_SOURCE_TYPES
        assert is_distillation_enabled(unsupported_type) is False

    def test_get_distillation_flag(self):
        for ds_type in DataSourceType:
            if ds_type in DISTILLATION_DATA_SOURCE_TYPES:
                assert get_distillation_flag(ds_type) is not None
            else:
                assert get_distillation_flag(ds_type) is None
