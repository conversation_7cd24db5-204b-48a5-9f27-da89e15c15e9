from typing import Optional, List
from enum import Enum
from base_tvui.feature_flipper import Flags, flag_enabled
from base_tvui.schema_utils import load_schemas_from_disk
from tvui.detections.detection_types import DetectionType


class DistillationReason(Enum):
    ENTITY_HISTORY_DETECTION = "Entity History - Detection Type"
    ENTITY_HISTORY_CONTEXT = "Entity History - Context"
    DETECTION_CO_OCCURRENCE = "Detection Co-occurrence"
    COMMON_CONTEXT = "Common Context"
    C2_DOMAIN_LIST = "C2 Domain List"


class DistillationDCVersions:
    ACCOUNT_DET_TYPES = "0.0.1"
    DISTILLATION_DETAIL = "0.0.1"
    DISTILLATION_CONTEXT = "0.0.1"
    LEARNER_STATE = "0.0.1"


INFO_DETECTIONS = {schema["type"] for schema in load_schemas_from_disk() if schema["category"] == "info"}
NO_DISTILL_DETECTION_TYPES = set(INFO_DETECTIONS)

NETWORK_DISTILL_DETECTION_TYPES = [
    "darknet",
    "internal_spreading",
    "kerberoasting_cipher_downgrade",
    "kerberoasting_spn_sweep",
    "kerberos_password_spray",
    "kerberos_user_account_scan",
    "ldap_recon",
    "port_scan",
    "port_sweep",
    "rdp_recon",
    "rpc_recon",
    "rpc_recon_1to1",
    "smb_enum_share",
    "smb_enum_user",
]

C2_TRIAGE_DETECTION_TYPES = [
    "binaryloader",
    "cnc_dga",
    # "conn_relay",
    "frontwatch",
    "hidden_dns_tunnel_cnc",
    "hidden_http_tunnel_cnc",
    "hidden_https_tunnel_cnc",
    "http_cnc",
    "icmp_tunnel",
    "p2p_cnc",
    "reverse_rat",
    "stealth_post",
]


class DataSourceType(Enum):
    O365 = "o365"
    AWS = "aws"
    AZURE = "azure"
    NETWORK = "network"
    UNSUPPORTED = "unsupported"

    @staticmethod
    def type_to_source_type(detection_type: str) -> "DataSourceType":
        if detection_type.lower().startswith("sw_o365") or detection_type.lower().startswith("o365"):
            return DataSourceType.O365
        elif detection_type.lower().startswith("aws"):
            return DataSourceType.AWS
        elif detection_type.lower().startswith("azure"):
            return DataSourceType.AZURE
        elif detection_type in NETWORK_DISTILL_DETECTION_TYPES or C2_TRIAGE_DETECTION_TYPES:
            return DataSourceType.NETWORK
        else:
            return DataSourceType.UNSUPPORTED


# which data source types we currently offer distillation for
DISTILLATION_FLAG_MAPPING = {
    DataSourceType.O365: Flags.o365_distillation,
    DataSourceType.AWS: Flags.aws_distillation,
    DataSourceType.AZURE: Flags.azure_distillation,
    DataSourceType.NETWORK: Flags.network_distillation,
}
DISTILLATION_DATA_SOURCE_TYPES = list(DISTILLATION_FLAG_MAPPING.keys())


def is_distillation_enabled(data_source_type: DataSourceType) -> bool:
    from base_tvui.smart_rules.auto_triage.lib_autotriage import is_auto_triage_fully_enabled

    """Check if distillation is enabled for the given data source type."""
    return (
        is_auto_triage_fully_enabled()
        and data_source_type in DISTILLATION_DATA_SOURCE_TYPES
        and flag_enabled(get_distillation_flag(data_source_type))
    )


def get_distillation_flag(data_source_type: DataSourceType) -> Optional[Flags]:
    return DISTILLATION_FLAG_MAPPING.get(data_source_type, None)


def get_distillation_detection_types() -> List[str]:
    """Return list of all detection types from distillable data sources."""
    return (
        DetectionType.get_list_of_o365_detection_types()
        + DetectionType.get_list_of_aws_detection_types()
        + DetectionType.get_list_of_azure_detection_types()
        + NETWORK_DISTILL_DETECTION_TYPES
        + C2_TRIAGE_DETECTION_TYPES
    )
