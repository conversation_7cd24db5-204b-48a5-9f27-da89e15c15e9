/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `archive_tvui_userinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `first_login` datetime DEFAULT NULL,
  `notification_enabled` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `userinfo_user_id` (`user_id`),
  CONSTRAINT `userinfo_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_group_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_id` (`group_id`,`permission_id`),
  KEY `auth_group_permissions_5f412f9a` (`group_id`),
  KEY `auth_group_permissions_83d7f98b` (`permission_id`),
  CONSTRAINT `group_id_refs_id_f4b32aac` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `permission_id_refs_id_6ba0f519` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `content_type_id` int(11) NOT NULL,
  `codename` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `content_type_id` (`content_type_id`,`codename`),
  KEY `auth_permission_37ef4eb4` (`content_type_id`),
  CONSTRAINT `content_type_id_refs_id_d043b34a` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `password` varchar(128) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(250) NOT NULL,
  `first_name` varchar(30) NOT NULL,
  `last_name` varchar(150) NOT NULL,
  `email` varchar(75) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `deactivated_ts` int(11) NOT NULL DEFAULT '0',
  `date_joined` datetime NOT NULL,
  `first_login` datetime DEFAULT NULL,
  `notification_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `password_set_timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `ldap_profile_id` int(11) unsigned DEFAULT NULL,
  `base_dn` varchar(255) DEFAULT NULL,
  `account_type` enum('LDAP','local','limitedtimelink','TACACS','special','RADIUS','SAML','JWT','API_CLIENT') NOT NULL,
  `authentication_profile_id` int(11) unsigned DEFAULT NULL,
  `password_expiration_warning_timestamp` datetime DEFAULT NULL,
  `saml_profile_id` int(11) unsigned DEFAULT NULL,
  `saas_group_id` int(11) DEFAULT NULL,
  `saas_group_timestamp` datetime DEFAULT NULL,
  `api_client_profile_id` int(11) DEFAULT NULL,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `saas_local_profile_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_deactivated_ts` (`username`,`deactivated_ts`),
  UNIQUE KEY `saas_local_profile_id` (`saas_local_profile_id`),
  KEY `ldap_profile_reference` (`ldap_profile_id`),
  KEY `authentication_profile_ref` (`authentication_profile_id`),
  KEY `saml_profile_ref` (`saml_profile_id`),
  KEY `saas_group_reference` (`saas_group_id`),
  KEY `fk_auth_user_api_client_profile` (`api_client_profile_id`),
  CONSTRAINT `authentication_profile_ref` FOREIGN KEY (`authentication_profile_id`) REFERENCES `tvui_authenticationprofile` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_auth_user_api_client_profile` FOREIGN KEY (`api_client_profile_id`) REFERENCES `tvui_apiclientprofile` (`id`),
  CONSTRAINT `fk_saas_local_profile_id` FOREIGN KEY (`saas_local_profile_id`) REFERENCES `tvui_saaslocaluserprofile` (`id`),
  CONSTRAINT `ldap_profile_reference` FOREIGN KEY (`ldap_profile_id`) REFERENCES `tvui_ldapprofile` (`id`) ON DELETE SET NULL,
  CONSTRAINT `saas_group_reference` FOREIGN KEY (`saas_group_id`) REFERENCES `auth_group` (`id`) ON DELETE SET NULL,
  CONSTRAINT `saml_profile_ref` FOREIGN KEY (`saml_profile_id`) REFERENCES `tvui_samlprofile` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_user_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`,`group_id`),
  KEY `auth_user_groups_6340c63c` (`user_id`),
  KEY `auth_user_groups_5f412f9a` (`group_id`),
  CONSTRAINT `group_id_refs_id_274b862c` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `user_id_refs_id_40c41112` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `auth_user_user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`,`permission_id`),
  KEY `auth_user_user_permissions_6340c63c` (`user_id`),
  KEY `auth_user_user_permissions_83d7f98b` (`permission_id`),
  CONSTRAINT `permission_id_refs_id_35d9ac25` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `user_id_refs_id_4dc23c39` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `authtoken_token` (
  `key` varchar(40) NOT NULL,
  `created` datetime NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`key`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `authtoken_token_fk_user` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `axes_accessattempt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_agent` varchar(255) NOT NULL,
  `ip_address` char(39) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `http_accept` varchar(1025) NOT NULL,
  `path_info` varchar(255) NOT NULL,
  `attempt_time` datetime(6) NOT NULL,
  `get_data` longtext NOT NULL,
  `post_data` longtext NOT NULL,
  `failures_since_start` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `axes_accessattempt_ip_address_10922d9c` (`ip_address`),
  KEY `axes_accessattempt_user_agent_ad89678b` (`user_agent`(191)),
  KEY `axes_accessattempt_username_3f2d4ca0` (`username`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `axes_accesslog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_agent` varchar(255) NOT NULL,
  `ip_address` char(39) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `http_accept` varchar(1025) NOT NULL,
  `path_info` varchar(255) NOT NULL,
  `attempt_time` datetime(6) NOT NULL,
  `logout_time` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `axes_accesslog_ip_address_86b417e5` (`ip_address`),
  KEY `axes_accesslog_user_agent_0e659004` (`user_agent`(191)),
  KEY `axes_accesslog_username_df93064b` (`username`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_admin_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action_time` datetime NOT NULL,
  `user_id` int(11) NOT NULL,
  `content_type_id` int(11) DEFAULT NULL,
  `object_id` longtext,
  `object_repr` varchar(200) NOT NULL,
  `action_flag` smallint(5) unsigned NOT NULL,
  `change_message` longtext NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_6340c63c` (`user_id`),
  KEY `django_admin_log_37ef4eb4` (`content_type_id`),
  CONSTRAINT `content_type_id_refs_id_93d2d1f8` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `user_id_refs_id_c0d12874` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_beat_clockedschedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `clocked_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_beat_crontabschedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `minute` varchar(240) NOT NULL,
  `hour` varchar(96) NOT NULL,
  `day_of_week` varchar(64) NOT NULL,
  `day_of_month` varchar(124) NOT NULL,
  `month_of_year` varchar(64) NOT NULL,
  `timezone` varchar(63) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_beat_intervalschedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `every` int(11) NOT NULL,
  `period` varchar(24) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_beat_periodictask` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `task` varchar(200) NOT NULL,
  `args` longtext NOT NULL,
  `kwargs` longtext NOT NULL,
  `queue` varchar(200) DEFAULT NULL,
  `exchange` varchar(200) DEFAULT NULL,
  `routing_key` varchar(200) DEFAULT NULL,
  `expires` datetime DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `last_run_at` datetime DEFAULT NULL,
  `total_run_count` int(10) unsigned NOT NULL,
  `date_changed` datetime NOT NULL,
  `description` longtext NOT NULL,
  `crontab_id` int(11) DEFAULT NULL,
  `interval_id` int(11) DEFAULT NULL,
  `solar_id` int(11) DEFAULT NULL,
  `one_off` tinyint(1) NOT NULL,
  `start_time` datetime DEFAULT NULL,
  `priority` int(10) unsigned DEFAULT NULL,
  `headers` longtext NOT NULL,
  `clocked_id` int(11) DEFAULT NULL,
  `expire_seconds` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `django_celery_beat_p_crontab_id_d3cba168_fk_django_ce` (`crontab_id`),
  KEY `django_celery_beat_p_interval_id_a8ca27da_fk_django_ce` (`interval_id`),
  KEY `django_celery_beat_p_solar_id_a87ce72c_fk_django_ce` (`solar_id`),
  KEY `django_celery_beat_p_clocked_id_47a69f82_fk_django_ce` (`clocked_id`),
  CONSTRAINT `django_celery_beat_p_clocked_id_47a69f82_fk_django_ce` FOREIGN KEY (`clocked_id`) REFERENCES `django_celery_beat_clockedschedule` (`id`),
  CONSTRAINT `django_celery_beat_p_crontab_id_d3cba168_fk_django_ce` FOREIGN KEY (`crontab_id`) REFERENCES `django_celery_beat_crontabschedule` (`id`),
  CONSTRAINT `django_celery_beat_p_interval_id_a8ca27da_fk_django_ce` FOREIGN KEY (`interval_id`) REFERENCES `django_celery_beat_intervalschedule` (`id`),
  CONSTRAINT `django_celery_beat_p_solar_id_a87ce72c_fk_django_ce` FOREIGN KEY (`solar_id`) REFERENCES `django_celery_beat_solarschedule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_beat_periodictasks` (
  `ident` smallint(6) NOT NULL,
  `last_update` datetime NOT NULL,
  PRIMARY KEY (`ident`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_beat_solarschedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event` varchar(24) NOT NULL,
  `latitude` decimal(9,6) NOT NULL,
  `longitude` decimal(9,6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_celery_beat_solar_event_latitude_longitude_ba64999a_uniq` (`event`,`latitude`,`longitude`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_results_chordcounter` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(191) NOT NULL,
  `sub_tasks` longtext NOT NULL,
  `count` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_results_groupresult` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(191) NOT NULL,
  `date_created` datetime NOT NULL,
  `date_done` datetime NOT NULL,
  `content_type` varchar(128) NOT NULL,
  `content_encoding` varchar(64) NOT NULL,
  `result` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_id` (`group_id`),
  KEY `django_cele_date_cr_bd6c1d_idx` (`date_created`),
  KEY `django_cele_date_do_caae0e_idx` (`date_done`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_celery_results_taskresult` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` varchar(191) NOT NULL,
  `status` varchar(50) NOT NULL,
  `content_type` varchar(128) NOT NULL,
  `content_encoding` varchar(64) NOT NULL,
  `result` longtext,
  `date_done` datetime NOT NULL,
  `traceback` longtext,
  `meta` longtext,
  `task_args` longtext,
  `task_kwargs` longtext,
  `task_name` varchar(191) DEFAULT NULL,
  `worker` varchar(100) DEFAULT NULL,
  `date_created` datetime NOT NULL,
  `periodic_task_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `task_id` (`task_id`),
  KEY `django_cele_task_na_08aec9_idx` (`task_name`),
  KEY `django_cele_status_9b6201_idx` (`status`),
  KEY `django_cele_worker_d54dd8_idx` (`worker`),
  KEY `django_cele_date_cr_f04a50_idx` (`date_created`),
  KEY `django_cele_date_do_f59aad_idx` (`date_done`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_content_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `app_label` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `app_label` (`app_label`,`model`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_session` (
  `session_key` varchar(40) NOT NULL,
  `session_data` longtext NOT NULL,
  `expire_date` datetime NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_b7b81f0c` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `django_site` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(100) NOT NULL,
  `name` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `schema_updates` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `update_name` varchar(255) NOT NULL,
  `date_applied` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taggit_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taggit_taggeditem` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tag_id` int(11) NOT NULL,
  `object_id` int(11) NOT NULL,
  `content_type_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `taggit_taggeditem_5659cca2` (`tag_id`),
  KEY `taggit_taggeditem_846f0221` (`object_id`),
  KEY `content_type_id_object_id_keys` (`content_type_id`,`object_id`),
  CONSTRAINT `content_type_id_refs_id_01d42cdf` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `tag_id_refs_id_c23fda9d` FOREIGN KEY (`tag_id`) REFERENCES `taggit_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON taggit_taggeditem FOR EACH ROW BEGIN
  SET @object_type = (SELECT model from django_content_type where id = NEW.content_type_id);
  CALL add_object_to_be_replicated(NEW.object_id, @object_type, 'insert', @object_type = 'detection');
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on taggit_taggeditem FOR EACH ROW BEGIN
 SET @object_type = (SELECT model from django_content_type where id = NEW.content_type_id);
 CALL add_object_to_be_replicated(NEW.object_id, @object_type, 'update', @object_type = 'detection');
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER DELETE ON taggit_taggeditem FOR EACH ROW BEGIN
 SET @object_type = (SELECT model from django_content_type where id = OLD.content_type_id);
 CALL add_object_to_be_replicated(OLD.object_id, @object_type, 'delete', @object_type = 'detection');
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `traffic_validation_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dr_batch_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_traffic_report_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `uid_md5` varchar(255) AS (MD5(uid)) PERSISTENT,
  `priv_score` double DEFAULT NULL,
  `priv_score_date` datetime DEFAULT NULL,
  `account_type` enum('kerberos','o365','aws','entra_principal') NOT NULL,
  `first_seen` datetime DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `priv_level` int(4) unsigned DEFAULT NULL,
  `priv_level_date` datetime DEFAULT NULL,
  `c_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `t_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `state` enum('active','inactive') NOT NULL DEFAULT 'inactive',
  `alarmed_datetime` datetime DEFAULT NULL,
  `alarmed_reason` enum('account','detection') DEFAULT NULL,
  `alarmed_state` enum('alarmed') DEFAULT NULL,
  `last_alarmed_check` datetime DEFAULT NULL,
  `last_alert` datetime DEFAULT NULL,
  `linked_account_id` bigint(20) DEFAULT NULL,
  `uid_domain` varchar(253) AS (SUBSTRING(uid FROM NULLIF(LOCATE("@", uid), 0) + 1)) PERSISTENT,
  `attack_rating` smallint(5) unsigned NOT NULL DEFAULT '0',
  `velocity_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `breadth_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `is_prioritized` tinyint(1) NOT NULL,
  `entity_importance` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_reason` varchar(255) DEFAULT NULL,
  `archetype` varchar(255) DEFAULT NULL,
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `rescore_pending` tinyint(1) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  `pure_uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid_md5` (`uid_md5`),
  KEY `tvui_account_uid` (`uid`(191)),
  KEY `uid_domain_index` (`uid_domain`),
  KEY `fk_linked_account_id` (`linked_account_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  KEY `account_pure_uid_idx` (`pure_uid`(191)),
  CONSTRAINT `fk_linked_account_id` FOREIGN KEY (`linked_account_id`) REFERENCES `tvui_linked_account` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_account FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'account', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_account FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'account', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER DELETE on tvui_account FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(OLD.id, 'account', 'delete', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_account_association_mapping` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `from_domain` varchar(253) NOT NULL,
  `to_domain` varchar(253) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_account_probable_homes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `host_id` int(11) NOT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_id` (`account_id`,`host_id`),
  KEY `fk_probable_homes_host_id` (`host_id`),
  CONSTRAINT `fk_probable_homes_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_probable_homes_host_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_accountgroup` (
  `groupcollection_ptr_id` int(11) NOT NULL,
  PRIMARY KEY (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_accountgroup_groupcollection_fk_ptr_tvui_groupcollection_id` FOREIGN KEY (`groupcollection_ptr_id`) REFERENCES `tvui_groupcollection` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_accountgroup_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountgroup_id` int(11) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  `date_added` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_accountgroup_accounts_accountgroup_account_id_uniq` (`accountgroup_id`,`account_id`),
  KEY `tvui_accountgroup_accounts_account_fk_tvui_account_id` (`account_id`),
  CONSTRAINT `tvui_accountgroup_accounts_account_fk_tvui_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_accountgroup_accounts_accountgroup_fk_tvui_account` FOREIGN KEY (`accountgroup_id`) REFERENCES `tvui_accountgroup` (`groupcollection_ptr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_accountgroup_linkedaccounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `accountgroup_id` int(11) NOT NULL,
  `linkedaccount_id` bigint(20) NOT NULL,
  `date_added` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_accountgroup_linkedaccounts_group_id_uniq` (`accountgroup_id`,`linkedaccount_id`),
  KEY `tvui_accountgroup_linkedaccounts_id_fk` (`linkedaccount_id`),
  CONSTRAINT `tvui_accountgroup_linkedaccounts_group_fk` FOREIGN KEY (`accountgroup_id`) REFERENCES `tvui_accountgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_accountgroup_linkedaccounts_id_fk` FOREIGN KEY (`linkedaccount_id`) REFERENCES `tvui_linked_account` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_accountlockdownhistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` enum('lock','unlock','grant_amnesty','revoke_amnesty','external_unlock','revoke_session') NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `info` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_lockdown_history_user_fk` (`user_id`),
  KEY `account_id` (`account_id`),
  CONSTRAINT `tvui_lockdown_history_account_fk` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_lockdown_history_user_fk` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_accountlockdownqueue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amnesty_threat` int(11) DEFAULT NULL,
  `amnesty_certainty` int(11) DEFAULT NULL,
  `amnesty_priv_level` int(4) unsigned DEFAULT NULL,
  `expiration` datetime DEFAULT NULL,
  `locked_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `amnesty_urgency` int(11) DEFAULT NULL,
  `amnesty_importance` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_id` (`account_id`),
  KEY `tvui_lockdown_user_fk` (`user_id`),
  CONSTRAINT `tvui_lockdown_account_fk` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_lockdown_user_fk` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_accountscorehistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `c_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `t_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `score_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `score_decrease` tinyint(1) DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `attack_rating` smallint(5) unsigned NOT NULL DEFAULT '0',
  `velocity_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `breadth_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `is_prioritized` tinyint(1) NOT NULL,
  `entity_importance` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_reason` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_score_account_id` (`account_id`),
  CONSTRAINT `fk_score_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_adv_inv_shortcut` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(80) NOT NULL,
  `data_source` varchar(100) NOT NULL,
  `data_stream` varchar(100) NOT NULL,
  `connectors` longtext NOT NULL,
  `search_params` longtext NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_modified_by_id` int(11) DEFAULT NULL,
  `shared` tinyint(1) NOT NULL DEFAULT '0',
  `usage` int(11) DEFAULT '0',
  `query_type` enum('sql','pill') DEFAULT 'pill',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `created_by_id` (`created_by_id`),
  KEY `last_modified_by_id` (`last_modified_by_id`),
  CONSTRAINT `tvui_adv_inv_shortcut_ibfk_1` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`),
  CONSTRAINT `tvui_adv_inv_shortcut_ibfk_2` FOREIGN KEY (`last_modified_by_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_alarm_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host_id` int(11) DEFAULT NULL,
  `alarmed_state` enum('alarmed') DEFAULT NULL,
  `alarmed_reason` enum('host','detection') DEFAULT NULL,
  `alarmed_datetime` datetime DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `linked_account_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_host` (`host_id`),
  KEY `fk_alarm_history_account_id` (`account_id`),
  KEY `fk_alarm_history_linked_account_id` (`linked_account_id`),
  CONSTRAINT `fk_alarm_history_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `fk_alarm_history_linked_account_id` FOREIGN KEY (`linked_account_id`) REFERENCES `tvui_linked_account` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_apiclientcredentials` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `api_client_secret` varchar(256) DEFAULT NULL,
  `api_client_profile_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `api_client_profile_fk` (`api_client_profile_id`),
  CONSTRAINT `api_client_profile_fk` FOREIGN KEY (`api_client_profile_id`) REFERENCES `tvui_apiclientprofile` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_apiclientprofile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` char(32) NOT NULL,
  `name` varchar(256) DEFAULT NULL,
  `description` varchar(512) DEFAULT NULL,
  `last_used` datetime DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_client_id` (`client_id`),
  KEY `fk_api_client_created_by` (`created_by_id`),
  CONSTRAINT `fk_api_client_created_by` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_assetinventoryreportdef` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `frequency` enum('daily','weekly','monthly','on_demand') NOT NULL DEFAULT 'daily',
  `from_date` datetime DEFAULT NULL,
  `to_date` datetime DEFAULT NULL,
  `email_addresses` longtext,
  `created_by_id` int(11) DEFAULT NULL,
  `lastsent_timestamp` datetime DEFAULT NULL,
  `created_timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `asset_inventory_report_def_created_by_fk_user_id` (`created_by_id`),
  CONSTRAINT `asset_inventory_report_def_created_by_fk_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_assignment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `obj_type` enum('linked_account','detection','host') DEFAULT NULL,
  `type_id` int(11) unsigned NOT NULL,
  `user_id` int(11) NOT NULL,
  `assigned_by_id` int(11) DEFAULT NULL,
  `date_assigned` datetime DEFAULT NULL,
  `date_resolved` datetime DEFAULT NULL,
  `resolved_by_id` int(11) DEFAULT NULL,
  `triaged_detections` longtext,
  `outcome_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `assignment_user_id` (`user_id`),
  KEY `assignment_assigned_by` (`assigned_by_id`),
  KEY `tvui_assignment_resolved_by_id_47e27a62_fk_auth_user_id` (`resolved_by_id`),
  KEY `tvui_assignment_outcome_id_6e665333_fk_tvui_assi` (`outcome_id`),
  KEY `object_type_type_id_outcome_id_keys` (`obj_type`,`type_id`,`outcome_id`),
  CONSTRAINT `assignment_assigned_by` FOREIGN KEY (`assigned_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL,
  CONSTRAINT `assignment_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tvui_assignment_outcome_id_6e665333_fk_tvui_assi` FOREIGN KEY (`outcome_id`) REFERENCES `tvui_assignmentoutcome` (`id`),
  CONSTRAINT `tvui_assignment_resolved_by_id_47e27a62_fk_auth_user_id` FOREIGN KEY (`resolved_by_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_assignment FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'assignment', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_assignment FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'assignment', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_assignmentevent` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_type` varchar(255) NOT NULL,
  `datetime` datetime NOT NULL,
  `context` longtext,
  `actor_id` int(11) DEFAULT NULL,
  `assignment_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_assignmentevent_actor_id_87351ecd_fk_auth_user_id` (`actor_id`),
  KEY `tvui_assignmentevent_assignment_id_4480fa3e_fk_tvui_assi` (`assignment_id`),
  CONSTRAINT `tvui_assignmentevent_actor_id_87351ecd_fk_auth_user_id` FOREIGN KEY (`actor_id`) REFERENCES `auth_user` (`id`),
  CONSTRAINT `tvui_assignmentevent_assignment_id_4480fa3e_fk_tvui_assi` FOREIGN KEY (`assignment_id`) REFERENCES `tvui_assignment` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_assignmentoutcome` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(127) NOT NULL,
  `description` text,
  `builtin` tinyint(1) NOT NULL DEFAULT '0',
  `user_selectable` tinyint(1) NOT NULL DEFAULT '1',
  `category` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_auditevents` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `user_role` varchar(255) DEFAULT NULL,
  `user_type` varchar(64) DEFAULT NULL,
  `api_client_id` varchar(255) DEFAULT NULL,
  `version` varchar(32) DEFAULT NULL,
  `source_ip` varchar(255) DEFAULT NULL,
  `event_timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `message` varchar(255) DEFAULT NULL,
  `result_status` enum('success','failure') DEFAULT NULL,
  `event_data` longtext,
  `event_object` varchar(255) DEFAULT NULL,
  `event_action` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_audit_user_id_by_fk_auth_user_id` (`user_id`),
  CONSTRAINT `tvui_audit_user_id_by_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_authenticationprofile` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `server_uri_0` varchar(255) DEFAULT NULL,
  `server_uri_1` varchar(255) DEFAULT NULL,
  `server_uri_2` varchar(255) DEFAULT NULL,
  `server_secret_0` varchar(255) DEFAULT NULL,
  `server_secret_1` varchar(255) DEFAULT NULL,
  `server_secret_2` varchar(255) DEFAULT NULL,
  `authentication` enum('pap','chap') DEFAULT NULL,
  `profile_type` enum('TACACS','RADIUS') NOT NULL,
  `timeout` smallint(5) unsigned NOT NULL,
  `authorization_mapping` varchar(2048) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_auto_triage_job_context` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_identifier` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mode` enum('ENABLED','DISABLED','METRICS_ONLY') COLLATE utf8mb4_unicode_ci NOT NULL,
  `context` longtext COLLATE utf8mb4_unicode_ci,
  `metrics_only_context` longtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `job_identifier` (`job_identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_aws_account_attrs` (
  `invoked_by` varchar(1024) DEFAULT NULL,
  `principal_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `session_name` varchar(255) DEFAULT NULL,
  `identity_type` enum('AWS Service','AWS External Account','IAM User','Federated Account','Root User','IAM Role') DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `service_id_attr` varchar(1024) DEFAULT NULL,
  `service_name` varchar(255) DEFAULT NULL,
  `role_name` varchar(255) DEFAULT NULL,
  `account_id` bigint(20) NOT NULL,
  PRIMARY KEY (`account_id`),
  CONSTRAINT `tvui_account` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_azure_ad_lockdown_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `retry_count` int(11) NOT NULL DEFAULT '0',
  `non_retry_response` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `account_id` (`account_id`),
  CONSTRAINT `tvui_azure_ad_lockdown_queue_ibfk_1` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_braintobrainbackup` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server` varchar(255) NOT NULL,
  `serial` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `status` enum('queued','transfering','skipped','failed','done') NOT NULL,
  `date_queued` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `retries` smallint(6) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `braintobrainbackup_unique_server_filename` (`serial`,`filename`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_campaign` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `external_ip` varchar(255) DEFAULT NULL,
  `external_domain` varchar(128) DEFAULT NULL,
  `state` enum('active','closed','init','closed_never_active') NOT NULL DEFAULT 'init',
  `couch_note_id` char(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `last_updated` datetime DEFAULT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `name` varchar(255) NOT NULL,
  `reason_closed` enum('triaged','idled') DEFAULT NULL,
  `state_transition_ts` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `campaign_luid` (`couch_note_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_campaignevent` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) unsigned NOT NULL,
  `event_type` enum('connection','detection','related_detection') NOT NULL,
  `event_name` varchar(128) DEFAULT NULL,
  `campaign_member_id` int(11) unsigned NOT NULL,
  `recorded_dest_name` varchar(128) DEFAULT NULL,
  `recorded_hostname` varchar(128) DEFAULT NULL,
  `recorded_src_ip` char(39) DEFAULT NULL,
  `recorded_dest_ip` char(39) DEFAULT NULL,
  `timestamp` datetime DEFAULT NULL,
  `partial` tinyint(1) NOT NULL DEFAULT '0',
  `detection_note_id` char(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `det_detail_id` int(11) DEFAULT NULL,
  `relayed_comm_id` int(11) DEFAULT NULL,
  `detail_type` enum('detection_detail') DEFAULT NULL,
  `is_original_det` tinyint(1) DEFAULT '0',
  `num_connections` int(10) unsigned DEFAULT NULL,
  `bytes_sent` bigint(20) DEFAULT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `bytes_recvd` bigint(20) DEFAULT NULL,
  `recorded_src_key_asset` tinyint(1) DEFAULT '0',
  `recorded_src_c_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `recorded_src_t_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `campaign_event_unique_dd` (`det_detail_id`,`campaign_member_id`),
  UNIQUE KEY `campaign_event_unique_rc` (`relayed_comm_id`,`campaign_member_id`),
  KEY `fk_campaign_event_to_campaign` (`campaign_id`),
  KEY `fk_campaign_event_to_cam_mem` (`campaign_member_id`),
  KEY `fk_camp_event_to_det_detail` (`det_detail_id`),
  KEY `fk_camp_event_to_relayed_comm` (`relayed_comm_id`),
  CONSTRAINT `fk_camp_event_to_det_detail` FOREIGN KEY (`det_detail_id`) REFERENCES `tvui_detection_detail` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_campaign_event_to_cam_mem` FOREIGN KEY (`campaign_member_id`) REFERENCES `tvui_campaignmembership` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_campaign_event_to_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `tvui_campaign` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_campaignmembership` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `host_session_luid` char(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `host_session_id` int(11) unsigned DEFAULT NULL,
  `campaign_id` int(11) unsigned NOT NULL,
  `latest_conn_ts` datetime DEFAULT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_es_timestamp` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `campaign_membership_unique` (`campaign_id`,`host_session_id`),
  KEY `fk_campaign_mem_to_host_sess` (`host_session_id`),
  KEY `fk_campaign_mem_to_campaign` (`campaign_id`),
  KEY `camp_mem_hs_luid` (`host_session_luid`),
  CONSTRAINT `fk_campaign_mem_to_campaign` FOREIGN KEY (`campaign_id`) REFERENCES `tvui_campaign` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_campaign_mem_to_host_sess` FOREIGN KEY (`host_session_id`) REFERENCES `tvui_host_session` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER new_campaign_mem_host_session_connect BEFORE INSERT ON tvui_campaignmembership FOR EACH ROW BEGIN
  IF NEW.host_session_luid IS NOT NULL THEN
    SET NEW.host_session_id = (SELECT id from tvui_host_session hs where NEW.host_session_luid = hs.session_luid);
  END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_campaignname` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `count` int(10) unsigned NOT NULL DEFAULT '0',
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `campaign_name_unique` (`name`),
  KEY `campaign_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_cantina_checkpoint` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `last_run` datetime NOT NULL,
  `obj_type` varchar(16) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_cisoreportdef` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `frequency` enum('daily','weekly','monthly','on_demand') NOT NULL DEFAULT 'daily',
  `from_date` datetime DEFAULT NULL,
  `to_date` datetime DEFAULT NULL,
  `email_addresses` longtext,
  `created_by_id` int(11) DEFAULT NULL,
  `lastsent_timestamp` datetime DEFAULT NULL,
  `created_timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `ciso_report_def_created_by_fk_user_id` (`created_by_id`),
  CONSTRAINT `ciso_report_def_created_by_fk_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_close_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host_id` int(11) DEFAULT NULL,
  `linked_account_id` bigint(20) DEFAULT NULL,
  `detection_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `closed_on` datetime NOT NULL,
  `reason` enum('benign','remediated','not_valuable') NOT NULL,
  `assignment_timestamp` datetime DEFAULT NULL,
  `is_prioritized` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id_fk` (`user_id`),
  KEY `host_id_index` (`host_id`),
  KEY `linked_account_id_index` (`linked_account_id`),
  KEY `detection_id_index` (`detection_id`),
  KEY `closed_on_index` (`closed_on`),
  CONSTRAINT `detection_id_fk` FOREIGN KEY (`detection_id`) REFERENCES `tvui_detection` (`id`) ON DELETE CASCADE,
  CONSTRAINT `host_id_fk` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE,
  CONSTRAINT `linked_account_id_fk` FOREIGN KEY (`linked_account_id`) REFERENCES `tvui_linked_account` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_cloudsensor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `sensor_type` varchar(16) NOT NULL,
  `serial_number` varchar(40) NOT NULL,
  `luid` varchar(16) NOT NULL,
  `sensor_data` longtext NOT NULL,
  `token` char(16) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `source_id` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_cloudsensor_unique_name` (`name`),
  UNIQUE KEY `tvui_cloudsensor_unique_sn` (`serial_number`),
  UNIQUE KEY `tvui_cloudsensor_unique_luid` (`luid`),
  UNIQUE KEY `source_id` (`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_custom_model_definition` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_model_id` varchar(36) NOT NULL,
  `full_custom_model_id` varchar(44) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `category` varchar(16) NOT NULL,
  `lucene` longtext NOT NULL,
  `elastic_filters` longtext NOT NULL,
  `index` varchar(16) NOT NULL,
  `threat_score` smallint(5) unsigned NOT NULL DEFAULT '1',
  `certainty_score` smallint(5) unsigned NOT NULL DEFAULT '1',
  `description` longtext,
  `custom_model_search_link` longtext,
  `previous_custom_model_id` varchar(36) DEFAULT NULL,
  `deprecated` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_custom_model_definition_cm_id_uniq` (`custom_model_id`),
  UNIQUE KEY `tvui_custom_model_definition_full_cm_id_uniq` (`full_custom_model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_custom_model_rate_limit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `custom_model_id` varchar(36) NOT NULL,
  `infected_host_session_luid` char(8) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `detection_details_total` int(10) unsigned NOT NULL,
  `detection_details_sent` int(10) unsigned NOT NULL,
  `from_timestamp` datetime NOT NULL,
  `to_timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `rate_limit_unique` (`custom_model_id`,`infected_host_session_luid`,`to_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_customer_files` (
  `id` varchar(36) CHARACTER SET utf8 NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `file_name` varchar(256) CHARACTER SET utf8 NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `topic` varchar(256) CHARACTER SET utf8 NOT NULL,
  `notes` mediumtext CHARACTER SET utf8,
  `upload_status` enum('completed','aborted','pending') NOT NULL DEFAULT 'pending',
  `file_sync_status` enum('in_progress','retrying','success','failed','not_started') NOT NULL DEFAULT 'not_started',
  `file_sync_created_at` datetime DEFAULT NULL,
  `file_sync_updated_at` datetime DEFAULT NULL,
  `file_sync_retries` int(11) NOT NULL DEFAULT '0',
  `file_sync_error` mediumtext CHARACTER SET utf8,
  `file_sync_error_code` int(11) DEFAULT NULL,
  `s3_object_key` varchar(2048) CHARACTER SET utf8 DEFAULT NULL,
  `s3_object_checksum` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `s3_object_checksum_algorithm` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_cf_user` (`user_id`),
  KEY `customer_files_updated_at_index` (`updated_at`),
  CONSTRAINT `fk_cf_user` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_data_source` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `source_id` varchar(128) NOT NULL,
  `source_type` enum('aws','o365','network') NOT NULL,
  `last_detection_sequence_id` varchar(16) NOT NULL,
  `last_seen` datetime NOT NULL,
  `details` longtext,
  `expiration_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_data_source_unique_id` (`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_deletion_event` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `obj_type` enum('detection') NOT NULL DEFAULT 'detection',
  `type_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_detection` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `type_vname` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `category` enum('BOTNET ACTIVITY','COMMAND & CONTROL','EXFILTRATION','LATERAL MOVEMENT','RECONNAISSANCE','INFO','Unclassified') CHARACTER SET utf8 DEFAULT 'Unclassified',
  `app_id` varchar(32) CHARACTER SET utf8 DEFAULT NULL,
  `description` longtext CHARACTER SET utf8,
  `description2` longtext CHARACTER SET utf8,
  `src_ip` char(39) CHARACTER SET utf8 DEFAULT NULL,
  `src_dns` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `src_geo` varchar(4) CHARACTER SET utf8 DEFAULT NULL,
  `src_geo_lat` decimal(9,6) DEFAULT NULL,
  `src_geo_lon` decimal(9,6) DEFAULT NULL,
  `first_timestamp` datetime DEFAULT NULL,
  `last_timestamp` datetime NOT NULL,
  `active_time` int(11) DEFAULT NULL,
  `pcap_file` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `total_bytes_sent` bigint(20) DEFAULT NULL,
  `total_bytes_rcvd` bigint(20) DEFAULT NULL,
  `count` int(10) unsigned DEFAULT NULL,
  `count_pos` int(10) unsigned DEFAULT NULL,
  `state` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `t_score` smallint(6) DEFAULT NULL,
  `c_score` smallint(6) DEFAULT NULL,
  `dfz_score` int(11) DEFAULT NULL,
  `sensor_luid` varchar(8) COLLATE utf8_bin DEFAULT NULL,
  `smart_rule_id` int(11) DEFAULT NULL,
  `host_session_id` int(11) unsigned DEFAULT NULL,
  `event_count` smallint(5) unsigned NOT NULL DEFAULT '0',
  `created_datetime` datetime DEFAULT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `targets_key_asset` tinyint(1) DEFAULT '0',
  `alarmed_datetime` datetime DEFAULT NULL,
  `updated_es_timestamp` datetime DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `subtype` varchar(32) COLLATE utf8_bin DEFAULT NULL,
  `mark_as_fixed_on` datetime DEFAULT NULL,
  `mark_as_fixed_user_id` int(11) DEFAULT NULL,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `data_source_type` varchar(8) COLLATE utf8_bin DEFAULT NULL,
  `state_reason` enum('benign','remediated','not_valuable') COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_bucket` (`type`,`host_session_id`,`smart_rule_id`,`app_id`),
  KEY `tvui_detection_403d8ff3` (`type`),
  KEY `tvui_detection_9193e0f8` (`type_vname`),
  KEY `tvui_detection_6f33f001` (`category`),
  KEY `tvui_detection_e25b796d` (`app_id`),
  KEY `tvui_detection_b9130ab0` (`smart_rule_id`),
  KEY `host_session_id` (`host_session_id`),
  KEY `tvui_detection_account_fk` (`account_id`),
  KEY `tvui_mark_as_fixed_user_id_fk` (`mark_as_fixed_user_id`),
  CONSTRAINT `host_session_id` FOREIGN KEY (`host_session_id`) REFERENCES `tvui_host_session` (`id`) ON DELETE CASCADE,
  CONSTRAINT `smart_rule_id_refs_id_401fe9c3` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`),
  CONSTRAINT `tvui_detection_account_fk` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_mark_as_fixed_user_id_fk` FOREIGN KEY (`mark_as_fixed_user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER update_host_src_time AFTER INSERT ON tvui_detection FOR EACH ROW BEGIN
   CALL detection_ins_upd(NEW.host_session_id, NEW.last_timestamp); 
  CALL add_object_to_be_replicated(NEW.id, 'detection', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER update_host_src_time2 AFTER UPDATE ON tvui_detection FOR EACH ROW BEGIN
   CALL detection_ins_upd(NEW.host_session_id, NEW.last_timestamp); 
  CALL add_object_to_be_replicated(NEW.id, 'detection', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER delete_detection_event AFTER DELETE ON tvui_detection FOR EACH ROW BEGIN
  IF OLD.last_timestamp > NOW() - INTERVAL 31 DAY THEN
    INSERT INTO `tvui_deletion_event` SET type_id = OLD.id, obj_type = 'detection';
  END IF;
  CALL add_object_to_be_replicated(OLD.id, 'detection', 'delete', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_detection_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dst_ip` char(39) CHARACTER SET utf8 DEFAULT NULL,
  `description` longtext CHARACTER SET utf8,
  `dst_port` smallint(5) unsigned DEFAULT NULL,
  `dst_dns` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `dst_geo` varchar(4) CHARACTER SET utf8 DEFAULT NULL,
  `dst_geo_lat` decimal(9,6) DEFAULT NULL,
  `dst_geo_lon` decimal(9,6) DEFAULT NULL,
  `first_timestamp` datetime DEFAULT NULL,
  `last_timestamp` datetime NOT NULL,
  `total_bytes_sent` bigint(20) DEFAULT NULL,
  `total_bytes_rcvd` bigint(20) DEFAULT NULL,
  `proto` varchar(25) CHARACTER SET utf8 DEFAULT NULL,
  `count` int(10) unsigned DEFAULT NULL,
  `count_pos` bigint(20) unsigned DEFAULT NULL,
  `pcap_file` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `skip_count` int(10) unsigned DEFAULT NULL,
  `host_detection_id` int(11) DEFAULT NULL,
  `account_detection_id` int(11) DEFAULT NULL,
  `skip_count_pos` int(10) unsigned DEFAULT '0',
  `skip_bytes_rcvd` bigint(20) DEFAULT NULL,
  `skip_bytes_sent` bigint(20) DEFAULT NULL,
  `src_ip` char(39) CHARACTER SET utf8 DEFAULT NULL,
  `couch_note_id` char(32) COLLATE utf8_bin DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `subtype` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `category` varchar(32) CHARACTER SET utf8 DEFAULT NULL,
  `sensor_luid` char(8) COLLATE utf8_bin DEFAULT NULL,
  `src_session_luid` char(8) COLLATE utf8_bin DEFAULT NULL,
  `src_host_session_id` int(11) unsigned DEFAULT NULL,
  `reason` varchar(8192) CHARACTER SET utf8 DEFAULT NULL,
  `dst_session_luid` char(8) COLLATE utf8_bin DEFAULT NULL,
  `dst_host_session_id` int(11) unsigned DEFAULT NULL,
  `identity` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `is_host_detail` tinyint(1) NOT NULL DEFAULT '1',
  `is_account_detail` tinyint(1) NOT NULL DEFAULT '0',
  `account_uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `flex1` longtext COLLATE utf8_bin,
  `flex2` longtext COLLATE utf8_bin,
  `flex3` longtext COLLATE utf8_bin,
  `flex4` longtext COLLATE utf8_bin,
  `flex5` longtext COLLATE utf8_bin,
  `flex6` longtext COLLATE utf8_bin,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `flex_json` longtext COLLATE utf8_bin,
  `detection_guid` varchar(36) COLLATE utf8_bin DEFAULT NULL,
  `sequence_id` varchar(16) COLLATE utf8_bin DEFAULT NULL,
  `distilled_context` longtext COLLATE utf8_bin,
  `date_couch` datetime DEFAULT NULL,
  `date_publish` datetime DEFAULT NULL,
  `date_s3` datetime DEFAULT NULL,
  `date_first_bucket` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_detection_detail_ccdfa9a7` (`dst_ip`),
  KEY `tvui_detection_detail_23aa256e` (`dst_port`),
  KEY `tvui_detection_detail_172db09a` (`host_detection_id`),
  KEY `dd_dst_host_session` (`dst_host_session_id`),
  KEY `dst_session_luid` (`dst_session_luid`),
  KEY `det_detail_note_id` (`couch_note_id`),
  KEY `dd_src_host_session` (`src_host_session_id`),
  KEY `tvui_detection_detail_src_session_luid` (`src_session_luid`),
  KEY `tvui_detection_detail_account_detection_fk` (`account_detection_id`),
  KEY `tvui_detection_detail_host_and_acc` (`host_detection_id`,`account_detection_id`),
  KEY `det_account_id` (`account_id`),
  KEY `tvui_detection_detail_sequence_sensor` (`sensor_luid`,`sequence_id`),
  KEY `detection_detail_distillation` (`type`,`account_id`),
  KEY `detection_detail_type_dst_dns` (`type`,`dst_dns`(255),`host_detection_id`),
  KEY `detection_detail_type_dst_ip` (`type`,`dst_ip`,`host_detection_id`),
  CONSTRAINT `dd_dst_host_session` FOREIGN KEY (`dst_host_session_id`) REFERENCES `tvui_host_session` (`id`),
  CONSTRAINT `dd_src_host_session` FOREIGN KEY (`src_host_session_id`) REFERENCES `tvui_host_session` (`id`),
  CONSTRAINT `det_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_detection_detail_account_detection_fk` FOREIGN KEY (`account_detection_id`) REFERENCES `tvui_detection` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tvui_detection_detail_host_detection_fk` FOREIGN KEY (`host_detection_id`) REFERENCES `tvui_detection` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER new_det_host_session_connect BEFORE INSERT ON tvui_detection_detail FOR EACH ROW BEGIN
  IF NEW.dst_session_luid IS NOT NULL THEN
    SET NEW.dst_host_session_id = (SELECT id from tvui_host_session hs where NEW.dst_session_luid = hs.session_luid);
  END IF;

  IF NEW.src_session_luid IS NOT NULL THEN
    SET NEW.src_host_session_id = (SELECT id from tvui_host_session hs where NEW.src_session_luid = hs.session_luid);
  END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_detection_detail FOR EACH ROW BEGIN
  IF NEW.host_detection_id IS NOT NULL OR NEW.account_detection_id IS NOT NULL THEN
    CALL add_object_to_be_replicated(NEW.id, 'detection_detail', 'insert', TRUE);
  END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_detection_detail FOR EACH ROW BEGIN
  IF NEW.host_detection_id IS NOT NULL OR NEW.account_detection_id IS NOT NULL THEN
    CALL add_object_to_be_replicated(NEW.id, 'detection_detail', 'update', TRUE);
  END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_detection_event` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` varchar(32) DEFAULT NULL,
  `type` varchar(64) DEFAULT NULL,
  `entity_type` enum('account','host') DEFAULT NULL,
  `detection_detail_id` int(11) DEFAULT NULL,
  `detection_id` int(11) DEFAULT NULL,
  `category` varchar(32) DEFAULT NULL,
  `threat` smallint(5) unsigned NOT NULL DEFAULT '0',
  `certainty` smallint(5) unsigned NOT NULL DEFAULT '0',
  `triage_id` int(11) DEFAULT NULL,
  `host_session_id` int(11) unsigned DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `account_id` bigint(11) DEFAULT NULL,
  `account_uid` varchar(1024) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `src_host_session_luid` varchar(128) DEFAULT NULL,
  `detection_vname` varchar(64) DEFAULT NULL,
  `mitre_list` longtext CHARACTER SET utf8 COLLATE utf8_bin,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_detection_schema` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` int(11) DEFAULT NULL,
  `type` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `name` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `category` varchar(32) DEFAULT NULL,
  `schema` longtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_distillation_learner_entity_history_state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `detection_type` varchar(128) NOT NULL,
  `entity_type` enum('account','host') NOT NULL,
  `entity_id` bigint(20) NOT NULL,
  `computed_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `type_state` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `composite_pk` (`detection_type`,`entity_type`,`entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_distillation_learner_state` (
  `detection_type` varchar(128) NOT NULL,
  `computed_at` datetime NOT NULL,
  `type_state` longtext,
  PRIMARY KEY (`detection_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_edr_context` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `edr_id` varchar(128) NOT NULL,
  `edr_type` varchar(128) NOT NULL,
  `operating_system` varchar(256) DEFAULT NULL,
  `edr_context` longtext NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_edr_schema` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(64) NOT NULL,
  `schema` longtext NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_email_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `to` varchar(255) NOT NULL,
  `payload` longtext,
  `queued_on` datetime(6) NOT NULL,
  `num_tries` int(11) NOT NULL,
  `last_tried` datetime(6) DEFAULT NULL,
  `error_message` longtext,
  `failed_permanent` tinyint(1) NOT NULL,
  `residual_metadata` longtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_entity_context` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `datasource_id` varchar(128) NOT NULL,
  `context_type` enum('entra_graph_v1_user','entra_graph_v1_service_principal','entra_sp_derived_application') NOT NULL,
  `canonical_id` varchar(128) NOT NULL,
  `canonical_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_tombstone` tinyint(1) NOT NULL DEFAULT '0',
  `alternate_names` longtext,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `valid_from` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
  `entity_batch_uid` varchar(1024) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `context` longtext,
  `metadata` longtext,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_context_account_id` (`account_id`),
  KEY `canonical_id_index` (`canonical_id`,`datasource_id`,`valid_from`),
  KEY `entity_batch_md5_index` (`entity_batch_uid_md5`,`entity_batch_index`),
  CONSTRAINT `fk_context_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_entity_work_queue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `entity_type` varchar(128) NOT NULL,
  `batch_uid` varchar(128) NOT NULL,
  `batch_index` int(11) NOT NULL,
  `data` longtext,
  `is_processed` tinyint(1) DEFAULT '0',
  `retry_count` int(11) NOT NULL DEFAULT '0',
  `forwarder_date` datetime NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_errored` tinyint(1) DEFAULT '0',
  `event_date` datetime DEFAULT NULL,
  `batch_uid_md5` varchar(32) DEFAULT NULL,
  `s3_full_path` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `entity_persister_filter_index` (`entity_type`,`is_processed`,`is_errored`,`updated_date`),
  KEY `entity_batch_identfier` (`batch_uid`,`batch_index`),
  KEY `entity_batch_md5_identfier` (`batch_uid_md5`,`batch_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_espreliminaryqueue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `object_type` varchar(64) NOT NULL,
  `object_id` bigint(20) NOT NULL,
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `operation` enum('insert','update','delete','unknown') NOT NULL DEFAULT 'unknown',
  `enqueue_related` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `obj_key` (`object_type`,`object_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_esreplicatorlogposition` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `position` int(11) unsigned NOT NULL,
  `schema_version` int(11) unsigned DEFAULT NULL,
  `last_event_ts` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_esreplicatorqueue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `endpoint` varchar(255) NOT NULL,
  `object_id` int(11) unsigned NOT NULL,
  `last_attempt_ts` datetime DEFAULT NULL,
  `num_attempts` int(11) DEFAULT '0',
  `created_ts` datetime DEFAULT NULL,
  `priority` int(11) NOT NULL DEFAULT '50',
  `triggered_by` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_esreplicatorlogposition_endpoint_object_id` (`endpoint`,`object_id`),
  KEY `last_attempt_ts` (`last_attempt_ts`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_externaldomaingroup` (
  `groupcollection_ptr_id` int(11) NOT NULL,
  `domains` longtext,
  PRIMARY KEY (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_externaldomaing_groupcollection_ptr__5eb28ca9_fk_tvui_grou` FOREIGN KEY (`groupcollection_ptr_id`) REFERENCES `tvui_groupcollection` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_externalnotification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `notification_type` enum('campaign_alert','host_alert','system','system_health','assignment','password','account_alert','ad_lockdown','host_lockdown','azure_ad_auto_lockdown') DEFAULT NULL,
  `notification_data` varchar(2048) DEFAULT NULL,
  `delivery_type` enum('email','sms','url','syslog','none') DEFAULT 'none',
  `result` enum('success','failed','skipped') DEFAULT NULL,
  `retries` tinyint(2) unsigned DEFAULT '0',
  `result_detail` varchar(255) DEFAULT NULL,
  `date_delivered` datetime DEFAULT NULL,
  `date_last_attempted` datetime DEFAULT NULL,
  `date_created` datetime NOT NULL,
  `skipped_count` smallint(4) unsigned DEFAULT '0',
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_ff_migration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_id` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_file_hash_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(150) NOT NULL,
  `hash_value` char(255) NOT NULL,
  `updated_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier` (`identifier`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_global_view_instances` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(256) DEFAULT NULL,
  `timezone` varchar(63) NOT NULL,
  `url` varchar(1024) DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `internal_brain_id` varchar(1024) DEFAULT NULL,
  `last_seen_status` varchar(1024) DEFAULT NULL,
  `last_seen_timestamp` datetime DEFAULT NULL,
  `client_api_id` varchar(1024) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_group_extend` (
  `group_id` int(11) NOT NULL,
  `vname` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`group_id`),
  CONSTRAINT `auth_group` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_group_membership_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_type` varchar(128) NOT NULL,
  `group_id` int(11) NOT NULL,
  `membership_action` varchar(255) DEFAULT NULL,
  `members` longtext,
  `reason` varchar(255) DEFAULT NULL,
  `event_timestamp` datetime DEFAULT NULL,
  `event_trigger` enum('GROUP_CREATE','GROUP_EDIT','GROUP_DELETE','ENTITY_CREATE','ENTITY_UPDATE','HOURLY_EVALUATION') DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_groupcollection` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(96) NOT NULL,
  `type` enum('host','domain','ip','account') NOT NULL,
  `description` longtext,
  `last_modified_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified_by_id` int(11) NOT NULL,
  `family` enum('PREDEFINED','CUSTOMER','PREDEFINED_CUSTOMER','ALGORITHM','ALGORITHM_CUSTOMER') NOT NULL DEFAULT 'CUSTOMER',
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `importance` enum('high','medium','low','never_prioritize') NOT NULL DEFAULT 'medium',
  `created_by_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `rule` longtext,
  `member_type` enum('static','dynamic','active_directory') DEFAULT NULL,
  `member_eval_pending` varchar(191) DEFAULT NULL,
  `ad_group_dn` varchar(1024) DEFAULT NULL,
  `ldap_context_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_collection_name_uniq` (`name`),
  KEY `tvui_collection_last_modified_by_fk_auth_user_id` (`last_modified_by_id`),
  KEY `tvui_group_collection_created_by_fk_user_id` (`created_by_id`),
  KEY `groupcollection_ldapcontext_fk` (`ldap_context_id`),
  CONSTRAINT `groupcollection_ldapcontext_fk` FOREIGN KEY (`ldap_context_id`) REFERENCES `tvui_ldap_context` (`id`) ON DELETE SET NULL,
  CONSTRAINT `tvui_collection_last_modified_by_fk_auth_user_id` FOREIGN KEY (`last_modified_by_id`) REFERENCES `auth_user` (`id`),
  CONSTRAINT `tvui_group_collection_created_by_fk_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_groupcollection FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'groupcollection', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_groupcollection FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'groupcollection', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_healthsubject` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `subject_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `customer_visible` tinyint(1) NOT NULL DEFAULT '0',
  `deprecated` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_healthsubjecthistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timestamp` datetime NOT NULL,
  `expires` datetime DEFAULT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci,
  `health_subject_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_health_subject_id` (`health_subject_id`),
  KEY `health_entry_timestamp` (`timestamp`),
  CONSTRAINT `fk_health_subject_id` FOREIGN KEY (`health_subject_id`) REFERENCES `tvui_healthsubject` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `new_host_pointer` int(11) DEFAULT NULL,
  `name` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `v1_name_history` longtext CHARACTER SET utf8,
  `state` enum('init','active','inactive','suspended','merged') CHARACTER SET utf8 NOT NULL DEFAULT 'init',
  `last_source` char(39) CHARACTER SET utf8 DEFAULT NULL,
  `t_score` smallint(6) DEFAULT NULL,
  `c_score` smallint(6) DEFAULT NULL,
  `dfz_score` int(11) DEFAULT NULL,
  `last_detection_timestamp` datetime DEFAULT NULL,
  `first_timestamp` datetime DEFAULT NULL,
  `last_alert` datetime DEFAULT NULL,
  `alarmed_state` enum('alarmed') COLLATE utf8_bin DEFAULT NULL,
  `alarmed_reason` enum('host','detection') COLLATE utf8_bin DEFAULT NULL,
  `alarmed_datetime` datetime DEFAULT NULL,
  `last_alarmed_check` datetime DEFAULT NULL,
  `sensor_luid` varchar(8) COLLATE utf8_bin DEFAULT NULL,
  `key_asset` tinyint(1) NOT NULL,
  `host_luid` char(8) COLLATE utf8_bin DEFAULT NULL,
  `artifact_crc` int(11) unsigned DEFAULT NULL,
  `admin_name_timestamp` datetime DEFAULT NULL,
  `hid_gen` enum('V1','V2') CHARACTER SET utf8 NOT NULL DEFAULT 'V2',
  `type` enum('normal','generic','nonattributable') COLLATE utf8_bin NOT NULL DEFAULT 'normal',
  `server` tinyint(1) NOT NULL,
  `priv_level` int(4) unsigned DEFAULT NULL,
  `priv_level_date` datetime DEFAULT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `targets_key_asset` tinyint(1) DEFAULT '0',
  `last_modified` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_es_timestamp` datetime DEFAULT NULL,
  `name_modified_by_id` int(11) DEFAULT NULL,
  `os_version` varchar(64) COLLATE utf8_bin DEFAULT NULL,
  `device_roles` varchar(256) COLLATE utf8_bin DEFAULT NULL,
  `marked_for_delete` tinyint(1) NOT NULL DEFAULT '0',
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `rev` int(11) NOT NULL DEFAULT '0',
  `attack_rating` smallint(5) unsigned NOT NULL DEFAULT '0',
  `velocity_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `breadth_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `is_prioritized` tinyint(1) NOT NULL,
  `entity_importance` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_reason` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `archetype` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `probable_owner_update_date` datetime DEFAULT NULL,
  `entity_batch_uid` varchar(128) COLLATE utf8_bin DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `last_source_numeric` int(11) unsigned AS (INET_ATON(TRIM(last_source))) PERSISTENT,
  `entity_batch_uid_md5` varchar(32) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `host_luid` (`host_luid`),
  KEY `tvui_host_4da47e07` (`name`),
  KEY `tvui_host_868e3a96` (`last_source`),
  KEY `c_score` (`c_score`),
  KEY `t_score` (`t_score`),
  KEY `host_modified` (`last_modified`),
  KEY `name_modified_by_id_fk` (`name_modified_by_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  KEY `idx_last_source_numeric` (`last_source_numeric`),
  KEY `tvui_host_state_and_last_source_numeric` (`state`,`last_source_numeric`),
  KEY `tvui_host_state_prioritized` (`state`,`is_prioritized`),
  CONSTRAINT `name_modified_by_id_fk` FOREIGN KEY (`name_modified_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_host FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'host', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_host FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'host', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER DELETE on tvui_host FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(OLD.id, 'host', 'delete', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host_artifact` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `type` varchar(64) NOT NULL,
  `value` varchar(255) NOT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `host_id` (`host_id`,`type`,`value`),
  CONSTRAINT `fk_sig_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host_audit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `month` varchar(128) NOT NULL,
  `host_luid` varchar(128) DEFAULT NULL,
  `host_session_luid` varchar(128) DEFAULT NULL,
  `ip_address` varchar(36) NOT NULL,
  `host_id` int(11) DEFAULT NULL,
  `host_artifact_value` varchar(128) DEFAULT NULL,
  `host_artifact_type` varchar(128) DEFAULT NULL,
  `start` datetime NOT NULL,
  `end` datetime DEFAULT NULL,
  `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tvui_host_audit_ip_address` (`ip_address`),
  KEY `tvui_host_audit_host_luid_month` (`host_luid`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host_audit_snapshot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date_created` datetime NOT NULL,
  `day` varchar(16) NOT NULL,
  `max_count` int(11) NOT NULL,
  `actual_count` int(11) DEFAULT NULL,
  `max_count_timestamp` datetime NOT NULL,
  `audit_data` longtext,
  `compressed_audit_data` longblob,
  `compression_type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `day` (`day`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host_probable_owners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `host_id` (`host_id`,`account_id`),
  KEY `fk_probalbe_owner_account_id` (`account_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  CONSTRAINT `fk_probable_owner_host_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_probalbe_owner_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_host_probable_owners FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.host_id, 'host', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_host_probable_owners FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.host_id, 'host', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host_role` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `role_name` varchar(64) NOT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `host_id_role_unique` (`host_id`,`role_name`),
  KEY `tvui_host_role_role_name_idx` (`role_name`),
  KEY `tvui_host_role_date_last_seen_idx` (`date_last_seen`),
  CONSTRAINT `fk_host_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_host_session` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `host_id` int(11) DEFAULT NULL,
  `host_luid` varchar(8) COLLATE utf8_bin DEFAULT NULL,
  `ip_address` varchar(255) CHARACTER SET utf8 NOT NULL,
  `session_luid` char(8) COLLATE utf8_bin NOT NULL,
  `start` datetime NOT NULL,
  `end` datetime DEFAULT NULL,
  `sensor_luid` varchar(8) COLLATE utf8_bin DEFAULT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `replication_version` int(11) NOT NULL DEFAULT '0',
  `entity_batch_uid` varchar(128) COLLATE utf8_bin DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_luid` (`session_luid`),
  KEY `host_id` (`host_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  CONSTRAINT `host_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER new_host_session_connect AFTER INSERT ON tvui_host_session FOR EACH ROW BEGIN
  UPDATE tvui_detection_detail dd SET dd.dst_host_session_id = NEW.id WHERE dd.dst_session_luid = NEW.session_luid;
  UPDATE tvui_detection_detail dd SET dd.src_host_session_id = NEW.id WHERE dd.src_session_luid = NEW.session_luid;
  UPDATE tvui_campaignmembership cm SET cm.host_session_id = NEW.id WHERE cm.host_session_luid = NEW.session_luid;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=*/ /*!50003 TRIGGER update_host_last_detection_timestamp_hs_moved AFTER UPDATE ON tvui_host_session FOR EACH ROW BEGIN
  IF NEW.host_id != OLD.host_id THEN
    CALL host_session_reassigned(NEW.id, NEW.host_id, OLD.host_id);
  END IF;
  CALL add_object_to_be_replicated(NEW.id, 'host_session', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_hostgroup` (
  `groupcollection_ptr_id` int(11) NOT NULL,
  PRIMARY KEY (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_hostgroup_groupcollection_fk_ptr_tvui_groupcollection_id` FOREIGN KEY (`groupcollection_ptr_id`) REFERENCES `tvui_groupcollection` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_hostgroup_hosts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hostgroup_id` int(11) NOT NULL,
  `host_id` int(11) NOT NULL,
  `date_added` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_hostgroup_hosts_hostgroup_host_id_uniq` (`hostgroup_id`,`host_id`),
  KEY `tvui_hostgroup_hosts_host_fk_tvui_host_id` (`host_id`),
  CONSTRAINT `tvui_hostgroup_hosts_host_fk_tvui_host_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`),
  CONSTRAINT `tvui_hostgroup_hosts_hostgroup_fk_tvui_host` FOREIGN KEY (`hostgroup_id`) REFERENCES `tvui_hostgroup` (`groupcollection_ptr_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_hostlockdownhistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` enum('lock','unlock','grant_amnesty','revoke_amnesty','external_unlock') NOT NULL,
  `edr_type` varchar(64) NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `tvui_hostlockdown_history_user_fk` (`user_id`),
  KEY `host_id` (`host_id`),
  CONSTRAINT `tvui_hostlockdown_history_host_fk` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tvui_hostlockdown_history_user_fk` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_hostlockdownqueue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amnesty_threat` int(11) DEFAULT NULL,
  `amnesty_certainty` int(11) DEFAULT NULL,
  `amnesty_priv_level` int(11) DEFAULT NULL,
  `edr_type` varchar(64) NOT NULL,
  `expiration` datetime NOT NULL,
  `locked_timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `amnesty_urgency` int(11) DEFAULT NULL,
  `amnesty_importance` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `host_id` (`host_id`),
  KEY `tvui_hostlockdown_user_fk` (`user_id`),
  CONSTRAINT `tvui_hostlockdown_host_fk` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`),
  CONSTRAINT `tvui_hostlockdown_user_fk` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_insight_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `investigation_id` varchar(64) NOT NULL,
  `sensor_id` int(11) NOT NULL,
  `time_id` smallint(6) NOT NULL,
  `count` int(11) NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `investigation_id` (`investigation_id`,`sensor_id`,`time_id`),
  KEY `fk_sensor_id` (`sensor_id`),
  CONSTRAINT `fk_sensor_id` FOREIGN KEY (`sensor_id`) REFERENCES `tvui_cloudsensor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_internal_detection_target` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `detection_detail_id` int(11) NOT NULL,
  `source_host_session_luid` varchar(32) DEFAULT NULL,
  `target_host_session_luid` varchar(32) DEFAULT NULL,
  `source_account_uid` varchar(1024) DEFAULT NULL,
  `target_account_uid` varchar(1024) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_detection_detail_id` (`detection_detail_id`),
  KEY `source_host_session_luid_index` (`source_host_session_luid`),
  KEY `target_host_session_luid_index` (`target_host_session_luid`),
  KEY `source_account_uid_index` (`source_account_uid`(255)),
  KEY `target_account_uid_index` (`target_account_uid`(255)),
  CONSTRAINT `fk_detection_detail_id` FOREIGN KEY (`detection_detail_id`) REFERENCES `tvui_detection_detail` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_ipgroup` (
  `groupcollection_ptr_id` int(11) NOT NULL,
  `ips` longtext,
  PRIMARY KEY (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_ipgroup_groupcollection_ptr_fk` FOREIGN KEY (`groupcollection_ptr_id`) REFERENCES `tvui_groupcollection` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_kerberos_event` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL,
  `service_id` int(11) NOT NULL,
  `host_session_id` int(11) unsigned NOT NULL,
  `last_seen` datetime NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_kerberos_event_unique` (`account_id`,`service_id`,`host_session_id`),
  KEY `tvui_account_host_session_account_id` (`account_id`),
  KEY `tvui_account_host_session_host_session_id` (`host_session_id`),
  KEY `tvui_kerberos_event_service_id` (`service_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  CONSTRAINT `tvui_account_host_session_account_fk` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_account_host_session_host_session_fk` FOREIGN KEY (`host_session_id`) REFERENCES `tvui_host_session` (`id`),
  CONSTRAINT `tvui_kerberos_event_service_fk` FOREIGN KEY (`service_id`) REFERENCES `tvui_service` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `tvui_latest_entity_context` AS SELECT
 1 AS `id`,
  1 AS `canonical_id`,
  1 AS `canonical_name`,
  1 AS `datasource_id`,
  1 AS `context_type`,
  1 AS `valid_from`,
  1 AS `account_id`,
  1 AS `linked_account_id` */;
SET character_set_client = @saved_cs_client;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `tvui_latest_entity_context_id` AS SELECT
 1 AS `id` */;
SET character_set_client = @saved_cs_client;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `tvui_latest_entity_context_time_max` AS SELECT
 1 AS `canonical_id`,
  1 AS `max_date` */;
SET character_set_client = @saved_cs_client;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_ldap_context` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ad_profile_id` varchar(128) NOT NULL,
  `operating_system` varchar(128) DEFAULT NULL,
  `servicePrincipalName` varchar(128) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ad_context` longtext NOT NULL,
  `object_class` varchar(1024) DEFAULT NULL,
  `distinguished_name` varchar(1024) DEFAULT NULL,
  `display_name` varchar(1024) DEFAULT NULL,
  `user_principal_name` varchar(1024) DEFAULT NULL,
  `common_name` varchar(1024) DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `host_id` int(11) DEFAULT NULL,
  `state` varchar(1024) DEFAULT NULL,
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ldap_account_id_fk` (`account_id`),
  KEY `ldap_host_id_fk` (`host_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  CONSTRAINT `ldap_account_id_fk` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE CASCADE,
  CONSTRAINT `ldap_host_id_fk` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_ldapprofile` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `bind_dn` varchar(255) NOT NULL,
  `bind_pwd` varchar(255) NOT NULL,
  `base_dn_0` varchar(255) DEFAULT NULL,
  `base_dn_1` varchar(255) DEFAULT NULL,
  `base_dn_2` varchar(255) DEFAULT NULL,
  `base_dn_3` varchar(255) DEFAULT NULL,
  `base_dn_4` varchar(255) DEFAULT NULL,
  `base_dn_5` varchar(255) DEFAULT NULL,
  `base_dn_6` varchar(255) DEFAULT NULL,
  `base_dn_7` varchar(255) DEFAULT NULL,
  `base_dn_8` varchar(255) DEFAULT NULL,
  `base_dn_9` varchar(255) DEFAULT NULL,
  `server_uri_0` varchar(255) DEFAULT NULL,
  `server_uri_1` varchar(255) DEFAULT NULL,
  `server_uri_2` varchar(255) DEFAULT NULL,
  `search_param` varchar(255) NOT NULL,
  `connection_timeout` smallint(5) unsigned NOT NULL,
  `query_timeout` smallint(5) unsigned NOT NULL,
  `starttls` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_licensestat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_count` int(10) unsigned DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `stat_type` varchar(255) DEFAULT NULL,
  `stat_key` varchar(255) DEFAULT NULL,
  `stat_value` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_limitedtimelink` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `link_id` varchar(6) NOT NULL,
  `model_type_id` int(11) NOT NULL,
  `type_id` int(11) NOT NULL,
  `secret` varchar(32) NOT NULL,
  `expiration_date` datetime NOT NULL,
  `creator_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_limitedtimelink_user_id` (`user_id`),
  UNIQUE KEY `tvui_limitedtimelink_link_id` (`link_id`),
  UNIQUE KEY `unique_creator_model_type_id` (`model_type_id`,`type_id`,`creator_id`),
  CONSTRAINT `content_type_id_refs_id` FOREIGN KEY (`model_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `user_id_refs_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_linked_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `display_uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_uid_md5` char(32) AS (MD5(display_uid)) PERSISTENT,
  `c_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `t_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `attack_rating` smallint(5) unsigned NOT NULL DEFAULT '0',
  `velocity_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `breadth_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `is_prioritized` tinyint(1) NOT NULL,
  `entity_importance` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_reason` varchar(255) DEFAULT NULL,
  `archetype` varchar(255) DEFAULT NULL,
  `alarmed_datetime` datetime DEFAULT NULL,
  `alarmed_reason` enum('account','detection') DEFAULT NULL,
  `alarmed_state` enum('alarmed') DEFAULT NULL,
  `last_alarmed_check` datetime DEFAULT NULL,
  `last_alert` datetime DEFAULT NULL,
  `state` enum('active','inactive') CHARACTER SET utf8 NOT NULL DEFAULT 'active',
  `pure_uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `display_uid_md5` (`display_uid_md5`),
  KEY `display_uid_index` (`display_uid`(191)),
  KEY `linked_account_pure_uid_idx` (`pure_uid`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_linked_account FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'linkedaccount', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_linked_account FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'linkedaccount', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER DELETE on tvui_linked_account FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(OLD.id, 'linkedaccount', 'delete', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_linkedaccountscorehistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `linked_account_id` bigint(20) DEFAULT NULL,
  `c_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `t_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `score_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `score_decrease` tinyint(1) DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `attack_rating` smallint(5) unsigned NOT NULL DEFAULT '0',
  `velocity_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `breadth_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `is_prioritized` tinyint(1) NOT NULL,
  `entity_importance` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_reason` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_score_linked_account_id` (`linked_account_id`),
  KEY `tvui_linkedaccountscorehistory_score_date_idx` (`score_date`),
  CONSTRAINT `fk_score_linked_account_id` FOREIGN KEY (`linked_account_id`) REFERENCES `tvui_linked_account` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_network_sensor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alias` varchar(128) DEFAULT NULL,
  `product_name` varchar(128) NOT NULL,
  `mode` enum('sensor','stream','') NOT NULL DEFAULT '',
  `luid` varchar(32) DEFAULT NULL,
  `serial_number` varchar(128) DEFAULT NULL,
  `ip_address` varchar(128) DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `status` enum('available','pairing','paired','unpairing') DEFAULT NULL,
  `details` longtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_networks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `network_addr` char(39) NOT NULL,
  `host_count` int(10) unsigned DEFAULT NULL,
  `first_timestamp` datetime DEFAULT NULL,
  `last_timestamp` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_networks_1e907c06` (`network_addr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_notes` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('host','detection','campaign','account','linked_account','assignment') DEFAULT NULL,
  `type_id` int(11) unsigned NOT NULL,
  `note` longtext,
  `date_created` datetime NOT NULL,
  `date_modified` datetime DEFAULT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `modified_by_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by_fk_user_id` (`created_by_id`),
  KEY `modified_by_fk_user_id` (`modified_by_id`),
  KEY `tvui_notes_type_type_id` (`type`,`type_id`),
  CONSTRAINT `created_by_fk_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL,
  CONSTRAINT `modified_by_fk_user_id` FOREIGN KEY (`modified_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_notificationevent` (
  `id` varchar(64) NOT NULL,
  `date_created` datetime(3) NOT NULL,
  `data` longtext NOT NULL,
  `event_type_id` varchar(36) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_notificationeve_event_type_id_6a98175e_fk_tvui_noti` (`event_type_id`),
  CONSTRAINT `tvui_notificationeve_event_type_id_6a98175e_fk_tvui_noti` FOREIGN KEY (`event_type_id`) REFERENCES `tvui_notificationeventtype` (`codename`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_notificationeventdelivery` (
  `id` varchar(64) NOT NULL,
  `date_created` datetime(3) NOT NULL,
  `date_delivered` datetime(3) DEFAULT NULL,
  `success` tinyint(1) DEFAULT NULL,
  `meta` longtext,
  `event_id` varchar(64) NOT NULL,
  `event_receiver_id` varchar(64) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_notificationdel_event_id_7d67c785_fk_tvui_noti` (`event_id`),
  KEY `tvui_notificationdel_event_receiver_id_19574db0_fk_tvui_noti` (`event_receiver_id`),
  CONSTRAINT `tvui_notificationdel_event_id_7d67c785_fk_tvui_noti` FOREIGN KEY (`event_id`) REFERENCES `tvui_notificationevent` (`id`),
  CONSTRAINT `tvui_notificationdel_event_receiver_id_19574db0_fk_tvui_noti` FOREIGN KEY (`event_receiver_id`) REFERENCES `tvui_notificationeventreceiver` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_notificationeventreceiver` (
  `id` varchar(64) NOT NULL,
  `date_created` datetime(3) NOT NULL,
  `title` varchar(255) NOT NULL,
  `enabled` tinyint(1) NOT NULL,
  `config` longtext NOT NULL,
  `created_by_id` int(11) DEFAULT NULL,
  `receiver_type_id` varchar(36) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_notificationeve_created_by_id_62b3251c_fk_auth_user` (`created_by_id`),
  KEY `tvui_notificationeve_receiver_type_id_2d3669a3_fk_tvui_noti` (`receiver_type_id`),
  CONSTRAINT `tvui_notificationeve_created_by_id_62b3251c_fk_auth_user` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`),
  CONSTRAINT `tvui_notificationeve_receiver_type_id_2d3669a3_fk_tvui_noti` FOREIGN KEY (`receiver_type_id`) REFERENCES `tvui_notificationeventreceivertype` (`codename`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_notificationeventreceivertype` (
  `codename` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  PRIMARY KEY (`codename`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_notificationeventtype` (
  `codename` varchar(36) NOT NULL,
  `title` varchar(255) NOT NULL,
  PRIMARY KEY (`codename`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_ntc_migration_state_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `operation` enum('migrate','rollback') NOT NULL,
  `operation_state` enum('in_progress','success','failed') NOT NULL,
  `s3_event_id` varchar(255) NOT NULL,
  `attempt_id` varchar(36) NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `report` longtext,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_packet_capture` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 NOT NULL,
  `sensor_luid` varchar(8) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `scheduling` datetime DEFAULT NULL,
  `max_size_in_bytes` int(11) DEFAULT NULL,
  `max_duration_in_seconds` int(11) DEFAULT NULL,
  `ip_subnet1` char(39) CHARACTER SET utf8 DEFAULT NULL,
  `ip_subnet2` char(39) CHARACTER SET utf8 DEFAULT NULL,
  `port` smallint(5) unsigned DEFAULT NULL,
  `protocol` smallint(5) unsigned DEFAULT NULL,
  `status` enum('stopped','starting','scheduled','transferring','in-progress','cancelling','successful','error','sensor-conflict-error') DEFAULT 'starting',
  `after_decapsulation` tinyint(1) NOT NULL DEFAULT '0',
  `max_packet_size_in_bytes` bigint(20) DEFAULT NULL,
  `packets` bigint(20) DEFAULT NULL,
  `packets_dropped` bigint(20) DEFAULT NULL,
  `total_bytes_captured` bigint(20) DEFAULT NULL,
  `total_packets_captured` bigint(20) DEFAULT NULL,
  `capture_start` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by_id` int(11) DEFAULT NULL,
  `download_attempts` tinyint(4) NOT NULL DEFAULT '0',
  `dr_upload_batch_id` varchar(36) DEFAULT NULL,
  `dr_upload_batch_status` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_pending_host_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_luid` varchar(8) COLLATE utf8_bin NOT NULL,
  `host_luid` varchar(8) COLLATE utf8_bin NOT NULL,
  `sensor_luid` varchar(8) COLLATE utf8_bin DEFAULT NULL,
  `ip_address` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `status` enum('pending','resolved') COLLATE utf8_bin NOT NULL DEFAULT 'pending',
  `has_detections` tinyint(1) DEFAULT '0',
  `start` datetime NOT NULL,
  `end` datetime DEFAULT NULL,
  `replication_version` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `entity_batch_uid` varchar(128) COLLATE utf8_bin DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) COLLATE utf8_bin DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_luid` (`session_luid`),
  KEY `tvui_pending_host_session_host_lookup` (`host_luid`,`status`),
  KEY `tvui_pending_host_session_session_lookup` (`session_luid`,`status`,`replication_version`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_prioritizationtimings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `obj_type` enum('host','linked_account') NOT NULL,
  `entity_id` int(11) NOT NULL,
  `duration_seconds` int(11) NOT NULL,
  `time_first_detected` datetime NOT NULL,
  `time_prioritized` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_privhistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `host_id` int(11) DEFAULT NULL,
  `service_id` int(11) DEFAULT NULL,
  `priv_level` int(11) NOT NULL,
  `priv_level_date` datetime DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` enum('account','service','host') NOT NULL,
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_priv_host_id` (`host_id`),
  KEY `fk_priv_service_id` (`service_id`),
  KEY `fk_priv_account_id` (`account_id`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`),
  CONSTRAINT `fk_priv_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_priv_host_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_priv_service_id` FOREIGN KEY (`service_id`) REFERENCES `tvui_service` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(16) NOT NULL,
  `report_file` varchar(64) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `pcap_file` varchar(64) DEFAULT NULL,
  `host_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_report_27f00f5d` (`host_id`),
  CONSTRAINT `tvui_report_host` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_report_def` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `frequency` enum('Daily','Weekly','Monthly','On_demand') NOT NULL DEFAULT 'Daily',
  `delivery_type` enum('email') NOT NULL DEFAULT 'email',
  `email_addresses` text,
  `created_by_id` int(11) DEFAULT NULL,
  `source` varchar(128) NOT NULL DEFAULT 'all',
  `content` set('hosts','detections') NOT NULL,
  `entity_t` smallint(5) unsigned NOT NULL DEFAULT '0',
  `entity_c` smallint(5) unsigned NOT NULL DEFAULT '0',
  `host_type` enum('all','key_assets','servers') DEFAULT NULL,
  `entity_tags` longtext,
  `detection_t` smallint(5) unsigned NOT NULL DEFAULT '0',
  `detection_c` smallint(5) unsigned NOT NULL DEFAULT '0',
  `detection_types` longtext,
  `detection_tags` longtext,
  `layout` enum('compact','full') NOT NULL DEFAULT 'compact',
  `file_format` enum('PDF','JSON') NOT NULL DEFAULT 'PDF',
  `created_timestamp` datetime DEFAULT NULL,
  `lastsent_timestamp` datetime DEFAULT NULL,
  `campaign` tinyint(1) DEFAULT '0',
  `custom_model_ids` longtext,
  `from_date` datetime DEFAULT NULL,
  `to_date` datetime DEFAULT NULL,
  `report_type` enum('host_severity','account_severity','entity_severity') NOT NULL DEFAULT 'host_severity',
  `account_type` enum('all','kerberos','o365') DEFAULT NULL,
  `send_report_type` enum('1','2') NOT NULL DEFAULT '1',
  `threshold_score` int(11) DEFAULT NULL,
  `connectors` longtext,
  `impact_types` longtext,
  `group_types` longtext,
  PRIMARY KEY (`id`),
  KEY `report_def_created_by_fk_user_id` (`created_by_id`),
  CONSTRAINT `report_def_created_by_fk_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(32) NOT NULL,
  `type_vname` varchar(32) DEFAULT NULL,
  `action` varchar(32) NOT NULL,
  `host_id` int(11) DEFAULT NULL,
  `app_id` varchar(32) DEFAULT NULL,
  `ignore_for_all` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_rule_27f00f5d` (`host_id`),
  KEY `tvui_rule_e25b796d` (`app_id`),
  CONSTRAINT `host_id_refs_id_4fa38909` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_saaslocaluserprofile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(191) NOT NULL,
  `email_verified` tinyint(1) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_saassamlprofile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internal_id` varchar(36) NOT NULL,
  `name` varchar(255) NOT NULL,
  `metadata_url` varchar(1024) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `metadata_xml` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `internal_id` (`internal_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_samlprofile` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 NOT NULL,
  `metadata` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_schema_migration` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_id` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_score` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `threat_score` smallint(5) unsigned NOT NULL,
  `confidence_score` smallint(5) unsigned NOT NULL,
  `dfz_score` int(11) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `end_timestamp` datetime DEFAULT NULL,
  `host_id` int(11) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `detection_profile` varchar(32) DEFAULT NULL,
  `attack_rating` smallint(5) unsigned NOT NULL DEFAULT '0',
  `velocity_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `breadth_contrib` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_score` smallint(5) unsigned NOT NULL DEFAULT '0',
  `is_prioritized` tinyint(1) NOT NULL,
  `entity_importance` smallint(5) unsigned NOT NULL DEFAULT '0',
  `urgency_reason` varchar(255) DEFAULT NULL,
  `score_decrease` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `tvui_score_27f00f5d` (`host_id`),
  KEY `timestamp_index` (`timestamp`),
  KEY `end_timestamp_index` (`end_timestamp`),
  CONSTRAINT `tvui_score_host` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_script_state` (
  `id` varchar(60) NOT NULL,
  `box` longtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_searchautocompletehistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `search_type` enum('workbench','all_campaigns','all_detections','all_hosts','all_accounts','account','detection','host','campaign','campaign_detections','campaign_hosts','priority_entities') CHARACTER SET utf8 NOT NULL,
  `query` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `first_timestamp` datetime DEFAULT NULL,
  `last_timestamp` datetime DEFAULT NULL,
  `count` int(8) DEFAULT '0',
  `avg_time` decimal(8,2) NOT NULL DEFAULT '0.00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_autocomplete_query_key` (`search_type`,`query`,`user_id`),
  KEY `query_term_key` (`query`),
  KEY `search_autocomplete_user_id` (`user_id`),
  CONSTRAINT `search_autocomplete_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_searchqueryhistory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `search_type` enum('workbench','all_campaigns','all_detections','all_hosts','all_accounts','account','detection','host','campaign','campaign_detections','campaign_hosts','priority_entities') CHARACTER SET utf8 NOT NULL,
  `query` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `query_hash` varchar(64) CHARACTER SET utf8 DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `first_timestamp` datetime DEFAULT NULL,
  `last_timestamp` datetime DEFAULT NULL,
  `count` int(8) DEFAULT '0',
  `avg_query_time` decimal(8,2) NOT NULL DEFAULT '0.00',
  `removed` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_search_query_key` (`search_type`,`query_hash`,`user_id`),
  KEY `query_term_key` (`query`),
  KEY `query_hash_key` (`query_hash`),
  KEY `user_id_key` (`user_id`),
  KEY `last_timestamp_key` (`last_timestamp`),
  CONSTRAINT `search_query_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_searchqueryhistorycleared` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `cleared_datetime` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `searchqueryhistory_cleared_user_id` (`user_id`),
  CONSTRAINT `searchqueryhistory_cleared_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_service` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `uid_md5` varchar(128) AS (MD5(uid)) PERSISTENT,
  `priv_level` int(4) unsigned DEFAULT NULL,
  `priv_level_date` datetime DEFAULT NULL,
  `ad_type` enum('kerberos') NOT NULL,
  `first_seen` datetime DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `state` enum('active','inactive') NOT NULL DEFAULT 'active',
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid_md5` (`uid_md5`),
  UNIQUE KEY `uid_md5_2` (`uid_md5`),
  KEY `tvui_service_uid` (`uid`(191)),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_setting` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group` varchar(32) NOT NULL,
  `key` varchar(100) NOT NULL,
  `value` mediumtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_group_key` (`group`,`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_shell_knocker_port` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host_session_luid` char(8) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `port` smallint(5) unsigned NOT NULL,
  `couch_note_id` char(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_shell_knocker_session_port` (`host_session_luid`,`port`),
  CONSTRAINT `fk_shell_knocker_session_luid` FOREIGN KEY (`host_session_luid`) REFERENCES `tvui_host_session` (`session_luid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_shell_knocker_port FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'ShellKnockerPort', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_shell_knocker_port FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'ShellKnockerPort', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_category` varchar(96) DEFAULT NULL,
  `description` longtext,
  `type` varchar(64) NOT NULL,
  `type_vname` varchar(64) DEFAULT NULL,
  `category` varchar(32) DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `version` smallint(5) unsigned DEFAULT NULL,
  `template_reference_id` int(11) DEFAULT NULL,
  `last_timestamp` datetime DEFAULT NULL,
  `created_timestamp` datetime NOT NULL,
  `is_whitelist` tinyint(1) NOT NULL,
  `priority` int(11) DEFAULT NULL,
  `family` enum('CUSTOMER','ALGORITHM','ALGORITHM_CUSTOMER','TEMPLATE','TEMPLATE_CUSTOMER','AI','DISTILLATION') NOT NULL DEFAULT 'CUSTOMER',
  `context` longtext,
  `internal_template_name` varchar(64) DEFAULT NULL,
  `last_updated_timestamp` datetime DEFAULT NULL,
  `conditions` longtext,
  `user_id` int(11) DEFAULT NULL,
  `ai_job_identifier` varchar(128) DEFAULT NULL,
  `ai_context` longtext,
  `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ai_marked_for_expire` tinyint(1) DEFAULT '0',
  `distillation_algorithm` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `internal_template_name_and_version` (`internal_template_name`,`version`),
  UNIQUE KEY `unique_distillation_smart_rule` (`type`,`distillation_algorithm`),
  KEY `smart_rule_version_template_refs_id` (`template_reference_id`),
  KEY `tvui_smart_rule_user_fk` (`user_id`),
  KEY `smart_rule_ai_job_identifier` (`ai_job_identifier`),
  CONSTRAINT `smart_rule_version_template_refs_id` FOREIGN KEY (`template_reference_id`) REFERENCES `tvui_smart_rule` (`id`),
  CONSTRAINT `tvui_smart_rule_user_fk` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_account_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `accountgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_account_gro_smart_rule_accountgroup_uniq` (`smart_rule_id`,`accountgroup_id`),
  KEY `tvui_smart_rule_account_accountgroup_fk_tvui_account` (`accountgroup_id`),
  CONSTRAINT `tvui_smart_rule_account_accountgroup_fk_tvui_account` FOREIGN KEY (`accountgroup_id`) REFERENCES `tvui_accountgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_account_smart_rule_fk_tvui_smart_rule` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `smart_rule_id` (`smart_rule_id`,`account_id`),
  KEY `smart_rule_accounts_account_id` (`account_id`),
  CONSTRAINT `smart_rule_accounts_account_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `smart_rule_accounts_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_ai_target` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `target_identifier` varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiry` datetime DEFAULT NULL,
  `original_conditions` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tvui_smart_rule_ai_target_smart_rule_id` (`smart_rule_id`),
  CONSTRAINT `tvui_smart_rule_ai_target_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_host_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `hostgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_host_gro_smart_rule_hostgroup_uniq` (`smart_rule_id`,`hostgroup_id`),
  KEY `tvui_smart_rule_host_hostgroup_fk_tvui_host` (`hostgroup_id`),
  CONSTRAINT `tvui_smart_rule_host_hostgroup_fk_tvui_host` FOREIGN KEY (`hostgroup_id`) REFERENCES `tvui_hostgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_host_smart_rule_fk_tvui_smart_rule` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_hosts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `host_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `smart_rule_id` (`smart_rule_id`,`host_id`),
  KEY `tvui_smart_rule_hosts_b9130ab0` (`smart_rule_id`),
  KEY `tvui_smart_rule_hosts_27f00f5d` (`host_id`),
  CONSTRAINT `host_id_refs_id_f55f692f` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`),
  CONSTRAINT `smart_rule_id_refs_id_bd0f3987` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_ip_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `ipgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_ipgroup__smart_rule_id_uniq` (`smart_rule_id`,`ipgroup_id`),
  KEY `tvui_smart_rule_ipgroup_id` (`ipgroup_id`),
  CONSTRAINT `tvui_smart_rule_ipgroup_id` FOREIGN KEY (`ipgroup_id`) REFERENCES `tvui_ipgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_ipgroup_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote1_account_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `accountgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote1_account__smart_rule_id_uniq` (`smart_rule_id`,`accountgroup_id`),
  KEY `tvui_smart_rule_remote1_account_id` (`accountgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote1_account_id` FOREIGN KEY (`accountgroup_id`) REFERENCES `tvui_accountgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote1_account_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote1_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote1_accounts__smart_rule_id_uniq` (`smart_rule_id`,`account_id`),
  KEY `tvui_smart_rule_remote1_accounts_id` (`account_id`),
  CONSTRAINT `tvui_smart_rule_remote1_accounts_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_smart_rule_remote1_accounts_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote1_dns_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `externaldomaingroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote1__smart_rule_id_externaldo_542038d0_uniq` (`smart_rule_id`,`externaldomaingroup_id`),
  KEY `tvui_smart_rule_remo_externaldomaingroup__e12ce290_fk_tvui_exte` (`externaldomaingroup_id`),
  CONSTRAINT `tvui_smart_rule_remo_externaldomaingroup__e12ce290_fk_tvui_exte` FOREIGN KEY (`externaldomaingroup_id`) REFERENCES `tvui_externaldomaingroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remo_smart_rule_id_a9587b8e_fk_tvui_smar` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote1_host_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `hostgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote1_host__smart_rule_id_uniq` (`smart_rule_id`,`hostgroup_id`),
  KEY `tvui_smart_rule_remote1_host_id` (`hostgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote1_host_id` FOREIGN KEY (`hostgroup_id`) REFERENCES `tvui_hostgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote1_host_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote1_hosts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `host_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote1_hosts__smart_rule_id_uniq` (`smart_rule_id`,`host_id`),
  KEY `tvui_smart_rule_remote1_hosts_id` (`host_id`),
  CONSTRAINT `tvui_smart_rule_remote1_hosts_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`),
  CONSTRAINT `tvui_smart_rule_remote1_hosts_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote1_ip_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `ipgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote1_ip__smart_rule_id_uniq` (`smart_rule_id`,`ipgroup_id`),
  KEY `tvui_smart_rule_remote1_ip_id` (`ipgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote1_ip_id` FOREIGN KEY (`ipgroup_id`) REFERENCES `tvui_ipgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote1_ip_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote2_account_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `accountgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote2_account__smart_rule_id_uniq` (`smart_rule_id`,`accountgroup_id`),
  KEY `tvui_smart_rule_remote2_account_id` (`accountgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote2_account_id` FOREIGN KEY (`accountgroup_id`) REFERENCES `tvui_accountgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote2_account_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote2_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote2_accounts__smart_rule_id_uniq` (`smart_rule_id`,`account_id`),
  KEY `tvui_smart_rule_remote2_accounts_id` (`account_id`),
  CONSTRAINT `tvui_smart_rule_remote2_accounts_id` FOREIGN KEY (`account_id`) REFERENCES `tvui_account` (`id`),
  CONSTRAINT `tvui_smart_rule_remote2_accounts_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote2_dns_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `externaldomaingroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote2__smart_rule_id_externaldo_4cafa5c5_uniq` (`smart_rule_id`,`externaldomaingroup_id`),
  KEY `tvui_smart_rule_remo_externaldomaingroup__70bdae2e_fk_tvui_exte` (`externaldomaingroup_id`),
  CONSTRAINT `tvui_smart_rule_remo_externaldomaingroup__70bdae2e_fk_tvui_exte` FOREIGN KEY (`externaldomaingroup_id`) REFERENCES `tvui_externaldomaingroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remo_smart_rule_id_823f9c81_fk_tvui_smar` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote2_host_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `hostgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote2_host__smart_rule_id_uniq` (`smart_rule_id`,`hostgroup_id`),
  KEY `tvui_smart_rule_remote2_host_id` (`hostgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote2_host_id` FOREIGN KEY (`hostgroup_id`) REFERENCES `tvui_hostgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote2_host_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote2_hosts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `host_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote2_hosts__smart_rule_id_uniq` (`smart_rule_id`,`host_id`),
  KEY `tvui_smart_rule_remote2_hosts_id` (`host_id`),
  CONSTRAINT `tvui_smart_rule_remote2_hosts_id` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`),
  CONSTRAINT `tvui_smart_rule_remote2_hosts_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote2_ip_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `ipgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote2_ip__smart_rule_id_uniq` (`smart_rule_id`,`ipgroup_id`),
  KEY `tvui_smart_rule_remote2_ip_id` (`ipgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote2_ip_id` FOREIGN KEY (`ipgroup_id`) REFERENCES `tvui_ipgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote2_ip_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_smart_rule_remote3_ip_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `smart_rule_id` int(11) NOT NULL,
  `ipgroup_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tvui_smart_rule_remote3_ip__smart_rule_id_uniq` (`smart_rule_id`,`ipgroup_id`),
  KEY `tvui_smart_rule_remote3_ip_id` (`ipgroup_id`),
  CONSTRAINT `tvui_smart_rule_remote3_ip_id` FOREIGN KEY (`ipgroup_id`) REFERENCES `tvui_ipgroup` (`groupcollection_ptr_id`),
  CONSTRAINT `tvui_smart_rule_remote3_ip_smart_rule_id` FOREIGN KEY (`smart_rule_id`) REFERENCES `tvui_smart_rule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_statistic` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timestamp` datetime NOT NULL,
  `name` varchar(128) NOT NULL,
  `value` longtext NOT NULL,
  `entity_batch_uid` varchar(128) DEFAULT NULL,
  `entity_batch_index` int(11) DEFAULT NULL,
  `entity_batch_uid_md5` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `name` (`name`),
  KEY `entity_batch` (`entity_batch_uid`,`entity_batch_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_syslogqueue` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `host_session_id` int(11) unsigned NOT NULL,
  `program` longtext NOT NULL,
  `priority` int(11) NOT NULL,
  `syslog_msg` longtext NOT NULL,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_host_id` (`host_id`),
  KEY `fk_hs_id_syslog_queue` (`host_session_id`),
  CONSTRAINT `fk_host_id_syslog_queue` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_hs_id_syslog_queue` FOREIGN KEY (`host_session_id`) REFERENCES `tvui_host_session` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_threatfeedupload` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `replace_filename` varchar(100) DEFAULT NULL,
  `filename` varchar(100) DEFAULT NULL,
  `couch_id` varchar(40) DEFAULT NULL,
  `filename_old` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `couch_id` (`couch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_ui_notification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) DEFAULT NULL,
  `notification_type` varchar(256) DEFAULT NULL,
  `notification_data` longtext,
  `date_created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `date_last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(64) DEFAULT NULL,
  `num_occurrences` int(11) DEFAULT NULL,
  `log_level` enum('info','warning','critical','error') DEFAULT NULL,
  `silenced` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_user_setting` (
  `user_id` int(11) NOT NULL,
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(32) NOT NULL,
  `setting` longtext,
  PRIMARY KEY (`id`),
  KEY `fk_user` (`user_id`),
  CONSTRAINT `fk_user` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_user_usage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(128) NOT NULL,
  `data` longtext NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_usersaassamlprofile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `federated_user_id` varchar(191) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `saas_saml_profile_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_id_per_provider` (`saas_saml_profile_id`,`federated_user_id`),
  KEY `fk_user_id` (`user_id`),
  CONSTRAINT `fk_saas_saml_profile_id` FOREIGN KEY (`saas_saml_profile_id`) REFERENCES `tvui_saassamlprofile` (`id`),
  CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_usersession` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `session_id` varchar(40) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_user_session` (`user_id`),
  KEY `fk_session` (`session_id`),
  CONSTRAINT `fk_session` FOREIGN KEY (`session_id`) REFERENCES `django_session` (`session_key`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_session` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_vsa_dashboard` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timerange_option` enum('24h','72h','7d') DEFAULT NULL,
  `query_name` varchar(255) DEFAULT NULL,
  `request_id` varchar(36) DEFAULT NULL,
  `date_from` datetime DEFAULT NULL,
  `date_to` datetime DEFAULT NULL,
  `status` enum('successful','error') DEFAULT NULL,
  `error_message` varchar(255) DEFAULT NULL,
  `last_update_start_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  UNIQUE KEY `unique_query_timerange` (`query_name`,`timerange_option`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_vsi_hyperv` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_vsi_kvm` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_vsi_nutanix` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_vsi_vmware` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_watchmennetworkxedge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `src_host_id` int(11) NOT NULL,
  `dst_host_id` int(11) NOT NULL,
  `first_seen` bigint(20) NOT NULL,
  `last_seen` bigint(20) NOT NULL,
  `protocol` varchar(16) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_edge` (`src_host_id`,`dst_host_id`,`protocol`),
  KEY `fk_dst_host_id_x` (`dst_host_id`),
  CONSTRAINT `fk_dst_host_id_x` FOREIGN KEY (`dst_host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_src_host_id_x` FOREIGN KEY (`src_host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_watchmennetworkxedge FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'watchmennetworkxedge', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_watchmennetworkxedge FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'watchmennetworkxedge', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tvui_watchmennetworkxscanner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `host_id` int(11) NOT NULL,
  `last_seen` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_host_id_watchmenscanner` (`host_id`),
  CONSTRAINT `fk_host_id_watchmenscanner` FOREIGN KEY (`host_id`) REFERENCES `tvui_host` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER INSERT ON tvui_watchmennetworkxscanner FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'watchmennetworkxscanner', 'insert', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8 */ ;
/*!50003 SET character_set_results = utf8 */ ;
/*!50003 SET collation_connection  = utf8_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER= AFTER UPDATE on tvui_watchmennetworkxscanner FOR EACH ROW BEGIN
  CALL add_object_to_be_replicated(NEW.id, 'watchmennetworkxscanner', 'update', TRUE);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `updater_offlineupgrade` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `filename` varchar(64) NOT NULL,
  `file_location` varchar(128) DEFAULT NULL,
  `version` varchar(32) DEFAULT NULL,
  `status` enum('uploading','upload_failed','upload_completed','verified','verify_failed','installing','install_completed','install_failed','canceled') DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_id` int(11) DEFAULT NULL,
  `min_version` varchar(32) DEFAULT NULL,
  `file_hash` varchar(100) DEFAULT NULL,
  `update_type` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_offline_upgrade_user` (`user_id`),
  CONSTRAINT `fk_offline_upgrade_user` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50001 DROP VIEW IF EXISTS `tvui_latest_entity_context`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER= SQL SECURITY DEFINER */
/*!50001 VIEW `tvui_latest_entity_context` AS select `tec`.`id` AS `id`,`tec`.`canonical_id` AS `canonical_id`,`tec`.`canonical_name` AS `canonical_name`,`tec`.`datasource_id` AS `datasource_id`,`tec`.`context_type` AS `context_type`,`tec`.`valid_from` AS `valid_from`,`tec`.`account_id` AS `account_id`,`ta`.`linked_account_id` AS `linked_account_id` from ((`tvui_entity_context` `tec` join `tvui_latest_entity_context_id` `tleci` on((`tleci`.`id` = `tec`.`id`))) left join `tvui_account` `ta` on((`ta`.`id` = `tec`.`account_id`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!50001 DROP VIEW IF EXISTS `tvui_latest_entity_context_id`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER= SQL SECURITY DEFINER */
/*!50001 VIEW `tvui_latest_entity_context_id` AS select min(`tec`.`id`) AS `id` from (`tvui_entity_context` `tec` join `tvui_latest_entity_context_time_max` `tlectm` on(((`tec`.`canonical_id` = `tlectm`.`canonical_id`) and (`tec`.`valid_from` = `tlectm`.`max_date`)))) group by `tec`.`canonical_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!50001 DROP VIEW IF EXISTS `tvui_latest_entity_context_time_max`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER= SQL SECURITY DEFINER */
/*!50001 VIEW `tvui_latest_entity_context_time_max` AS select `tvui_entity_context`.`canonical_id` AS `canonical_id`,max(`tvui_entity_context`.`valid_from`) AS `max_date` from `tvui_entity_context` where (`tvui_entity_context`.`is_tombstone` = 0) group by `tvui_entity_context`.`canonical_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
