from typing import List, Union
from django.core.exceptions import ObjectDoesNotExist
from base_tvui.feature_flipper import flag_enabled, Flags
from django.utils import timezone

from tvui.models import CloseHistory, detection, User, host, LinkedAccount

from pure_utils import log_utils

LOG = log_utils.get_vectra_logger(__name__)


class NoEntityProvided(Exception):
    pass


class CloseFacilitator:
    """
    Core functionality for adding records to close history table.
    """

    QUX_IS_PRIORITIZED_THRESHOLD = 50

    def _is_prioritized(self, entity_obj):
        """
        Determines whether the given entity object is prioritized.
        For RUX we go based on the flag. Default to QUX otherwise.

        Args:
            entity_obj: The entity object to be evaluated.

        Returns:
            bool: True if the entity is prioritized, False otherwise.
        """

        if flag_enabled(Flags.cloud_only):
            return entity_obj.is_prioritized

        return entity_obj.t_score > self.QUX_IS_PRIORITIZED_THRESHOLD

    def close_detections(
        self,
        detection_objs: List[detection],
        user: User,
        reason: str,
        entity_obj: Union[host, LinkedAccount] = None,
        entity_type: str = None,
    ):
        """
        Method to add detection records to close history table
        """
        try:
            closed_on = timezone.now()

            for detection_obj in detection_objs:
                record = {'closed_on': closed_on, 'reason': reason, 'user': user, 'detection': detection_obj}
                if detection_obj.assignment is not None:
                    record['assignment_timestamp'] = detection_obj.assignment.date_assigned

                if entity_obj is not None:
                    record[entity_type] = entity_obj

                if det_entity := getattr(detection_obj, 'host') or getattr(detection_obj, 'account'):
                    if type(det_entity) == host:
                        record['host'] = det_entity
                    else:
                        record['linked_account'] = det_entity.linked_account

                    record['is_prioritized'] = self._is_prioritized(det_entity)

                CloseHistory.objects.create(**record)
        except ObjectDoesNotExist:
            raise ObjectDoesNotExist
        except Exception as error:
            LOG.exception(f'Unable to record detection closure {error}')
            raise

    def close_entity(self, user: User, reason: str, entity_obj: Union[host, LinkedAccount], entity_type: str):
        """
        Method to add entity records to close history table
        """
        try:
            closed_on = timezone.now()
            record = {'closed_on': closed_on, 'reason': reason, 'user': user}

            if entity_obj is None:
                raise NoEntityProvided()
            else:
                record[entity_type] = entity_obj
                if entity_obj.assignment is not None:
                    record['assignment_timestamp'] = entity_obj.assignment.date_assigned

                record['is_prioritized'] = self._is_prioritized(entity_obj)

            CloseHistory.objects.create(**record)

        except NoEntityProvided:
            LOG.exception('No entity provided to close')
            raise
        except Exception:
            LOG.exception('Unable to record detection closure')
            raise
