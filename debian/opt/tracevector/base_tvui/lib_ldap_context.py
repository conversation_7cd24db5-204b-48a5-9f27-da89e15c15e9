import json
from typing import Dict, List, Optional, Union
from functools import reduce
from operator import or_
from glom import glom

from pure_utils.log_utils import get_vectra_logger

from django.db.models import Q
from django.utils import timezone

from entity_receiver.entity_helpers import EntityIdentifier, EntityPersister, EntityTypes, LongS3EntityIdentifier, PublisherResult

from tvui.models import LdapContext, HostGroup, AccountGroup
from tvui.async_tasks.celery_tasks import run_task
from tvui.group_collections.ad_groups.ad_groups_tasks import evaluate_ad_account_groups, evaluate_ad_host_groups


LOG = get_vectra_logger(__name__)


class LDAPAction:
    """
    Actions that ldap_service track
    """

    MEMBER_ADD = 'MEMBER_ADD'
    MEMBER_DELETE = 'MEMBER_DELETE'
    MEMBEROF_ADD = 'MEMBEROF_ADD'
    MEMBEROF_DELETE = 'MEMBEROF_DELETE'
    GROUP_ADD = 'GROUP_ADD'
    GROUP_DELETE = 'GROUP_DELETE'
    USER_ADD = 'USER_ADD'
    USER_DELETE = 'USER_DELETE'
    HOST_ADD = 'HOST_ADD'
    HOST_DELETE = 'HOST_DELETE'
    FULL_UPDATE = 'FULL_UPDATE'


def process_ldap_context(entity_dataset: List[Dict], entity_identifiers: List[EntityIdentifier]) -> List[Dict]:
    """
    Process LDAP Context as a batch and handle the entity response

    args:
        entity_dataset: A list of entities to be processesed
        entity_identiifer: the identifier associated with the dataset, this should be one-to-on to entity_dataset
    returns:
        A list of results from processing the ldap context data
    """
    results = [{"success": False, "retry": True, "batch_uid": ei.batch_uid, "batch_index": ei.batch_index} for ei in entity_identifiers]
    update_ldap_contexts, new_ldap_contexts = [], []
    update_result_idx, new_result_idx = [], []

    # This is used to track async evaluations against these group types
    affected_host_ldap_context_ids, affected_acct_ldap_context_ids = [], []

    try:
        check_existing_profile_dn = [(Q(ad_profile_id=ec['ad_profile_id']) & Q(distinguished_name=ec['DN'])) for ec in entity_dataset]
        check_existing_profile_dn_q_set = reduce(or_, check_existing_profile_dn)
        existing_ldap_contexts = {
            (lc.ad_profile_id, lc.distinguished_name): lc for lc in LdapContext.objects.filter(check_existing_profile_dn_q_set)
        }
    except Exception:
        LOG.exception(f"Failed to create {len(new_ldap_contexts)} LDAP context")
        return results

    for idx, (ldap_context, entity_identifier) in enumerate(zip(entity_dataset, entity_identifiers)):
        ldap_context_key = (ldap_context['ad_profile_id'], ldap_context['DN'])
        existing_ldap_context = existing_ldap_contexts.get(ldap_context_key)
        ad_context = json.loads(ldap_context['context']) if isinstance(ldap_context['context'], str) else ldap_context['context']
        try:
            if existing_ldap_context:
                if ldap_context['event_action'] == LDAPAction.FULL_UPDATE:
                    # Non-Dirsync updates
                    existing_ldap_context.updated_at = timezone.now()
                    existing_ldap_context.ad_context = ad_context
                    existing_ldap_context.operating_system = ldap_context.get('operatingSystem')
                    existing_ldap_context.user_principal_name = ldap_context.get('userPrincipalName')
                else:
                    # Dirsync updates
                    dirsync_action_handler(existing_ldap_context, ldap_context['event_action'], ad_context)

                existing_ldap_context.entity_batch_uid = entity_identifier.batch_uid
                existing_ldap_context.entity_batch_index = entity_identifier.batch_index
                update_ldap_contexts.append(existing_ldap_context)
                update_result_idx.append(idx)
            elif ldap_context['event_action'] in (
                LDAPAction.FULL_UPDATE,
                LDAPAction.GROUP_ADD,
                LDAPAction.USER_ADD,
                LDAPAction.HOST_ADD,
            ):
                if ldap_context['event_action'] == LDAPAction.GROUP_ADD and not validate_group_add_context(ad_context):
                    # This is to handle group_add which emits a message but with missing context, ignore it for now and
                    # only process it if the context is present.
                    new_result_idx.append(idx)
                    continue
                new_ldap_contexts.append(
                    LdapContext(
                        ad_profile_id=ldap_context['ad_profile_id'],
                        distinguished_name=ldap_context.get('DN'),  # Defined outside
                        object_class=ldap_context['object_class'],  # Defined outside
                        ad_context=ad_context,
                        operating_system=ldap_context.get('operatingSystem'),  # Defined in context (optional)
                        display_name=ldap_context.get('displayName'),  # Defined in context (optional)
                        user_principal_name=ldap_context.get('userPrincipalName'),  # Defined in context (optional)
                        servicePrincipalName=ldap_context.get('servicePrincipalName'),  # Defined in context (optional)
                        entity_batch_uid=entity_identifier.batch_uid,
                        entity_batch_index=entity_identifier.batch_index,
                    )
                )
                new_result_idx.append(idx)
            else:
                # Dirsync updates which has no baseline updates
                LOG.info(f"LDAPAction {ldap_context['event_action']} has no baseline to update")
        except Exception:
            if existing_ldap_context:
                LOG.exception(f"Failed to update {existing_ldap_context} with ldap_context key {ldap_context_key}")
            else:
                LOG.exception(f"Failed to create ldap_context {ldap_context_key}")

    try:
        LOG.info(f"Bulk create {len(new_ldap_contexts)} LDAP context")
        LdapContext.objects.bulk_create(new_ldap_contexts)
        for nri in new_result_idx:
            results[nri]["success"] = True
            results[nri]["retry"] = False
    except Exception:
        LOG.exception(f"Failed to create {len(new_ldap_contexts)} LDAP context")

    try:
        LOG.info(f"Bulk update {len(update_ldap_contexts)} LDAP context")
        LdapContext.objects.bulk_update(
            update_ldap_contexts,
            fields=[
                "ad_context",
                "operating_system",
                "display_name",
                "user_principal_name",
                "entity_batch_uid",
                "entity_batch_index",
                "state",
            ],
        )
        for uri in update_result_idx:
            results[uri]["success"] = True
            results[uri]["retry"] = False
    except Exception:
        LOG.exception(f"Failed to update {len(update_ldap_contexts)} LDAP context")

    # TODO: Process AD Context to attach to Entities
    # _attribute_ad_context_to_hosts()
    # _attribute_ad_context_to_accounts()

    # Process Group AD Context
    latest_ldap_context_groups_after_commit = list(
        LdapContext.objects.filter(check_existing_profile_dn_q_set, object_class="group").values_list('id', flat=True)
    )
    host_group_ids = list(HostGroup.objects.filter(ldap_context__in=latest_ldap_context_groups_after_commit).values_list('id', flat=True))
    acct_group_ids = list(
        AccountGroup.objects.filter(ldap_context__in=latest_ldap_context_groups_after_commit).values_list('id', flat=True)
    )

    # Using GROUP_EDIT for now, this may need to be more specific to the event
    run_task(evaluate_ad_account_groups, host_group_ids, "GROUP_EDIT")
    run_task(evaluate_ad_host_groups, acct_group_ids, "GROUP_EDIT")

    return results


def validate_group_add_context(ad_context):
    """Ensure that GROUP_ADD ad context contains two fields [member, memberOf]"""
    if 'member' not in ad_context:
        return False
    if 'memberOf' not in ad_context:
        return False
    return True


def dirsync_action_handler(ldap_context: LdapContext, action: str, context: Union[Dict, List]):
    """
    Update the existing ldap_context with the proper action

    args:
        ldap_context: An existing ldap_context to make updates against
        action: the update to make against ldap_context
        context: The changes to be made with the proper action
    """
    existing_context_data = ldap_context.ad_context
    if action == LDAPAction.MEMBER_ADD:
        existing_context_data['member'].extend(context)
    elif action == LDAPAction.MEMBER_DELETE:
        if isinstance(context, list):
            for cd_member_delete in context:
                if cd_member_delete in existing_context_data['member']:
                    existing_context_data['member'].remove(cd_member_delete)
    elif action == LDAPAction.MEMBEROF_ADD:
        existing_context_data['memberOf'].extend(context)
    elif action == LDAPAction.MEMBEROF_DELETE:
        if isinstance(context, list):
            for cd_member_delete in context:
                if cd_member_delete in existing_context_data['memberOf']:
                    existing_context_data['memberOf'].remove(cd_member_delete)
    elif action in (LDAPAction.GROUP_DELETE, LDAPAction.USER_DELETE, LDAPAction.HOST_DELETE):
        existing_context_data = {}
        ldap_context.state = LdapContext.DELETED_STATE
    else:
        LOG.info("Unsupported Action {action} for AD Context")
    ldap_context.ad_context = existing_context_data


def _attribute_ad_context_to_hosts(ad_contexts):
    """Attach the ad context to the host"""
    try:
        # TODO: add logic
        return
    except Exception:
        LOG.error("Unable to attribute AD Contexts to hosts")


def _attribute_ad_context_to_accounts(ad_contexts):
    """Attach the ad context to the subaccount"""
    try:
        # TODO: add logic
        return
    except Exception:
        LOG.error("Unable to attribute AD Contexts to accounts")
