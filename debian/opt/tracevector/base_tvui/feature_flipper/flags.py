# Copyright (c) 2020 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
import os
import warnings

import requests
from base_tvui.cache_utils import VuiDualCache
from base_tvui.lib_cloudbridge import make_get_request, make_put_request
from base_tvui.settings import CLOUD_ENV
from pure_utils import log_utils
from tvui.models import setting

from .base import ConfigFlag, EnvFlag, FlagRegistry, ReleaseFlag, SimpleFlag
from .conditions import (
    FEATURE_FLAG_CACHE_TTL,
    feature_flag_on,
    is_appliance,
    is_cloud,
    is_cloudbridge,
)
from .types import FlagDatabaseInfo

LOG = log_utils.get_vectra_logger(__name__)
FEATURE_FLAG_URL = 'http://127.0.0.1:5010/api/flag/'
FEATURE_MULTI_FLAG_URL = 'http://127.0.0.1:5010/api/list/flags?query='

# feature flags when need CBI call in NtC tenant
CBI_FEATURE_FLAGS = [
    {'name': 'account_lockdown_account_expires', 'full_name': 'vui/account_lockdown_account_expires'},
    {'name': 'account_lockdown_info_field', 'full_name': 'vui/account_lockdown_info_field'},
]
MDR_FEATURE_FLAG_APPLIANCE = "SIDEKICK_MDR/licensed"

DEFAULT_REGISTRY = FlagRegistry()

# When adding a new flag, add in FE_FEATURE_FLAGS if you want to see in FE


class Flags:
    appliance_only = 'appliance_only'
    cloud_only = 'cloud_only'

    hosts = 'hosts'
    campaigns = 'campaigns'
    observed_privilege = 'observed_privilege'
    limited_time_links = 'limited_time_links'
    recall = 'recall'
    stream = 'stream'
    groups = 'groups'
    edit_user = 'edit_user'
    add_remove_roles = 'add_remove_roles'
    physical_hosts = 'physical_hosts'
    threat_feeds = 'threat_feeds'
    resources = 'resources'
    advanced_search = 'advanced_search'
    entity_search = 'entity_search'
    updater = 'updater'
    couch_detection_scoring = 'couch_detection_scoring'
    user_notifications = 'user_notifications'
    metadata_sharing = 'metadata_sharing'
    cloud_user_management = 'cloud_user_management'
    automatic_filtering = 'automatic_filtering'
    authorize_aws = 'authorize_aws'
    unified_prioritization = 'unified_prioritization'
    o365_distillation = 'o365_distillation'
    aws_distillation = 'aws_distillation'
    azure_distillation = 'azure_distillation'
    network_distillation = 'network_distillation'
    c2_distillation = 'c2_distillation'
    entity_history_distillation_learned_state = 'entity_history_distillation_learned_state'
    airgapped = 'airgapped'
    offline_upgrade = 'offline_upgrade'
    platform_bundled_manual_update = 'platform/bundled_manual_update'
    show_update_toggle = 'show_update_toggle'
    scoring_up = 'scoring_up'
    azure_ad_lockdown = 'azure_ad_lockdown'
    account_lockdown_account_expires = 'account_lockdown_account_expires'
    account_lockdown_info_field = 'account_lockdown_info_field'
    direct_hosts_sync = 'direct_hosts_sync'
    global_view_enabled = 'global_view_enabled'
    vectra_match_ruleset_modification = 'vectra_match_ruleset_modification'

    # Appliance APIs
    vsupport_api = 'vsupport_api'
    platform_rest = 'platform_rest'
    couchdb = 'couchdb'
    contextual_links = 'contextual_links'
    platform_algo_health = 'platform/algo_health/enabled'

    # Customer-facing /api/* endpoints, which does not include internal/updater sensor/stream endpoints
    customer_api_v2 = 'customer_api_v2'
    customer_api_v2_4 = 'customer_api_v2_4'
    customer_api_v2_5 = 'customer_api_v2_5'
    customer_api_v3 = 'customer_api_v3'
    customer_api_v3_1 = 'customer_api_v3_1'
    customer_api_v3_2 = 'customer_api_v3_2'
    customer_api_v3_3 = 'customer_api_v3_3'
    customer_api_v3_4 = 'customer_api_v3_4'

    external_auth_ldap = 'external_auth_ldap'
    external_auth_radius = 'external_auth_radius'
    external_auth_tacacs = 'external_auth_tacacs'
    external_auth_saml = 'external_auth_saml'

    oauth2_on_prem = 'oauth2_on_prem'

    # Network Stats, sensors, PCAPS, network diagnostics
    network_traffic = 'network_traffic'
    packet_captures = 'packet_captures'
    traffic_validation = 'traffic_validation'
    entv_dashboard = 'entv_dashboard'

    # Vectra Product Licensing
    licenses = 'licenses'

    # Reports
    reports = 'reports'
    report_host_severity = 'report_host_severity'
    report_asset_inventory = 'report_asset_inventory'
    report_operational_metrics = 'report_operational_metrics'

    # General settings sections
    settings_general = 'settings_general'
    settings_brain = 'settings_brain'
    settings_datawriter = 'settings_datawriter'
    settings_inactive_user_logout = 'settings_inactive_user_logout'
    settings_nps_participation = 'settings_nps_participation'
    settings_static_ip_addresses = 'settings_static_ip_addresses'
    settings_internal_vpn_ip_addresses = 'settings_internal_vpn_ip_addresses'
    settings_local_user_account_lockout = 'settings_local_user_account_lockout'
    settings_login_caption = 'settings_login_caption'
    settings_edit_timezone = 'settings_edit_timezone'
    settings_user_password_policy = 'settings_user_password_policy'
    settings_version = 'settings_version'
    settings_sensor_flow = 'settings_sensor_flow'
    settings_remote_support = 'settings_remote_support'

    # Notifications settings sections
    settings_aws_cloudwatch = 'settings_aws_cloudwatch'
    settings_aws_security_hub = 'settings_aws_security_hub'
    settings_digest_emails = 'settings_digest_emails'
    settings_syslogs_to_kafka = 'settings_syslogs_to_kafka'
    settings_smtp = 'settings_smtp'
    settings_syslog = 'settings_syslog'
    settings_system_health_notifications = 'settings_system_health_notifications'

    # Sentry
    sentry = 'sentry'
    sentry_fe = 'sentry_fe'

    # Cloudfront CDN
    cloudfront = 'cloudfront'

    # Generic Host and Account notifications settings
    generic_alarms = 'generic_alarms'

    # Cognito Saas settings sections
    settings_account_association = 'settings_account_association'

    # Settings Pages
    settings_external_connectors = 'settings_external_connectors'
    settings_zpa_log_ingestion = 'settings_zpa_log_ingestion'
    settings_edr = 'settings_edr'

    # Scoring-related flags
    multi_event_escalation = "multi_event_escalation"
    enable_prioritization_tuner = "enable_prioritization_tuner"

    # Investigation flags
    mock_insight_data = 'mock_insight_data'
    advanced_inv_query_language = 'advanced_inv_query_language'

    # Investigations CRON Flags
    vsa_dashboard_cron = 'vsa_dashboard_cron'

    # Dashboard Flags
    vsa_dashboard_naive = 'vsa_dashboard_naive'

    # SaaS App FE Metrics flags
    saas_app_fe_metrics = 'saas_app_fe_metrics'

    # Data Sources Flags
    create_azure_cp_sensor = 'create_azure_cp_sensor'
    create_crowdstrike_sensor = 'create_crowdstrike_sensor'
    create_defender_sensor = 'create_defender_sensor'
    create_sentinel_one_sensor = 'create_sentinel_one_sensor'
    brain_setup = 'brain_setup'
    data_sources = 'data_sources'
    data_sources_config = 'data_sources_config'
    data_sources_connections = 'data_sources_connections'
    data_sources_regions = 'data_sources_regions'
    data_sources_network = 'data_sources_network'
    data_sources_network_appliance = 'data_sources_network_appliance'
    data_sources_network_to_cbi = 'data_sources_network_to_cbi'
    data_sources_nexus = 'data_sources_nexus'
    sensor_configuration = 'sensor_configuration'
    sase_remote_users = 'sase_remote_users'

    # Misc flags
    pendo_on_saas = 'pendo_on_saas'
    pendo_on_prem = 'pendo_on_prem'
    on_cloud_document = 'on_cloud_document'
    host_context = 'host_context'
    configure_rest_api = 'configure_rest_api'
    detection_events = 'detection_events'
    saml_aws_cognito = 'saml_aws_cognito'
    entity_receiver = 'entity_receiver'
    sql_audit_event = 'sql_audit_event'
    account_groups = 'account_groups'
    cloudbridge = 'cloudbridge'
    unauthorized_saml_redirect = 'unauthorized_saml_redirect'
    cbi_host_create = 'cbi_host_create'
    v3_3_detection_event_details = 'v3_3_detection_event_details'
    v3_3_unique_hosts = 'v3_3_unique_hosts'
    host_audit_snapshot = 'host_audit_snapshot'
    saas_unified_user_management = 'saas_unified_user_management'
    saas_saml_profiles = 'saas_saml_profiles'
    enable_async_tasks = 'enable_async_tasks'
    limit_triage_contains_filter = 'limit_triage_contains_filter'
    fast_rest_paginator = 'fast_rest_paginator'
    v3_4_unique_hosts_timespan = 'v3_4_unique_hosts_timespan'
    gmq_demo_2025 = 'gmq_demo_2025'  # EC-2345
    network_discovery_dashboard = 'network_discovery_dashboard'
    dfazure_enablement_redirect = 'dfazure_enablement_redirect'  # RKT-438

    # SQL Migration flag
    network_sql_migration_import = 'network_sql_migration_import'
    network_sql_key_asset_only = 'network_sql_key_asset_only'
    # VUI Settings
    sensor_autopair_enabled = 'vui/sensor/autopair_enabled'
    stream_autopair_enabled = 'vui/stream/autopair_enabled'

    # cognito user sync flag
    users_sync_enabled = 'users_sync_enabled'
    users_sync_report = 'users_sync_report'

    # cloudbridge VSI download
    saasui_vsi_link_enabled = 'saasui_vsi_link_enabled'

    platform_grafana_enabled = 'platform/grafana_enabled'

    # auto-lockdown flag
    rux_ad_account_auto_lockdown = 'vui/rux_ad_account_auto_lockdown'
    rux_host_auto_lockdown = 'vui/rux_host_auto_lockdown'
    ad_aad_accounts_lockdown_consolidation = 'ad_aad_accounts_lockdown_consolidation'
    azure_ad_lockdown_automatic = 'azure_ad_lockdown_automatic'

    # TME Demo and Test flags
    extended_detection_active_time = "extended_detection_active_time"

    # enables use of v2 detection model
    detection_v2_model = 'detection_v2_model'

    # enables use of schema_foundry for detection notes, details, transformations
    schema_foundry_for_detections = "schema_foundry_for_detections"

    # enables ui tasks to be run reorderImpact in celery and polls for the results
    celery_ui_reorder_impact_tasks = 'celery_ui_reorder_impact_tasks'

    # feature flag for entra context processing
    entra_context_processing = 'entra_context_processing'
    entra_context_processing__renaming = 'entra_context_processing__renaming'
    entra_context_processing__reconciliation = 'entra_context_processing__reconciliation'
    entra_context_processing__federated_reconciliation = 'entra_context_processing__federated_reconciliation'
    entra_context_processing__periodic_federated_reconciliation = 'entra_context_processing__periodic_federated_reconciliation'

    # feature flag for dynamic host and account groups
    dynamic_groups = 'dynamic_groups'
    dynamic_groups_member_transition = 'dynamic_groups_member_transition'
    dynamic_host_groups_batch = 'dynamic_host_groups_batch'
    dynamic_account_groups_batch = 'dynamic_account_groups_batch'

    # feature flag for detection pivots to investifations
    detection_investigation_pivot = 'detection_investigation_pivot'

    # flag gating the use of the host role table to poll roles
    use_host_role_table = 'use_host_role_table'

    # feature flags for linked account triage
    linked_account_triage_engine = 'linked_account_triage_engine'
    linked_account_triage_display = 'linked_account_triage_display'

    # feature flag for using linked accounts in groups instead of subaccounts
    linked_account_groups = 'linked_account_groups'

    # Flags related to migrations for linked account groups (the non-dry run flag currently does nothing)
    linked_account_groups_dry_run_migration = 'linked_account_groups_dry_run_migration'
    linked_account_groups_migration = 'linked_account_groups_migration'
    smart_rules_with_linked_accounts_and_subaccount_groups_dry_run_migration = (
        'smart_rules_with_linked_accounts_and_subaccount_groups_dry_run_migration'
    )
    smart_rules_with_linked_accounts_and_subaccount_groups_migration = 'smart_rules_with_linked_accounts_and_subaccount_groups_migration'

    # feature flag for Signal Efficacy
    signal_efficacy_closed_as = 'signal_efficacy_closed_as'
    signal_efficacy_public_preview = 'signal_efficacy_public_preview'
    detection_resolution_setting = 'detection_resolution_setting'

    # cull entity contexts more aggressively
    entity_context_cull_aggressive = 'entity_context_cull_aggressive'
    entity_persister_v2 = 'entity_persister_v2'

    # feature flag for attack graph
    attack_graph = 'attack_graph'
    attack_graph_metrics = 'attack_graph_metrics'
    attack_graph_v2 = 'attack_graph_v2'
    attack_graph_qux = 'attack_graph_qux'

    # feature flags for updated internal target attack graphs
    attack_graph_updated_data_contract = 'attack_graph_updated_data_contract'

    # V2 Config flags
    config_enable_cloud_flag_api = "config_enable_cloud_flag_api"

    # V2 Release Flags
    executive_overview_extra_widgets = "executive_overview_extra_widgets"
    executive_overview_signal_efficacy = "executive_overview_signal_efficacy"

    traffic_report_large_file_download = "traffic_report_large_file_download"
    SAASAPPS_6865_cloud_stats_push_fix_distinct_count = 'SAASAPPS_6865_cloud_stats_push_fix_distinct_count'

    # feature flag for uWSGI stats in Grafana
    uwsgi_stats = 'uwsgi_stats'

    # Detect whether MDR is enabled
    mdr_enabled = 'mdr_enabled'

    # AWS Reconciliation
    aws_reconciliation = 'aws_reconciliation'
    aws_reconciliation_dryrun = 'aws_reconciliation_dryrun'

    # Pruning Flags
    aggressive_pruning_kerberos_entities = "aggressive_pruning_kerberos_entities"

    # Global view refresh client tokens celery task
    global_view_refresh_client_tokens = 'global_view_refresh_client_tokens'

    # Account Details V2
    account_detail_v2 = "account_detail_v2"
    account_detail_tab_v2 = "account_detail_tab_v2"

    # Rest profiling middleware
    rest_profiling = 'rest_profiling'

    # Feature Flags for Dynamic AD Groups
    ad_groups = 'ad_groups'
    ad_context_receiver = 'ad_context_receiver'
    ad_groups_sync = 'ad_groups_sync'

    # Notification Event System
    nes_events = 'nes_events'
    nes_events__delivery = 'nes_events__delivery'

    # Cache results of ldap service CBI call for associated accounts used during automatic account reconciliation
    cache_associated_accounts_cbi_call = 'cache_associated_accounts_cbi_call'


_appliance_only_flag = SimpleFlag(is_appliance)
_cloud_only_flag = SimpleFlag(is_cloud)


# Add flags here
_VUI_FLAGS = {
    Flags.attack_graph_qux: SimpleFlag(lambda: feature_flag_on('attack_graph_qux')),
    Flags.global_view_refresh_client_tokens: ReleaseFlag(
        ticket="TITAN-3247", db_meta=FlagDatabaseInfo(group='feature', key='global_view_refresh_client_tokens')
    ),
    Flags.vectra_match_ruleset_modification: SimpleFlag(
        lambda: (is_appliance() or is_cloudbridge()) and feature_flag_on('vectra_match_ruleset_modification')
    ),
    Flags.traffic_report_large_file_download: ReleaseFlag(
        ticket='TITAN-2901', db_meta=FlagDatabaseInfo(group='feature', key='traffic_report_large_file_download'), active_envs=['prod']
    ),
    Flags.config_enable_cloud_flag_api: ConfigFlag(ticket='TITAN-2757', enabled=is_cloud()),
    Flags.executive_overview_extra_widgets: ReleaseFlag(
        ticket='NOVA-78', db_meta=FlagDatabaseInfo(group='feature', key='executive_overview_extra_widgets')
    ),
    Flags.executive_overview_signal_efficacy: ReleaseFlag(
        ticket='NOVA-180', db_meta=FlagDatabaseInfo(group='feature', key='executive_overview_signal_efficacy')
    ),
    Flags.appliance_only: _appliance_only_flag,
    Flags.cloud_only: _cloud_only_flag,
    Flags.hosts: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.campaigns: SimpleFlag(lambda: is_appliance() and feature_flag_on('campaigns')),
    Flags.observed_privilege: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.limited_time_links: _appliance_only_flag,
    Flags.recall: _appliance_only_flag,
    Flags.stream: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.groups: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.edit_user: _appliance_only_flag,
    Flags.add_remove_roles: _appliance_only_flag,
    Flags.physical_hosts: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.threat_feeds: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.resources: _appliance_only_flag,
    Flags.advanced_search: _appliance_only_flag,
    Flags.entity_search: _appliance_only_flag,
    Flags.updater: _appliance_only_flag,
    Flags.couch_detection_scoring: _appliance_only_flag,
    Flags.user_notifications: SimpleFlag(lambda: True),
    Flags.metadata_sharing: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.vsupport_api: _appliance_only_flag,
    Flags.platform_rest: _appliance_only_flag,
    Flags.platform_algo_health: SimpleFlag(
        lambda: get_appliance_flag(Flags.platform_algo_health, default=False) if is_appliance() or is_cloudbridge() else False
    ),
    Flags.couchdb: _appliance_only_flag,
    Flags.contextual_links: _appliance_only_flag,
    Flags.customer_api_v2: _appliance_only_flag,
    Flags.customer_api_v2_4: _appliance_only_flag,
    Flags.customer_api_v2_5: SimpleFlag(lambda: is_appliance() and feature_flag_on('customer_api_v2_5')),
    Flags.customer_api_v3: SimpleFlag(lambda: is_cloud() and feature_flag_on('customer_api_v3')),
    Flags.customer_api_v3_1: SimpleFlag(lambda: is_cloud() and feature_flag_on('customer_api_v3_1')),
    Flags.customer_api_v3_2: SimpleFlag(lambda: is_cloud() and feature_flag_on('customer_api_v3_2')),
    Flags.customer_api_v3_3: SimpleFlag(lambda: is_cloud() and feature_flag_on('customer_api_v3_3')),
    Flags.customer_api_v3_4: SimpleFlag(lambda: is_cloud() and feature_flag_on('customer_api_v3_4')),
    Flags.external_auth_ldap: _appliance_only_flag,
    Flags.external_auth_radius: _appliance_only_flag,
    Flags.external_auth_tacacs: _appliance_only_flag,
    Flags.external_auth_saml: _appliance_only_flag,
    Flags.oauth2_on_prem: SimpleFlag(lambda: is_appliance() and feature_flag_on('oauth2_on_prem')),
    Flags.network_traffic: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.packet_captures: SimpleFlag(lambda: feature_flag_on('packet_captures')),
    Flags.traffic_validation: SimpleFlag(lambda: (is_appliance() or is_cloudbridge()) and feature_flag_on('traffic_validation')),
    Flags.entv_dashboard: SimpleFlag(lambda: (is_appliance() or is_cloudbridge()) and feature_flag_on('entv_dashboard')),
    Flags.cloud_user_management: _cloud_only_flag,
    Flags.automatic_filtering: SimpleFlag(lambda: (is_appliance() or is_cloudbridge()) and feature_flag_on('automatic_filtering')),
    Flags.authorize_aws: SimpleFlag(lambda: is_cloud() and authorize_aws()),
    Flags.configure_rest_api: SimpleFlag(
        lambda: (is_cloud() or feature_flag_on('oauth2_on_prem')) and feature_flag_on('configure_rest_api')
    ),
    Flags.unified_prioritization: SimpleFlag(lambda: (is_cloud() and feature_flag_on('unified_prioritization')) or is_cloudbridge()),
    Flags.o365_distillation: SimpleFlag(lambda: is_cloud() and feature_flag_on('o365_distillation')),
    Flags.aws_distillation: SimpleFlag(lambda: is_cloud() and feature_flag_on('aws_distillation')),
    Flags.azure_distillation: SimpleFlag(lambda: is_cloud() and feature_flag_on('azure_distillation')),
    Flags.network_distillation: SimpleFlag(lambda: feature_flag_on('network_distillation')),
    Flags.c2_distillation: SimpleFlag(lambda: is_cloud() and feature_flag_on('c2_distillation')),
    Flags.entity_history_distillation_learned_state: SimpleFlag(lambda: feature_flag_on('entity_history_distillation_learned_state')),
    Flags.airgapped: SimpleFlag(lambda: get_appliance_flag('platform/airgapped') if is_appliance() else False),
    Flags.offline_upgrade: SimpleFlag(lambda: get_appliance_flag('platform/offline_update_enabled') if is_appliance() else False),
    Flags.platform_bundled_manual_update: SimpleFlag(
        lambda: get_appliance_flag('platform/bundled_manual_update', default=True) if is_appliance() else False
    ),
    Flags.show_update_toggle: SimpleFlag(lambda: get_appliance_flag('vui/show_update_toggle') if is_appliance() else False),
    Flags.scoring_up: _cloud_only_flag,
    Flags.azure_ad_lockdown: SimpleFlag(lambda: is_cloud() and feature_flag_on('azure_ad_lockdown')),
    # Reports
    Flags.reports: SimpleFlag(lambda: True),
    Flags.report_host_severity: _appliance_only_flag,
    Flags.report_asset_inventory: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.report_operational_metrics: SimpleFlag(lambda: True),
    # General settings sections
    Flags.settings_general: SimpleFlag(lambda: feature_flag_on('settings_general')),
    Flags.settings_brain: _appliance_only_flag,
    Flags.settings_datawriter: _appliance_only_flag,
    Flags.settings_inactive_user_logout: _appliance_only_flag,
    Flags.settings_nps_participation: _appliance_only_flag,
    Flags.settings_static_ip_addresses: _appliance_only_flag,
    Flags.settings_internal_vpn_ip_addresses: _appliance_only_flag,
    Flags.settings_local_user_account_lockout: _appliance_only_flag,
    Flags.settings_login_caption: _appliance_only_flag,
    Flags.settings_edit_timezone: SimpleFlag(lambda: is_appliance() or feature_flag_on('saas_timezone_editable')),
    Flags.settings_user_password_policy: _appliance_only_flag,
    Flags.settings_version: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.settings_sensor_flow: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.settings_remote_support: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    # Notifications settings sections
    Flags.settings_aws_cloudwatch: _appliance_only_flag,
    Flags.settings_aws_security_hub: _appliance_only_flag,
    Flags.settings_digest_emails: _appliance_only_flag,
    Flags.settings_syslogs_to_kafka: _appliance_only_flag,
    Flags.settings_smtp: _appliance_only_flag,
    Flags.settings_syslog: _appliance_only_flag,
    Flags.settings_system_health_notifications: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    # Cognito Saas settings sections
    Flags.settings_account_association: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    # Settings Pages
    Flags.settings_external_connectors: SimpleFlag(lambda: is_appliance() or is_cloudbridge() or feature_flag_on('azure_ad_lockdown')),
    Flags.settings_edr: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    Flags.settings_zpa_log_ingestion: SimpleFlag(lambda: feature_flag_on('zpa_log_ingestion')),
    # Scoring-related
    Flags.multi_event_escalation: SimpleFlag(lambda: feature_flag_on('multi_event_escalation')),
    Flags.enable_prioritization_tuner: SimpleFlag(lambda: feature_flag_on('enable_prioritization_tuner')),
    # Investigations flags
    Flags.mock_insight_data: SimpleFlag(lambda: is_cloud() and feature_flag_on('mock_insight_data')),
    # SaaS App FE Metrics flags
    Flags.saas_app_fe_metrics: _cloud_only_flag,
    # Misc flags
    Flags.pendo_on_saas: _cloud_only_flag,
    Flags.pendo_on_prem: SimpleFlag(lambda: is_appliance() and feature_flag_on('pendo_on_prem')),
    Flags.on_cloud_document: _cloud_only_flag,
    Flags.host_context: _appliance_only_flag,
    Flags.detection_events: SimpleFlag(lambda: is_cloud() and feature_flag_on('detection_events')),
    Flags.saml_aws_cognito: _cloud_only_flag,
    Flags.account_groups: SimpleFlag(lambda: feature_flag_on('account_groups')),
    Flags.cloudbridge: SimpleFlag(is_cloudbridge),
    Flags.saasui_vsi_link_enabled: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on('saasui_vsi_link_enabled')),
    Flags.enable_async_tasks: SimpleFlag(
        lambda: EnvFlag('CELERY_ENABLED').is_enabled() if is_cloud() else feature_flag_on('enable_async_tasks')
    ),
    Flags.limit_triage_contains_filter: SimpleFlag(lambda: feature_flag_on('limit_triage_contains_filter')),
    # Data Sources flags
    Flags.create_azure_cp_sensor: SimpleFlag(lambda: is_cloud() and feature_flag_on('create_azure_cp_sensor')),
    Flags.dfazure_enablement_redirect: SimpleFlag(lambda: is_cloud() and feature_flag_on('dfazure_enablement_redirect')),  # RKT-438
    Flags.create_crowdstrike_sensor: SimpleFlag(lambda: is_cloud() and feature_flag_on('create_crowdstrike_sensor')),
    Flags.create_defender_sensor: SimpleFlag(lambda: is_cloud() and feature_flag_on('create_defender_sensor')),
    Flags.create_sentinel_one_sensor: SimpleFlag(lambda: is_cloud() and feature_flag_on('create_sentinel_one_sensor')),
    Flags.data_sources: _cloud_only_flag,
    Flags.data_sources_connections: _cloud_only_flag,
    Flags.data_sources_regions: _appliance_only_flag,
    Flags.sensor_configuration: SimpleFlag(
        lambda: is_data_sources_network_enabled() or is_cloudbridge() and feature_flag_on('sensor_configuration')
    ),
    Flags.brain_setup: SimpleFlag(lambda: is_data_sources_network_enabled() or is_cloudbridge() and feature_flag_on('brain_setup')),
    Flags.data_sources_network: SimpleFlag(lambda: is_data_sources_network_enabled() or is_cloudbridge()),
    Flags.data_sources_network_appliance: SimpleFlag(lambda: feature_flag_on('data_sources_network_appliance')),
    Flags.data_sources_network_to_cbi: SimpleFlag(lambda: feature_flag_on('data_sources_network_to_cbi')),
    Flags.data_sources_config: SimpleFlag(lambda: feature_flag_on('data_sources_config')),
    Flags.data_sources_nexus: SimpleFlag(lambda: feature_flag_on('data_sources_nexus')),
    Flags.sase_remote_users: SimpleFlag(lambda: feature_flag_on('sase_remote_users') and (is_appliance() or is_cloudbridge())),
    # Advanced Investigations flags
    Flags.advanced_inv_query_language: SimpleFlag(lambda: is_cloud() and feature_flag_on('advanced_inv_query_language')),
    # Investigation Dashboard flags
    Flags.vsa_dashboard_naive: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on('vsa_dashboard_naive')),
    # Investigations CRON flags
    Flags.vsa_dashboard_cron: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on('vsa_dashboard_cron')),
    # Generic Host and Account alarms flags
    Flags.generic_alarms: SimpleFlag(lambda: feature_flag_on('generic_alarms')),
    # Entity Receiver flag
    Flags.entity_receiver: SimpleFlag(lambda: False),
    # Vectra Product Licensing
    Flags.licenses: SimpleFlag(lambda: is_appliance() or is_cloudbridge()),
    # Audit Event flag
    Flags.sql_audit_event: SimpleFlag(lambda: is_cloud() and feature_flag_on('sql_audit_event')),
    Flags.unauthorized_saml_redirect: SimpleFlag(lambda: is_appliance() and feature_flag_on('unauthorized_saml_redirect')),
    # SQL Migration flag
    Flags.network_sql_migration_import: SimpleFlag(lambda: is_cloud() and feature_flag_on('network_sql_migration_import')),
    Flags.network_sql_key_asset_only: SimpleFlag(lambda: is_cloud() and feature_flag_on('network_sql_key_asset_only')),
    ## VUI Settings
    Flags.sensor_autopair_enabled: SimpleFlag(lambda: get_appliance_flag(Flags.sensor_autopair_enabled) if is_appliance() else False),
    Flags.stream_autopair_enabled: SimpleFlag(lambda: get_appliance_flag(Flags.stream_autopair_enabled) if is_appliance() else False),
    # cognito user sync flag
    Flags.users_sync_enabled: SimpleFlag(lambda: is_cloud() and feature_flag_on('users_sync_enabled')),
    Flags.users_sync_report: SimpleFlag(lambda: is_cloud() and feature_flag_on('users_sync_report')),
    Flags.cbi_host_create: SimpleFlag(lambda: is_appliance()),
    Flags.v3_3_detection_event_details: SimpleFlag(lambda: is_cloud() and feature_flag_on('v3_3_detection_event_details')),
    Flags.v3_3_unique_hosts: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on('v3_3_unique_hosts')),
    Flags.host_audit_snapshot: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on('host_audit_snapshot')),
    Flags.saas_unified_user_management: SimpleFlag(lambda: is_cloud() and feature_flag_on('saas_unified_user_management')),
    Flags.saas_saml_profiles: SimpleFlag(lambda: is_cloud() and feature_flag_on('saas_saml_profiles')),
    # Grafana
    Flags.platform_grafana_enabled: SimpleFlag(
        lambda: get_appliance_flag(Flags.platform_grafana_enabled, default=False) if is_appliance() else False
    ),
    # Account lockdown
    Flags.account_lockdown_account_expires: SimpleFlag(lambda: get_appliance_flag('vui/account_lockdown_account_expires', default=False)),
    Flags.account_lockdown_info_field: SimpleFlag(lambda: get_appliance_flag('vui/account_lockdown_info_field', default=False)),
    Flags.rux_ad_account_auto_lockdown: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on("vui/rux_ad_account_auto_lockdown")),
    Flags.ad_aad_accounts_lockdown_consolidation: SimpleFlag(lambda: feature_flag_on('ad_aad_accounts_lockdown_consolidation')),
    Flags.azure_ad_lockdown_automatic: SimpleFlag(lambda: feature_flag_on('azure_ad_lockdown_automatic')),
    # Host lockdown
    Flags.rux_host_auto_lockdown: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on("vui/rux_host_auto_lockdown")),
    # Sentry
    Flags.sentry: SimpleFlag(lambda: feature_flag_on('sentry')),
    Flags.sentry_fe: SimpleFlag(lambda: feature_flag_on('sentry_fe')),
    Flags.cloudfront: SimpleFlag(lambda: is_cloud() and feature_flag_on('cloudfront')),
    Flags.direct_hosts_sync: SimpleFlag(lambda: feature_flag_on('direct_hosts_sync')),
    # TME Demo and Test flags
    Flags.extended_detection_active_time: SimpleFlag(lambda: feature_flag_on('extended_detection_active_time')),
    # enables use of v2 detection model
    Flags.detection_v2_model: SimpleFlag(lambda: feature_flag_on('detection_v2_model')),
    Flags.schema_foundry_for_detections: SimpleFlag(lambda: feature_flag_on('schema_foundry_for_detections')),
    Flags.celery_ui_reorder_impact_tasks: SimpleFlag(lambda: feature_flag_on('celery_ui_reorder_impact_tasks')),
    Flags.fast_rest_paginator: SimpleFlag(lambda: feature_flag_on('fast_rest_paginator')),
    Flags.global_view_enabled: SimpleFlag(lambda: is_cloud() and feature_flag_on('global_view_enabled')),
    Flags.entra_context_processing: SimpleFlag(lambda: is_cloud() and feature_flag_on('entra_context_processing')),
    Flags.entra_context_processing__renaming: SimpleFlag(lambda: is_cloud() and feature_flag_on('entra_context_processing__renaming')),
    Flags.entra_context_processing__reconciliation: SimpleFlag(
        lambda: is_cloud() and feature_flag_on('entra_context_processing__reconciliation')
    ),
    Flags.entra_context_processing__federated_reconciliation: SimpleFlag(
        lambda: is_cloud() and feature_flag_on('entra_context_processing__federated_reconciliation')
    ),
    Flags.entra_context_processing__periodic_federated_reconciliation: SimpleFlag(
        lambda: is_cloud() and feature_flag_on('entra_context_processing__periodic_federated_reconciliation')
    ),
    Flags.v3_4_unique_hosts_timespan: SimpleFlag(lambda: is_cloudbridge() and feature_flag_on('v3_4_unique_hosts_timespan')),
    Flags.dynamic_groups: SimpleFlag(lambda: feature_flag_on('dynamic_groups')),
    Flags.dynamic_groups_member_transition: SimpleFlag(lambda: feature_flag_on('dynamic_groups_member_transition')),
    Flags.dynamic_host_groups_batch: SimpleFlag(lambda: feature_flag_on('dynamic_host_groups_batch')),
    Flags.dynamic_account_groups_batch: SimpleFlag(lambda: feature_flag_on('dynamic_account_groups_batch')),
    # Detection Investigations Pivots
    Flags.detection_investigation_pivot: SimpleFlag(lambda: is_cloud() and feature_flag_on('detection_investigation_pivot')),
    Flags.use_host_role_table: SimpleFlag(lambda: feature_flag_on('use_host_role_table')),
    Flags.linked_account_triage_engine: SimpleFlag(lambda: feature_flag_on('linked_account_triage_engine')),
    Flags.linked_account_triage_display: SimpleFlag(lambda: feature_flag_on('linked_account_triage_display')),
    Flags.linked_account_groups: SimpleFlag(lambda: feature_flag_on('linked_account_groups')),
    Flags.linked_account_groups_dry_run_migration: SimpleFlag(lambda: feature_flag_on('linked_account_groups_dry_run_migration')),
    Flags.linked_account_groups_migration: SimpleFlag(lambda: feature_flag_on('linked_account_groups_migration')),
    Flags.smart_rules_with_linked_accounts_and_subaccount_groups_dry_run_migration: SimpleFlag(
        lambda: feature_flag_on('smart_rules_with_linked_accounts_and_subaccount_groups_dry_run_migration')
    ),
    Flags.smart_rules_with_linked_accounts_and_subaccount_groups_migration: SimpleFlag(
        lambda: feature_flag_on('smart_rules_with_linked_accounts_and_subaccount_groups_migration')
    ),
    Flags.signal_efficacy_closed_as: SimpleFlag(lambda: feature_flag_on('signal_efficacy_closed_as')),
    Flags.signal_efficacy_public_preview: SimpleFlag(
        lambda: feature_flag_on('signal_efficacy_public_preview') and feature_flag_on('signal_efficacy_closed_as')
    ),
    Flags.detection_resolution_setting: SimpleFlag(lambda: feature_flag_on('detection_resolution_setting')),
    Flags.entity_context_cull_aggressive: SimpleFlag(lambda: feature_flag_on('entity_context_cull_aggressive')),
    Flags.entity_persister_v2: SimpleFlag(lambda: feature_flag_on('entity_persister_v2')),
    Flags.attack_graph: SimpleFlag(lambda: feature_flag_on('attack_graph')),
    Flags.attack_graph_metrics: SimpleFlag(lambda: feature_flag_on('attack_graph_metrics')),
    Flags.attack_graph_v2: SimpleFlag(lambda: feature_flag_on('attack_graph_v2')),
    # uwsgi stats to Grafana
    Flags.uwsgi_stats: SimpleFlag(lambda: is_cloud() and feature_flag_on('uwsgi_stats')),
    Flags.gmq_demo_2025: SimpleFlag(lambda: feature_flag_on('gmq_demo_2025')),
    Flags.network_discovery_dashboard: SimpleFlag(lambda: is_cloud() and feature_flag_on('network_discovery_dashboard')),
    Flags.mdr_enabled: SimpleFlag(
        lambda: (is_cloud() and feature_flag_on(Flags.mdr_enabled))
        or (is_appliance() and get_appliance_flag(MDR_FEATURE_FLAG_APPLIANCE, default=False))
    ),
    Flags.aws_reconciliation: SimpleFlag(lambda: feature_flag_on('aws_reconciliation')),
    Flags.aws_reconciliation_dryrun: SimpleFlag(lambda: feature_flag_on('aws_reconciliation_dryrun')),
    Flags.SAASAPPS_6865_cloud_stats_push_fix_distinct_count: ReleaseFlag(
        ticket='SAASAPPS-6865',
        db_meta=FlagDatabaseInfo(group='feature', key='SAASAPPS_6865_cloud_stats_push_fix_distinct_count'),
        active_envs=[],
    ),
    Flags.aggressive_pruning_kerberos_entities: SimpleFlag(lambda: feature_flag_on("aggressive_pruning_kerberos_entities")),
    Flags.account_detail_v2: SimpleFlag(lambda: feature_flag_on("account_detail_v2")),
    Flags.account_detail_tab_v2: SimpleFlag(lambda: feature_flag_on("account_detail_tab_v2")),
    Flags.rest_profiling: SimpleFlag(lambda: feature_flag_on('rest_profiling')),
    # AD Groups
    Flags.ad_groups: SimpleFlag(lambda: feature_flag_on('ad_groups')),
    Flags.ad_context_receiver: SimpleFlag(lambda: feature_flag_on('ad_context_receiver')),
    Flags.ad_groups_sync: SimpleFlag(lambda: feature_flag_on('ad_groups_sync')),
    Flags.nes_events: SimpleFlag(lambda: feature_flag_on('nes_events')),
    Flags.nes_events__delivery: SimpleFlag(lambda: feature_flag_on('nes_events__delivery')),
    Flags.cache_associated_accounts_cbi_call: SimpleFlag(lambda: feature_flag_on('cache_associated_accounts_cbi_call')),
    Flags.attack_graph_updated_data_contract: SimpleFlag(lambda: feature_flag_on('attack_graph_updated_data_contract')),
}

# Add feature flag here if you want to appear in FE
FE_FEATURE_FLAGS = {
    Flags.executive_overview_extra_widgets,
    Flags.ad_aad_accounts_lockdown_consolidation,
    Flags.add_remove_roles,
    Flags.advanced_inv_query_language,
    Flags.account_groups,
    Flags.account_lockdown_account_expires,
    Flags.account_lockdown_info_field,
    Flags.advanced_search,
    Flags.airgapped,
    Flags.appliance_only,
    Flags.attack_graph,
    Flags.attack_graph_metrics,
    Flags.attack_graph_qux,
    Flags.automatic_filtering,
    Flags.aws_distillation,
    Flags.azure_ad_lockdown,
    Flags.azure_ad_lockdown_automatic,
    Flags.azure_distillation,
    Flags.brain_setup,
    Flags.campaigns,
    Flags.celery_ui_reorder_impact_tasks,
    Flags.cloud_only,
    Flags.cloud_user_management,
    Flags.cloudbridge,
    Flags.cloudfront,
    Flags.configure_rest_api,
    Flags.contextual_links,
    Flags.couchdb,
    Flags.create_azure_cp_sensor,
    Flags.create_crowdstrike_sensor,
    Flags.create_defender_sensor,
    Flags.create_sentinel_one_sensor,
    Flags.customer_api_v2,
    Flags.data_sources,
    Flags.data_sources_config,
    Flags.data_sources_connections,
    Flags.data_sources_network,
    Flags.data_sources_nexus,
    Flags.data_sources_regions,
    Flags.detection_investigation_pivot,
    Flags.dynamic_groups,
    Flags.dynamic_groups_member_transition,
    Flags.ad_groups,
    Flags.dynamic_host_groups_batch,
    Flags.dynamic_account_groups_batch,
    Flags.edit_user,
    Flags.enable_async_tasks,
    Flags.entity_receiver,
    Flags.entity_search,
    Flags.entv_dashboard,
    Flags.external_auth_ldap,
    Flags.external_auth_radius,
    Flags.external_auth_tacacs,
    Flags.global_view_enabled,
    Flags.gmq_demo_2025,
    Flags.groups,
    Flags.host_context,
    Flags.hosts,
    Flags.licenses,
    Flags.limited_time_links,
    Flags.linked_account_triage_display,
    Flags.linked_account_triage_engine,
    Flags.linked_account_groups,
    Flags.metadata_sharing,
    Flags.mdr_enabled,
    Flags.mock_insight_data,
    Flags.network_traffic,
    Flags.network_discovery_dashboard,
    Flags.o365_distillation,
    Flags.observed_privilege,
    Flags.on_cloud_document,
    Flags.packet_captures,
    Flags.pendo_on_prem,
    Flags.pendo_on_saas,
    Flags.physical_hosts,
    Flags.recall,
    Flags.reports,
    Flags.report_asset_inventory,
    Flags.report_host_severity,
    Flags.report_operational_metrics,
    Flags.executive_overview_signal_efficacy,
    Flags.resources,
    Flags.rux_ad_account_auto_lockdown,
    Flags.rux_host_auto_lockdown,
    Flags.saas_app_fe_metrics,
    Flags.saas_saml_profiles,
    Flags.saas_unified_user_management,
    Flags.saasui_vsi_link_enabled,
    Flags.sase_remote_users,
    Flags.saml_aws_cognito,
    Flags.sensor_configuration,
    Flags.settings_account_association,
    Flags.settings_aws_cloudwatch,
    Flags.settings_aws_security_hub,
    Flags.settings_brain,
    Flags.settings_datawriter,
    Flags.settings_digest_emails,
    Flags.settings_edit_timezone,
    Flags.settings_edr,
    Flags.settings_external_connectors,
    Flags.settings_general,
    Flags.settings_inactive_user_logout,
    Flags.settings_internal_vpn_ip_addresses,
    Flags.settings_local_user_account_lockout,
    Flags.settings_login_caption,
    Flags.settings_nps_participation,
    Flags.settings_remote_support,
    Flags.settings_sensor_flow,
    Flags.settings_smtp,
    Flags.settings_static_ip_addresses,
    Flags.settings_syslog,
    Flags.settings_syslogs_to_kafka,
    Flags.settings_user_password_policy,
    Flags.settings_version,
    Flags.signal_efficacy_closed_as,
    Flags.signal_efficacy_public_preview,
    Flags.detection_resolution_setting,
    Flags.stream,
    Flags.threat_feeds,
    Flags.traffic_validation,
    Flags.unified_prioritization,
    Flags.updater,
    Flags.use_host_role_table,
    Flags.user_notifications,
    Flags.vsa_dashboard_cron,
    Flags.vsa_dashboard_naive,
    Flags.vectra_match_ruleset_modification,
    Flags.account_detail_v2,
    Flags.account_detail_tab_v2,
    Flags.nes_events,
}

for flag_name, flag in _VUI_FLAGS.items():
    DEFAULT_REGISTRY.register_flag(flag_name, flag)


def authorize_aws():
    return True


def is_data_sources_network_enabled():
    """
    Determine whether networking is enabled as part of Data Sources.

    Network is initially being added to Data Sources on the appliance.
    Once CloudBridge is completed, Networking will also exist in SAAS.
    """
    if feature_flag_on('data_sources_network'):
        return True
    else:
        return is_appliance() and feature_flag_on('data_sources_network_appliance')


_appliance_flag_cache = VuiDualCache('appliance_flag_cache')


def get_cbi_flags():
    """
    Retrieve all cbi feature flags in one call.
    """

    if (result := _appliance_flag_cache.get('_all_cbi_flags')) is not None:
        return result

    cbi_flag_full_names = [flag.get('full_name') for flag in CBI_FEATURE_FLAGS]

    try:
        response = make_get_request(f"{FEATURE_MULTI_FLAG_URL}{' OR '.join([f'name~{item}' for item in cbi_flag_full_names])}")
        if response and response.ok:
            cbi_flags = [{'id': item['name'], 'value': item['value']} for item in response.json()['data']['values']]
        else:
            raise Exception('Fail to get data')
    except (
        requests.exceptions.ConnectTimeout,
        requests.exceptions.ReadTimeout,
        requests.exceptions.ConnectionError,
    ) as e:
        raise Exception(f"Could not retrieve CBI feature flags status, output: {e}")
    except Exception as e:
        raise Exception(f"Exception requesting CBI feature flags: {e}")

    _appliance_flag_cache.set('_all_cbi_flags', cbi_flags, ttl=FEATURE_FLAG_CACHE_TTL)

    return cbi_flags


def get_appliance_flag(flag: str, default=None, cache_ttl: int = FEATURE_FLAG_CACHE_TTL, **kwargs):
    """
    Gets the value of a feature flag from the colossus feature flag API, not VUI SQL
    """
    flag_value = default if default is not None else False

    if CLOUD_ENV and not DEFAULT_REGISTRY.flag_enabled(Flags.cloudbridge):
        return flag_value

    if cache_ttl and ((val := _appliance_flag_cache.get(flag)) is not None):
        return val

    # We recover from different problems like timeouts, etc
    # so we generate only WARNING logs
    try:
        response = make_get_request(FEATURE_FLAG_URL + flag)
        if response.ok:
            flag_value = response.json()['data']['value']
        elif response.status_code == 404 and default is not None:
            LOG.warning(
                'Feature flag not initialized; initializing flag with default value',
                extra={'flag_name': flag, 'default_value': default},
            )
            set_result = set_flag(flag, default, **kwargs)
            if set_result is False:
                LOG.warning(
                    'Failed to initialize flag with default value',
                    extra={'flag_name': flag, 'default_value': default},
                )

            return default
        else:
            LOG.warning(
                'Could not retrieve feature flag; returning default value',
                extra={'flag_name': flag, 'default_value': flag_value},
            )
    except (
        requests.exceptions.ConnectTimeout,
        requests.exceptions.ReadTimeout,
        requests.exceptions.ConnectionError,
    ) as e:
        LOG.warning(f"Could not retrieve feature flag status, output: {e}")
    except Exception as e:
        LOG.warning(f"Exception requesting feature flag: {e}")

    if cache_ttl and flag_value is not None:
        _appliance_flag_cache.set(flag, flag_value, cache_ttl)

    return flag_value


def set_flag(flag, value, **kwargs):
    if CLOUD_ENV and not DEFAULT_REGISTRY.flag_enabled(Flags.cloudbridge):
        return False
    data = {"value": value, **kwargs}
    try:
        response = make_put_request(FEATURE_FLAG_URL + flag, json=data)
        response.raise_for_status()
    except Exception as e:
        LOG.exception(f"Exception setting feature flag: {e}")
        return False

    return True
