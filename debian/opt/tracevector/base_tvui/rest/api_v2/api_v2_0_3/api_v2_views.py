# Copyright (c) 2017-2022 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

"""
api_v2_views.py contains V2 API views that are implemented with the django-rest-framework.

---------------------------------
WARNING: THIS IS A READONLY FILE!

No new functionality or bugfixes
should be added to this file.

Import statements can be updated.
---------------------------------
"""
from typing import List, Dict

import base64
import hashlib
import ipaddress
import json
import copy
import re
from collections import defaultdict, OrderedDict
from datetime import datetime, date, timedelta
from itertools import chain
from functools import reduce
from urllib.parse import urljoin

import pytz
from calendar import monthrange
from dateutil import parser
from django.db.models import Q, Max, Min, Prefetch, F
from django.http import JsonResponse, HttpResponse
from django.http.response import HttpResponseForbidden
from django.utils.html import escape
from django.core.exceptions import ObjectDoesNotExist, PermissionDenied
from django.core.exceptions import ValidationError as DjangoValidationError
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django.contrib.auth.hashers import check_password
from django.contrib.sessions.backends.db import SessionStore
from django.contrib.sessions.models import Session
from rest_framework import generics, mixins, filters, permissions, status
from rest_framework.decorators import api_view
from rest_framework.exceptions import NotFound
from rest_framework.permissions import AllowAny
from rest_framework.renderers import JSONRenderer
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers, viewsets
from taggit.models import Tag
from base_tvui.feature_flipper import view_requires_flag, Flags, flag_enabled
from base_tvui.lib_account import get_account_access
from base_tvui.lib_entity import get_host_access
from base_tvui.lib_ntv import NTVStats
from base_tvui.lib_notes import NotesHandler, get_note_modified_gte
from base_tvui.providers import dam_client
from base_tvui.providers.lc39_helpers import is_cloud
from base_tvui.detect_license_utils import get_aggregate_stats
from base_tvui import validator_utils, string_utils, lib_tv, lib_tagging, custom_errors, audit_factory, couch_utils, lib_host_role
from base_tvui.rest.api_v2.api_v2_0_3.api_v2_serializers import (
    DetectionSerializerV2,
    DetectionSerializerV2_1ForElasticSearch,
    DetectionSerializerV2_2,
    HostSerializerV2,
    RuleSerializerV2,
    RuleSerializerV2_1,
    CampaignSerializerV2,
    SubnetSerializerV2,
    UserSerializerV2,
    GroupSerializerV2,
    AccountSerializerV2,
    AccountSerializerV2_2,
    AccountSerializerV2_2ForElasticSearch,
    NoteSerializerV2_2,
)
from base_tvui.rest.api_v2.api_v2_0_3.api_v2_metadata import (
    DetectionsMetadataV2,
    GroupMetadataV2,
    HostsMetadataV2,
    HostMetadataV2,
    RuleMetadataV2,
    UserMetadataV2,
    AccountMetadataV2,
)
from base_tvui.rest.views import SettingsGroupBaseAPI
from base_tvui.rest.custom_filter import AliasedOrderingFilter
from base_tvui.rest.custom_authentication import VUITokenAuthentication, ApplianceApiClientAuthentication
from base_tvui.rest.custom_exceptions import InvalidQueryParams, InvalidQueryParamValues
from base_tvui.rest.custom_permissions import RoleBasedPermission, VadminOnlyPermission
from base_tvui.rest.pagination import StandardResultSetPagination
from base_tvui.rest.api_v2.api_v2_0_3.api_v2_utils import versioned_reverse
from base_tvui.lib_tv import get_audit_user_data
from base_tvui.lib_tv import get_couch_traffic, get_couch_traffic_stats, str2bool
from base_tvui.lib_vsupport import get_vsupport_api
from base_tvui.models_utils import get_category_to_privilege_level
from base_tvui.platform_api import PlatformClient, SyslogConfig, KafkaConfig
from base_tvui.settings import PLATFORM_REST_URL, FULL_COUCH_LOCATION
from base_tvui.string_utils import keys_to_snake_case
from base_tvui.lib_host_context import get_all_host_context, get_watchmen_networkx_learnings, get_shell_knocker_ports, get_host_ldap_context
from base_tvui.host_api import HostAttributeAPI

from tvui.campaigns.campaign_api_processor import CampaignAPIProcessor
from tvui.campaigns.campaign_views import CampaignBase
from tvui import customer_health
from tvui.detections.detection_orchestrator import DetectionOrchestrator
from tvui.detection_views import get_related_detection_campaigns
from tvui.detections.detection_utils import get_pcap_filename, serve_pcap_file
from tvui.helpers import HEADERS, DETECTION_API_NAME_TO_COL_NAME, DET_CATEGORY_VNAME_TO_CATEGORY, DET_VNAME_CAT_TO_TYPE, EDR_ARTIFACT_TYPES
from tvui.host_views import get_related_host_campaigns
from tvui.account_views import get_all_account_context
from tvui.models import (
    Account,
    ApiClientCredentials,
    Assignment,
    AssignmentOutcome,
    LinkedAccount,
    host,
    detection,
    smart_rule,
    Campaign,
    host,
    User,
    AuthenticationProfile,
    LDAPProfile,
    GroupCollection,
    AccountLockdownQueue,
    HostLockdownQueue,
    HealthSubject,
    notes,
    UserSession,
    host_session,
    HostRole,
)
from tvui.proxy_views import ProxyListView, ProxyDetailView
from tvui.qa.views import NotificationSetting
from tvui.settings_group import AuditLoggerSettings
from tvui.threat_feed_views import ThreatFeedList, ThreatFeedDetail, ThreatFeedFileDownload
from tvui.triage_helpers import (
    SR_OPTIONAL_FIELDS,
    SR_GEN_FIELDS_MAP,
    SR_GEN_API_NAME_TO_COL,
    convert_to_triage_legacy,
    convert_to_triage_nme,
)
from tvui.triage.lib_triage import TriageFacilitator
from tvui.triage.triage_endpoints import AppAllTriageFiltersEndpoint
from tvui.group_collections.config import API_MEMBER_LIMIT
from tvui.group_collections.lib_group_collection import GroupCollectionFacilitator
from tvui.group_collections.host_groups.lib_host_group import HostGroupsFacilitator
from tvui.group_collections.external_domain_groups.lib_external_domain_group import ExternalDomainGroupsFacilitator
from tvui.group_collections.ip_groups.lib_ip_group import IPGroupsFacilitator
from tvui.group_collections.host_groups.host_group_api_processor import HostGroupAPIProcessor
from tvui.group_collections.external_domain_groups.external_domain_group_api_processor import ExternalDomainGroupAPIProcessor
from tvui.group_collections.ip_groups.ip_group_api_processor import IPGroupAPIProcessor
from tvui.account_views import AccountPermissionView
from tvui.assignment.serializers import AssignmentSerializer, AssignmentOutcomeSerializer, ResolveParamSerializer
from tvui.settings.cognito_views import CognitoConfigView
from rest_framework.serializers import ValidationError
from base_tvui.rest.api_v3 import api_v3_utils
from base_tvui.lib_migrate_detection_names import OLD_VNAME_MAPPINGS
from base_tvui.lib_cloudbridge import make_get_request
from tvui.group_collections.group_ui_processors import HostGroupUIProcessor

from pure_utils import log_utils, cloud_conn
from pure_utils.lookup_mac_vendor import get_mac_vendor
from tvui.assignment import lib_assignment
import urllib.parse

"""
------------------------------------------
WARNING: NO MODIFICATIONS BELOW THIS LINE!
------------------------------------------
"""

LOG = log_utils.get_vectra_logger(__name__)
AUDIT = audit_factory.get_audit_object()
MAX_ACC_HOST_ACCESS_HISTORY_LENGTH = 10


def parse_time_string(ts):
    """Parses a string and returns a Datetime object, sets to UTC
    if no timezone specified.  Returns None if not a valid time string.
    """
    try:
        if isinstance(ts, str):
            parsed_ts = parser.parse(ts)
            if not parsed_ts.tzinfo:
                parsed_ts.replace(tzinfo=pytz.UTC)
            return parsed_ts
    except Exception:
        LOG.error('Failed to parse invalid timestamp string: "%s".  Validate params before parsing', ts)


def parse_tz_offset(offset):
    """
    Parses a string and returns a timezone offset if valid. Returns None if not.
    """
    if offset:
        if not (offset.startswith('-') or offset.startswith('+')):
            offset = f'+{str(offset).strip()}'
        return datetime.strptime(offset, '%z')
    return None


def supplement_campaigns(campaigns):
    """Supplements campaign information from campaign_views for detection/host endpoints"""
    full_campaign_info = []
    cbp = CampaignBase()
    for comp in campaigns:
        try:
            cp = Campaign.objects.get(id=comp['id'])
            campaign_stats = cbp.get_campaign_data(cp)
            full_campaign_info.append(
                {
                    'last_timestamp': campaign_stats['lastActivity'],
                    'duration': campaign_stats['duration'],
                    'num_hosts': comp['internalHosts'],
                    'num_detections': comp['detections'],
                    'id': comp['id'],
                    'name': comp['name'],
                }
            )
        except Exception:
            pass
    return full_campaign_info


def clear_requested_fields(fields, serialized_data):
    """Clear fields that were not requested"""
    for serialized_data_field in list(serialized_data.keys()):
        if serialized_data_field not in fields:
            serialized_data.pop(serialized_data_field)
    return serialized_data


class APIV2ListView(generics.ListAPIView):
    """
    Base class for ListAPIView for API V2
    """

    authentication_classes = (
        VUITokenAuthentication,
        ApplianceApiClientAuthentication,
    )
    permission_classes = (permissions.IsAuthenticated, RoleBasedPermission)

    def dispatch(self, request, *args, **kwargs):
        """
        Identify if the request is from 127.0.0.1 (localhost). This will allow the endpoint
        to determine whether to include url data in the response.
        """
        if request.META.get('REMOTE_ADDR') == '127.0.0.1':
            kwargs['internal'] = True

        return super(APIV2ListView, self).dispatch(request, *args, **kwargs)


class APIV2RetrieveView(generics.RetrieveAPIView):
    """
    Base class for RetrieveAPIView for API V2
    """

    authentication_classes = (
        VUITokenAuthentication,
        ApplianceApiClientAuthentication,
    )
    permission_classes = (permissions.IsAuthenticated, RoleBasedPermission)

    def dispatch(self, request, *args, **kwargs):
        """
        Identify if the request is from 127.0.0.1 (localhost). This will allow the endpoint
        to determine whether to include url data in the response.
        """
        if request.META.get('REMOTE_ADDR') == '127.0.0.1':
            kwargs['internal'] = True

        return super(APIV2RetrieveView, self).dispatch(request, *args, **kwargs)


class APIV2GenericView(generics.GenericAPIView):
    authentication_classes = (
        VUITokenAuthentication,
        ApplianceApiClientAuthentication,
    )
    permission_classes = (permissions.IsAuthenticated, RoleBasedPermission)


class APIV2Root(APIView):
    authentication_classes = (VUITokenAuthentication, ApplianceApiClientAuthentication)
    permission_classes = (permissions.IsAuthenticated,)

    def routes(self, request, fmt=None):
        """
        The entry endpoint of our API V2
        """
        routes = {
            'detections': versioned_reverse('api-detections-v2', request=request, format=fmt),
            'rules': versioned_reverse('api-rules-v2', request=request, format=fmt),
            'hosts': versioned_reverse('api-hosts-v2', request=request, format=fmt),
            'groups': versioned_reverse('api-groups-v2', request=request, format=fmt),
            'threat_feeds': versioned_reverse('api-feeds-v2', request=request, format=fmt),
            'audits': versioned_reverse('api-audits-v2', request=request, format=fmt),
        }
        return Response(routes)

    def get(self, request, fmt=None):
        return self.routes(request, fmt)

    def options(self, request):
        return self.routes(request)


class QueryParameterValueValidationMixin(object):
    """
    Mixin that validates URL query parameters for an endpoint
    """

    is_accepted_bool_val = lambda self, b: b in frozenset(['True', 'False', 'true', 'false', '1', '0'])
    is_greater_than_zero = lambda self, n: int(n) > 0
    is_greater_than_or_equal_zero = lambda self, n: int(n) >= 0
    is_non_empty_str = lambda self, s: isinstance(s, str) and bool(s)
    is_valid_ip = lambda self, ip: validator_utils.validate_ip(ip)
    is_valid_timestamp = lambda self, timestamp: validator_utils.validate_timestamp(timestamp)

    _bool_params = ()
    _choices_params = {}
    _gt_zero_params = ()
    _gte_zero_params = ()
    _ip_params = ()
    _list_params = ()
    _str_params = ()
    _timestamp_params = ()
    _int_list_params = ()
    _tz_offset_params = ()

    MAX_LIST_LENGTH = 5000

    def get_valid_ordering_params(self):
        return [field if isinstance(field, str) else field[0] for field in self.ordering_fields]

    def _is_int(self, param_value):
        try:
            int(param_value)
            return True
        except ValueError:
            return False

    def _is_list_valid(self, param_value):
        if isinstance(param_value, str) and bool(param_value):
            split_list = param_value.split(',')
        else:
            return False
        if len(split_list) > self.MAX_LIST_LENGTH:
            return False
        return True

    def _is_valid_choice(self, param, param_value):
        if param in ('fields', 'exclude_fields', 'ordering'):
            param_values = param_value.split(',')
        else:
            param_values = [param_value]
        valid_values = self._choices_params[param]
        invalid_values = [value for value in param_values if value not in valid_values]
        return invalid_values == []

    def _is_valid_ordering(self, ordering):
        requested_ordering_fields = ordering.split(',')
        requested_ordering_fields = [field.lstrip('-') for field in requested_ordering_fields]
        invalid_ordering_fields = [field for field in requested_ordering_fields if field not in self.get_valid_ordering_params()]
        return invalid_ordering_fields == []

    def _valid_choices(self, param):
        return self._choices_params[param]

    def _is_valid_page_size(self, page_size):
        if self._is_int(page_size):
            return self.is_greater_than_zero(page_size)
        else:
            return page_size == 'all'

    def _is_valid_int_list(self, param):
        if param.isnumeric():
            return True
        return all(x.isnumeric() for x in param.split(','))

    def _is_valid_tz_offset(self, param):
        if not param.startswith('-'):
            param = f'+{str(param).strip()}'
        try:
            datetime.strptime(param, '%z')
            return True
        except Exception:
            return False

    def _validate_query_param_values(self):
        validation_checks = OrderedDict(
            [
                (
                    'int',
                    {
                        'params': chain(self._gt_zero_params, self._gte_zero_params),
                        'validator': self._is_int,
                        'error_message': "Invalid value given for parameter '{param}'. Must be an integer.",
                    },
                ),
                (
                    'list',
                    {
                        'params': self._list_params,
                        'validator': self._is_list_valid,
                        'error_message': "Invalid value given for parameter '{param}'."
                        + f' Must be non-empty comma seperated list with at most {self.MAX_LIST_LENGTH} values.',
                    },
                ),
                (
                    'bool',
                    {
                        'params': self._bool_params,
                        'validator': self.is_accepted_bool_val,
                        'error_message': "Invalid value given for parameter '{param}'. Must be one of: 'True', 'False', 'true', 'false', '1', '0'.",
                    },
                ),
                (
                    'gt zero',
                    {
                        'params': self._gt_zero_params,
                        'validator': self.is_greater_than_zero,
                        'error_message': "Invalid value given for parameter '{param}'. Must be greater than zero.",
                    },
                ),
                (
                    'gte zero',
                    {
                        'params': self._gte_zero_params,
                        'validator': self.is_greater_than_or_equal_zero,
                        'error_message': "Invalid value given for parameter '{param}'. Must be greater than or equal to zero.",
                    },
                ),
                (
                    'str',
                    {
                        'params': self._str_params,
                        'validator': self.is_non_empty_str,
                        'error_message': "Empty string given for parameter '{param}'. Must be non-empty.",
                    },
                ),
                (
                    'ip',
                    {
                        'params': self._ip_params,
                        'validator': self.is_valid_ip,
                        'error_message': "Invalid value given for parameter '{param}'. Must be a valid IP.",
                    },
                ),
                (
                    'choice',
                    {
                        'params': self._choices_params,
                        'validator': self._is_valid_choice,
                        'error_message': "Invalid value given for paramater '{param}'. Valid values: {valid}.",
                        'valid': self._valid_choices,
                    },
                ),
                (
                    'timestamp',
                    {
                        'params': self._timestamp_params,
                        'validator': self.is_valid_timestamp,
                        'error_message': "Invalid value given for parameter '{param}'. Must be a valid timestamp: 2017-12-31T16:55:50Z.",
                    },
                ),
                (
                    'page size',
                    {
                        'params': [StandardResultSetPagination.page_size_query_param],
                        'validator': self._is_valid_page_size,
                        'error_message': "Invalid value given for parameter '{param}'. Must be an integer greater than zero or 'all'.",
                    },
                ),
                (
                    'ordering',
                    {
                        'params': ['ordering'],
                        'validator': self._is_valid_ordering,
                        'error_message': "Invalid field(s) requested for '{param}'. Valid ordering fields: {valid}.",
                        'valid': tuple(self.get_valid_ordering_params()) if hasattr(self, 'ordering_fields') else None,
                    },
                ),
                (
                    'int_list',
                    {
                        'params': self._int_list_params,
                        'validator': self._is_valid_int_list,
                        'error_message': "Invalid value given for parameter '{param}'. Must be an integer or list of integers (i.e. 1,2,3)",
                    },
                ),
                (
                    'tz_offset',
                    {
                        'params': self._tz_offset_params,
                        'validator': self._is_valid_tz_offset,
                        'error_message': "Invalid value given for parameter '{param}'. Expected to be in the ISO 8601 Format: +HHMM or -HHMM",
                    },
                ),
            ]
        )
        errors = {}
        for _, check in validation_checks.items():
            errors.update(self._check_validation(errors, **check))

        return errors

    def _check_validation(self, current_errors, params, validator, error_message, valid=None):
        errors = {}
        for param in params:
            param_value = self.request.query_params.get(param)
            if param_value is not None and param not in current_errors:
                v_argcount = validator.__code__.co_argcount
                is_valid = validator(param, param_value) if v_argcount == 3 else validator(param_value)
                if not is_valid:
                    valid_values = valid(param) if callable(valid) else valid
                    errors[param] = error_message.format(param=param, valid=valid_values)
        return errors

    def _validate_query_params(self, request_query_params, valid_query_params):
        bad_query_params = [param for param in request_query_params if param not in valid_query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        bad_query_param_values = self._validate_query_param_values()
        if bad_query_param_values:
            raise InvalidQueryParamValues(bad_query_param_values)


class BodyParameterValueValidationMixin(object):
    """
    Mixin that validates parameters sent in the body of PATCH/POST requests for an endpoint
    """

    is_valid_cidr_ip = lambda self, cidr_ip: validator_utils.validate_cidr_ip(cidr_ip)
    is_valid_tag = lambda self, tag: validator_utils.validate_tag(tag)

    _cidr_ip_list_params = ()
    _tag_list_params = ()

    def _validate_body_param_values(self):
        validation_checks = {
            'cidr_ip': {
                'params': self._cidr_ip_list_params,
                'validator': self.is_valid_cidr_ip,
                'error_message': 'Invalid value(s) {values} given in parameter {param}. Must only contain valid CIDR IPs.',
            },
            'tag': {
                'params': self._tag_list_params,
                'validator': self.is_valid_tag,
                'error_message': 'Invalid value(s) {values} given in parameter {param}. Must only contain valid tags.',
            },
        }
        errors = {}
        for check in validation_checks.values():
            errors.update(self._check_list_validation(**check))

        return errors

    def _check_list_validation(self, params, validator, error_message):
        errors = {}
        for param in params:
            invalid_objects = []
            param_values = self.request.data.get(param)
            if param_values is not None:
                for value in param_values:
                    if not validator(value):
                        invalid_objects.append(value)
                if invalid_objects:
                    errors[param] = error_message.format(values=invalid_objects, param=param)
        return errors


# Some of these endpoints are used by VUI to populate elasticsearch
# Should this decorator allow requests from localhost to bypass?
class DetectionsV2(APIV2ListView, QueryParameterValueValidationMixin):
    """
    V2 API endpoint that represents a list of detections
    """

    view_permission = 'detection'
    edit_permission = 'triage'

    allowed_methods = ('GET', 'OPTIONS', 'PATCH')
    renderer_classes = (JSONRenderer,)
    serializer_class = DetectionSerializerV2
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    ordering_fields = ('created_datetime', 'last_timestamp', 't_score', 'c_score', 'id')
    ordering = ('-last_timestamp',)
    metadata_class = DetectionsMetadataV2

    # fields added by view
    added_fields = ['grouped_details', 'summary']

    _query_params = (
        'category',
        'certainty',
        'certainty_gte',
        'c_score',
        'c_score_gte',
        'description',
        'detection',
        'detection_type',
        'detection_category',
        'exclude_fields',
        'fields',
        'host_id',
        'last_timestamp',
        'last_timestamp_gte',
        'min_id',
        'max_id',
        'ordering',
        'note_modified_timestamp_gte',
        'src_ip',
        'state',
        'tags',
        'targets_key_asset',
        'is_targeting_key_asset',
        'threat',
        'threat_gte',
        'is_triaged',
        't_score',
        't_score_gte',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
    )
    _bool_params = ('targets_key_asset', 'is_targeting_key_asset', 'is_triaged')
    _choices_params = {
        'fields': list(DetectionSerializerV2.Meta.fields) + added_fields,
        'exclude_fields': list(DetectionSerializerV2.Meta.fields),
    }
    _gt_zero_params = ('min_id', 'max_id', 'host_id', StandardResultSetPagination.page_query_param)
    _gte_zero_params = ('c_score', 'c_score_gte', 'certainty', 'certainty_gte', 't_score', 't_score_gte', 'threat', 'threat_gte')
    _ip_params = ('src_ip',)
    _str_params = (
        'category',
        'description',
        'detection',
        'detection_type',
        'detection_category',
        'fields',
        'ordering',
        'src_ip',
        'state',
        'tags',
        StandardResultSetPagination.page_size_query_param,
    )
    _timestamp_params = ('last_timestamp_gte', 'last_timestamp', 'note_modified_timestamp_gte')

    def _process_response(self, det_data):
        """
        Process data for any response, overwrite as needed
            - Currently only used in v2.3
        """
        return det_data

    def _process_internal_response(self, det_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
        """
        for url_field in ('url', 'detection_url'):
            det_data.pop(url_field, None)
        if det_data.get('src_host'):
            det_data['src_host'].pop('url', None)
        for detail in det_data.get('grouped_details', []):
            detail.pop('grouping_field', None)
        det_data.pop('t_score', None)
        det_data.pop('c_score', None)

        return det_data

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        queryset = (
            detection.objects.all()
            .select_related('smart_rule', 'host_session__host')
            .prefetch_related('host_details', 'account_details', 'tags')
            .prefetch_primary_accounts()
            .prefetch_recent_details()
        )

        # Detection
        min_id = self.request.query_params.get('min_id')
        max_id = self.request.query_params.get('max_id')
        state = self.request.query_params.get('state')
        type_vname = self.request.query_params.get('detection_type') or self.request.query_params.get('detection')
        category = self.request.query_params.get('detection_category') or self.request.query_params.get('category')
        description = self.request.query_params.get('description')
        src_ip = self.request.query_params.get('src_ip')
        t_score = self.request.query_params.get('threat') or self.request.query_params.get('t_score')
        t_score_gte = self.request.query_params.get('threat_gte') or self.request.query_params.get('t_score_gte')
        c_score = self.request.query_params.get('certainty') or self.request.query_params.get('c_score')
        c_score_gte = self.request.query_params.get('certainty_gte') or self.request.query_params.get('c_score_gte')
        last_timestamp = parse_time_string(self.request.query_params.get('last_timestamp'))
        last_timestamp_gte = parse_time_string(self.request.query_params.get('last_timestamp_gte'))
        host_id = self.request.query_params.get('host_id')
        tags = self.request.query_params.get('tags')
        targets_key_asset = self.request.query_params.get('is_targeting_key_asset') or self.request.query_params.get('targets_key_asset')
        note_ts_gte = parse_time_string(self.request.query_params.get('note_modified_timestamp_gte'))
        is_triaged = self.request.query_params.get('is_triaged')

        if min_id is not None:
            queryset = queryset.filter(id__gte=min_id)
        if max_id is not None:
            queryset = queryset.filter(id__lte=max_id)
        if state is not None:
            queryset = queryset.filter(state=state)
        if type_vname is not None:
            queryset = queryset.filter(type_vname__icontains=type_vname)
        if category is not None:
            queryset = queryset.filter(category__icontains=category)
        if description is not None:
            queryset = queryset.filter(description__icontains=description)
        if src_ip is not None:
            queryset = queryset.filter(src_ip__icontains=src_ip)
        if t_score is not None:
            queryset = queryset.filter(t_score=t_score)
        if t_score_gte is not None:
            queryset = queryset.filter(t_score__gte=t_score_gte)
        if c_score is not None:
            queryset = queryset.filter(c_score=c_score)
        if c_score_gte is not None:
            queryset = queryset.filter(c_score__gte=c_score_gte)
        if last_timestamp is not None:
            queryset = queryset.filter(last_timestamp=last_timestamp)
        if last_timestamp_gte is not None:
            queryset = queryset.filter(last_timestamp__gte=last_timestamp_gte)
        if host_id is not None:
            queryset = queryset.filter(host_session__host_id=host_id)
        if targets_key_asset is not None:
            targets_key_asset = targets_key_asset in ('True', 'true', '1')
            queryset = queryset.filter(targets_key_asset=targets_key_asset)
        if tags is not None:
            tag_qs = Tag.objects.filter(reduce(lambda x, y: x | y, [Q(name__icontains=t) for t in tags.split(',')]))
            queryset = queryset.filter(tags__in=tag_qs)
        if note_ts_gte is not None:
            id_values = get_note_modified_gte('detection', note_ts_gte)
            queryset = queryset.filter(id__in=id_values)
        if is_triaged is not None:
            is_triaged = is_triaged in ('True', 'true', '1')

            # a triaged detection will have a smart rule aka smart_rule is not null
            smart_rule_isnull = not is_triaged
            queryset = queryset.filter(smart_rule__isnull=smart_rule_isnull)

        # Don't return triaged detections on a threat>0 search, even if threat hasn't yet been zeroed.
        for param in (t_score, t_score_gte, c_score, c_score_gte):
            if param is not None and int(param) > 0:
                queryset = queryset.filter(smart_rule__isnull=True)
                break

        # Exclude account based detections until API v2.1
        if self.request.api_version < 2.1:
            queryset = queryset.exclude(account_id__isnull=False)
        return queryset.distinct()

    @staticmethod
    def _score_triaged_detections(detections):
        """
        This is to ensure that the Detections API immediately shows
        threat and certainty have 0 for triaged detections.
        """
        for det in detections:
            if det.smart_rule:
                det.c_score = 0
                det.t_score = 0
        return detections

    @classmethod
    def _cache_on_detections(cls, page: List[detection]):
        detection.cache_notes(page)
        detection.cache_network_sensor_aliases(page)
        LinkedAccount.cache_assignments([det.account.linked_account for det in page if det.account and det.account.linked_account])
        host.cache_assignments([det.host for det in page if det.host])

    def list(self, request, *args, **kwargs):
        det_queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(det_queryset)
        internal = kwargs.get('internal', False)

        if page is not None:
            page = self._score_triaged_detections(page)
            self._cache_on_detections(page)

            serializer = self.get_serializer(page, many=True)

            detection_data = []

            fields = request.query_params.get('fields')
            if fields:
                fields = fields.split(',')

            for det, serializer_data in zip(serializer.instance, serializer.data):
                serialized_data = DetectionOrchestrator(det, serializer_data).get_detection_data(destination="api")
                if fields:
                    for serialized_data_field in list(serialized_data.keys()):
                        if serialized_data_field not in fields:
                            serialized_data.pop(serialized_data_field)

                if internal:
                    serialized_data = self._process_internal_response(serialized_data)

                serialized_data = self._process_response(serialized_data)

                detection_data.append(serialized_data)

            return self.get_paginated_response(detection_data)

        self._score_triaged_detections(det_queryset)
        serializer = self.get_serializer(det_queryset, many=True)
        return Response(serializer.data)

    def patch(self, request, *args, **kwargs):
        """Toggle the 'fixed' state of detections

        Data Attributes:
            detectionIdList (list): A list of detection ids we want to mark/unmark as fixed.
            mark_as_fixed (str): 'true' if we want to mark all detections as fixed.
                                 'false' if we want to unmark all detections as fixed.
        """
        status = 422
        errors = defaultdict(list)
        level = 'error'
        message = 'Error'
        if 'detectionIdList' in request.data:
            detection_id_list = request.data.get('detectionIdList')

            success, error = validator_utils.validate_detection_id_list(detection_id_list)
            if not success:
                errors['detectionIdList'].append(error)
                return JsonResponse({'errors': errors, '_meta': {'level': 'error'}}, status=status)
            LOG.info(f"PATCH detection request received for ids: {detection_id_list}")
            det_queryset = detection.objects.filter(id__in=detection_id_list)
            if validator_utils.validate_str_is_bool(request.data.get('mark_as_fixed', '')):
                mark_as_fixed = str2bool(request.data.get('mark_as_fixed'))

                if det_queryset.count() > 0:
                    # Validate requested detections actually need to update. Return if they are already in requested state
                    det_queryset = validator_utils.validate_detection_list_states(det_queryset, mark_as_fixed)
                    if det_queryset.count() == 0:
                        return JsonResponse({'_meta': {'level': 'Success', 'message': 'Successfully marked detections'}}, status=200)

                tf = TriageFacilitator(request, 'api')
                affected_ids = tf.mark_as_fixed(det_queryset) if mark_as_fixed else tf.unmark_as_fixed(det_queryset)
                new_state = detection.FIXED if mark_as_fixed else detection.ACTIVE
                event_action = audit_factory.EventActions.MARKED_AS_FIXED if mark_as_fixed else audit_factory.EventActions.UNMARKED_AS_FIXED
                event = {
                    'action': event_action,
                    'object': audit_factory.EventObjects.DETECTION,
                    'data': {'detections': list({'id': id} for id in det_queryset.values_list('id', flat=True))},
                }

                if not affected_ids:
                    AUDIT.audit(
                        get_audit_user_data(request),
                        audit_factory.Result.FAILURE,
                        'Detection IDs {} have failed to be marked as {}'.format(detection_id_list, new_state),
                        event,
                    )
                    return JsonResponse(
                        {'_meta': {'level': 'errors', 'message': 'Failed to mark detections: no valid detection ids provided'}},
                        status=404,
                    )

                AUDIT.audit(
                    get_audit_user_data(request),
                    audit_factory.Result.SUCCESS,
                    'Detection IDs {} have been marked as {}'.format(detection_id_list, new_state),
                    event,
                )
                return JsonResponse({'_meta': {'level': 'Success', 'message': 'Successfully marked detections'}}, status=200)
            else:
                errors['mark_as_fixed'].append("mark_as_fixed is required")
                return JsonResponse({'errors': errors, '_meta': {'level': 'error'}}, status=status)

        else:
            errors['detectionIdList'].append("detectionIdList is required")
            return JsonResponse({'errors': errors, '_meta': {'level': 'error'}}, status=status)


class DetectionsV2_2(DetectionsV2):
    """
    V2.2 API endpoint that represents a list of detections
    """

    serializer_class = DetectionSerializerV2_2


class DetectionsV2_3(DetectionsV2_2):
    """
    V2.3 API endpoint that represents a list of detections
    """

    def _process_response(self, det_data):
        """
        Process data for any response
            - Rename grouped_details[].src_hosts to dst_hosts for Suspicious Remote Desktop
        """
        super(DetectionsV2_3, self)._process_response(det_data)
        if det_data.get('detection') == 'Suspicious Remote Desktop':
            for grouped_detail in det_data.get('grouped_details'):
                grouped_detail['dst_hosts'] = grouped_detail.pop('src_host', [])
        return det_data


class DetectionV2(APIV2RetrieveView, QueryParameterValueValidationMixin):
    """
    V2 API endpoint that represents a single detection
    """

    view_permission = 'detection'
    edit_permission = 'note'

    allowed_methods = ('GET', 'OPTIONS', 'PATCH')
    renderer_classes = (JSONRenderer,)
    # fields added by view
    added_fields = ['campaign_summaries', 'grouped_details', 'summary', 'is_triaged', 'src_linked_account']

    _query_params = ('fields', 'exclude_fields')
    _choices_params = {
        'fields': list(DetectionSerializerV2.Meta.fields) + added_fields,
        'exclude_fields': list(DetectionSerializerV2.Meta.fields),
    }
    _str_params = ('fields',)

    def _process_response(self, det_data):
        """
        Process data for any response, overwrite as needed
            - Currently only used in v2.3
        """
        return det_data

    def _process_internal_response(self, det_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
            - Add additional triage filter data
        """
        det = self.get_object()
        for url_field in ('url', 'detection_url'):
            det_data.pop(url_field, None)
        if det_data.get('src_host'):
            det_data['src_host'].pop('url', None)
        for detail in det_data.get('grouped_details', []):
            detail.pop('grouping_field', None)
        det_data.pop('t_score', None)
        det_data.pop('c_score', None)
        det_data['filtered_by_ai'] = det.is_filtered_by_ai
        det_data['filtered_by_user'] = det.is_filtered_by_user
        det_data['filtered_by_rule'] = det.is_filtered_by_rule
        return det_data

    def get_serializer_class(self):
        if self.request.query_params.get('src_linked_account'):
            return DetectionSerializerV2_1ForElasticSearch
        else:
            return DetectionSerializerV2

    def get_queryset(self):
        queryset = detection.objects.all()
        if self.request.META.get('REMOTE_ADDR') == '127.0.0.1':
            queryset = detection.pure.all()

        # Exclude account based detections until API v2.1
        if self.request.api_version < 2.1:
            queryset = queryset.exclude(account_id__isnull=False)

        return queryset

    def get(self, request, *args, **kwargs):
        self._validate_query_params(self.request.query_params.keys(), self._query_params + ('src_linked_account',))

        det = self.get_object()

        # This is to ensure that the Detections API immediately shows
        # threat and certainty have 0 for triaged detections.
        if det.smart_rule_id:
            det.c_score = 0
            det.t_score = 0

        serializer = self.get_serializer(det)
        serialized_data = DetectionOrchestrator(det, serializer.data).get_detection_data(destination="api")
        serialized_data['campaign_summaries'] = []

        serialized_data['is_triaged'] = bool(det.smart_rule_id)
        # we are doing this outside of the serializer because the api otherwise returns 'false' if 'triage_rule_id' is not included as a query parameter (APP-8059)

        fields = request.query_params.get('fields')
        if fields:
            fields = fields.split(',')
            serialized_data = clear_requested_fields(fields, serialized_data)

        if serialized_data.get('note') is not None:
            serialized_data['note'] = string_utils.unescape_html(serialized_data['note'])
        if 'campaign_summaries' in serialized_data:
            detection_campaigns = get_related_detection_campaigns(kwargs['pk'])
            serialized_data['campaign_summaries'] = supplement_campaigns(detection_campaigns)

        if kwargs.get('internal', False):
            serialized_data = self._process_internal_response(serialized_data)

        serialized_data = self._process_response(serialized_data)

        return Response(serialized_data)

    def patch(self, request, *args, **kwargs):
        """Update existing attributes of a detection object.  Use drf request.data for getting
        patch data.

        Data Attributes:
            note (str): overwrite note for object.
            note_id (int): optional note id to overwrite;
                            if not declared, oldest note is overwritten (or new note created)
            mark_as_fixed (str): 'true' if we want to mark object as fixed.
                                 'false' if we want to unmark object as fixed.
            triageAs (str): used for one-time triage of a detection
        """
        det = self.get_object()
        status = 422

        try:
            action = None
            event_data = None
            if request.data.get('note', ''):
                note_id = request.data.get('note_id', None)
                if note_id:
                    note_id = int(note_id)

                note_handler = NotesHandler('detection', det.id)

                _notes, num_notes = note_handler.get_notes()
                _note = None

                # escape special characters
                sanitized_note = escape(request.data.get('note'))

                if num_notes == 0:  # create a note if none exists
                    _note = note_handler.create_note(sanitized_note, request.user)
                    action = audit_factory.EventActions.CREATED
                    event_data = {'text': sanitized_note, 'note_id': note_id, 'detection_id': det.id}
                else:  # update note
                    # if note id is not declared, update oldest note
                    action = audit_factory.EventActions.UPDATED
                    if note_id is None:
                        note_id = min(_notes, key=lambda n: n['date_created'])['id']
                        note = next((x for x in _notes if x['id'] == note_id), None)
                    else:
                        # confirm this is the user's note or they have permissions to edit other users' notes
                        note = next((x for x in _notes if x['id'] == note_id), None)

                        if note is None:
                            return HttpResponse(status, status=404)

                        if note['created_by_id'] != request.user.id and not request.user.has_perm('edit_other_users_notes'):
                            return HttpResponse(status, status=403)

                    # only update note if content is different
                    if note['note'] != sanitized_note:
                        _note = note_handler.update_note(note_id, sanitized_note, request.user)
                        event_data = {'previous_text': note['note'], 'new_text': sanitized_note, 'note_id': note_id, 'detection_id': det.id}

                if _note:
                    event = {'action': action, 'object': audit_factory.EventObjects.DETECTION_NOTE, 'data': event_data}
                    AUDIT.audit(
                        get_audit_user_data(request),
                        audit_factory.Result.SUCCESS,
                        'note for detection@{det_id} updated to {note_text}...'.format(det_id=det.id, note_text=_note['note'][:10]),
                        event,
                    )

                status = 200

            tf = TriageFacilitator(request, 'api')

            if request.data.get('mark_as_fixed'):
                if validator_utils.validate_str_is_bool(request.data.get('mark_as_fixed')):
                    mark_as_fixed = str2bool(request.data.get('mark_as_fixed'))
                    det_queryset = detection.objects.filter(id=det.id)

                    tf.mark_as_fixed(det_queryset) if mark_as_fixed else tf.unmark_as_fixed(det_queryset)
                    new_state = detection.FIXED if mark_as_fixed else detection.ACTIVE
                    event_action = (
                        audit_factory.EventActions.MARKED_AS_FIXED if mark_as_fixed else audit_factory.EventActions.UNMARKED_AS_FIXED
                    )
                    event = {'action': event_action, 'object': audit_factory.EventObjects.DETECTION, 'data': {'id': det.id}}

                    AUDIT.audit(
                        get_audit_user_data(request),
                        audit_factory.Result.SUCCESS,
                        'Detection with ID {} has successfully been marked as {}'.format(det.id, new_state),
                        event,
                    )
                    status = 200

            if request.data.get('triageAs'):
                smart_category = request.data.get('triageAs')
                sc_success, sc_error = validator_utils.validate_smart_category(smart_category)
                if not sc_success:
                    AUDIT.audit(get_audit_user_data(request), True, '{}'.format(sc_error))
                    status = 422
                else:
                    output = tf.create_triage_singleton(det.id, smart_category)
                    if output['status_code'] == TriageFacilitator.STATUS_CODE_CREATED:
                        AUDIT.audit(
                            get_audit_user_data(request),
                            True,
                            'Successfully created triage filter for detection {}, triaged as {}'.format(det.id, smart_category),
                        )
                        status = 201

        except Exception:
            LOG.exception('Unexpected exception with request')
            status = 400
        return HttpResponse(status, status=status)


class DetectionV2_2(DetectionV2):
    """
    V2.2 API endpoint that represents a single detection
    """

    def get_serializer_class(self):
        return DetectionSerializerV2_2


class DetectionV2_3(DetectionV2_2):
    """
    V2.3 API endpoint that represents a single detection
    """

    def _process_response(self, det_data):
        """
        Process data for any response
            - Rename grouped_details[].src_hosts to dst_hosts for Suspicious Remote Desktop
        """
        super(DetectionV2_3, self)._process_response(det_data)
        if det_data.get('detection') == 'Suspicious Remote Desktop':
            for grouped_detail in det_data.get('grouped_details'):
                grouped_detail['dst_hosts'] = grouped_detail.pop('src_host', [])
        return det_data


class DetectionPCAPV2(APIV2RetrieveView):
    """
    V2 API endpoint that returns the master PCAP for a detection
    """

    permission = 'pcap'

    allowed_methods = ('GET',)
    queryset = detection.objects.all()

    def get(self, request, pk, *args, **kwargs):
        file_not_found_resp = Response({'status': 404, 'reason': 'File Not Found'}, status=status.HTTP_404_NOT_FOUND)
        try:
            filename = get_pcap_filename(request, pk)
            if not filename:
                return file_not_found_resp
        except Exception:
            return file_not_found_resp

        return serve_pcap_file(request, filename)


class DetectionSummaryMixin(object):
    """
    Mixin defining how detection summaries should be included for an API response
    """

    summary_removal = {'port_scan': ['dst_ports']}
    _api_version = 'v2'

    def generate_detection_summaries(self, request, obj, internal, limit=1000):
        detection_summaries = []
        for det in obj.detection_set.order_by('-last_timestamp')[:limit]:
            det_summary = {
                'detection_id': det.id,
                'detection_url': api_v3_utils.build_url(request, 'detections', det.id),
                'detection_type': det.type_vname,
                'detection_category': det.category,
                'is_targeting_key_asset': det.targets_key_asset,
                'state': det.state,
                'threat': det.t_score,
                'certainty': det.c_score,
                'is_triaged': bool(det.smart_rule_id),
                'tags': list(det.tags.names()),
                'summary': DetectionOrchestrator(det).get_detection_data(destination="api", summary_only=True).get('summary', {}),
                'assigned_to': det.assigned_to_username,
                'assigned_date': det.assigned_date if det.assigned_date else None,
            }
            if internal:
                det_summary['filtered_by_ai'] = det.is_filtered_by_ai
                det_summary['filtered_by_user'] = det.is_filtered_by_user
                det_summary['filtered_by_rule'] = det.is_filtered_by_rule

            if det.type in self.summary_removal:
                for field in self.summary_removal[det.type]:
                    det_summary['summary'].pop(field, None)
            detection_summaries.append(det_summary)

        return detection_summaries


class GroupsV2(APIV2ListView, QueryParameterValueValidationMixin):
    """V2 API for Managing Groups"""

    permission = 'groups'

    allowed_methods = ('GET', 'POST', 'OPTIONS')
    renderer_classes = (JSONRenderer,)
    serializer_class = GroupSerializerV2
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    ordering_fields = ('name',)
    ordering = ('-name',)

    _query_params = (
        'name',
        'type',
        'description',
        'last_modified_timestamp',
        'last_modified_by',
        'host_ids',
        'host_names',
        'domains',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
    )
    _str_params = ('name', 'description', 'type', 'last_modified_by', 'host_ids', 'host_names', 'domains')
    _timestamp_params = ('last_modified_timestamp',)

    _gt_zero_params = (StandardResultSetPagination.page_query_param,)
    _str_params = (StandardResultSetPagination.page_size_query_param,)

    def dispatch(self, request, *args, **kwargs):
        """
        Overwrite dispatch method to validate whether host_groups feature flag is enabled
        before fulfilling request.
        """
        if lib_tv.has_feature('host_groups'):
            return super(GroupsV2, self).dispatch(request, *args, **kwargs)

        return JsonResponse({}, status=404)

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        queryset = GroupCollection.objects.all()
        name = self.request.query_params.get('name')
        group_type = self.request.query_params.get('type')
        description = self.request.query_params.get('description')
        last_modified_timestamp = parse_time_string(self.request.query_params.get('last_modified_timestamp'))
        last_modified_by = self.request.query_params.get('last_modified_by')
        # HostGroup specific query params
        host_ids = self.request.query_params.get('host_ids')
        host_names = self.request.query_params.get('host_names')
        domains = self.request.query_params.get('domains')
        ips = self.request.query_params.get('ips')

        if name is not None:
            queryset = queryset.filter(name__icontains=name)
        if group_type is not None:
            queryset = queryset.filter(type__exact=group_type)
        if description is not None:
            queryset = queryset.filter(description__icontains=description)
        if last_modified_timestamp is not None:
            queryset = queryset.filter(last_modified_timestamp__gte=last_modified_timestamp)
        if last_modified_by is not None:
            queryset = queryset.filter(last_modified_by__username__exact=last_modified_by)
        if host_ids is not None:
            queryset = queryset.filter(hostgroup__hosts__in=host_ids.split(','))
        if host_names is not None:
            queryset = queryset.filter(hostgroup__hosts__name__in=host_names.split(','))
        if domains is not None:
            queryset = queryset.filter(externaldomaingroup__domains__icontains=domains)
        if ips is not None:
            queryset = queryset.filter(ipgroup__ips__icontains=ips)

        return queryset

    def list(self, request, *args, **kwargs):
        group_queryset = self.get_queryset()
        page = self.paginate_queryset(group_queryset)
        serializer = self.get_serializer(page, many=True)

        # Only API v3.4 will allow this query param, so this will be true for all other minor versions
        include_members = request.query_params.get('include_members', 'True') in ('True', 'true', 1)
        if include_members:
            for group_obj, serializer_data in zip(serializer.instance, serializer.data):
                if group_obj.type == GroupCollection.HOST:
                    hg = group_obj.hostgroup
                    host_list = hg.hosts.extra(select={'is_key_asset': 'key_asset'}).values('id', 'name', 'is_key_asset')
                    if api_v3_utils.is_dynamic_groups_api(request):
                        host_list = host_list[:API_MEMBER_LIMIT]
                    for h in host_list:
                        h['is_key_asset'] = bool(h['is_key_asset'])
                        h['url'] = api_v3_utils.build_url(request, 'hosts', h['id'])
                    serializer_data['members'] = host_list
                    # list the smart_rules with each host group
                    serializer_data['rules'] = hg.smart_rule_set.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                if group_obj.type == GroupCollection.DOMAIN:
                    edg = group_obj.externaldomaingroup
                    if api_v3_utils.is_dynamic_groups_api(request):
                        serializer_data['members'] = edg.domains[:API_MEMBER_LIMIT]
                    else:
                        serializer_data['members'] = edg.domains
                    serializer_data['cognito_managed'] = edg.family == GroupCollection.FAMILY_PREDEFINED
                    remote1_dns_rules = edg.remote1_dns_smart_rules.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                    remote2_dns_rules = edg.remote2_dns_smart_rules.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                    serializer_data['rules'] = list(chain(remote1_dns_rules, remote2_dns_rules))
                if group_obj.type == GroupCollection.IP:
                    ipg = group_obj.ipgroup
                    if api_v3_utils.is_dynamic_groups_api(request):
                        serializer_data['members'] = ipg.ips[:API_MEMBER_LIMIT]
                    else:
                        serializer_data['members'] = ipg.ips
                    serializer_data['cognito_managed'] = False
                    ipg_ip_rules = ipg.ip_smart_rules.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                    ipg_remote1_ip_rules = ipg.remote1_ip_smart_rules.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                    ipg_remote2_ip_rules = ipg.remote2_ip_smart_rules.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                    ipg_remote3_ip_rules = ipg.remote3_ip_smart_rules.extra(select={'triage_category': 'smart_category'}).values(
                        'id', 'description', 'triage_category'
                    )
                    serializer_data['rules'] = list(chain(ipg_ip_rules, ipg_remote1_ip_rules, ipg_remote2_ip_rules, ipg_remote3_ip_rules))
        return self.get_paginated_response(serializer.data)

    def post(self, request):
        """create group"""
        group_payload = request.data
        group_payload['last_modified_timestamp'] = timezone.now()
        group_payload['last_modified_by'] = request.user

        members = group_payload.get('members')

        if type(members) != list:
            return JsonResponse(
                {
                    'members': 'Members must be provided as a list. Example: [member1, member2]',
                    '_meta': {'level': 'error', 'message': 'Members must be list'},
                },
                status=422,
            )

        if group_payload['type'] == GroupCollection.HOST:
            # accept list of host ids, or host urls

            for index, hst in enumerate(members):
                if str(hst).startswith("http") or str(hst).isdigit():
                    group_payload['members'][index] = lib_tv.get_id_from_str(hst)
                    if not group_payload['members'][index]:
                        return JsonResponse(
                            {'members': 'Invalid members provided', '_meta': {'level': 'error', 'message': 'Invalid group members'}},
                            status=422,
                        )
            output = HostGroupsFacilitator(request, 'api').create_group(group_payload)
            response_data = (
                {'group': output['data']}
                if output['status_code'] == HostGroupsFacilitator.STATUS_CODE_CREATED
                else {'_meta': {'level': 'error', 'message': output.get('errors')}}
            )
            return JsonResponse(response_data, status=output['status_code'])
        if group_payload['type'] == GroupCollection.DOMAIN:
            output = ExternalDomainGroupsFacilitator(request, 'api').create_group(group_payload)
            response_data = (
                {'group': output['data']}
                if output['status_code'] == ExternalDomainGroupsFacilitator.STATUS_CODE_CREATED
                else {'_meta': {'level': 'error', 'message': output.get('errors')}}
            )
            return JsonResponse(response_data, status=output['status_code'])
        if group_payload['type'] == GroupCollection.IP:
            output = IPGroupsFacilitator(request, 'api').create_group(group_payload)
            response_data = (
                {'group': output['data']}
                if output['status_code'] == IPGroupsFacilitator.STATUS_CODE_CREATED
                else {'_meta': {'level': 'error', 'message': output.get('errors')}}
            )
            return JsonResponse(response_data, status=output['status_code'])
        else:
            return JsonResponse(
                {'type': 'Invalid group type provided', '_meta': {'level': 'error', 'message': 'Invalid field(s) found'}}, status=422
            )


class GroupV2(APIV2RetrieveView, QueryParameterValueValidationMixin):
    """V2 API for Managing Groups"""

    permission = 'groups'

    allowed_methods = ('GET', 'PATCH', 'DELETE', 'OPTIONS')
    renderer_classes = (JSONRenderer,)
    serializer_class = GroupSerializerV2
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    _get_query_params = ('fields',)
    _patch_query_params = ('membership_action',)
    metadata_class = GroupMetadataV2
    _choices_params = {'membership_action': ['remove', 'replace', 'append']}

    queryset = GroupCollection.objects.all()

    def dispatch(self, request, *args, **kwargs):
        """
        Overwrite dispatch method to validate whether host_groups feature flag is enabled
        before fulfilling request.
        """
        if lib_tv.has_feature('host_groups'):
            return super(GroupV2, self).dispatch(request, *args, **kwargs)

        return JsonResponse({}, status=404)

    def get(self, request, pk):
        # Only API v3.4 will allow this query param, so this will be true for all other minor versions
        dynamic_groups_api = api_v3_utils.is_dynamic_groups_api(request)
        include_members = request.query_params.get('include_members', 'True') in ('True', 'true', 1)
        output = GroupCollectionFacilitator(request, 'api').get_group(pk)
        # Turn off dynamic groups for < 3.4 for now
        if not dynamic_groups_api and output.get('data') and output['data'].member_type == 'dynamic':
            return JsonResponse(data={"detail": 'Not found.'}, status=404)
        if output['status_code'] == GroupCollectionFacilitator.STATUS_CODE_SUCCESS:
            group_data = {}
            if output['data'].type == GroupCollection.HOST:
                group_data = HostGroupAPIProcessor.process_host_group_data(
                    request, output['data'], include_members=include_members, limit_members=dynamic_groups_api
                )
            if output['data'].type == GroupCollection.DOMAIN:
                group_data = ExternalDomainGroupAPIProcessor.process_external_domain_group_data(
                    request, output['data'], include_members=include_members, limit_members=dynamic_groups_api
                )
            if output['data'].type == GroupCollection.IP:
                group_data = IPGroupAPIProcessor.process_ip_group_data(
                    request, output['data'], include_members=include_members, limit_members=dynamic_groups_api
                )

            # Add serializer fields specific to api v3.4
            if dynamic_groups_api and flag_enabled(Flags.dynamic_groups):
                if output['data'].type == GroupCollection.HOST and output['data'].rule:
                    group_data['regex'] = HostGroupUIProcessor(output['data'])._get_dynamic_rule_str(output['data'].rule)
                else:
                    group_data['regex'] = None
                group_data['membership_evaluation_ongoing'] = True if output['data'].member_eval_pending else False
                group_data['member_count'] = output['data'].members_count
                member_type = output['data'].member_type
                if member_type == GroupCollection.MEMBER_TYPE_DYNAMIC:
                    group_data['built_using'] = 'regex'
                elif member_type == GroupCollection.MEMBER_TYPE_AD:
                    group_data['built_using'] = 'ad_group'
                else:
                    group_data['built_using'] = 'static_members'
                group_data['members_truncated'] = True if output['data'].members_count > API_MEMBER_LIMIT else False
                if flag_enabled(Flags.ad_groups):
                    group_data['ad_group_dn'] = output['data'].ad_group_dn
            return JsonResponse(group_data, status=output['status_code'])
        else:
            return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])

    def patch(self, request, pk):
        self._validate_query_params(self.request.query_params.keys(), self._patch_query_params)
        group_payload = request.data
        group_payload['last_modified_timestamp'] = timezone.now()
        group_payload['last_modified_by'] = request.user

        group = self.get_object()
        group_payload['type'] = group.type

        if group_payload['type'] == GroupCollection.HOST:
            # accept list of host ids, or host urls
            if group_payload.get('members'):
                for index, hst in enumerate(group_payload['members']):
                    if str(hst).startswith("http") or str(hst).isdigit():
                        group_payload['members'][index] = lib_tv.get_id_from_str(hst)
                        if not group_payload['members'][index]:
                            return JsonResponse(
                                {'members': 'Invalid members provided', '_meta': {'level': 'error', 'message': 'Invalid group members'}},
                                status=422,
                            )
            output = HostGroupsFacilitator(request, 'api').edit_group(pk, group_payload)
            if output['status_code'] == HostGroupsFacilitator.STATUS_CODE_SUCCESS:
                group_data = HostGroupAPIProcessor.process_host_group_data(request, output['data'])
                return JsonResponse(group_data, status=output['status_code'])
            else:
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])
        if group_payload['type'] == GroupCollection.DOMAIN:
            output = ExternalDomainGroupsFacilitator(request, 'api').edit_group(pk, group_payload)
            if output['status_code'] == ExternalDomainGroupsFacilitator.STATUS_CODE_SUCCESS:
                group_data = ExternalDomainGroupAPIProcessor.process_external_domain_group_data(request, output['data'])
                return JsonResponse(group_data, status=output['status_code'])
            else:
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])
        if group_payload['type'] == GroupCollection.IP:
            output = IPGroupsFacilitator(request, 'api').edit_group(pk, group_payload)
            if output['status_code'] == IPGroupsFacilitator.STATUS_CODE_SUCCESS:
                group_data = IPGroupAPIProcessor.process_ip_group_data(request, output['data'])
                return JsonResponse(group_data, status=output['status_code'])
            else:
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])
        return JsonResponse(
            {'type': 'Invalid group type provided', '_meta': {'level': 'error', 'message': 'Invalid field(s) found'}}, status=422
        )

    def delete(self, request, pk):
        group = self.get_object()
        try:
            if group.type == GroupCollection.HOST:
                group = group.hostgroup
                output = HostGroupsFacilitator(request, 'api').delete_group(group)
                if output['status_code'] == HostGroupsFacilitator.STATUS_CODE_ACTION_SUCCESS:
                    return HttpResponse(status=output['status_code'])
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])
            if group.type == GroupCollection.DOMAIN:
                group = group.externaldomaingroup
                output = ExternalDomainGroupsFacilitator(request, 'api').delete_group(group)
                if output['status_code'] == ExternalDomainGroupsFacilitator.STATUS_CODE_SUCCESS:
                    return HttpResponse(status=output['status_code'])
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])
            if group.type == GroupCollection.IP:
                group = group.ipgroup
                output = IPGroupsFacilitator(request, 'api').delete_group(group)
                if output['status_code'] == IPGroupsFacilitator.STATUS_CODE_SUCCESS:
                    return HttpResponse(status=output['status_code'])
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output['status_code'])
            return JsonResponse(
                {'type': 'Invalid group type provided', '_meta': {'level': 'error', 'message': 'Invalid field(s) found'}}, status=422
            )
        except Exception:
            LOG.exception('Unexpected exception encountered deleting group')
            return JsonResponse({'_meta': {'level': 'error', 'message': 'Failed to delete group.'}}, status=output['status_code'])


class HostsV2(APIV2ListView, QueryParameterValueValidationMixin, DetectionSummaryMixin):
    """
    V2 API endpoint that represents a list of hosts
    """

    permission = 'host'

    serializer_class = HostSerializerV2
    allowed_methods = ('GET', 'OPTIONS')
    renderer_classes = (JSONRenderer,)
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    ordering_fields = ('last_detection_timestamp', 't_score', 'c_score', 'id')
    ordering = ('-last_detection_timestamp',)
    metadata_class = HostsMetadataV2

    # fields added by view
    added_fields = ['detection_summaries', 'host_session_luids', 'host_luid']

    _query_params = (
        'all',
        'active_traffic',
        'has_active_traffic',
        'certainty',
        'certainty_gte',
        'c_score',
        'c_score_gte',
        'exclude_fields',
        'fields',
        'include_detection_summaries',
        'key_asset',
        'is_key_asset',
        'last_detection_timestamp',
        'last_source',
        'min_id',
        'max_id',
        'mac_address',
        'name',
        'note_modified_timestamp_gte',
        'ordering',
        'state',
        'tags',
        'targets_key_asset',
        'is_targeting_key_asset',
        'threat',
        'threat_gte',
        't_score',
        't_score_gte',
        'uuid',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
        'privilege_level',
        'privilege_level_gte',
        'privilege_category',
        'last_detection_timestamp_gte',
        'last_detection_timestamp_lte',
        'all_active',
    )
    _bool_params = (
        'all',
        'active_traffic',
        'has_active_traffic',
        'key_asset',
        'is_key_asset',
        'include_detection_summaries',
        'targets_key_asset',
        'is_targeting_key_asset',
        'all_active',
    )
    _choices_params = {
        'fields': list(HostSerializerV2.Meta.fields) + added_fields,
        'exclude_fields': list(HostSerializerV2.Meta.fields),
        'state': ('active', 'inactive'),
        'privilege_category': ('low', 'medium', 'high'),
    }
    _gt_zero_params = ('min_id', 'max_id', StandardResultSetPagination.page_query_param, 'privilege_level', 'privilege_level_gte')
    _gte_zero_params = ('certainty', 'certainty_gte', 'c_score', 'c_score_gte', 'threat', 'threat_gte', 't_score', 't_score_gte')
    _ip_params = ('last_source',)
    _str_params = (
        'fields',
        'mac_address',
        'name',
        'ordering',
        'state',
        'tags',
        'uuid',
        StandardResultSetPagination.page_size_query_param,
        'privilege_category',
    )
    _timestamp_params = (
        'last_detection_timestamp',
        'note_modified_timestamp_gte',
        'last_detection_timestamp_gte',
        'last_detection_timestamp_lte',
    )

    def _validate_query_param_values(self):
        errors = super(HostsV2, self)._validate_query_param_values()

        param = 'mac_address'
        mac_address_param_value = self.request.query_params.get(param)
        if mac_address_param_value is not None and not validator_utils.validate_mac_address(mac_address_param_value):
            errors[param] = "Invalid value given for parameter '{}'. Must be a valid MAC address.".format(param)
        return errors

    def _process_internal_response(self, host_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
        """
        for url_field in ('url', 'host_url', 'detection_set'):
            host_data.pop(url_field, None)
        if host_data.get('detection_summaries'):
            for det_summary in host_data['detection_summaries']:
                det_summary.pop('detection_url', None)
        host_data.pop('t_score', None)
        host_data.pop('c_score', None)

        return host_data

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        active_host_sessions_q = Q(host_session__end__isnull=True)
        detections_q = Q(host_session__detection__state='active') & Q(host_session__detection__smart_rule__isnull=True)
        key_assets_q = Q(key_asset=True)
        state_q = Q(state__in=('active', 'inactive', 'suspended'))

        all_hosts = self.request.query_params.get('all')
        all_active = self.request.query_params.get('all_active')
        if all_hosts and all_hosts in ('True', 'true', '1'):
            # When querying for all host objects we no longer do the state filter using state_q
            # This allows the resulting query set to include merged hosts, which are then consolidated
            # after all of the filters below have been applied.  See CS-6796 for details.
            queryset = host.objects.all()
        elif all_active in ('True', 'true', '1'):
            # Exclude hosts with no active host sessions or detections
            host_ids = host_session.objects.filter(
                (Q(detection__state='active') & Q(detection__smart_rule_id__isnull=True)) | Q(end=None)
            ).values_list('host_id', flat=True)
            queryset = host.objects.filter(id__in=host_ids)
        else:
            # default is to only return hosts that are key assets, have active host sessions, or have active detections
            # By splitting this into two queries, we avoid having to call distinct over thousands(+) of duplicate records
            hs_active_detections = host.objects.filter(detections_q | active_host_sessions_q).values_list('id', flat=True)
            queryset = host.objects.filter(Q(id__in=hs_active_detections) | key_assets_q)
            # only 'active', 'inactive', and 'suspended' (i.e. inactive) state hosts should be returned
            queryset = queryset.filter(state_q)

        active_traffic = self.request.query_params.get('has_active_traffic') or self.request.query_params.get('active_traffic')
        min_id = self.request.query_params.get('min_id')
        max_id = self.request.query_params.get('max_id')
        name = self.request.query_params.get('name')
        last_source = self.request.query_params.get('last_source')
        t_score = self.request.query_params.get('threat') or self.request.query_params.get('t_score')
        t_score_gte = self.request.query_params.get('threat_gte') or self.request.query_params.get('t_score_gte')
        c_score = self.request.query_params.get('certainty') or self.request.query_params.get('c_score')
        c_score_gte = self.request.query_params.get('certainty_gte') or self.request.query_params.get('c_score_gte')
        state = self.request.query_params.get('state')
        last_detection_timestamp = parse_time_string(self.request.query_params.get('last_detection_timestamp'))
        tags = self.request.query_params.get('tags')
        key_asset = self.request.query_params.get('is_key_asset') or self.request.query_params.get('key_asset')
        mac_address = self.request.query_params.get('mac_address')
        uuid = self.request.query_params.get('uuid')
        targets_key_asset = self.request.query_params.get('is_targeting_key_asset') or self.request.query_params.get('targets_key_asset')
        note_ts_gte = parse_time_string(self.request.query_params.get('note_modified_timestamp_gte'))
        priv_level = self.request.query_params.get('privilege_level')
        priv_level_gte = self.request.query_params.get('privilege_level_gte')
        priv_category = self.request.query_params.get('privilege_category')
        last_detection_timestamp_lte = parse_time_string(self.request.query_params.get('last_detection_timestamp_lte'))
        last_detection_timestamp_gte = parse_time_string(self.request.query_params.get('last_detection_timestamp_gte'))

        if active_traffic is not None:
            if active_traffic in ('True', 'true', '1'):
                queryset = queryset.filter(active_host_sessions_q & Q(host_session__id__isnull=False))
            elif active_traffic in ('False', 'false', '0'):
                queryset = queryset.exclude(active_host_sessions_q & Q(host_session__id__isnull=False))
        if min_id is not None:
            queryset = queryset.filter(id__gte=min_id)
        if max_id is not None:
            queryset = queryset.filter(id__lte=max_id)
        if name is not None:
            queryset = queryset.filter(name__icontains=name)
        if last_source is not None:
            queryset = queryset.filter(last_source__icontains=last_source)
        if t_score is not None:
            queryset = queryset.filter(t_score=t_score)
        if t_score_gte is not None:
            queryset = queryset.filter(t_score__gte=t_score_gte)
        if c_score is not None:
            queryset = queryset.filter(c_score=c_score)
        if c_score_gte is not None:
            queryset = queryset.filter(c_score__gte=c_score_gte)
        if state is not None:
            if state == 'inactive':
                # equivalent logic for showing 'inactive' hosts in the UI
                queryset = queryset.filter(Q(state__in=('inactive', 'suspended')) | (Q(state='active') & Q(t_score=0) & Q(c_score=0)))
            elif state == 'active':
                queryset = queryset.filter(Q(state='active') & (Q(t_score__gte=1) | Q(c_score__gte=1)))
            else:
                queryset = queryset.filter(state=state)
        if last_detection_timestamp is not None:
            queryset = queryset.filter(last_detection_timestamp=last_detection_timestamp)
        if tags is not None:
            queryset = queryset.filter(tags__name__in=tags.split(','))
        if key_asset is not None:
            key_asset = key_asset in ('True', 'true', '1')
            queryset = queryset.filter(Q(key_asset=key_asset) | Q(targets_key_asset=key_asset))
        if mac_address is not None:
            mac_address_q = Q(host_artifact__type='mac', host_artifact__value__icontains=mac_address)
            queryset = queryset.filter(mac_address_q)
        if uuid:
            uuid_q = Q(host_artifact__type='vm_uuid', host_artifact__value__icontains=uuid)
            queryset = queryset.filter(uuid_q)
        if targets_key_asset is not None:
            targets_key_asset = targets_key_asset in ('True', 'true', '1')
            queryset = queryset.filter(targets_key_asset=targets_key_asset)
        if note_ts_gte is not None:
            id_values = get_note_modified_gte('host', note_ts_gte)
            queryset = queryset.filter(id__in=id_values)
        if priv_level:
            queryset = queryset.filter(priv_level=priv_level)
        if priv_level_gte:
            queryset = queryset.filter(priv_level__gte=priv_level_gte)
        if priv_category:
            # We don't have direct access to the privilege category from the account model.
            # Translate the category to privilege levels
            levels = get_category_to_privilege_level(priv_category)
            queryset = queryset.filter(priv_level__in=levels)
        if last_detection_timestamp_lte is not None:
            queryset = queryset.filter(last_detection_timestamp__lte=last_detection_timestamp_lte)
        if last_detection_timestamp_gte is not None:
            queryset = queryset.filter(last_detection_timestamp__gte=last_detection_timestamp_gte)

        if all_hosts and all_hosts in ('True', 'true', '1'):
            # After running the above queries the queryset may include merged hosts (see CS-6796).
            # We resolve this by extracting the non-merged hosts into one queryset, the merged hosts
            # into a second queryset.  The second queryset is then morphed into a new queryset containing
            # the new hosts pointed by the merged hosts.  We then merge (!) the two querysets to give
            # the final search results.
            # Since merged records will only be returned with 'all' param, this merge should only execute then.
            nonmerged = queryset.filter(state_q).distinct()
            merged_subjects = queryset.filter(Q(state='merged'))
            merged_target_pointers = merged_subjects.values_list('new_host_pointer')
            merged_targets = host.objects.filter(Q(id__in=merged_target_pointers)).distinct()
            queryset = (nonmerged | merged_targets).distinct()

        return queryset

    def list(self, request, *args, **kwargs):
        host_queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(host_queryset)
        internal = kwargs.get('internal', False)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            host_data = serializer.data
            include_summaries = request.query_params.get('include_detection_summaries') in ('True', 'true', '1')

            fields = request.query_params.get('fields')
            if fields:
                fields = fields.split(',')
                if 'detection_summaries' not in fields:
                    include_summaries = False
                host_data = [clear_requested_fields(fields, host_serialized_data) for host_serialized_data in host_data]

            if include_summaries:
                for host_obj, host_serialized_data in zip(serializer.instance, host_data):
                    try:
                        host_serialized_data['detection_summaries'] = self.generate_detection_summaries(request, host_obj, internal)
                    except Exception:
                        LOG.exception('Unexpected exception encountered when retrieving detection summaries for host %s', host_obj.id)
                        host_serialized_data['detection_summaries'] = None

            for host_obj, host_serialized_data in zip(serializer.instance, host_data):
                host_serialized_data['host_session_luids'] = host_obj.host_session_set.all().values_list('session_luid', flat=True)
                host_serialized_data['host_luid'] = host_obj.host_luid

                if 'host_artifact_set' in host_serialized_data:
                    artifacts = host_serialized_data['host_artifact_set']
                    for artifact in artifacts:
                        if artifact['type'] == 'mac':
                            artifact['vendor'] = None
                            try:
                                artifact['vendor'] = get_mac_vendor(artifact['value'])
                            except IOError or FileNotFoundError:
                                LOG.exception('Error encountered in opening MAC to vendor mapping file')
                            except ValueError:
                                LOG.exception('Decoding JSON MAC to vendor mapping file has failed')
                            except Exception:
                                LOG.exception('An unexpected error occured in processing JSON MAC to vendor mapping file')

            if internal:
                for host_serialized_data in host_data:
                    self._process_internal_response(host_serialized_data)

            return self.get_paginated_response(host_data)

        serializer = self.get_serializer(host_queryset, many=True)
        return Response(serializer.data)


class HostV2(APIV2RetrieveView, QueryParameterValueValidationMixin, DetectionSummaryMixin):
    """
    V2 API endpoint that represents a single host
    """

    permission = 'host'

    allowed_methods = ('GET', 'OPTIONS', 'PATCH')
    renderer_classes = (JSONRenderer,)
    serializer_class = HostSerializerV2
    metadata_class = HostMetadataV2

    # fields added by view
    added_fields = [
        'campaign_summaries',
        'detection_summaries',
        'has_shell_knocker_learnings',
        'last_seen',
        'carbon_black',
        'vcenter',
        'crowdstrike',
        'ldap',
        'suspicious_admin_learnings',
        'shell_knocker',
        'host_session_luid',
        'host_luid',
    ]

    _query_params = (
        'exclude_fields',
        'fields',
        'include_external',
        'include_ldap',
        'include_access_history',
        'include_host_roles',
        'include_edrs',
        'include_detection_summaries',
    )

    _bool_params = (
        'include_external',
        'include_ldap',
        'include_access_history',
        'include_host_roles',
        'include_edrs',
        'include_detection_summaries',
    )

    _choices_params = {'fields': list(HostSerializerV2.Meta.fields) + added_fields, 'exclude_fields': list(HostSerializerV2.Meta.fields)}
    _str_params = ('fields',)

    def _process_internal_response(self, host_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
        """
        for url_field in ('url', 'host_url', 'detection_set'):
            host_data.pop(url_field, None)
        if host_data.get('detection_summaries'):
            for det_summary in host_data['detection_summaries']:
                det_summary.pop('detection_url', None)
        host_data.pop('t_score', None)
        host_data.pop('c_score', None)

        return host_data

    def _translate_host_context_data(self, data):
        """
        Renames keys and labels for host context data
        """

        remove_keys = []

        field_translations = {
            'carbon_black': {
                'connection_status': 'is_connected',
                'host_name': 'name',
                'isolated': 'is_isolated',
                'sensor_details_url': 'url',
                'sensor_id': 'uuid',
            },
            'crowdstrike': {
                'connection_status': 'is_connected',
                'device_id': 'uuid',
                'host_name': 'name',
                'isolated': 'is_isolated',
                'sensor_details_url': 'url',
            },
            'vcenter': {'guestOs': 'os', 'vsphere_name': 'vsphere', 'vsphereUuid': 'vsphere_uuid', 'hostname': 'name'},
            'ldap': {
                'telephoneNumber': 'telephone_number',
                'userPrincipalName': 'user_principal_name',
                'machineRole': 'machine_role',
                'cn': 'common_name',
                'displayName': 'display_name',
                'distinguishedName': 'distinguished_name',
                'memberOf': 'member_of',
                'operatingSystem': 'operating_system',
                'objectClass': 'object_class',
                'pwdLastSet': 'pwd_last_set',
                'nETBIOSName': 'netbios_name',
                'physicalLocationObject': 'physical_location_object',
                'dNSHostName': 'dns_hostname',
                'managedBy': 'managed_by',
                'networkAddress': 'network_address',
                'objectSid': 'object_sid',
                'servicePrincipalName': 'service_principal_name',
                'mail': 'email',
                'l': 'location',
                'macAddress': 'mac_address',
            },
        }

        remove_fields = {
            'carbon_black': ['go_live_url'],
            'crowdstrike': ['go_live_url'],
            'vcenter': ['host', 'nets', 'powerState', 'uuid', 'is_sensor', 'instanceUuid', 'unique_uuid', 'biosuuid'],
            'ldap': ['dn'],
        }

        for key in remove_keys:
            data.pop(key, None)

        for key, fields_list in remove_fields.items():
            for field in fields_list:
                if key in data and data[key]:
                    data[key].pop(field, None)

        for key, field_labels in field_translations.items():
            if key in data and data[key]:
                for old_key, new_key in field_labels.items():
                    if old_key in data[key]:
                        data[key][new_key] = data[key].pop(old_key)

        return data

    def _translate_edr_details_data(self, data):
        """
        Converts keys and labels for edrDetail item into the depreciated top level edr objects
        Receives list of objects within edrdetails, and returns a dict with the edr uid as keys
        """
        remove_keys = []

        field_translations = {
            'crowdstrike': {
                'url': 'detailsUrl',
                'uuid': 'hostId',
                'name': 'hostName',
                'last_seen': 'lastSeen',
                'is_isolated': ['lockdownState', 'locked'],
            },
        }

        remove_fields = {
            'crowdstrike': ['go_live_url'],
        }

        add_fields = {
            'crowdstrike': {'status': 'connected'},
        }
        translated = {detail['uid']: detail for detail in data}
        edrs_in_details = translated.keys()

        for edr in remove_keys:
            translated.pop(edr, None)

        for edr, fields_list in remove_fields.items():
            if edr in edrs_in_details and translated[edr]:
                for field in fields_list:
                    translated[edr].pop(field, None)

        # Note this has swapped order
        # structure is 'key to be set': 'key with value' | ['keys', 'to', 'the', 'value']
        for edr, field_labels in field_translations.items():
            if edr in edrs_in_details and translated[edr]:
                for new_key, old_key in field_labels.items():
                    value = translated.get(edr)
                    # multi step key indexing
                    if isinstance(old_key, list):
                        for o_key in old_key:
                            if value:
                                value = value.get(o_key)
                        translated[edr].pop(old_key[0])
                    else:
                        value = value.get(old_key)
                        translated[edr].pop(old_key)
                    translated[edr][new_key] = value

        # sets defaults for fields, since edrDetails will mean connected inherently
        for edr, field_labels in add_fields.items():
            if edr in edrs_in_details and translated[edr]:
                for label, value in field_labels.items():
                    translated[edr][label] = value

        LOG.info("Translated top level edrs:{}".format(translated))
        return translated

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        return host.objects.all()

    def _get_host_access(self, host_id):
        return get_host_access(host_id, use_linked_account=False)

    def get(self, request, *args, **kwargs):
        host_obj = self.get_object()
        serializer = self.get_serializer(host_obj)
        serialized_data = serializer.data
        include_external = request.query_params.get('include_external') in ('True', 'true', '1')
        include_ldap = request.query_params.get('include_ldap') in ('True', 'true', '1')
        include_access_history = request.query_params.get('include_access_history', 'True') in ('True', 'true', '1')
        include_host_roles = request.query_params.get('include_host_roles', 'True') in ('True', 'true', '1')
        include_edrs = request.query_params.get('include_edrs', 'True') in ('True', 'true', '1')
        include_detection_summaries = request.query_params.get('include_detection_summaries', 'True') in ('True', 'true', '1')
        host_context = {}
        if include_external:
            try:
                host_context.update(self._translate_host_context_data(get_all_host_context(host_obj, flatten=True)))
            except Exception:
                LOG.exception('Unexpected exception encountered when retrieving host details for host %s', host_obj.id)

        if include_host_roles:
            serialized_data['roles'] = []

            if host_obj.host_luid:
                serialized_data['roles'] = lib_host_role.fetch_sorted_host_role_list(host_obj)

        if include_edrs:
            artifact_edrs = list(
                filter(None, (EDR_ARTIFACT_TYPES.get(artf['type']) for artf in serialized_data.get('host_artifact_set', [])))
            )
            attr_edrs = []
            if host_obj.host_luid:
                attrs = HostAttributeAPI.get_attributes('host_edr', host_luid=host_obj.host_luid)
                attr_edrs = [attr.get('edr_display_name') for attr in attrs]

            serialized_data['edrs'] = sorted(set(artifact_edrs + attr_edrs), key=str.lower)

        if include_ldap:
            try:
                ldap_context = {'ldap': get_host_ldap_context(host_obj, flatten=True)}
                host_context.update(self._translate_host_context_data(ldap_context))
            except Exception:
                LOG.exception('Unexpected exception encountered when retrieving host ldap details for host %s', host_obj.id)

        if include_access_history:
            host_access = self._get_host_access(host_obj.id)
            # Only include account history if this user has account permission
            if AccountPermissionView().is_authorized(request):
                serialized_data['account_access_history'] = keys_to_snake_case(
                    host_access['accountAccessHistory'][:MAX_ACC_HOST_ACCESS_HISTORY_LENGTH]
                )

            serialized_data['service_access_history'] = keys_to_snake_case(
                host_access['serviceAccessHistory'][:MAX_ACC_HOST_ACCESS_HISTORY_LENGTH]
            )

        if include_detection_summaries:
            # limit summaries to 100 to prevent request timeout, these take a while to generate...
            try:
                serialized_data['detection_summaries'] = self.generate_detection_summaries(
                    request, host_obj, internal=kwargs.get('internal', False), limit=100
                )
            except Exception:
                LOG.exception('Unexpected exception encountered when retrieving detection summaries for host %s', host_obj.id)
                serialized_data['detection_summaries'] = None

        # add in fields whose data is generated outside the serializer
        serialized_data['campaign_summaries'] = []
        serialized_data['last_seen'] = None
        serialized_data['suspicious_admin_learnings'] = get_watchmen_networkx_learnings(host_obj)
        serialized_data['shell_knocker'] = get_shell_knocker_ports(host_obj)
        serialized_data['has_shell_knocker_learnings'] = bool(serialized_data['shell_knocker'])

        serialized_data['host_session_luids'] = (
            host_obj.host_session_set.all().values_list('session_luid', flat=True).order_by('-start')[:84]
        )
        serialized_data['host_luid'] = host_obj.host_luid

        fields = request.query_params.get('fields')
        if fields:
            fields = fields.split(',')
            serialized_data = clear_requested_fields(fields, serialized_data)

        if serialized_data.get('note') is not None:
            serialized_data['note'] = string_utils.unescape_html(serialized_data['note'])
        if 'campaign_summaries' in serialized_data:
            host_campaigns = get_related_host_campaigns(kwargs['pk'])
            serialized_data['campaign_summaries'] = supplement_campaigns(host_campaigns)
        if 'last_seen' in serialized_data:
            serialized_data['last_seen'] = lib_tv.get_host_last_seen(host_obj)

        # DEPRECIATED: Backwards compatible top level edr information
        dep_edrs = ('carbon_black', 'vcenter', 'crowdstrike', 'ldap')
        for h_key in dep_edrs:
            if h_key in host_context:
                serialized_data[h_key] = host_context.get(h_key)
        # Fill in depreciated top level fields if available
        if 'edrDetails' in host_context:
            translated_details = self._translate_edr_details_data(host_context['edrDetails'])
            serialized_data.update(translated_details)

        if kwargs.get('internal', False):
            serialized_data = self._process_internal_response(serialized_data)

        if 'host_artifact_set' in serialized_data.keys():
            artifacts = serialized_data['host_artifact_set']
            for artifact in artifacts:
                if artifact['type'] == 'mac':
                    artifact['vendor'] = None
                    try:
                        artifact['vendor'] = get_mac_vendor(artifact['value'])
                    except IOError or FileNotFoundError:
                        LOG.exception('Error encountered in opening MAC to vendor mapping file')
                    except ValueError:
                        LOG.exception('Decoding JSON MAC to vendor mapping file has failed')
                    except Exception:
                        LOG.exception('An unexpected error occured in processing JSON MAC to vendor mapping file')

        return Response(serialized_data)

    def patch(self, request, *args, **kwargs):
        """Update attributes for a host object.  Use request.data to get patch data

        Data Attributes:
            key_asset (str): 'true' or 'false'
            note (str): overwrite note for object.
            note_id (int): optional note id to overwrite;
                            if not declared, oldest note is overwritten (or new note created)
        """
        host_obj = self.get_object()
        audit_msg = None
        try:
            status = 422
            if request.data.get('key_asset', '').lower() == 'true':
                if not host_obj.key_asset:
                    host_obj.key_asset = True
                    host_obj.save(update_fields=['key_asset'])
                    audit_msg = 'marked host@{host_id} with name \'{host_name}\' as key_asset'.format(
                        host_id=host_obj.id, host_name=host_obj.name
                    )
                status = 200
            elif request.data.get('key_asset', '').lower() == 'false':
                if host_obj.key_asset:
                    host_obj.key_asset = False
                    host_obj.save(update_fields=['key_asset'])
                    audit_msg = 'unmarked host@{host_id} with name \'{host_name}\' as key_asset'.format(
                        host_id=host_obj.id, host_name=host_obj.name
                    )
                status = 200

            if request.data.get('note', ''):
                note_id = request.data.get('note_id', None)
                if note_id:
                    note_id = int(note_id)

                note_handler = NotesHandler('host', host_obj.id)

                _notes, num_notes = note_handler.get_notes()
                _note = None

                # escape special characters
                sanitized_note = escape(request.data.get('note'))

                if num_notes == 0:  # create a note if none exists
                    _note = note_handler.create_note(sanitized_note, request.user)
                else:  # update note
                    # if note id is not declared, update oldest note
                    if note_id is None:
                        note_id = min(_notes, key=lambda n: n['date_created'])['id']
                        note = next((x for x in _notes if x['id'] == note_id), None)
                    else:
                        # confirm this is the user's note or they have permissions to edit other users' notes
                        note = next((x for x in _notes if x['id'] == note_id), None)

                        if note is None:
                            return HttpResponse(status, status=404)

                        if note['created_by_id'] != request.user.id and not request.user.has_perm('edit_other_users_notes'):
                            return HttpResponse(status, status=403)

                    # only update note if content is different
                    if note['note'] != sanitized_note:
                        _note = note_handler.update_note(note_id, sanitized_note, request.user)

                if _note:
                    AUDIT.audit(
                        get_audit_user_data(request),
                        True,
                        'note for host@{h_id} updated to {note_text}...'.format(h_id=host_obj.id, note_text=_note['note'][:10]),
                    )

                status = 200

            if request.data.get('note', ''):
                status = 200
        except Exception:
            LOG.exception('Unexpected exception with request')
            status = 400
        if audit_msg is not None:
            AUDIT.audit(get_audit_user_data(request), status == 200, audit_msg)
        return HttpResponse(status, status=status)


class RuleNameMappingMixin(object):
    """
    Mixin helper for taking smart rules and mapping them to and from external representations and
    internal representations.
    """

    @staticmethod
    def _to_external_repr(serializer):
        """
        Helper function to take a serialized smart rule(s) and transform it to its external representation: renaming fields where appropriate,
        and including only relevant fields.

        Args:
            serializer (object): a serialized smart_rule(s).
        Returns:
            final_sdata: a dict or a list of dicts with the final serialized/renamed smart rule(s).
        """
        many = getattr(serializer, 'many', False)
        final_sdata = []
        s_data, s_instance = (serializer.data, serializer.instance) if many else ([serializer.data], [serializer.instance])

        for serialized_data, instance in zip(s_data, s_instance):
            field_mapping = HEADERS.get(instance.type, dict())
            accepted_fields = set(field_mapping.keys())

            if 'remote1_dns' in accepted_fields:
                accepted_fields.add('remote1_dns_group')
            if 'remote2_dns' in accepted_fields:
                accepted_fields.add('remote2_dns_group')

            not_applicable_fields = SR_OPTIONAL_FIELDS - accepted_fields
            # Remove fields that are not triageable for this rule type
            for field in not_applicable_fields:
                serialized_data.pop(field, None)

            # Rename fields where necessary
            for field, mapping in chain.from_iterable(
                [((field, field_mapping.get(field, {})) for field in accepted_fields), SR_GEN_FIELDS_MAP.items()]
            ):
                if field in serialized_data and mapping.get('api_name', field) != field:
                    serialized_data[mapping.get('api_name', field)] = serialized_data.pop(field)

            final_sdata.append(serialized_data)

        return final_sdata if many else final_sdata[0]

    def _to_internal_repr(self):
        """
        Helper function to take an external representation of a smart rule and transform it to its
        internal representation: renaming external-facing field names to database column names.
        """
        # Map general smart rule fields
        for api_name, col_name in SR_GEN_API_NAME_TO_COL.items():
            if api_name in self.request.data:
                self.request.data[col_name] = self.request.data[api_name]

        # Infer detection category from category vname
        det_category = None
        if 'category' in self.request.data and isinstance(self.request.data['category'], str):
            category_vname = ' '.join(self.request.data['category'].split()).title()
            det_category = DET_CATEGORY_VNAME_TO_CATEGORY.get(category_vname, '')

        # Attempt to determine rule type from type_vname and detection category
        det_type_vname = self.request.data.get('type_vname', '')
        rule_type = None
        if isinstance(det_type_vname, str):
            # If the received name is a detection type that has had its name changed, swap it to the new name
            if det_type_vname in OLD_VNAME_MAPPINGS:
                det_type_vname = OLD_VNAME_MAPPINGS[det_type_vname]
            det_type_vname = ' '.join(det_type_vname.split()).lower()
            rule_type = DET_VNAME_CAT_TO_TYPE.get((det_type_vname, det_category), '')
            self.request.data['type'] = rule_type

        # Map type dependent fields if know type
        if rule_type:
            for fname, col_name in DETECTION_API_NAME_TO_COL_NAME.get(rule_type, {}).items():
                if fname in self.request.data:
                    self.request.data[col_name] = self.request.data[fname]


class RulesV2(RuleNameMappingMixin, mixins.ListModelMixin, APIV2GenericView, QueryParameterValueValidationMixin):
    """
    Base class for all rule related V2 requests to represent list of rules (triage filters)
    V2 API endpoint that represents a list of rules (triage filters)
    """

    permission = 'triage'

    serializer_class = RuleSerializerV2
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    ordering_fields = ('last_timestamp', 'id')
    ordering = ('-last_timestamp',)
    _query_params = (
        'fields',
        'ordering',
        'include_templates',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
        'contains',
    )
    _choices_params = {
        'fields': list(RuleSerializerV2.Meta.fields),
        'exclude_fields': list(RuleSerializerV2.Meta.fields),
    }
    metadata_class = RuleMetadataV2

    def __init__(self):
        super(RulesV2, self).__init__()

    def can_represent_rule(self, rule):
        raise NotImplementedError("Implement in subclass")

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        queryset = smart_rule.objects.filter(Q(priority__isnull=False) | Q(family__iexact=smart_rule.FAMILY_TEMPLATE)).prefetch_related(
            'host_groups'
        )

        include_templates_param = self.request.query_params.get("include_templates", "false")
        include_templates = str2bool(include_templates_param) if validator_utils.validate_str_is_bool(include_templates_param) else False
        if not include_templates:
            queryset = queryset.exclude(family__iexact=smart_rule.FAMILY_TEMPLATE).filter(conditions__isnull=False)

        contains = self.request.query_params.get('contains')
        if contains:
            q_objs = AppAllTriageFiltersEndpoint._apply_search_filters(contains)
            queryset = queryset.filter(q_objs)

        # Subclasses may filter out rules they cannot represent
        exclude_rule_ids = [rule.id for rule in queryset if not self.can_represent_rule(rule)]

        queryset = queryset.exclude(id__in=exclude_rule_ids)
        return queryset.distinct()


class RulesV2_0(RulesV2):
    """
    V2.0 API request handler. Can be used to perform deletes/post/gets on all simple rules (triage filters)
    """

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            serialized_data = self._to_external_repr(serializer)
            return self.get_paginated_response(serialized_data)

        serializer = self.get_serializer(queryset, many=True)
        serialized_data = self._to_external_repr(serializer)
        return Response(serialized_data)

    def can_represent_rule(self, rule):
        # We want to filter out rules that are too complex for V2
        # Is this rule representable in the legacy format?
        _, errors = convert_to_triage_legacy({'conditions': rule.conditions})
        return not errors.get('errors')

    def get(self, request, *args, **kwargs):
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)
        return self.list(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self._to_internal_repr()
        kwargs['rest_api_v2'] = True

        legacy_payload = request.data
        # is this a singleton or new filter?
        tf = TriageFacilitator(request, 'api')
        errors = defaultdict(list)

        smart_category = legacy_payload.get('smart_category')
        sc_success, sc_error = validator_utils.validate_smart_category(smart_category)
        if not sc_success:
            errors['smart_category'].append(sc_error)

        id_dict = {}
        if 'detectionId' in legacy_payload:
            detection_id = legacy_payload['detectionId']
            id_success, id_error = validator_utils.validate_django_orm_id(detection_id)
            if not id_success:
                errors['detectionId'].append(id_error)
            if errors:
                return JsonResponse({'errors': errors, '_meta': {'level': 'error'}}, status=422)
            output = tf.create_triage_singleton(detection_id, smart_category)
            if output['status_code'] == TriageFacilitator.STATUS_CODE_CREATED:
                id_dict['id'] = output['data']['id']
        elif 'detectionIdList' in legacy_payload:
            detection_id_list = legacy_payload['detectionIdList']
            success, error = validator_utils.validate_detection_id_list(detection_id_list)
            if not success:
                errors['detectionIdList'].append(error)

            if errors:
                return JsonResponse({'errors': errors, '_meta': {'level': 'error'}}, status=422)
            output = tf.bulk_create_triage_singleton(detection_id_list, smart_category)
            if output['status_code'] == TriageFacilitator.STATUS_CODE_CREATED:
                id_dict['ids'] = output['data']['created_rules']
        else:
            output, errors = self.create_new_filter(legacy_payload, tf)

            if errors:
                return JsonResponse(errors, status=422)

            if output['status_code'] == TriageFacilitator.STATUS_CODE_CREATED:
                id_dict['id'] = output['data']['id']

        if output['status_code'] == TriageFacilitator.STATUS_CODE_CREATED:
            created_response = {'_meta': {'level': 'success', 'message': 'Successfully created triage filter'}}
            created_response.update(id_dict)
            return JsonResponse(created_response, status=201)
        else:
            error_response = output.get('errors') or {}
            error_response.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return JsonResponse(error_response, status=output['status_code'])

    def delete(self, request, *args, **kwargs):
        tf = TriageFacilitator(request, 'api')
        errors = defaultdict(list)

        # The user could be deleting a bulk singleton smart rule
        if 'detectionIdList' in request.data:
            detection_ids = request.data['detectionIdList']
            success, error = validator_utils.validate_detection_id_list(detection_ids)
            if not success:
                errors['detectionIdList'].append(error)
                return JsonResponse({'errors': errors}, status=422)

            output = tf.delete_triage_singletons(detection_ids, stats_reason='user_delete_singletons_api_v2.0')
            return JsonResponse({}, status=output['status_code'])
        else:
            err_msg = "No detection list provided. In order to bulk unmark as custom, please provide a list of detection IDs to unmark"
            errors['detectionIdList'].append(err_msg)
            return JsonResponse({'errors': errors}, status=422)

    def create_new_filter(self, payload, tf):
        # This is a regular creation of a new triage filter. Convert from legacy payload to new payload
        valid, errors = tf.validate_legacy_rule(payload, rest_api_v2=True)

        if errors:
            errors.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return None, errors

        nme_payload, error = convert_to_triage_nme(payload)
        if error.get('error'):
            errors = {'errors': 'This rule cannot be represented by the triage engine', '_meta': {'level': 'error'}}
            return None, errors

        output = tf.create_triage_filter(nme_payload, stats_reason='user_create_filter_api_v2.0')
        errors = output.get('errors')
        if errors:
            errors['_meta'] = {'level': 'error', 'message': 'Invalid field(s) found'}
        return output, errors


class RulesV2_1(RulesV2_0):
    """
    V2.1 API request handler. Can be used to perform deletes/post/gets on all complex and simple rules (triage filters)
    """

    serializer_class = RuleSerializerV2_1

    def can_represent_rule(self, rule):
        return True

    def create_new_filter(self, payload, tf):
        errors = defaultdict(list)
        # The user needs to send 'source_conditions' and 'additional_conditions', even though our code references 'sourceConditions' and 'additionalConditions'
        if 'source_conditions' not in payload:
            errors['source_conditions'].append('The field "source_conditions" is missing. Maybe you used "sourceConditions"?')
        if 'additional_conditions' not in payload:
            errors['additional_conditions'].append('The field "additional_conditions" is missing. Maybe you used "additionalConditions"?')
        if errors:
            errors.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return None, errors

        # Since the rest of our code base expects camelcase 'sourceConditions' and 'additionalConditions', reformat now
        source = payload.pop('source_conditions')
        additional = payload.pop('additional_conditions')
        payload['sourceConditions'] = source
        payload['additionalConditions'] = additional

        output = tf.create_triage_filter(payload, stats_reason='user_create_filter_api_v2.1')
        errors = output.get('errors')
        if errors:
            errors['_meta'] = {'level': 'error', 'message': 'Invalid field(s) found'}
        return output, errors


class RuleV2(RuleNameMappingMixin, APIV2GenericView):
    """
    V2 API endpoint that represents a single rule
    """

    permission = 'triage'

    renderer_classes = (JSONRenderer,)
    serializer_class = RuleSerializerV2
    _get_query_params = ('fields',)
    _post_query_params = ('restore_detections',)
    metadata_class = RuleMetadataV2

    queryset = smart_rule.pure.exclude(
        Q(priority__isnull=True)
        & Q(family__in=(smart_rule.FAMILY_CUSTOMER, smart_rule.FAMILY_TEMPLATE_CUSTOMER, smart_rule.FAMILY_ALGORITHM_CUSTOMER))
    ).exclude(family__in=[smart_rule.FAMILY_AI, smart_rule.FAMILY_ALGORITHM, smart_rule.FAMILY_DISTILLATION])


class RuleV2_0(RuleV2):
    """
    V2.0 API request handler. Can be used to perform get/edit/delete on a single simple rule (triage filter)
    """

    def get(self, request, *args, **kwargs):
        bad_query_params = [param for param in list(self.request.query_params.keys()) if param not in self._get_query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        srule = self.get_object()
        # never return algorithm family rules
        if srule.family == smart_rule.FAMILY_ALGORITHM:
            return JsonResponse({}, status=404)

        # Is this rule representable in the legacy format?
        _, errors = convert_to_triage_legacy({'conditions': srule.conditions})
        if errors.get('errors') or not srule.conditions:
            return JsonResponse({}, status=404)

        serializer = self.get_serializer(srule)
        serialized_data = self._to_external_repr(serializer)

        return Response(serialized_data)

    def put(self, request, *args, **kwargs):
        self._to_internal_repr()
        legacy_payload = request.data
        tf = TriageFacilitator(request, 'api')
        valid, errors = tf.validate_legacy_rule(legacy_payload, rest_api_v2=True)

        if errors:
            errors.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return JsonResponse(errors, status=422)

        # Now convert the legacy payload into the new format
        nme_payload, errors = convert_to_triage_nme(legacy_payload)
        if errors:
            return JsonResponse(
                {'errors': 'This update cannot be represented by the triage engine', '_meta': {'level': 'error'}}, status=422
            )

        kwargs['f_id'] = '/' + kwargs['pk']
        kwargs['rest_api_v2'] = True
        output = TriageFacilitator(request, 'api').edit_triage_filter(kwargs['pk'], nme_payload, stats_reason='user_edit_filter_api_v2.0')

        if output['status_code'] == TriageFacilitator.STATUS_CODE_SUCCESS:
            serializer = self.get_serializer(output['data'])
            serialized_data = self._to_external_repr(serializer) if serializer else output.get('errors', {})
            edit_response = {'data': serialized_data, '_meta': {'level': 'success', 'message': 'Successfully edited triage filter'}}
            return JsonResponse(edit_response, status=200)
        else:
            error_response = output.get('errors') or {}
            error_response.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return JsonResponse(error_response, status=output['status_code'])

    def delete(self, request, *args, **kwargs):
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._post_query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        restore_detections = self.request.query_params.get('restore_detections')
        triage_filter_id = kwargs['pk']
        if restore_detections in ('True', 'true', '1'):
            output = TriageFacilitator(request, 'api').delete_triage_filter(triage_filter_id, stats_reason='user_delete_filter_api_v2.0')
        else:
            output = TriageFacilitator(request, 'api').convert_triage_filter_to_singleton(triage_filter_id)

        return JsonResponse({}, status=output['status_code'])


class RuleV2_1(RuleV2_0):
    """
    V2.1 API request handler. Can be used to perform get/edit/delete on a single simple or complex rule (triage filter)
    """

    serializer_class = RuleSerializerV2_1

    def put(self, request, *args, **kwargs):
        self._to_internal_repr()
        payload = request.data

        errors = {}
        # The user needs to send 'source_conditions' and 'additional_conditions', even though our code references 'sourceConditions' and 'additionalConditions'
        if 'source_conditions' not in payload:
            errors['source_conditions'] = ['The field "source_conditions" is missing. Maybe you used "sourceConditions"?']
        if 'additional_conditions' not in payload:
            errors['additional_conditions'] = ['The field "additional_conditions" is missing. Maybe you used "additionalConditions"?']
        if errors:
            errors.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return JsonResponse(errors, status=422)

        # Since the rest of our code base expects camelcase 'sourceConditions' and 'additionalConditions', reformat now
        source = payload.pop('source_conditions')
        additional = payload.pop('additional_conditions')
        payload['sourceConditions'] = source
        payload['additionalConditions'] = additional

        # Now convert the legacy payload into the new format
        kwargs['f_id'] = '/' + kwargs['pk']
        output = TriageFacilitator(request, 'api').edit_triage_filter(kwargs['pk'], payload, stats_reason='user_edit_filter_api_v2.1')

        if output['status_code'] == TriageFacilitator.STATUS_CODE_SUCCESS:
            serializer = self.get_serializer(output['data'])
            serialized_data = self._to_external_repr(serializer) if serializer else output.get('errors', {})
            edit_response = {'data': serialized_data, '_meta': {'level': 'success', 'message': 'Successfully edited triage filter'}}
            return JsonResponse(edit_response, status=200)
        else:
            error_response = output.get('errors') or {}
            error_response.update({'_meta': {'level': 'error', 'message': 'Invalid field(s) found'}})
            return JsonResponse(error_response, status=output['status_code'])

    def get(self, request, *args, **kwargs):
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._get_query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        srule = self.get_object()
        # never return algorithm family rules
        if srule.family == smart_rule.FAMILY_ALGORITHM or not srule.conditions:
            return JsonResponse({}, status=404)

        serializer = self.get_serializer(srule)
        serialized_data = self._to_external_repr(serializer)

        return Response(serialized_data)


class UsersV2(APIV2ListView, QueryParameterValueValidationMixin):
    """
    V2 API endpoint to view a list of all users.
    """

    permission = 'users'

    serializer_class = UserSerializerV2
    pagination_class = StandardResultSetPagination
    metadata_class = UserMetadataV2

    _query_params = (
        'username',
        'role',
        'account_type',
        'authentication_profile',
        'last_login_gte',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
    )

    _gt_zero_params = (StandardResultSetPagination.page_query_param,)
    _str_params = ('username', 'role', 'account_type', 'authentication_profile', StandardResultSetPagination.page_size_query_param)
    _timestamp_params = ('last_login_gte',)

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        queryset = User.objects.visible_users()

        username = self.request.query_params.get('username')
        role = self.request.query_params.get('role')
        account_type = self.request.query_params.get('account_type')
        authentication_profile = self.request.query_params.get('authentication_profile')
        last_login_gte = self.request.query_params.get('last_login_gte')

        if username is not None:
            queryset = queryset.filter(username=username)
        if role is not None:
            if self.request.api_version > 3.3:
                queryset = queryset.filter(groups__name=role)
            else:
                queryset = queryset.filter(groups__group_extend__vname=role)
        if account_type is not None:
            queryset = queryset.filter(account_type=account_type)
        if authentication_profile is not None:
            queryset = queryset.filter(
                Q(authentication_profile__name=authentication_profile)
                | Q(ldap_profile__name=authentication_profile)
                | Q(saml_profile__name=authentication_profile)
            )
        if last_login_gte is not None:
            queryset = queryset.filter(last_login__gte=last_login_gte)

        return queryset.distinct()

    def list(self, request, *args, **kwargs):
        user_queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(user_queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            user_data = serializer.data
            return self.get_paginated_response(user_data)

        serializer = self.get_serializer(user_queryset, many=True)
        return Response(serializer.data)


class UserV2(APIV2RetrieveView):
    """
    V2 API endpoint to view a single user.
    """

    permission = 'users'

    serializer_class = UserSerializerV2

    queryset = User.objects.visible_users()

    lookup_field = 'id'

    _query_params = ('account_type', 'authentication_profile')
    TYPE_OBJECTS = {'TACACS': AuthenticationProfile, 'LDAP': LDAPProfile, 'RADIUS': AuthenticationProfile}

    def patch(self, request, *args, **kwargs):
        bad_query_params = [param for param in request.data.keys() if param not in self._query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        user_id = kwargs['id']
        try:
            account_type = request.data['account_type']
        except KeyError:
            return JsonResponse({'errors': [{'title': 'No account type found in request'}]}, status=400)

        try:
            authentication_profile_name = request.data['authentication_profile']
        except KeyError:
            return JsonResponse({'errors': [{'title': 'No authentication profile found in request'}]}, status=400)

        try:
            user = self.queryset.get(id=user_id)
        except User.DoesNotExist:
            return JsonResponse({}, status=404)

        current_account_type = user.account_type

        if user.id == request.user.id:
            return JsonResponse({'errors': [{'title': 'User may not change their own account type'}]}, status=400)

        authentication_profile = None
        if account_type in list(self.TYPE_OBJECTS.keys()):
            try:
                authentication_profile = self.TYPE_OBJECTS[account_type].objects.get(name=authentication_profile_name)
            except (KeyError, ObjectDoesNotExist):
                return JsonResponse({'errors': [{'title': 'Could not find requested object'}]}, status=404)

        if current_account_type == 'LDAP':
            current_authentication_profile_name = user.ldap_profile.name
        elif current_account_type in ('TACACS', 'RADIUS'):
            current_authentication_profile_name = user.authentication_profile.name
        elif current_account_type == 'SAML':
            current_authentication_profile_name = user.saml_profile.name
        else:
            current_authentication_profile_name = None

        try:
            user.switch_type(account_type, authentication_profile)
        except AssertionError as e:
            return JsonResponse({'errors': [{'title': str(e)}]}, status=400)

        AUDIT.audit(
            get_audit_user_data(request),
            True,
            '[updated user@{user_id} with username [{username}] from account_type [{current_account_type}][{current_authentication_profile}] to [{new_account_type}][{new_authentication_profile}]'.format(
                user_id=user.id,
                username=user.username,
                current_account_type=current_account_type,
                current_authentication_profile=current_authentication_profile_name,
                new_account_type=account_type,
                new_authentication_profile=authentication_profile_name,
            ),
        )

        return JsonResponse({'type': account_type, 'profile': authentication_profile_name}, status=200)


class BaseFeedView:
    """V2 API base class to set authentication"""

    authentication_classes = (
        VUITokenAuthentication,
        ApplianceApiClientAuthentication,
    )
    permission_classes = (permissions.IsAuthenticated, RoleBasedPermission)


class ThreatFeeds(BaseFeedView, ThreatFeedList, QueryParameterValueValidationMixin):
    """V2 API maps directly to ThreatFeedList REST API"""

    ordering_fields = (
        'name',
        'lastUpdatedBy',
        'lastUpdated',
        'category',
        'certainty',
        'expiration',
    )
    _query_params = (
        'ordering',
        'category',
        'certainty',
        'expiration',
        'last_updated',
        'last_updated_by',
        'name',
        'expiration_gte',
        'expiration_lte',
        'last_updated_gte',
        'last_updated_lte',
    )
    _str_params = ('ordering', 'name', 'last_updated_by')

    _timestamp_params = (
        'expiration',
        'last_updated',
        'last_updated_lte',
        'last_updated_gte',
        'expiration_gte',
        'expiration_lte',
    )
    _choices_params = {'certainty': ['High', 'Medium', 'Low'], 'category': ['cnc', 'lateral', 'exfil']}

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)
        return super().get_queryset()


class ThreatFeed(BaseFeedView, ThreatFeedDetail):
    """
    V2 API maps directly to ThreatFeedDetail REST API.
    POST requests also auto link the file to avoid an additional PATCH request
    """

    def post(self, request, feed_id):
        return super(ThreatFeed, self).post(request, feed_id, auto_link=True)


class ThreatFeedFile(BaseFeedView, ThreatFeedFileDownload):
    """V2 API maps directly to ThreatFeedFileDownload REST API"""

    pass


class BaseProxyView(object):
    """V2 API base class to set authentication"""

    authentication_classes = (
        VUITokenAuthentication,
        ApplianceApiClientAuthentication,
    )
    permission_classes = (permissions.IsAuthenticated, RoleBasedPermission)


class Proxies(BaseProxyView, ProxyListView):
    """V2 API maps directly to ProxyListView REST API"""

    pass


class ProxyDetail(BaseProxyView, ProxyDetailView):
    """V2 API maps directly to ProxyListDetail REST API"""

    pass


class Tagging(APIV2RetrieveView, BodyParameterValueValidationMixin):
    """V2 API for PATCHing the tag list"""

    permission = 'tag'

    _tag_list_params = ('tags',)

    def get(self, request, table, tag_id, *args, **kwargs):
        """return the tags associated with the object specified by tag_id"""
        try:
            elem = lib_tagging.TAG_OBJECTS[table].objects.get(id=tag_id)
        except (KeyError, ObjectDoesNotExist):
            return JsonResponse({'status': 'failure', 'message': 'Could not find requested object'}, status=404)

        try:
            # grab the tags currently associated with the object
            return JsonResponse({'status': 'success', 'tag_id': tag_id, 'tags': list(elem.tags.values_list('name', flat=True))})
        except Exception:
            return JsonResponse({'status': 'failure', 'message': 'Unknown server error'}, status=500)

    def patch(self, request, table, tag_id, *args, **kwargs):
        """body of patch request contains list of tags"""
        tag_id = int(tag_id)

        try:
            tag_names = request.data['tags']
        except KeyError:
            return JsonResponse({'status': 'failure', 'message': 'no tags contained in request'}, status=400)

        if type(tag_names) is not list:
            return JsonResponse({'status': 'failure', 'message': 'Tags must be a list of valid strings'}, status=400)

        bad_body_param_values = self._validate_body_param_values()
        if bad_body_param_values:
            raise InvalidQueryParamValues(bad_body_param_values)

        try:
            lib_tagging.set_tags(
                table=table, object_id=tag_id, tag_names=tag_names, audit_user_data=get_audit_user_data(request), request=request
            )
        except (custom_errors.InvalidTableError, custom_errors.ObjectNotFoundError):
            return JsonResponse({'status': 'failure', 'message': 'Could not find requested object'}, status=404)
        except Exception:
            LOG.exception('Failed to set tags.')
            return JsonResponse({'status': 'failure', 'message': 'Unknown server error'}, status=500)

        return JsonResponse({'status': 'success', 'tag_id': tag_id, 'tags': tag_names})


class TaggingV2_2(Tagging):
    permission = 'tag'

    def _fix_table(self, table):
        return 'linked_account' if table == 'account' else table

    def get(self, request, table, tag_id, *args, **kwargs):
        return super().get(request, self._fix_table(table), tag_id, *args, **kwargs)

    def patch(self, request, table, tag_id, *args, **kwargs):
        return super().patch(request, self._fix_table(table), tag_id, *args, **kwargs)


class BulkTagging(APIV2GenericView, BodyParameterValueValidationMixin):
    """V2 API for adding and removing tags"""

    permission = 'tag'

    @staticmethod
    def _perform_tagging_operation(request, table, tagging_function):
        try:
            tag_name = request.data['tag']
        except KeyError:
            return JsonResponse({'status': 'failure', 'message': 'no tag contained in request'}, status=400)

        try:
            object_ids = request.data['objectIds']
        except KeyError:
            return JsonResponse({'status': 'failure', 'message': 'no objectIds contained in request'}, status=400)

        try:
            tagging_function(
                table=table, object_ids=object_ids, tag_name=tag_name, audit_user_data=get_audit_user_data(request), request=request
            )
        except custom_errors.InvalidTableError:
            return JsonResponse({'status': 'failure', 'message': 'Invalid table: {table}'.format(table=table)}, status=404)
        except custom_errors.InvalidTagError:
            return JsonResponse({'status': 'failure', 'message': 'Invalid tag: {tag}'.format(tag=tag_name)}, status=400)
        except Exception:
            LOG.exception('Failed to perform tagging operation.')
            return JsonResponse({'status': 'failure', 'message': 'Unknown server error'}, status=500)

        return JsonResponse({'status': 'success'})

    def post(self, request, table, *args, **kwargs):
        return self._perform_tagging_operation(request=request, table=table, tagging_function=lib_tagging.add_tag)

    def delete(self, request, table, *args, **kwargs):
        return self._perform_tagging_operation(request=request, table=table, tagging_function=lib_tagging.remove_tag)


class BulkTaggingV2_2(BulkTagging):
    def _fix_table(self, table):
        return 'linked_account' if table == 'account' else table

    def post(self, request, table, *args, **kwargs):
        return super().post(request, self._fix_table(table), *args, **kwargs)

    def delete(self, request, table, *args, **kwargs):
        return super().delete(request, self._fix_table(table), *args, **kwargs)


@api_view(['OPTION', 'GET', 'POST', 'PUT', 'PATCH', 'DELETE'])
def api_v2_handler404(request):
    """V2 API Page Not Found"""
    return Response({'status': 404, 'reason': 'Page Not Found'}, status=status.HTTP_404_NOT_FOUND)


class InvalidContentTypeError(Exception):
    """Exception to raise when content-type is not passed in."""

    pass


class AWSHostIDConnector(APIV2RetrieveView):
    """API for AWS External Connector for Host ID."""

    allowed_methods = ("GET", "POST")
    permission = "setting_aws"
    # FIXME Is there access logs for this out of the box?

    def get(self, request, *args, **kwargs):
        """Return information about configured AWS connectors."""
        try:
            aws_config = CognitoConfigView.retrieve_aws_config()
            if aws_config:
                body = {"credentials": aws_config["values"]["credentials"]}
                return JsonResponse(body, status=200)
        except Exception:
            LOG.exception("Error getting external connectors for AWS Accounts")
        return JsonResponse({"error": "Internal server error"}, status=500)

    def post(self, request, *args, **kwargs):
        """Update AWS external connectors."""
        try:
            # Ensure credentials field is included in request
            json.loads(request.body)["credentials"]
            # Ensure correct content type is in request
            if request.content_type != "application/json":
                raise InvalidContentTypeError
            return CognitoConfigView.post_aws_config(request)
        except json.decoder.JSONDecodeError:
            return JsonResponse({"error": "Payload must be valid JSON"}, status=400)
        except KeyError:
            return JsonResponse({"error": "'credentials' is a required field"}, status=400)
        except InvalidContentTypeError:
            return JsonResponse({"error": "Unsupported Media Type"}, status=415)
        except Exception:
            LOG.exception("Error updating AWS external connectors")
            return JsonResponse({"error": "Internal server error"}, status=500)


class CampaignsV2(CampaignBase, APIV2RetrieveView):
    """V2 API for campaign details (uses APIView and Token Auth)"""

    EVENTS_DISPLAY_LIMIT = 500

    def _process_internal_response(self, campaign_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
        """
        campaign_data.pop('campaign_url', None)
        if campaign_data.get('reason'):
            campaign_data['reason'].pop('detection_url', None)
            campaign_data['reason']['src_host'].pop('host_url', None)

        if campaign_data.get('detection_summaries'):
            for det_summary in campaign_data['detection_summaries']:
                det_summary.pop('detection_url', None)
                det_summary['src_host'].pop('host_url', None)

        if campaign_data.get('host_summaries'):
            for host_summary in campaign_data['host_summaries']:
                host_summary.pop('host_url', None)

        if campaign_data.get('events'):
            for event in campaign_data['events']:
                event.pop('detection_url', None)
                event['src_host'].pop('host_url', None)
                if event.get('dst_hosts'):
                    for dst_host in event['dst_hosts']:
                        dst_host.pop('host_url', None)

        return campaign_data

    def get(self, request, pk, *args, **kwargs):
        """Return information for a single campaign"""
        try:
            api_processor = CampaignAPIProcessor(request)
            campaign = Campaign.objects.get(id=pk)
            campaign_detections = self.get_detections(campaign).exclude(smart_rule__is_whitelist=True)
            campaign_detections = campaign_detections.annotate(
                first_seen_dd=Min('host_details__campaignevent__timestamp'), last_seen_dd=Max('host_details__campaignevent__timestamp')
            )
            campaign_detections_data = api_processor.process_campaign_detections_data(campaign_detections)

            campaign_events = campaign.events.filter(timestamp__isnull=False, partial=False)[: self.EVENTS_DISPLAY_LIMIT]
            campaign_events_data = api_processor.process_campaign_events_data(campaign_events)

            campaign_hosts = host.objects.filter(
                Q(host_session__campaignmembership__campaign=campaign) & Q(host_session__campaignmembership__events__partial=False)
            )
            campaign_hosts = campaign_hosts.annotate(
                first_seen=Min('host_session__campaignmembership__events__timestamp'),
                last_seen=Max('host_session__campaignmembership__events__timestamp'),
            )
            campaign_hosts_data = api_processor.process_campaign_hosts_data(campaign_hosts)

            campaign_data = self.get_campaign_data(campaign, destination='api', request=request)
            campaign_data['events'] = campaign_events_data
            campaign_data['detection_summaries'] = campaign_detections_data
            campaign_data['host_summaries'] = campaign_hosts_data

            if kwargs.get('internal', False):
                campaign_data = self._process_internal_response(campaign_data)

            return JsonResponse(campaign_data, status=200)
        except Campaign.DoesNotExist:
            return JsonResponse({'errors': [{'title': 'Campaign not found'}]}, status=404)
        except Exception:
            return JsonResponse({'errors': [{'title': 'Could not retrieve campaign data'}]}, status=500)


class CampaignsListV2(CampaignBase, APIV2ListView, QueryParameterValueValidationMixin):
    """V2 API to list basic information for all campaigns"""

    serializer_class = CampaignSerializerV2
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    ordering_fields = ('last_updated',)
    ordering = ('-last_updated',)

    _query_params = (
        'fields',
        'dst_ip',
        'target_domain',
        'state',
        'name',
        'last_updated_gte',
        'note_modified_timestamp_gte',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
    )
    _choices_params = {
        'fields': list(CampaignSerializerV2.Meta.fields),
        'state': [Campaign.INIT, Campaign.ACTIVE, Campaign.CLOSED, Campaign.CLOSED_NEVER_ACTIVE],
    }
    _str_params = ('dst_ip', 'target_domain', 'state', 'name')
    _timestamp_params = ('last_updated_gte', 'note_modified_timestamp_gte')

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        queryset = Campaign.objects.all()

        external_ip = self.request.query_params.get('dst_ip')
        external_domain = self.request.query_params.get('target_domain')
        state = self.request.query_params.get('state')
        name = self.request.query_params.get('name')
        last_updated_gte = parse_time_string(self.request.query_params.get('last_updated_gte'))
        note_ts_gte = parse_time_string(self.request.query_params.get('note_modified_timestamp_gte'))

        if external_ip is not None:
            queryset = queryset.filter(external_ip=external_ip)
        if external_domain is not None:
            queryset = queryset.filter(external_domain__icontains=external_domain)
        if state is not None:
            queryset = queryset.filter(state=state)
        if name is not None:
            queryset = queryset.filter(name=name)
        if last_updated_gte is not None:
            queryset = queryset.filter(last_updated__gte=last_updated_gte)
        if note_ts_gte is not None:
            id_values = get_note_modified_gte('campaign', note_ts_gte)
            queryset = queryset.filter(id__in=id_values)

        return queryset.distinct()

    def list(self, request, *args, **kwargs):
        camp_queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(camp_queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            camp_data = serializer.data
            fields = request.query_params.get('fields')
            if fields:
                fields = fields.split(',')
                camp_data = [clear_requested_fields(fields, camp_serialized_data) for camp_serialized_data in camp_data]
            return self.get_paginated_response(camp_data)

        serializer = self.get_serializer(camp_queryset, many=True)
        return Response(serializer.data)


class TrafficV2(APIV2ListView):
    """
    API V2 endpoint for traffic stats
    """

    serializer_class = serializers.Serializer
    pagination_class = StandardResultSetPagination

    permission = 'traffic'

    def list(self, request, *args, **kwargs):
        date_now = timezone.now()
        date_now_ts = datetime.strftime(timezone.localtime(date_now), "%m/%d/%Y, %H:%M")
        time_stride = timezone.now() - timedelta(hours=8)
        time_stride_ts = datetime.strftime(timezone.localtime(time_stride), "%m/%d/%Y, %H:%M")

        sensor_luid = kwargs.get('sensor_luid', 'all')

        # Set session luid variable, should always be "all"
        request.session['health_default_luid'] = sensor_luid

        # Fetch traffic data from couch for overall traffic and overall traffic breakdown graphs
        try:
            all_health_data = get_couch_traffic(sensor_luid, include_traffic=True, include_subnets=False)
            traffic_data = all_health_data.get('traffic', [])
            page = self.paginate_queryset(traffic_data)
            # page is None if pagination is disabled
            return Response(traffic_data) if page is None else self.get_paginated_response(traffic_data)

        except Exception:
            LOG.exception('Could not load data from couchdb for overall traffic data')
            return JsonResponse({'errors': [{'title': 'Could not retrieve traffic data.'}]}, status=500)


class SubnetsV2(APIV2ListView):
    """V2 API maps directly to SubnetList REST API"""

    permission = 'traffic'

    _editable_fields = ()
    _orderable_fields = ('subnet', 'hosts', 'firstSeen', 'lastSeen')
    _query_params = ('ordering', 'search')

    serializer_class = SubnetSerializerV2
    pagination_class = StandardResultSetPagination

    def _apply_query_ordering(self, data_queryset, order_field, default_field):
        """
        Validates and applies the ordering query parameter to the collected data.
        If the ordering field passed is invalid, uses the passed default field.
        """

        order_field_parsed = re.sub(r"^-", "", order_field)
        if order_field_parsed not in self._orderable_fields:
            order_field = default_field

        reverse = order_field.startswith('-')
        order_field = re.sub(r"^-", "", order_field)

        key = lambda x: x[order_field]
        if order_field == 'subnet':
            key = lambda x: ipaddress.ip_address(x['subnet'])

        sorted_queryset = sorted(data_queryset, key=key, reverse=reverse)

        return sorted_queryset

    def _get_query_params(self, request):
        """
        Retrieves valid query parameters for GET requests.
        """
        return {param: request.GET.get(param, '') for param in self._query_params}

    def list(self, request, *args, **kwargs):
        sensor_luid = kwargs.get("sensor_luid", "all")
        try:
            traffic_stats = get_couch_traffic_stats(sensor_luid=sensor_luid)
            subnets = traffic_stats['subnets']

            for item in subnets:
                item['id'] = hashlib.sha1(item['network_addr'].encode('utf8')).hexdigest()
                item['subnet'] = item['network_addr']
                item['hosts'] = item['num_hosts']
                item['firstSeen'] = timezone.localtime(parser.parse(item['first_seen']))
                item['lastSeen'] = timezone.localtime(parser.parse(item['last_seen']))

            query_params = self._get_query_params(request)

            if query_params['search']:
                subnets = [subnet for subnet in subnets if query_params['search'] in subnet['subnet']]

            subnets = self._apply_query_ordering(subnets, query_params['ordering'], '-firstSeen')
            page = self.paginate_queryset(subnets)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(subnets, many=True)
            return Response(serializer.data)

        except Exception as ex:
            LOG.exception(ex)
            return JsonResponse({'errors': [{'title': 'Could not retrieve subnets.'}]}, status=500)


class IPAddressesV2(APIV2RetrieveView, QueryParameterValueValidationMixin):
    """
    API V2 endpoint for IP Address (host session) data
    """

    permission = 'host'

    _query_params = ['include_ipv4', 'include_ipv6']
    _bool_params = ('include_ipv4', 'include_ipv6')

    def get(self, request, *args, **kwargs):
        try:
            bad_query_params = [param for param in self.request.query_params.keys() if param not in self._query_params]
            if bad_query_params:
                raise InvalidQueryParams(bad_query_params)

            bad_query_param_values = self._validate_query_param_values()
            if bad_query_param_values:
                raise InvalidQueryParamValues(bad_query_param_values)

            include_ipv4 = str2bool(request.query_params.get('include_ipv4', 'true'))
            include_ipv6 = str2bool(request.query_params.get('include_ipv6', 'true'))
            raw_hs_data = PlatformClient().get_host_sessions(include_ipv4=include_ipv4, include_ipv6=include_ipv6)

            formatted_hs_data = [
                {'ip': doc['ip'], 'first_seen': doc['first_seen'], 'last_seen': doc['last_seen']} for doc in raw_hs_data.values()
            ]

            return Response(formatted_hs_data)
        except InvalidQueryParams as e:
            return JsonResponse({'errors': [{'type': 'InvalidQueryParamsError', 'title': str(e)}]}, status=422)
        except InvalidQueryParamValues as e:
            return JsonResponse({'errors': [{'type': 'InvalidQueryParamValuesError', 'title': str(e)}]}, status=422)
        except Exception:
            LOG.exception('Could not load data from platform rest for ip addresses')
            return JsonResponse({'errors': [{'title': 'Could not retrieve ip address data.'}]}, status=500)


class BaseVadminEndpointV2(APIV2RetrieveView):
    """
    Base class for V2 API Endpoint that is only accessible by vadmin
    """

    authentication_classes = (
        VUITokenAuthentication,
        ApplianceApiClientAuthentication,
    )
    permission_classes = (permissions.IsAuthenticated, VadminOnlyPermission)


class SettingsSyslogConfigV2(APIV2RetrieveView):
    """
    V2 API Endpoint that manages Syslog config filters to supplement UI settings
    """

    allowed_methods = ('GET', 'POST')
    permission = 'notification_syslog'

    def get(self, request, *args, **kwargs):
        try:
            couch = couch_utils.server(FULL_COUCH_LOCATION)
            db = couch['configuration']
            syslog_servers = db['syslog_config']['syslog_servers']
        except Exception:
            LOG.warning('Could not retrieve syslog_config doc from couchdb')
            return JsonResponse(
                {'error': 'syslog not configured. visit Settings > Notifications to configure a remote syslog destination'}, status=404
            )

        for server in syslog_servers:
            if server.get('server'):
                if not server.get('filters'):
                    server['filters'] = copy.deepcopy(AuditLoggerSettings.DEFAULT_LOG_FILTERS)
                    for filter, v in server['filters'].items():
                        if filter in AuditLoggerSettings.UI_LOG_FILTERS:
                            v['enabled'] = not set(server['types']).isdisjoint(set(v['info']['syslog_message_types']))
                        v.pop('info')

        return JsonResponse({'syslog_servers': syslog_servers})

    def post(self, request, *args, **kwargs):
        post_data = request.data
        validated_config, errors = self.validate_syslog_config(post_data)
        if errors:
            return JsonResponse({'errors': errors}, status=500)

        try:
            couch = couch_utils.server(FULL_COUCH_LOCATION)
            db = couch['configuration']
            syslog_config_doc = db['syslog_config']
            syslog_config_doc['syslog_servers'] = validated_config['syslog_servers']
            success, status, message = SyslogConfig().set_running_config(
                syslog_config_doc['syslog_servers'], lib_tv.get_audit_user_data(request)
            )
            if success:
                db.save(syslog_config_doc)
        except Exception:
            LOG.exception('Could not retrieve syslog_config doc from couchdb')
            return JsonResponse(
                {'error': 'syslog not configured. visit Settings > Notifications to configure a remote syslog destination'}, status=404
            )

        return JsonResponse({'status': 'success' if status == 200 else 'error', 'message': message}, status=status)

    def validate_syslog_config(self, post_data):
        """
        post_data is a dict with "syslog_servers" : [ ... ]
        """
        errors = []
        server_expected_keys = ['cef', 'proto', 'server', 'port', 'types', 'filters']
        if list(post_data.keys()) != ['syslog_servers']:
            errors.append('syslog_servers missing from post data')
        elif not isinstance(post_data.get('syslog_servers'), list) or len(post_data.get('syslog_servers')) != 3:
            errors.append('syslog_servers is not a list and does not contain 3 objects')
        for server in post_data.get('syslog_servers', []):
            if server.get('server'):
                missing_expected_keys = set(server_expected_keys).difference(set(server.keys()))
                if len(missing_expected_keys) > 0:
                    errors.append('syslog server: {} missing expected keys: {}'.format(server.get('server'), missing_expected_keys))
                else:
                    for filter_name, filter_data in server.get('filters').items():
                        filter_data['info'] = AuditLoggerSettings.DEFAULT_LOG_FILTERS[filter_name]['info']

        return post_data, errors


class SettingsKafkaConfigV2(APIV2RetrieveView):
    """
    V2 API Endpoint that manages Kafka config filters to supplement UI settings
    """

    allowed_methods = ('GET', 'POST')
    permission = 'notification_kafka'

    def get(self, request, *args, **kwargs):
        try:
            couch = couch_utils.server(FULL_COUCH_LOCATION)
            db = couch['configuration']
            kafka_servers = db['syslog_config']['kafka_servers']
        except Exception:
            LOG.warning('Could not retrieve syslog_config doc from couchdb')
            return JsonResponse(
                {'error': 'syslog/kafka not configured. visit Settings > Notifications to configure a remote kafka destination'}, status=404
            )

        for server in kafka_servers:
            if server.get('bootstrap_servers'):
                if not server.get('filters'):
                    server['filters'] = copy.deepcopy(AuditLoggerSettings.DEFAULT_LOG_FILTERS)
                    for filter, v in server['filters'].items():
                        if filter in AuditLoggerSettings.UI_LOG_FILTERS:
                            v['enabled'] = not set(server['types']).isdisjoint(set(v['info']['syslog_message_types']))
                        v.pop('info')

        return JsonResponse({'kafka_servers': kafka_servers})

    def post(self, request, *args, **kwargs):
        post_data = request.data
        validated_config, errors = self.validate_kafka_config(post_data)
        if errors:
            return JsonResponse({'errors': errors}, status=400)

        try:
            couch = couch_utils.server(FULL_COUCH_LOCATION)
            db = couch['configuration']
            syslog_config_doc = db['syslog_config']
            syslog_config_doc['kafka_servers'] = validated_config['kafka_servers']
            success, status, message = KafkaConfig().set_running_config(
                syslog_config_doc['kafka_servers'], lib_tv.get_audit_user_data(request)
            )
            if success:
                db.save(syslog_config_doc)
        except Exception:
            LOG.exception('Could not retrieve syslog_config doc from couchdb')
            return JsonResponse(
                {'error': 'syslog/kafka not configured. visit Settings > Notifications to configure a remote syslog destination'},
                status=404,
            )
        return JsonResponse({'status': 'success' if status == 200 else 'error', 'message': message}, status=status)

    def validate_kafka_config(self, post_data):
        """
        post_data is a dict with "kafka_servers" : [ ... ]
        """
        errors = []
        server_expected_keys = ['cef', 'proto', 'topic', 'bootstrap_servers', 'types', 'filters']
        if list(post_data.keys()) != ['kafka_servers']:
            errors.append('kafka_servers missing from post data')
        elif not isinstance(post_data.get('kafka_servers'), list) or len(post_data.get('kafka_servers')) != 3:
            errors.append('kafka_servers is not a list and does not contain 3 objects')
        for server in post_data.get('kafka_servers', []):
            if server.get('bootstrap_servers'):
                # validate that bootstrap_servers of the form 'ip:port'
                for bootstrap_server in server.get('bootstrap_servers'):
                    if len(bootstrap_server.split(':')) != 2:
                        errors.append('kafka server: {} missing valid bootstrap_servers as ip:port'.format(server.get('bootstrap_servers')))
                    else:
                        bootstrap_server_ip, bootstrap_server_port = bootstrap_server.split(':')
                        if not validator_utils.validate_port(bootstrap_server_port):
                            errors.append('kafka server: {} missing valid bootstrap_server port'.format(server.get('bootstrap_servers')))
                        if not (validator_utils.validate_ip(bootstrap_server_ip) or validator_utils.validate_domain(bootstrap_server_ip)):
                            errors.append('kafka server: {} missing valid bootstrap_server ip'.format(server.get('bootstrap_servers')))

                # check for missing keys
                missing_expected_keys = set(server_expected_keys).difference(set(server.keys()))
                if len(missing_expected_keys) > 0:
                    errors.append(
                        'kafka server: {} missing expected keys: {}'.format(server.get('bootstrap_servers'), missing_expected_keys)
                    )
                # inject the filter info information that is not required to be supplied by user
                else:
                    for filter_name, filter_data in server.get('filters').items():
                        filter_data['info'] = AuditLoggerSettings.DEFAULT_LOG_FILTERS[filter_name]['info']

        return post_data, errors


class SettingsGroupV2(BaseVadminEndpointV2):
    """
    V2 API Endpoint that returns the current configuration for a settings section
    """

    def get(self, request, section, *args, **kwargs):
        if section == 'metadata':
            # metadata settings are not viewable by vadmin
            return JsonResponse({}, status=403)

        settings_api = SettingsGroupBaseAPI()
        requested_settings_group = settings_api.settings_group_avail.get(section)
        if not requested_settings_group:
            return JsonResponse({}, status=404)
        if not requested_settings_group.required_flags_enabled(request=request):
            return JsonResponse({}, status=404)

        return JsonResponse(requested_settings_group.get_settings_data_json(), status=200)

    def post(self, request, section, *args, **kwargs):
        return JsonResponse({}, status=403)


class SettingsInternalNetworkV2(APIV2RetrieveView, BodyParameterValueValidationMixin):
    """
    V2 API Endpoint for configuring the internal and external IPs
    """

    permission = 'system_internal_ip'

    _query_params = ()
    _body_params = ('include', 'exclude', 'drop')
    _cidr_ip_list_params = ('include', 'exclude', 'drop')

    def get(self, request, *args, **kwargs):
        """
        Retrieve the included, excluded, and dropped Internal Networks
        """
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        platform_client = PlatformClient()
        doc = platform_client.get_internal_networks_config()
        response = {'included_subnets': [], 'excluded_subnets': [], 'dropped_subnets': []}
        for included_subnet in doc.get('networks', []):
            response['included_subnets'].append('{ip}/{cidr}'.format(ip=included_subnet['net_id'], cidr=str(included_subnet['mask'])))

        for excluded_subnet in doc.get('excludes', []):
            response['excluded_subnets'].append('{ip}/{cidr}'.format(ip=excluded_subnet['net_id'], cidr=str(excluded_subnet['mask'])))

        for dropped_subnet in doc.get('drop_networks', []):
            response['dropped_subnets'].append('{ip}/{cidr}'.format(ip=dropped_subnet['net_id'], cidr=str(dropped_subnet['mask'])))

        return JsonResponse(response, status=200)

    def post(self, request, *args, **kwargs):
        """
        Change the included, excluded, and dropped internal network configuration
        """
        bad_query_params = [param for param in self.request.data.keys() if param not in self._body_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        bad_body_param_values = self._validate_body_param_values()
        if bad_body_param_values:
            raise InvalidQueryParamValues(bad_body_param_values)

        platform_client = PlatformClient()
        doc = platform_client.get_internal_networks_config()

        current_doc = copy.deepcopy(doc)
        included_networks = []
        excluded_networks = []
        dropped_networks = []

        try:
            included = request.data.get('include', [])
            for include in included:
                ip_cidr = include.split('/')
                net_id, mask = ip_cidr[0], int(ip_cidr[1])
                included_networks.append({'net_id': net_id, 'mask': mask})
            doc['networks'] = included_networks

            excluded = request.data.get('exclude', [])
            for exclude in excluded:
                ip_cidr = exclude.split('/')
                net_id, mask = ip_cidr[0], int(ip_cidr[1])
                excluded_networks.append({'net_id': net_id, 'mask': mask})
            doc['excludes'] = excluded_networks

            dropped = request.data.get('drop', [])
            for drop in dropped:
                ip_cidr = drop.split('/')
                net_id, mask = ip_cidr[0], int(ip_cidr[1])
                dropped_networks.append({'net_id': net_id, 'mask': mask})
            doc['drop_networks'] = dropped_networks

            platform_client.set_internal_networks_config(doc)
        except IndexError:
            LOG.exception('Unexpected error saving Internal IP network settings')
            return JsonResponse({'errors': [{'title': 'Valid IP CIDR expected'}]}, status=400)
        except Exception:
            LOG.exception('Unexpected error saving Internal IP network settings')
            return JsonResponse({'errors': [{'title': 'Unexpected error saving Internal IP network settings'}]}, status=500)

        AUDIT.audit(
            get_audit_user_data(request),
            True,
            'Change Public IPs (CIDR) settings from [{current_val}] to [{new_val}]'.format(
                current_val=string_utils.truncate(
                    json.dumps({k: v for k, v in current_doc.items() if not k.startswith('_')}), AUDIT.VALUE_SIZE_LIMIT
                ),
                new_val=string_utils.truncate(json.dumps({k: v for k, v in doc.items() if not k.startswith('_')}), AUDIT.VALUE_SIZE_LIMIT),
            ),
        )

        return JsonResponse({'included': included_networks, 'excluded': excluded_networks, 'dropped': dropped_networks}, status=200)


class UINotification(BaseVadminEndpointV2):
    def get(self, request):
        return JsonResponse({}, status=418)

    def post(self, request):
        return JsonResponse({}, status=418)


class SettingsNotificationsV2(BaseVadminEndpointV2):
    """
    V2 API Endpoint that returns the current requested notifications settings
    """

    def get(self, request, group=None, key=None, *args, **kwargs):
        return NotificationSetting().get(request, group, key)

    def post(self, request, group=None, key=None, *args, **kwargs):
        return JsonResponse({}, status=403)


class SettingsProxyV2(APIV2RetrieveView):
    """
    V2 API Endpoint that returns the current configuration for a settings section
    """

    permission = 'service_proxy_connection'

    def get(self, request, *args, **kwargs):
        if not is_cloud() and cloud_conn.get_proxy():
            proxy = cloud_conn.get_proxy()
            url = proxy.get('https')
        else:
            url = None

        if url:
            parsed_url = urllib.parse.urlparse(url)

        proxy_dict = {
            "proxy": {
                "host": parsed_url.hostname if url else "",
                "enable": bool(url),
                "port": parsed_url.port if url else "",
                "authentication": {
                    "enable": bool(url and parsed_url.username and parsed_url.password),
                    "username": parsed_url.username if url and parsed_url.username and parsed_url.password else "",
                },
            }
        }

        return JsonResponse(proxy_dict, status=200)


class AccountsV2(APIV2ListView, QueryParameterValueValidationMixin, DetectionSummaryMixin):
    """
    V2 API endpoint that represents a list of accounts
    """

    permission = 'account'

    serializer_class = AccountSerializerV2
    allowed_methods = ('GET', 'OPTIONS')
    renderer_classes = (JSONRenderer,)
    pagination_class = StandardResultSetPagination
    filter_backends = (AliasedOrderingFilter,)
    ordering_fields = ('last_seen', 't_score', 'c_score', 'id', ('last_detection_timestamp', 'last_detection_time'))
    ordering = ('-last_seen',)
    metadata_class = AccountMetadataV2

    # fields added by view
    added_fields = ['detection_summaries']

    _query_params = (
        'all',
        'uid',
        'certainty',
        'certainty_gte',
        'c_score',
        'c_score_gte',
        'exclude_fields',
        'fields',
        'first_seen',
        'last_seen',
        'last_source',
        'min_id',
        'max_id',
        'name',
        'note_modified_timestamp_gte',
        'ordering',
        'state',
        'tags',
        'threat',
        'threat_gte',
        't_score',
        't_score_gte',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
        'include_detection_summaries',
        'privilege_level',
        'privilege_level_gte',
        'privilege_category',
        'last_detection_timestamp_gte',
        'last_detection_timestamp_lte',
    )
    _bool_params = ('all', 'active_traffic', 'has_active_traffic', 'include_detection_summaries')
    _choices_params = {
        'fields': list(AccountSerializerV2.Meta.fields) + added_fields,
        'exclude_fields': list(AccountSerializerV2.Meta.fields),
        'state': ('active', 'inactive'),
        'privilege_category': ('low', 'medium', 'high'),
    }
    _gt_zero_params = ('min_id', 'max_id', StandardResultSetPagination.page_query_param, 'privilege_level', 'privilege_level_gte')
    _gte_zero_params = ('certainty', 'certainty_gte', 'c_score', 'c_score_gte', 'threat', 'threat_gte', 't_score', 't_score_gte')
    _ip_params = ('last_source',)
    _str_params = ('fields', 'name', 'uid', 'ordering', 'state', 'tags', StandardResultSetPagination.page_size_query_param)
    _timestamp_params = (
        'first_seen',
        'last_seen',
        'last_detection_timestamp_lte',
        'last_detection_timestamp_gte',
        'note_modified_timestamp_gte',
    )

    def _process_internal_response(self, account_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
        """
        for url_field in ('url', 'detection_set'):
            account_data.pop(url_field, None)
        if account_data.get('detection_summaries'):
            for det_summary in account_data['detection_summaries']:
                det_summary.pop('detection_url', None)

        return account_data

    @property
    def queryset(self):
        """Overridden by V2_2"""
        return Account.objects.all().annotate(last_detection_time=Max('detection__last_timestamp'))

    def get_queryset(self):
        using_linked_account = self.serializer_class.Meta.model is LinkedAccount
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        queryset = self.queryset

        min_id = self.request.query_params.get('min_id')
        max_id = self.request.query_params.get('max_id')
        name = self.request.query_params.get('name') or self.request.query_params.get('uid')
        t_score = self.request.query_params.get('threat') or self.request.query_params.get('t_score')
        t_score_gte = self.request.query_params.get('threat_gte') or self.request.query_params.get('t_score_gte')
        c_score = self.request.query_params.get('certainty') or self.request.query_params.get('c_score')
        c_score_gte = self.request.query_params.get('certainty_gte') or self.request.query_params.get('c_score_gte')
        state = self.request.query_params.get('state')
        last_detection_timestamp = parse_time_string(self.request.query_params.get('last_detection_timestamp'))
        tags = self.request.query_params.get('tags')
        note_ts_gte = parse_time_string(self.request.query_params.get('note_modified_timestamp_gte'))
        priv_level = self.request.query_params.get('privilege_level')
        priv_level_gte = self.request.query_params.get('privilege_level_gte')
        priv_category = self.request.query_params.get('privilege_category')
        last_detection_timestamp_lte = parse_time_string(self.request.query_params.get('last_detection_timestamp_lte'))
        last_detection_timestamp_gte = parse_time_string(self.request.query_params.get('last_detection_timestamp_gte'))

        if min_id is not None:
            queryset = queryset.filter(id__gte=min_id)
        if max_id is not None:
            queryset = queryset.filter(id__lte=max_id)
        if name is not None:
            if using_linked_account:
                # Azure accounts have display names that are not equal to any subaccount uid
                queryset = queryset.filter(Q(display_uid__icontains=name) | Q(uid__icontains=name))
            else:
                queryset = queryset.filter(uid__icontains=name)
        if t_score is not None:
            queryset = queryset.filter(t_score=t_score) if using_linked_account else queryset.filter(linked_account__t_score=t_score)
        if t_score_gte is not None:
            queryset = (
                queryset.filter(t_score__gte=t_score_gte)
                if using_linked_account
                else queryset.filter(linked_account__t_score__gte=t_score_gte)
            )
        if c_score is not None:
            queryset = queryset.filter(c_score=c_score) if using_linked_account else queryset.filter(linked_account__c_score=c_score)
        if c_score_gte is not None:
            queryset = (
                queryset.filter(c_score__gte=c_score_gte)
                if using_linked_account
                else queryset.filter(linked_account__c_score__gte=c_score_gte)
            )
        if state is not None:
            if state == 'inactive':
                # equivalent logic for showing 'inactive' hosts in the UI
                queryset = queryset.filter(Q(state__in=('inactive', 'suspended')) | (Q(state='active') & Q(t_score=0) & Q(c_score=0)))
            else:
                queryset = queryset.filter(state=state)
        if last_detection_timestamp is not None:
            queryset = queryset.filter(last_detection_timestamp=last_detection_timestamp)
        if tags is not None:
            queryset = queryset.filter(_tags__name__in=tags.split(','))
        if note_ts_gte is not None:
            id_values = get_note_modified_gte('host', note_ts_gte)
            queryset = queryset.filter(id__in=id_values)
        if priv_level:
            queryset = queryset.filter(priv_level=priv_level)
        if priv_level_gte:
            queryset = queryset.filter(priv_level__gte=priv_level_gte)
        if priv_category:
            # We don't have direct access to the privilege category from the account model.
            # Translate the category to privilege levels
            levels = get_category_to_privilege_level(priv_category)
            queryset = queryset.filter(priv_level__in=levels)

        queryset = self.filter_last_detection_timetstamp(queryset, last_detection_timestamp_lte, last_detection_timestamp_gte)

        return queryset.distinct()

    def filter_last_detection_timetstamp(self, queryset, last_detection_timestamp_lte, last_detection_timestamp_gte):
        if last_detection_timestamp_lte is not None:
            queryset = queryset.filter(last_detection_time__lte=last_detection_timestamp_lte)
        if last_detection_timestamp_gte is not None:
            queryset = queryset.filter(last_detection_time__gte=last_detection_timestamp_gte)
        return queryset

    def list(self, request, *args, **kwargs):
        if request.api_version < 2.1:
            return Response(status=404)

        account_queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(account_queryset)
        internal = kwargs.get('internal', False)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            account_data = serializer.data
            include_summaries = str2bool(request.query_params.get('include_detection_summaries'))

            fields = request.query_params.get('fields')
            if fields:
                fields = fields.split(',')
                if 'detection_summaries' not in fields:
                    include_summaries = False
                account_data = [clear_requested_fields(fields, account_serialized_data) for account_serialized_data in account_data]

            if include_summaries:
                for account_obj, account_serialized_data in zip(serializer.instance, account_data):
                    try:
                        account_serialized_data['detection_summaries'] = self.generate_detection_summaries(request, account_obj, internal)
                    except Exception:
                        LOG.exception("Unexpected exception encountered when retrieving detection summaries for account %s", account_obj.id)
                        account_serialized_data['detection_summaries'] = None

            if internal:
                for account_serialized_data in account_data:
                    self._process_internal_response(account_serialized_data)

            return self.get_paginated_response(account_data)

        serializer = self.get_serializer(account_queryset, many=True)
        return Response(serializer.data)


class AccountsV2_2(AccountsV2):
    """
    V2.2 API endpoint that represents a list of accounts
    """

    serializer_class = AccountSerializerV2_2

    @property
    def queryset(self):
        return LinkedAccount.objects.all().annotate(last_detection_time=Max('subaccounts__detection__last_timestamp'))


class AccountV2(APIV2RetrieveView, QueryParameterValueValidationMixin, DetectionSummaryMixin):
    """
    V2 API endpoint that represents a single account
    """

    permission = 'account'

    allowed_methods = ('GET', 'OPTIONS', 'PATCH')
    renderer_classes = (JSONRenderer,)
    serializer_class = AccountSerializerV2
    metadata_class = AccountMetadataV2

    # fields added by view
    added_fields = ['detection_summaries', 'note_modified_by', 'url', 'ldap']

    _query_params = (
        'exclude_fields',
        'fields',
        'note_modified_by',
        'include_external',
        'include_access_history',
        'include_detection_summaries',
    )

    _choices_params = {
        'fields': list(AccountSerializerV2.Meta.fields) + added_fields,
        'exclude_fields': list(AccountSerializerV2.Meta.fields),
    }
    _str_params = ('fields',)

    def _process_internal_response(self, account_data):
        """
        Process data for internal responses
            - Remove Hyperlinked fields
        """
        for url_field in ('url', 'detection_set'):
            account_data.pop(url_field, None)
        if account_data.get('detection_summaries'):
            for det_summary in account_data['detection_summaries']:
                det_summary.pop('detection_url', None)
        return account_data

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        return Account.objects.all()

    def _translate_account_context_data(self, data):
        """
        Renames keys and labels for host context data
        """

        remove_keys = []

        field_translations = {
            'ldap': {
                'telephoneNumber': 'telephone_number',
                'userPrincipalName': 'user_principal_name',
                'cn': 'common_name',
                'displayName': 'display_name',
                'lockoutTime': 'lockout_time',
                'distinguishedName': 'distinguished_name',
                'memberOf': 'member_of',
                'groupType': 'group_type',
                'lockoutDuration': 'lockout_duration',
                'objectClass': 'object_class',
                'pwdLastSet': 'pwd_last_set',
                'nETBIOSName': 'netbios_name',
                'employeeType': 'employee_type',
                'nTSecurityDescriptor': 'ntsecurity_descriptor',
                'ou': 'organizational_unit',
                'managedBy': 'managed_by',
                'o': 'organization',
                'objectSid': 'object_sid',
                'mail': 'email',
            }
        }

        remove_fields = {
            'ldap': [
                'userAccountControl',
                'dn',
                'networkAddress',
                'servicePrincipalName',
                'dNSHostName',
                'operatingSystem',
                'machineRole',
                'physicalLocationObject',
                'macAddress',
            ]
        }

        for key in remove_keys:
            data.pop(key, None)

        for key, fields_list in remove_fields.items():
            for field in fields_list:
                if key in data and data[key]:
                    data[key].pop(field, None)

        for key, field_labels in field_translations.items():
            if key in data and data[key]:
                for old_key, new_key in field_labels.items():
                    if old_key in data[key]:
                        # The 'email,' field is deprecated as of APIv2.5 but required to maintain backwards
                        # compatibility. The 'email,' field will be removed by CAT-3247 in APIv2.6.
                        if key == 'ldap' and old_key == 'mail':
                            data[key]['email,'] = data[key][old_key]
                        data[key][new_key] = data[key].pop(old_key)
        return data

    def get(self, request, *args, **kwargs):
        if request.api_version < 2.1:
            return Response(status=404)

        account_obj = self.get_object()
        serializer = self.get_serializer(account_obj)
        serialized_data = serializer.data

        network_account = account_obj.network_subaccount

        include_external = request.query_params.get('include_external') in ('True', 'true', '1')
        if include_external:
            try:
                account_context = self._translate_account_context_data(get_all_account_context(network_account, flatten=True))
                serialized_data['ldap'] = account_context.get('ldap')
            except Exception:
                LOG.exception('Unexpected exception encountered when retrieving account details for account %s', account_obj.id)
                serialized_data['ldap'] = None

        include_access_history = request.query_params.get('include_access_history', 'True') in ('True', 'true', '1')

        if include_access_history and network_account:
            account_access = get_account_access(network_account.id)
            serialized_data['host_access_history'] = keys_to_snake_case(
                account_access['hostAccessHistory'][:MAX_ACC_HOST_ACCESS_HISTORY_LENGTH]
            )
            serialized_data['service_access_history'] = keys_to_snake_case(
                account_access['serviceAccessHistory'][:MAX_ACC_HOST_ACCESS_HISTORY_LENGTH]
            )

        include_detection_summaries = request.query_params.get('include_detection_summaries', 'True') in ('True', 'true', '1')
        if include_detection_summaries:
            try:
                # limit summaries to 300 to prevent request timeout, these take a while to generate...
                serialized_data['detection_summaries'] = self.generate_detection_summaries(
                    request, account_obj, internal=kwargs.get('internal', False), limit=300
                )
            except Exception:
                LOG.exception('Unexpected exception encountered when retrieving detection summaries for account %s', account_obj.id)
                serialized_data['detection_summaries'] = None

        fields = request.query_params.get('fields')
        if fields:
            fields = fields.split(',')
            serialized_data = clear_requested_fields(fields, serialized_data)

        if kwargs.get('internal', False):
            serialized_data = self._process_internal_response(serialized_data)

        return Response(serialized_data)

    def patch(self, request, *args, **kwargs):
        pass


class AccountV2_2(AccountV2):
    _query_params = AccountV2._query_params + ('src_linked_account',)

    def get_serializer_class(self):
        if self.request.query_params.get('src_linked_account'):
            return AccountSerializerV2_2ForElasticSearch
        else:
            return AccountSerializerV2_2

    def get_queryset(self):
        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        return LinkedAccount.objects.all()


class AccountLockdown(APIV2RetrieveView):
    """
    Currently locked accounts
    """

    view_permission = 'account_lockdown'

    def get(self, request, *args, **kwargs):
        rows = AccountLockdownQueue.objects.filter(amnesty_threat__isnull=True).select_related('user', 'account')

        parsed_rows = [
            {
                'account_id': row.account_id,
                'account_name': row.account.uid,
                'lock_date': row.locked_timestamp,
                'unlock_date': row.expiration,
                'locked_by': row.user.username if row.user else None,
            }
            for row in rows
        ]

        return JsonResponse(parsed_rows, safe=False)


class HostLockdown(APIV2RetrieveView):
    """
    Currently locked hosts
    """

    view_permission = 'host_lockdown'

    def get(self, request, *args, **kwargs):
        rows = HostLockdownQueue.objects.filter(amnesty_threat__isnull=True).select_related('user', 'host')

        parsed_rows = [
            {
                'host_id': row.host_id,
                'host_name': row.host.name,
                'lock_date': row.locked_timestamp,
                'unlock_date': row.expiration,
                'locked_by': row.user.username if row.user else None,
                'edr': EDR_ARTIFACT_TYPES.get(row.edr_type),
            }
            for row in rows
        ]

        return JsonResponse(parsed_rows, safe=False)


class HealthV2(APIV2RetrieveView):
    """
    Health check of the Vectra Box

    """

    view_permission = 'health'

    allowed_methods = ('GET', 'OPTIONS')
    renderer_classes = (JSONRenderer,)

    # This endpoint is a proxy to health_check_api that is in colossus served through platform rest
    PLATFORM_URL = 'http://{}/{}/'.format(PLATFORM_REST_URL, 'health_check_api')

    CHECK_TYPES = {'cpu', 'disk', 'memory', 'network', 'power', 'sensors', 'system', 'hostid', 'connectivity', 'trafficdrop'}

    CACHE_ID_PREFIX = 'v2-health-api_'
    CACHE_DURATION = timedelta(minutes=5)

    def get_health_check(self, check, use_cache=False):
        if use_cache:
            subject = HealthSubject.objects.filter(subject_id=self.CACHE_ID_PREFIX + check).first()
            cached_result = subject.latest_entry if subject else None
            if cached_result and cached_result.expires and cached_result.expires <= timezone.now():
                cached_result.delete()
                cached_result = None
            if cached_result:
                result = cached_result.content or (
                    [] if check == 'sensors' else {}
                )  # content is JSONTextField, which doesn't save [], {} correctly
                if isinstance(result, dict):
                    result.update({'updated_at': str(cached_result.timestamp)})
                return result

        resp = make_get_request(urljoin(self.PLATFORM_URL, check), timeout=60)
        resp.raise_for_status()

        result = resp.json().get(check) or ([] if check == 'sensors' else {})

        customer_health.store.save_subject_update(
            customer_health.utils.SubjectUpdate(
                subject_id=self.CACHE_ID_PREFIX + check,
                category=self.CACHE_ID_PREFIX,
                content=result,
                customer_visible=False,
                expires=timezone.now() + self.CACHE_DURATION,
            )
        )

        return result

    def _get_vlan_information(self):
        ntv_stats = NTVStats().fold_subnets()
        unique_vlan = set()
        for subnet in ntv_stats.values():
            vlans = subnet.get('vlan_ids', {})
            unique_vlan.update(list(vlans.keys()))
        return {'vlan_ids': list(unique_vlan), 'count': len(unique_vlan)}

    def append_additional_data(self, health_checks, track_vlans=True):
        """
        Fill in health data that health_check_api does not include.

        @args:
            health_checks (dict) - health check data from health_check_api from colossus.
                This dict will be modified in-place.

        Note: Due to the transition of splitting colossus and vui we do not have all the functionality
        to support all the checks on colossus by itself. This is in place to fill in data that vui
        is able to gather that colossus is unable to fulfil. This should be a temporary change until
        all parts are in their proper places.
        """

        # add vlan to network if network is apart of the health_check.
        if 'network' in health_checks:
            network_details = health_checks['network']
            if 'traffic' in network_details and 'interfaces' in network_details and track_vlans:
                network_details['vlans'] = self._get_vlan_information()

    def get(self, request, check_type=None, *args, **kwargs):
        if not check_type:
            checks_to_do = list(self.CHECK_TYPES)
        elif check_type not in self.CHECK_TYPES:
            return Response({'error': '"{}" is not a valid health check'.format(check_type)}, status=400)
        else:
            checks_to_do = [check_type]

        use_cache = request.query_params.get('cache', 'true').lower() != 'false'
        track_vlans = request.query_params.get('vlans', 'true').lower() != 'false'

        try:
            response = {check: self.get_health_check(check=check, use_cache=use_cache) for check in checks_to_do}
            self.append_additional_data(response, track_vlans)
            return Response(response)
        except Exception:
            LOG.exception("Encountered and error running health check")
            return Response({'error': 'Encountered and error running health check'}, status=401)


class UsageV2_1(APIV2RetrieveView):
    """
    Usage of detect
    """

    view_permission = 'traffic'

    def get(self, request, *args, **kwargs):
        if request.api_version < 2.1:
            return Response(status=404)

        date_format = '%Y-%m'

        try:
            start_date = request.GET.get('start')
            if start_date:
                start = datetime.strptime(start_date, date_format)
            else:
                today = timezone.now()
                start = today.replace(day=1)

            end_date = request.GET.get('end')
            if end_date:
                end = datetime.strptime(end_date, date_format)
                last_day = monthrange(end.year, end.month)[1]
                end = end.replace(day=last_day)
            else:
                end = timezone.now()

            response = {'concurrent_ips': get_aggregate_stats(start, end)}
            return JsonResponse(response)

        except Exception:
            LOG.exception("Unable to generate usage data")
            return JsonResponse({'error': "Unable to generate usage data"}, status=422)


class AuditsV2(APIV2RetrieveView):
    """
    Get local audits
    """

    permission = "audit"
    allowed_methods = ('GET',)
    _query_params = ("start", "end")

    def get(self, request, *args, **kwargs):
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params)

        resp = {"start": None, "end": None, "audits": [], "_meta": {"level": "success", "message": "Success."}}
        try:
            start_date = None
            if "start" in self.request.query_params.keys():
                start_date = parser.parse(self.request.query_params["start"]).date()
            else:
                start_date = date.min
            resp["start"] = start_date.isoformat()

            end_date = None
            if "end" in self.request.query_params.keys():
                end_date = parser.parse(self.request.query_params["end"]).date()
            else:
                end_date = date.max
            resp["end"] = end_date.isoformat()

            if end_date < start_date:
                resp["_meta"] = {"level": "error", "message": "End date must be after (or the same day as) start date."}
                return JsonResponse(resp, status=422)

        except Exception as e:
            LOG.exception("Unable to parse date string: " + str(e))
            resp["_meta"] = {"level": "error", "message": "Error in parsing dates. Ensure they match YYYY-MM-DD format."}
            return JsonResponse(resp, status=422)

        try:
            response = get_vsupport_api('/system/localaudits?start={start}&end={end}'.format(start=resp["start"], end=resp["end"]))
            if response.status_code != 200:
                resp["_meta"] = {"level": "error", "message": "Internal Server Error."}
            else:
                resp["audits"] = response.json()
            return JsonResponse(resp, status=response.status_code)
        except Exception as e:
            LOG.exception(
                "Exception getting internal endpoint /system/localaudits, start={start}, end={end}".format(
                    start=resp["start"], end=resp["end"]
                )
            )
            resp["_meta"] = {"level": "error", "message": "Internal Server Error."}
            return JsonResponse(resp, status=500)


class HostV2_2(HostV2):
    def _get_host_access(self, host_id):
        return get_host_access(host_id, use_linked_account=True)


class NotesV2_2ViewSet(APIV2GenericView, viewsets.ModelViewSet):
    permission = 'note'
    serializer_class = NoteSerializerV2_2
    pagination_class = None
    valid_types = {'host': host, 'detection': detection, 'campaign': Campaign, 'account': Account, 'linked_account': LinkedAccount}

    def get_queryset(self):
        tvui_type = self.kwargs['tvui_type']
        type_id = self.kwargs['type_id']

        try:
            NotesHandler(tvui_type, type_id)  # just checks if type and type_id exist; could probably be refactored later
        except Exception:
            raise NotFound()

        if tvui_type == 'account':
            tvui_type = 'linked_account'

        note_objs_q = Q(type=tvui_type, type_id=type_id)
        if tvui_type == 'linked_account':
            # add notes from all subaccounts
            note_objs_q |= Q(type='account', type_id__in=self.valid_types['account'].objects.filter(linked_account_id=type_id))

        return notes.objects.filter(note_objs_q)

    def get_object(self):
        qs = self.get_queryset()  # filter using tvui_type and type_id
        return get_object_or_404(qs, id=self.kwargs['note_id'])

    def create(self, request, **kwargs):
        note_text = request.data.get('note', '')
        tvui_type = kwargs['tvui_type']
        type_id = kwargs['type_id']
        event = None
        if tvui_type in ['account', 'linked_account', 'detection', 'host']:
            label = None
            if tvui_type == 'detection':
                label = 'detection_'
                object = audit_factory.EventObjects.DETECTION_NOTE
            elif tvui_type == 'host':
                label = 'host_'
                object = audit_factory.EventObjects.HOST_NOTE
            else:
                label = 'account_'
                object = audit_factory.EventObjects.ACCOUNT_NOTE
            event = {
                'action': audit_factory.EventActions.CREATED,
                'object': object,
                'data': {
                    label
                    + 'note': {
                        'text': (note_text[:500] + '..') if len(note_text) > 500 else note_text,
                        # note_id is not available, creation is not until below.
                        label + 'id': type_id,
                    }
                },
            }
        try:
            NotesHandler(tvui_type, type_id).touch_updated_date()
        except Exception:
            return Response(status=status.HTTP_404_NOT_FOUND)
        AUDIT.audit(
            get_audit_user_data(request),
            audit_factory.Result.SUCCESS,
            'note for {tvui_type}@{type_id} created {note_text}...'.format(tvui_type=tvui_type, type_id=type_id, note_text=note_text[:10]),
            event,
        )
        return super().create(request, **kwargs)

    def update(self, request, **kwargs):
        note_text = request.data.get('note', '')
        tvui_type = kwargs['tvui_type']
        type_id = kwargs['type_id']
        note_id = kwargs['note_id']

        if not note_text:
            error_message = 'Invalid or missing body data. "note" is a required key.'
            LOG.info(f'Error for {request.api_version} PATCH note: {error_message}')
            return JsonResponse({'message': error_message}, status=422)

        event = None
        if tvui_type in ['account', 'linked_account', 'detection', 'host']:
            label = None
            if tvui_type == 'detection':
                label = 'detection_'
                object = audit_factory.EventObjects.DETECTION_NOTE
            elif tvui_type == 'host':
                label = 'host_'
                object = audit_factory.EventObjects.HOST_NOTE
            else:
                label = 'account_'
                object = audit_factory.EventObjects.ACCOUNT_NOTE
            event = {
                'action': audit_factory.EventActions.UPDATED,
                'object': object,
                'data': {
                    label
                    + 'note': {
                        # previous text not available
                        'new_text': (note_text[:500] + '..') if len(note_text) > 500 else note_text,
                        'note_id': note_id,
                        label + 'id': type_id,
                    }
                },
            }

        try:
            NotesHandler(tvui_type, type_id).touch_updated_date()
        except Exception:
            return Response(status=status.HTTP_404_NOT_FOUND)
        AUDIT.audit(
            get_audit_user_data(request),
            audit_factory.Result.SUCCESS,
            'note {note_id} for {tvui_type}@{type_id} updated to {note_text}...'.format(
                note_id=note_id, tvui_type=tvui_type, type_id=type_id, note_text=note_text[:10]
            ),
            event,
        )

        return super().update(request, **kwargs)  # pylint: disable=no-member

    def list(self, *args, **kwargs):
        queryset = self.get_queryset()

        # sort so most recently modified is on top
        notes = sorted(queryset, key=lambda note_obj: note_obj.date_modified or note_obj.date_created, reverse=True)
        return Response(self.serializer_class(notes, many=True).data)

    def destroy(self, request, **kwargs):
        tvui_type = kwargs['tvui_type']
        type_id = kwargs['type_id']
        note_id = kwargs['note_id']
        note = self.get_object()

        if (note.created_by is None or note.created_by.id != request.user.id) and not request.user.has_perm('edit_other_users_notes'):
            raise PermissionDenied
        self.perform_destroy(note)

        event = None
        if tvui_type in ['account', 'linked_account', 'detection', 'host']:
            label = None
            if tvui_type == 'detection':
                label = 'detection_'
                object = audit_factory.EventObjects.DETECTION_NOTE
            elif tvui_type == 'host':
                label = 'host_'
                object = audit_factory.EventObjects.HOST_NOTE
            else:
                label = 'account_'
                object = audit_factory.EventObjects.ACCOUNT_NOTE
            event = {
                'action': audit_factory.EventActions.DELETED,
                'object': object,
                'data': {label + 'note': {'note_id': note_id, label + 'id': type_id}},
            }
        try:
            NotesHandler(tvui_type, type_id).touch_updated_date()
        except Exception:
            return Response(status=status.HTTP_404_NOT_FOUND)
        AUDIT.audit(
            get_audit_user_data(request),
            audit_factory.Result.SUCCESS,
            'note {note_id} for {tvui_type}@{type_id} deleted'.format(note_id=note_id, tvui_type=tvui_type, type_id=type_id),
            event,
        )

        return Response(status=status.HTTP_204_NO_CONTENT)


NotesV2_2Detail = NotesV2_2ViewSet.as_view(actions={'get': 'retrieve', 'patch': 'partial_update', 'delete': 'destroy'})
NotesV2_2ListCreate = NotesV2_2ViewSet.as_view(actions={'get': 'list', 'post': 'create'})


class AssignmentsV2_2ViewSet(APIV2GenericView, viewsets.ModelViewSet, QueryParameterValueValidationMixin):
    permission = 'assignment'
    serializer_class = AssignmentSerializer
    pagination_class = StandardResultSetPagination

    _query_params = (
        'accounts',
        'hosts',
        'assignees',
        'resolution',
        'resolved',
        'created_after',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
    )
    _gt_zero_params = (StandardResultSetPagination.page_query_param,)
    _str_params = StandardResultSetPagination.page_size_query_param
    _bool_params = ('resolved',)
    _int_list_params = ('accounts', 'hosts', 'assignees', 'resolution')
    _timestamp_params = ('created_after',)

    def get_queryset(self):
        """
        /assignments?accounts=1,2,3
        /assignments?hosts=1,2,3
        /assignments?assignees=1,2,3
        /assignments?resolution=1,2,3
        /assignments?resolved=false
        /assignments?created_after=2021-01-01T00:00:00Z
        :return:
        """

        self._validate_query_params(self.request.query_params.keys(), self._query_params)

        # parse arguments
        if all([_arg in self.request.query_params for _arg in ('accounts', 'hosts')]):
            raise ValidationError('Cannot filter on both accounts and hosts.')

        if 'accounts' in self.request.query_params and not self.request.user.has_perm('view_account'):
            raise ValidationError('Insufficient permissions to view assignments for accounts.')

        if 'hosts' in self.request.query_params and not self.request.user.has_perm('view_host'):
            raise ValidationError('Insufficient permissions to view assignments for hosts.')

        parsed_lists = {}
        resolved = self.request.query_params.get('resolved')
        created_after = self.request.query_params.get('created_after')

        if created_after:
            created_after = parse_time_string(created_after)

        if resolved:
            resolved = resolved.lower() != 'false'

        for _list_param in ('accounts', 'hosts', 'assignees', 'resolution'):
            _list_arg = self.request.query_params.get(_list_param)

            if _list_arg:
                parsed_lists[_list_param] = [int(_item) for _item in _list_arg.split(',')]

        # build query
        queryset = Assignment.objects.all()

        if created_after:
            queryset = queryset.filter(date_assigned__gte=created_after)

        if resolved is not None:
            queryset = queryset.filter(outcome__isnull=not resolved)

        if 'accounts' in parsed_lists:
            queryset = queryset.filter(obj_type='linked_account').filter(type_id__in=parsed_lists.get('accounts'))

        if 'hosts' in parsed_lists:
            queryset = queryset.filter(obj_type='host').filter(type_id__in=parsed_lists.get('hosts'))

        if 'assignees' in parsed_lists:
            queryset = queryset.filter(user_id__in=parsed_lists.get('assignees'))

        if 'resolution' in parsed_lists:
            queryset = queryset.filter(outcome_id__in=parsed_lists.get('resolution'))

        return queryset.order_by('-date_assigned')

    def create(self, request, **kwargs):
        assign_host_id = request.data.get('assign_host_id')
        assign_account_id = request.data.get('assign_account_id')
        assign_to_user_id = request.data.get('assign_to_user_id')

        if assign_account_id and assign_host_id:
            return JsonResponse({'errors': [{'title': 'Only one of assign_account_id or assign_host_id can be specified.'}]}, status=400)

        if not assign_account_id and not assign_host_id:
            return JsonResponse({'errors': [{'title': 'Please specify assign_account_id or assign_host_id.'}]}, status=400)

        if not assign_to_user_id:
            return JsonResponse({'errors': [{'title': 'Please specify assign_to_user_id.'}]}, status=400)

        try:
            if assign_account_id:
                entity = LinkedAccount.objects.get(id=assign_account_id)
            else:
                entity = host.objects.get(id=assign_host_id)
        except Exception:
            return JsonResponse({'errors': [{'title': 'Unable to look up specified entity'}]}, status=400)

        try:
            permission_model = 'account' if assign_account_id else 'host'
            assign_to = User.objects.assignable_users(permission_model).get(id=assign_to_user_id)
        except ValueError as err:
            return JsonResponse(
                {'errors': [{'title': f"Field 'assign_to_user_id' expected a number but got {assign_to_user_id}."}]},
                status=400,
            )
        except Exception as err:
            return JsonResponse(
                {'errors': [{'title': f'User {assign_to_user_id} does not have permissions to be assigned to {permission_model}s.'}]},
                status=400,
            )

        created_assignment = lib_assignment.assign(entity, assign_to, request)
        serializer = AssignmentSerializer(created_assignment)

        return JsonResponse({'assignment': serializer.data}, status=201)

    def update(self, request, **kwargs):
        assign_to_user_id = request.data.get('assign_to_user_id')
        assignment = self.get_object()

        if not assign_to_user_id:
            return JsonResponse({'errors': [{'title': 'Please specify assign_to_user_id.'}]}, status=400)

        try:
            permission_model = 'account' if assignment.obj_type == 'linked_account' else assignment.obj_type
            assign_to = User.objects.assignable_users(permission_model).get(id=assign_to_user_id)
        except ValueError as err:
            return JsonResponse(
                {'errors': [{'title': f"Field 'assign_to_user_id' expected a number but got {assign_to_user_id}."}]},
                status=400,
            )
        except Exception as err:
            return JsonResponse(
                {'errors': [{'title': f'User {assign_to_user_id} does not have permissions to be assigned to {permission_model}s.'}]},
                status=400,
            )
        try:
            lib_assignment.reassign(assignment, assign_to, request)
        except DjangoValidationError as e:
            return Response({'error': str(e)}, 422)
        assignment.refresh_from_db()
        serializer = AssignmentSerializer(assignment)

        return Response({'assignment': serializer.data}, status=200)

    def retrieve(self, request, *args, **kwargs):
        assignment = self.get_object()
        serializer = AssignmentSerializer(assignment)

        required_perm = 'view_host' if assignment.obj_type == 'host' else 'view_account'

        if not self.request.user.has_perm(required_perm):
            return HttpResponseForbidden()

        return Response({'assignment': serializer.data})

    def destroy(self, request, **kwargs):
        assignment = self.get_object()
        try:
            lib_assignment.delete(assignment, request)
        except DjangoValidationError as e:
            return Response({'error': str(e)}, 422)

        return Response(status=status.HTTP_204_NO_CONTENT)

    def resolve(self, request, **kwargs):
        assignment = self.get_object()
        entity_type = assignment.obj_type

        if entity_type == 'linked_account':
            entity = LinkedAccount.objects.get(id=assignment.type_id)
        else:
            entity = host.objects.get(id=assignment.type_id)

        raw_params = ResolveParamSerializer(data=request.data, context={'entity': entity, 'entity_type': entity_type})
        raw_params.is_valid(raise_exception=True)
        params = raw_params.validated_data

        lib_assignment.resolve(
            assignment,
            outcome=params['outcome'],
            triage_as=params.get('triage_as'),
            detection_ids=params.get('detection_ids'),
            request=request,
            note=params.get('note'),
        )

        assignment.refresh_from_db()

        return JsonResponse({'assignment': self.serializer_class(assignment).data})

    def list(self, request, *args, **kwargs):
        assignment_queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(assignment_queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            assignment_data = serializer.data
            return self.get_paginated_response(assignment_data)

        serializer = self.get_serializer(assignment_queryset, many=True)
        return Response(serializer.data)


AssignmentV2_2Detail = AssignmentsV2_2ViewSet.as_view(actions={'get': 'retrieve', 'delete': 'destroy', 'put': 'update'})
AssignmentsV2_2ListCreate = AssignmentsV2_2ViewSet.as_view(actions={'get': 'list', 'post': 'create'})
AssignmentsV2_2Resolve = AssignmentsV2_2ViewSet.as_view(actions={'put': 'resolve'})


class AssignmentOutcomesV2_2ViewSet(APIV2GenericView, viewsets.ModelViewSet):
    permission = 'assignment'
    serializer_class = AssignmentOutcomeSerializer
    pagination_class = StandardResultSetPagination
    queryset = AssignmentOutcome.objects.filter(user_selectable=True)

    def perform_destroy(self, outcome):
        if outcome.builtin:
            raise ValidationError(f'Assignment outcome {outcome.id} is built-in and cannot be modified.')

        if Assignment.objects.filter(outcome=outcome).exists():
            raise ValidationError('The outcome cannot be deleted after it has been used to resolve an assignment.')

        # pylint: disable=no-member
        return super().perform_destroy(outcome)


AssignmentOutcomeV2_2Detail = AssignmentOutcomesV2_2ViewSet.as_view(
    actions={'get': 'retrieve', 'delete': 'destroy', 'put': 'partial_update'}
)
AssignmentOutcomesV2_2ListCreate = AssignmentOutcomesV2_2ViewSet.as_view(actions={'get': 'list', 'post': 'create'})


class SettingsSensorRegistrationTokenV2_2(APIV2GenericView):
    """Endpoint for creating, showing, and expiring sensor pairing tokens."""

    allowed_methods = ('GET', 'DELETE', 'POST')
    permission = 'system_sensors'

    def get(self, request, *args, **kwargs):
        token_config = dam_client.retrieve_registration_token()
        token = token_config['token']
        expiration = token_config['expiration']

        if token is None or expiration is None:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return JsonResponse({'token': token, 'expiration': expiration}, status=200)

    def delete(self, request, *args, **kwargs):
        dam_client.delete_registration_token()

        return Response(status=204)

    def post(self, request, *args, **kwargs):
        token_config = dam_client.create_registration_token()
        token = token_config['token']
        expiration = token_config['expiration']

        return JsonResponse({'token': token, 'expiration': expiration}, status=200)


@view_requires_flag(Flags.oauth2_on_prem)
class OAuthTokenV2(APIView):
    """Returns valid access token if incoming client credentials are valid"""

    authentication_classes = []
    permission_classes = [AllowAny]

    ACCESS_TOKEN_EXPIRATION = 21_600  # 6 hours

    def post(self, request, *args, **kwargs):
        """authenticate oauth2 request"""
        # Parse input values
        if request.data.get('grant_type', '') != "client_credentials":
            LOG.warning(f'Grant type not client_credentials')
            return JsonResponse({'message': 'Invalid grant type'}, status=401)

        # parse client id and secret from authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION', '').split()
        try:
            auth_decoded = base64.b64decode(auth_header[1]).decode("utf-8")
        except Exception:
            LOG.warning('Failed to decode on-prem oauth access token')
            return JsonResponse({'message': 'Invalid payload'}, status=401)
        client_id, client_secret = auth_decoded.split(":")

        try:
            # Retrieve related active API client user
            api_client_user = User.objects.filter(is_active=True).get(username='api_client_' + client_id)
        except User.DoesNotExist:
            LOG.warning(f'Could not find matching API client for client id: {client_id}')
            return JsonResponse({'message': 'Invalid payload'}, status=401)

        try:
            # Retrieve local db client secret
            hashed_password = ApiClientCredentials.objects.get(api_client_profile=api_client_user.api_client_profile).api_client_secret
        except ApiClientCredentials.DoesNotExist:
            LOG.warning(f'Could not find API client credentials for api client: {client_id}')
            return JsonResponse({'message': 'Invalid payload'}, status=401)

        # verify incoming access token
        if not check_password(client_secret, hashed_password):
            LOG.warning('Invalid api client secret')
            return JsonResponse({'message': 'Invalid payload'}, status=401)

        try:
            # Generate a token if the client user exists
            session = SessionStore()
            session.create()
            session.set_expiry(self.ACCESS_TOKEN_EXPIRATION)
            session.save()
            # associate api client user with session
            UserSession.objects.create(user=api_client_user, session=Session.objects.get(pk=session.session_key))
        except Exception as e:
            LOG.warning(f'Failed to generate session due to error: {e}')
            return JsonResponse({'message': 'Unexpected excpetion occured when generating access token'}, status=500)

        # return formatted token and expiration time
        return JsonResponse(
            {
                'access_token': session.session_key,
                'expires_in': self.ACCESS_TOKEN_EXPIRATION,
                'token_type': 'Bearer',
            },
            status=200,
        )
