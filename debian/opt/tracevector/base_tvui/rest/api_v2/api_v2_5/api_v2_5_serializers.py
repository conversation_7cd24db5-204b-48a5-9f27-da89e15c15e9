# Copyright (c) 2023 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
"""
Django-rest-framework serializers for API V2.3 Endpoints
"""

from rest_framework import serializers
from typing import List
from base_tvui.feature_flipper import flag_enabled, Flags
from base_tvui.rest.api_v2.api_v2_0_3.api_v2_serializers import DetectionSerializerV2_2, NoteSerializerV2_2, HostSerializerV2
from base_tvui.rest.api_v2.api_v2_4.api_v2_4_serializers import AccountSerializerV2_4, GroupSerializerV2_4
from tvui.models import GroupCollection, detection
from tvui.group_collections.config import API_MEMBER_LIMIT
from tvui.group_collections.account_groups.account_group_api_processor import AccountGroupAPIProcessor
from tvui.group_collections.group_ui_processors import AccountGroupUIProcessor, HostGroupUIProcessor
from pure_utils import log_utils


LOG = log_utils.get_vectra_logger(__name__)


class NoteSerializerV2_5(NoteSerializerV2_2):
    note = serializers.SerializerMethodField()

    def get_note(self, obj):
        position = self.context.get('note_position', {}).get(obj.id)
        if position is None:
            return obj.note  # fallback: no truncation
        return obj.note[:1000] if position == 0 else obj.note[:100]


class AccountSerializerV2_5(AccountSerializerV2_4):
    # Rename of subaccounts (to be deprecated in v2.6)
    associated_accounts = serializers.SerializerMethodField(method_name='_get_subaccounts')
    url = serializers.HyperlinkedIdentityField(view_name='api-v2.5:api-account-v2.5')
    notes = serializers.SerializerMethodField()

    class Meta(AccountSerializerV2_4.Meta):
        fields = ['associated_accounts'] + list(AccountSerializerV2_4.Meta.fields)

    def get_notes(self, acct_obj):
        notes = acct_obj.notes.order_by('-date_created')[:10]
        notes = list(notes)  # force evaluation to get stable ordering

        # Create a map of note.id -> position (0 for most recent)
        note_position = {note.id: idx for idx, note in enumerate(notes)}

        return NoteSerializerV2_5(notes, many=True, context={'note_position': note_position}).data


class GroupSerializerV2_5(GroupSerializerV2_4):
    regex = serializers.SerializerMethodField()
    membership_evaluation_ongoing = serializers.SerializerMethodField()
    members = serializers.SerializerMethodField(method_name='_get_members')
    member_count = serializers.SerializerMethodField(method_name='_get_member_count')
    built_using = serializers.SerializerMethodField(method_name='_get_built_using')
    members_truncated = serializers.SerializerMethodField()
    ad_group_dn = serializers.ReadOnlyField()

    class Meta(GroupSerializerV2_4.Meta):
        fields = list(GroupSerializerV2_4.Meta.fields) + [
            'regex',
            'membership_evaluation_ongoing',
            'member_count',
            'built_using',
            'members_truncated',
            'ad_group_dn',
        ]

    def get_regex(self, group_obj):
        try:
            regex = None
            if group_obj.rule:
                if group_obj.type == GroupCollection.HOST:
                    regex = HostGroupUIProcessor(group_obj)._get_dynamic_rule_str(group_obj.rule)
                elif group_obj.type == GroupCollection.ACCOUNT:
                    regex = AccountGroupUIProcessor(group_obj)._get_dynamic_rule_str(group_obj.rule)
            return regex
        except Exception as e:
            LOG.exception(f'Unexpected error getting group rule: {e}')
            return None

    def get_membership_evaluation_ongoing(self, group_obj):
        if group_obj.member_eval_pending:
            return True
        else:
            return False

    def get_members_truncated(self, group_obj):
        include_members = self.context['request'].query_params.get('include_members', 'True') in ('True', 'true', '1')
        if group_obj.member_eval_pending or not include_members:
            return False
        else:
            if hasattr(group_obj, 'accountgroup'):
                try:
                    return group_obj.accountgroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            elif hasattr(group_obj, 'hostgroup'):
                try:
                    return group_obj.hostgroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            elif hasattr(group_obj, 'externaldomaingroup'):
                try:
                    return group_obj.externaldomaingroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            elif hasattr(group_obj, 'ipgroup'):
                try:
                    return group_obj.ipgroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            else:
                LOG.error('Group does not have a valid member count attribute')
                return False

    def _get_members(self, group_obj):
        include_members = self.context['request'].query_params.get('include_members', 'True') in ('True', 'true', '1')
        if group_obj.member_eval_pending or not include_members:
            return []
        else:
            if hasattr(group_obj, 'accountgroup'):
                try:
                    return AccountGroupAPIProcessor.get_group_members(
                        self.context['request'], group_obj.accountgroup, limit=API_MEMBER_LIMIT
                    )
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return []

    def _get_member_count(self, group_obj):
        if group_obj.type == GroupCollection.HOST:
            return group_obj.hostgroup.members_count
        elif group_obj.type == GroupCollection.ACCOUNT:
            return group_obj.accountgroup.members_count
        elif group_obj.type == GroupCollection.DOMAIN:
            return group_obj.externaldomaingroup.members_count
        elif group_obj.type == GroupCollection.IP:
            return group_obj.ipgroup.members_count
        else:
            return 0

    def _get_built_using(self, group_obj):
        if group_obj.member_type == GroupCollection.MEMBER_TYPE_DYNAMIC:
            return 'regex'
        elif group_obj.member_type == GroupCollection.MEMBER_TYPE_AD:
            return 'ad_group'
        else:
            return 'static_members'


class DetectionSerializerV2_5(DetectionSerializerV2_2):
    """
    Serializer for the detection object.
    """

    reason = serializers.SerializerMethodField()
    notes = serializers.SerializerMethodField()

    class Meta(DetectionSerializerV2_2.Meta):
        fields = list(set(DetectionSerializerV2_2.Meta.fields)) + ['reason']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        request = self.context.get('request', None)
        if request and request.query_params.get('include_src_dst_groups', 'False') in ('True', 'true', '1'):
            # If include_src_dst_groups is True, we need to include src and dst groups
            self.fields['src_groups'] = serializers.SerializerMethodField()
            self.fields['dst_groups'] = serializers.SerializerMethodField()

    def get_src_groups(self, det_obj):
        """
        Get the source groups for the detection object.
        """
        src_groups = list(det_obj.host.hostgroup_set.all() if det_obj.host else [])
        src_groups.extend(list(det_obj.account.accountgroup_set.all()) if det_obj.account else [])
        src_groups.extend(det_obj.src_ip_groups)
        return [
            {
                'id': grp.id,
                'name': grp.name,
                'description': grp.description,
                'type': grp.type,
                'last_modified': grp.last_modified_timestamp,
                'last_modified_by': grp.last_modified_by.username,
            }
            for grp in src_groups
        ]

    def get_dst_groups(self, det_obj):
        """
        Get the destination groups for the detection object.
        """
        dst_groups: List[GroupCollection] = detection.external_domain_groups_injectable(
            detection_domains=set(detail.dst_dns for detail in det_obj.details.all() if detail.dst_dns),
            external_domain_groups=self.context['domain_groups'],
        )
        dst_groups.extend(det_obj.dst_ip_groups)

        return [
            {
                'id': grp.id,
                'name': grp.name,
                'description': grp.description,
                'type': grp.type,
                'last_modified': grp.last_modified_timestamp,
                'last_modified_by': grp.last_modified_by.username,
            }
            for grp in dst_groups
        ]

    def matching_groups(self, det_obj):
        # Using GroupCollection type hint so that only shared attributes between ip and domain groups resolve.
        # NOTE: `det_obj.details.all()` is prefetched in our detections list views, so if you ever need to apply
        # any additional filtering/sorting to the details, make sure to update that prefetch.
        groups: List[GroupCollection] = detection.external_domain_groups_injectable(
            detection_domains=set(detail.dst_dns for detail in det_obj.details.all() if detail.dst_dns),
            external_domain_groups=self.context['domain_groups'],
        )
        groups.extend(self.fetch_ip_groups(det_obj))
        if det_obj.host:
            # If the detection is associated with a host, add its host groups
            groups.extend(det_obj.host.hostgroup_set.all())
        if det_obj.account:
            # If the detection is associated with an account, add its account groups
            groups.extend(det_obj.account.accountgroup_set.all())
        return [
            {
                'id': grp.id,
                'name': grp.name,
                'description': grp.description,
                'type': grp.type,
                'last_modified': grp.last_modified_timestamp,
                'last_modified_by': grp.last_modified_by.username,
            }
            for grp in groups
        ]

    def get_reason(self, det_obj):
        if flag_enabled(Flags.signal_efficacy_closed_as) and flag_enabled(Flags.signal_efficacy_public_preview):
            return det_obj.get_signal_efficacy_status()
        return None

    def get_notes(self, det_obj):
        notes = det_obj.notes.order_by('-date_created')[:10]
        notes = list(notes)  # force evaluation to get stable ordering

        # Create a map of note.id -> position (0 for most recent)
        note_position = {note.id: idx for idx, note in enumerate(notes)}

        return NoteSerializerV2_5(notes, many=True, context={'note_position': note_position}).data


class DetectionIdListSerializerV2_5(serializers.Serializer):
    """
    Serializer for a list of detection IDs.
    """

    detectionIdList = serializers.ListField(
        child=serializers.IntegerField(min_value=0),
        required=True,
        error_messages={
            'empty': 'detectionIdList cannot be empty',
            'required': 'detectionIdList is required',
            'not_a_list': 'detectionIdList must be a list',
        },
        allow_empty=False,
        allow_null=False,
    )

    class Meta:
        fields = ['detectionIdList']


class HostSerializerV2_5(HostSerializerV2):
    """
    Serializer for the host object.
    """

    notes = serializers.SerializerMethodField()

    class Meta(HostSerializerV2.Meta):
        fields = list(set(HostSerializerV2.Meta.fields))

    def get_notes(self, host_obj):
        notes = host_obj.notes.order_by('-date_created')[:10]
        notes = list(notes)  # force evaluation to get stable ordering

        # Create a map of note.id -> position (0 for most recent)
        note_position = {note.id: idx for idx, note in enumerate(notes)}

        return NoteSerializerV2_5(notes, many=True, context={'note_position': note_position}).data
