# Copyright (c) 2023 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

"""
Views for V2.5 endpoints.
"""

import os
import json
import requests
from django.utils import timezone
from django.http import HttpRequest, JsonResponse, StreamingHttpResponse, Http404
from django.core.exceptions import ObjectDoesNotExist
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.exceptions import ErrorDetail
from wsgiref.util import FileWrapper
from rest_framework.response import Response
from base_tvui import audit_factory, lib_tv
from base_tvui.feature_flipper import Flags, flag_enabled, view_requires_flag
from base_tvui.lib_ldap import LdapClient
from base_tvui.lib_tv import get_audit_user_data
from base_tvui.lib_entity import get_detection_status_filters
from tvui.models import GroupCollection, detection, StateReasons, LinkedAccount, host, StateReasons
from tvui.group_collections.host_groups.host_group_api_processor import HostGroupAPIProcessor
from tvui.group_collections.account_groups.account_group_api_processor import AccountGroupAPIProcessor
from base_tvui.rest.api_v2.api_v2_0_3.api_v2_views import (
    APIV2ListView,
    APIV2GenericView,
    HealthV2,
    DetectionsV2_3,
    DetectionV2_3,
    QueryParameterValueValidationMixin,
    HostsV2,
)
from base_tvui.rest.api_v2.api_v2_4.api_v2_4_views import GroupsV2_4, AccountsV2_4, AccountV2_4, GroupV2_4
from base_tvui.rest.api_v2.api_v2_5.api_v2_5_serializers import (
    AccountSerializerV2_5,
    DetectionIdListSerializerV2_5,
    GroupSerializerV2_5,
    DetectionSerializerV2_5,
    HostSerializerV2_5,
)
from base_tvui.rest.api_v3.api_v3_utils import is_dynamic_groups_api
from tvui.vectra_match.models.vectra_match_models import (
    VsupportApiModelDeviceSerialException,
    VsupportApiModelDeviceSerialNotFoundException,
    VsupportApiModelException,
    VsupportApiModeluuidException,
    VsupportApiModeluuidNotFoundException,
    VsupportApiEnablementModel,
    VsupportApiStatsModel,
    VsupportApiStatusModel,
    VsupportApiAlertStatsModel,
    VsupportApiAvailableDevicesModel,
    VsupportApiRulesModel,
    VsupportApiAssignmentModel,
    VsupportApiRulesetModel,
    VectraMatchInvalidLicenseException,
)
from tvui.group_collections.account_groups.lib_account_group import AccountGroupsFacilitator
from tvui.group_collections.host_groups.lib_host_group import HostGroupsFacilitator
from tvui.group_collections.lib_group_collection import GroupCollectionFacilitator
from tvui.vectra_match.models.vectra_match import VectraMatch
from tvui.close_workflow.close_workflow_utils import (
    InvalidStateReasonException,
    MissingReasonException,
    close_detections,
    delete_filters,
    close_entity,
    filter_single_detection,
    log_audit_event,
    open_detections,
    delete_filters,
    validate_model_object_exists,
    validate_reason,
    filter_bulk_detections,
)
from base_tvui.rest.custom_exceptions import InvalidRequestField
from base_tvui.rest.pagination import StandardResultSetPagination
from rest_framework import filters, serializers
from tvui.events.destinations.notification_receiver_views import NotificationReceiversBaseView

from host_scoring_v2 import host_scoring
from pure_utils import log_utils

AUDIT = audit_factory.get_audit_object()
LOG = log_utils.get_vectra_logger(__name__)
RULES_MANAGEMENT_API_BASE_URL = 'http://localhost:5011/api/'
NUM_RULES_LIMIT = 25


class GroupsV2_5(GroupsV2_4):
    """
    V2.5 API endpoint for Groups.
    - API URL: /api/v2.5/groups
    - Permission: groups
    - Methods: GET, POST
    - Required Parameters: None
    - Possible Query Parameters: {
        'account_names'
        'domains'
        'host_ids'
        'host_names'
        'importance'
        'ips'
        'description'
        'last_modified_timestamp'
        'last_modified_by'
        'name'
        'type'
        'page'
        'page_size'
        'is_regex'
        'is_membership_evaluation_ongoing'
        'include_members'
        'is_ad_group'
        'ad_group_dn'
    }
    """

    def __init__(self):
        super().__init__()

        self._query_params = (
            'account_names',
            'domains',
            'host_ids',
            'host_names',
            'importance',
            'ips',
            'description',
            'last_modified_timestamp',
            'last_modified_by',
            'name',
            'type',
            'page',
            'page_size',
            'is_regex',
            'is_membership_evaluation_ongoing',
            'include_members',
            'is_ad_group',
            'ad_group_dn',
        )
        self._list_params = ('account_names', 'host_ids', 'host_names', 'ips', 'domains')
        self._bool_params = self._bool_params + ('is_regex', 'is_membership_evaluation_ongoing', 'include_members', 'is_ad_group')
        self._str_params = self._str_params + ('ad_group_dn',)

    def get_serializer_class(self):
        if flag_enabled(Flags.dynamic_groups):
            return GroupSerializerV2_5
        else:
            return super().serializer_class

    def _validate_keys(self, group_payload):
        invalid_keys = []
        for key in group_payload.keys():
            if key not in ['name', 'type', 'members', 'description', 'regex']:
                invalid_keys.append(key)
        if len(invalid_keys) > 0:
            raise InvalidRequestField(invalid_keys)

    def get_queryset(self):
        queryset = super().get_queryset()
        if flag_enabled(Flags.dynamic_groups):
            is_regex = self.request.query_params.get('is_regex')
            is_membership_evaluation_ongoing = self.request.query_params.get('is_membership_evaluation_ongoing')

            if is_regex is not None:
                if is_regex in ('True', 'true', '1'):
                    queryset = queryset.filter(rule__isnull=False)
                else:
                    queryset = queryset.filter(rule__isnull=True)

            if is_membership_evaluation_ongoing is not None:
                if is_membership_evaluation_ongoing in ('True', 'true', '1'):
                    queryset = queryset.filter(member_eval_pending__isnull=False)
                else:
                    queryset = queryset.filter(member_eval_pending__isnull=True)

        if flag_enabled(Flags.ad_groups):
            is_ad_group = self.request.query_params.get('is_ad_group')
            if is_ad_group is not None:
                if is_ad_group in ('True', 'true', '1'):
                    queryset = queryset.filter(ad_group_dn__isnull=False)
                else:
                    queryset = queryset.filter(ad_group_dn__isnull=True)

            ad_group_dn = self.request.query_params.get('ad_group_dn')
            if ad_group_dn is not None:
                queryset = queryset.filter(ad_group_dn__icontains=ad_group_dn)

        return queryset.distinct()

    def post(self, request, *args, **kwargs):
        if flag_enabled(Flags.dynamic_groups) and is_dynamic_groups_api(request):
            if request.content_type != "application/json":
                return JsonResponse({"error": "Unsupported Media Type"}, status=415)
            try:
                json.loads(request.body)
            except json.decoder.JSONDecodeError:
                return JsonResponse({"error": "Payload must be valid JSON"}, status=400)

            group_payload = request.data

            self._validate_keys(group_payload)

            regex = group_payload.get('regex')
            group_type = group_payload['type']

            if group_type is None:
                raise InvalidRequestField(['type'])

            group_payload['member_type'] = 'dynamic' if regex else 'static'

            if regex:
                try:
                    # Dynamic group type can currently only be HOST or ACCOUNT
                    if group_type not in [GroupCollection.HOST, GroupCollection.ACCOUNT]:
                        return JsonResponse(
                            {'_meta': {'level': 'error', 'message': "Regex should only be used with Host or Account groups."}},
                            status=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        )

                    # Cannot have members and regex in payload
                    if group_payload.get('members'):
                        return JsonResponse(
                            {
                                '_meta': {
                                    'level': 'error',
                                    'message': "Members cannot be specified when creating a regex group. Please only provide a regex. Members will be added upon creation.",
                                }
                            },
                            status=status.HTTP_422_UNPROCESSABLE_ENTITY,
                        )

                    group_payload['last_modified_timestamp'] = timezone.now()
                    group_payload['last_modified_by'] = request.user

                    if group_payload.get('description') is None:
                        group_payload['description'] = ""

                    if group_payload.get('importance') is None:
                        group_payload['importance'] = "medium"

                    # members is a required field in validation
                    group_payload['members'] = ""

                    # Create dynamic account group
                    if group_type == GroupCollection.ACCOUNT:
                        output = AccountGroupsFacilitator(request, 'api').create_group(group_payload)
                        response_data = (
                            {'group': output['data']}
                            if output['status_code'] == AccountGroupsFacilitator.STATUS_CODE_CREATED
                            else {'_meta': {'level': 'error', 'message': output.get('errors')}}
                        )
                        return JsonResponse(response_data, status=output['status_code'])
                    # Create dynamic host group
                    elif group_type == GroupCollection.HOST:
                        output = HostGroupsFacilitator(request, 'api').create_group(group_payload)
                        response_data = (
                            {'group': output['data']}
                            if output['status_code'] == HostGroupsFacilitator.STATUS_CODE_CREATED
                            else {'_meta': {'level': 'error', 'message': output.get('errors')}}
                        )
                        return JsonResponse(response_data, status=output['status_code'])
                except Exception as e:
                    LOG.exception(f'Unexpected exception encountered attempting to POST dynamic group. {e}')
                    return JsonResponse({'error': 'An unexpected exception occurred.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        try:
            # use v2.4 implementation if dynamic_groups is off or regex not provided
            return super().post(request)
        except Exception as e:
            LOG.exception(f'Unexpected exception encountered attempting to POST dynamic group. {e}')
            raise


class GroupV2_5(GroupV2_4):
    """
    View for v2.5 of the /api/v2.5/groups/<id> endpoint.
    - Permission: groups
    - Methods: GET, PATCH, DELETE
    """

    def __init__(self):
        super().__init__()
        self.serializer_class = GroupSerializerV2_5
        self._query_params = 'include_members'
        self._bool_params = 'include_members'

    def _validate_keys(self, group_payload):
        invalid_keys = []
        for key in group_payload.keys():
            if key not in ['name', 'type', 'members', 'description', 'regex']:
                invalid_keys.append(key)
        if len(invalid_keys) > 0:
            raise InvalidRequestField(invalid_keys)

    def patch(self, request, pk, *args, **kwargs):
        if flag_enabled(Flags.dynamic_groups):
            self._validate_query_params(self.request.query_params.keys(), self._patch_query_params)
            group_payload = request.data

            # validate payload
            if not isinstance(group_payload, dict) or request.content_type != "application/json":
                return JsonResponse({"error": "Payload must be valid JSON"}, status=400)

            self._validate_keys(group_payload)

            group_payload['last_modified_timestamp'] = timezone.now()
            group_payload['last_modified_by'] = request.user

            group_obj = self.get_object()

            if group_obj.member_type == 'dynamic':
                group_payload['member_type'] = 'dynamic'
            else:
                group_payload['member_type'] = 'static'

            if flag_enabled(Flags.dynamic_groups_member_transition):
                if group_obj.type in [GroupCollection.ACCOUNT, GroupCollection.HOST]:
                    group_payload['transitionGroup'] = False
                    # Static -> Dynamic Transition
                    if group_obj.member_type != 'dynamic' and group_payload.get('regex') is not None:
                        group_payload['transitionGroup'] = True
                        group_payload['member_type'] = 'dynamic'
                    # Dynamic -> Static Transition (if regex is not explicitly set to None/empty, we leave regex unchanged)
                    if group_obj.member_type == 'dynamic' and (
                        'regex' in group_payload.keys() and (group_payload.get('regex') == None or group_payload.get('regex') == "")
                    ):
                        group_payload['transitionGroup'] = True
                        group_payload['member_type'] = 'static'
            else:
                if group_obj.member_type != 'dynamic' and group_payload.get('regex'):
                    return JsonResponse({'_meta': {'level': 'error', 'message': "Static groups cannot have regex"}}, status=400)

                if group_obj.member_type == 'dynamic' and (group_payload.get('regex') == None or group_payload.get('regex') == ""):
                    return JsonResponse({'_meta': {'level': 'error', 'message': "Dynamic groups must have regex defined"}}, status=400)

            if group_payload.get('type') != None and group_payload.get('type') != group_obj.type:
                return JsonResponse({'_meta': {'level': 'error', 'message': "Group type cannot be changed"}}, status=400)
            else:
                group_payload['type'] = group_obj.type

            if group_payload.get('importance') is None:
                group_payload['importance'] = group_obj.importance

            if group_payload['type'] in [GroupCollection.IP, GroupCollection.DOMAIN] and group_payload.get('regex'):
                return JsonResponse(
                    {'_meta': {'level': 'error', 'message': "Regex should only be used with Host or Account groups."}}, status=400
                )

            if group_payload.get('regex'):
                # Cannot have members and regex in payload
                if group_payload.get('members'):
                    return JsonResponse(
                        {
                            '_meta': {
                                'level': 'error',
                                'message': "Members cannot be specified when creating a regex group. Please only provide a regex. Members will be added upon creation.",
                            }
                        },
                        status=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    )

            if group_payload['type'] == GroupCollection.ACCOUNT:
                output = AccountGroupsFacilitator(request, 'api').edit_group(pk, group_payload)
                if output['status_code'] == AccountGroupsFacilitator.STATUS_CODE_SUCCESS:
                    new_group_obj = self.get_object()
                    serializer = self.get_serializer(new_group_obj)
                    return Response(serializer.data)
                return JsonResponse({'_meta': {'level': 'error', 'message': output.get('errors')}}, status=output.get('status_code'))
            elif group_obj.type in [GroupCollection.HOST, GroupCollection.IP, GroupCollection.DOMAIN]:
                output = super().patch(request, pk)
                if output.status_code == GroupCollectionFacilitator.STATUS_CODE_SUCCESS:
                    new_group_obj = self.get_object()
                    serializer = self.get_serializer(new_group_obj)
                    return Response(serializer.data)
                return output
            return JsonResponse(
                {'type': 'Invalid group type provided', '_meta': {'level': 'error', 'message': 'Invalid field(s) found'}}, status=422
            )
        else:
            return super().patch(request, pk)


class ActiveDirectoryGroupsV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for Active Directory Groups.
    - API URL: /api/v2.5/settings/active_directory/groups/
    - Permission: groups
    - Methods: GET
    - Required Parameters: None
    """

    permission = 'groups'
    allowed_methods = ('GET',)

    def get(self, request):
        if not flag_enabled(Flags.ad_groups):
            return JsonResponse({'error': 'Not Found'}, status=status.HTTP_404_NOT_FOUND)

        try:
            ad_groups = LdapClient.get_ldap_groups_multi()
            config = LdapClient.get_ldap_service_top_config_multi()
            data = {}
            for ad_profile_id, groups in ad_groups.items():
                ad_profile_name = config['profiles'][ad_profile_id]['ad_profile_name']
                data[ad_profile_name] = {'groups': groups}
        except Exception as e:
            LOG.error(f"Error fetching AD groups: {e}")
            return JsonResponse({'error': 'Unable to fetch AD groups. Please retry.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return JsonResponse(data, status=status.HTTP_200_OK)


class VectraMatchEnablementViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with Enablement requests from the UI/API call

    """

    permission = 'vectra_match_setting'
    allowed_methods = ('GET', 'POST', 'OPTIONS')

    def get(self, request):
        try:
            device_serial = request.GET.dict().get('device_serial')
            if (device_serial is None) or (not isinstance(device_serial, str)):
                return JsonResponse({"details": "device_serial not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)

            model = VsupportApiEnablementModel(device_serial)
            try:
                data = model.get()
                return JsonResponse(data, status=status.HTTP_200_OK)

            except VsupportApiModelDeviceSerialException:
                return JsonResponse({"details": "device_serial not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)
            except VsupportApiModelDeviceSerialNotFoundException:
                return JsonResponse({"details": "Provided device serial is not connected/paired"}, status=status.HTTP_404_NOT_FOUND)
            except VsupportApiModelException:
                return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as exc:
            LOG.exception(f"Unexpected error occured: {repr(exc)}")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        event = {'action': audit_factory.EventActions.UPDATED, 'object': audit_factory.EventObjects.GENERAL_SETTING, 'data': request.data}
        model = VsupportApiEnablementModel()

        try:
            device_serial = request.data.get('device_serial')
            desired_state = request.data.get('desired_state')

            if (device_serial is None) or (not isinstance(device_serial, str)):
                return JsonResponse({"details": "device_serial not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)
            if (desired_state is None) or (not isinstance(desired_state, bool)):
                return JsonResponse({"details": "desired_state not provided or is not a boolean"}, status=status.HTTP_400_BAD_REQUEST)

            data = model.post(device_serial, desired_state, request)

            return JsonResponse(data['return_data'], status=data['return_code'])

        except Exception:
            AUDIT.audit(
                get_audit_user_data(request),
                audit_factory.Result.FAILURE,
                f'Unexpected error has occurred while trying to toggle Vectra Match',
                event,
            )
            LOG.exception(f"Unexpected error occured")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VectraMatchStatsViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with statistics requests from the UI/API call

    """

    permission = 'vectra_match_setting'
    allowed_methods = ('GET', 'OPTIONS')

    def get(self, request):
        try:
            device_serial = request.GET.dict().get('device_serial', None)
            model = VsupportApiStatsModel(device_serial)
            try:
                data = model.get()
                return JsonResponse(data, status=status.HTTP_200_OK)

            except VsupportApiModelDeviceSerialException:
                return JsonResponse({"details": "device_serial not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)
            except VsupportApiModelDeviceSerialNotFoundException:
                return JsonResponse({"details": "Provided device serial is not connected/paired"}, status=status.HTTP_404_NOT_FOUND)
            except VsupportApiModelException:
                return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as exc:
            LOG.exception(f"Unexpected error occured: {repr(exc)}")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VectraMatchStatusViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with status requests from the UI/API call

    """

    permission = 'vectra_match_setting'
    allowed_methods = ('GET', 'OPTIONS')

    def get(self, request):
        try:
            device_serial = request.GET.dict().get('device_serial', None)
            model = VsupportApiStatusModel(device_serial)
            try:
                data = model.get()
                return JsonResponse(data, status=status.HTTP_200_OK)

            except VsupportApiModelDeviceSerialException:
                return JsonResponse({"details": "device_serial not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)
            except VsupportApiModelDeviceSerialNotFoundException:
                return JsonResponse({"details": "Provided device serial is not connected/paired"}, status=status.HTTP_404_NOT_FOUND)
            except VsupportApiModelException:
                return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as exc:
            LOG.exception(f"Unexpected error occured: {repr(exc)}")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VectraMatchAlertStatsViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with alert stats requests from the UI/API call

    """

    permission = 'vectra_match_setting'
    allowed_methods = ('GET', 'OPTIONS')

    def get(self, request):
        try:
            device_serial = request.GET.dict().get('device_serial', None)
            model = VsupportApiAlertStatsModel(device_serial)
            try:
                data = model.get()
                return JsonResponse(data, status=status.HTTP_200_OK)

            except VsupportApiModelDeviceSerialException:
                return JsonResponse({"details": "device_serial not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)
            except VsupportApiModelDeviceSerialNotFoundException:
                return JsonResponse({"details": "Provided device serial is not connected/paired"}, status=status.HTTP_404_NOT_FOUND)
            except VsupportApiModelException:
                return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as exc:
            LOG.exception(f"Unexpected error occured: {repr(exc)}")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class VectraMatchAvailableDevicesViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with requests to show all registered devices
    """

    permission = 'vectra_match_setting'
    allowed_methods = ('GET', 'OPTIONS')

    def get(self, request):
        devices_list = []

        model = VsupportApiAvailableDevicesModel()
        try:
            devices_list = model.get()
        except Exception as exc:
            LOG.exception(f"Exception encountered when retrieving devices: {repr(exc)}")
            return JsonResponse({'details': 'Internal Server Error'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return JsonResponse({'devices': devices_list}, status=200)


class VectraMatchRulesViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with rules file requests from the UI/API call

    """

    permission = 'vectra_match_ruleset'
    allowed_methods = ('GET', 'POST', 'DELETE', 'OPTIONS')

    def _get_rules_endpoint(self, url_to_get):
        """Retrieves the corresponding rules url from the appliance"""
        return requests.get(url_to_get)

    def _post_rule(self, url_to_post, file_to_send, notes):
        """Sends the rule file to the appliance"""
        return requests.post(url_to_post, files=file_to_send, data=notes)

    def get(self, request):
        """
        Takes in query param uuid, returns data about that rules file
        """
        try:
            uuid = request.GET.dict().get('uuid')
            if (uuid is None) or (not isinstance(uuid, str)):
                return JsonResponse({"details": "uuid not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)

            model = VsupportApiRulesModel(uuid)
            try:
                data = model.get()
                return JsonResponse(data, status=status.HTTP_200_OK)

            except VsupportApiModeluuidException:
                return JsonResponse({"details": "uuid not provided or is not a string"}, status=status.HTTP_400_BAD_REQUEST)

            except VsupportApiModeluuidNotFoundException:
                return JsonResponse({"details": "Provided rules file uuid does not exist"}, status=status.HTTP_404_NOT_FOUND)

        except Exception as exc:
            LOG.exception(f"Unexpected error occured: {repr(exc)}")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """
        Takes in multipart form body that contains the file in question and metadata, and returns validation information and metadata once upload has completed
        Also adds protection/checks for making sure we are not above the individual file size cap and max number of files stored locally
        """
        event = {
            'action': audit_factory.EventActions.UPDATED,
            'object': audit_factory.EventObjects.GENERAL_SETTING,
            'data': str(request.data),
        }

        model = VsupportApiRulesetModel()
        try:
            data = model.post(request)
            return JsonResponse(data['return_data'], status=data['return_code'])
        except Exception:
            AUDIT.audit(
                get_audit_user_data(request),
                audit_factory.Result.FAILURE,
                "Unexpected error has occurred while trying to upload a Vectra Match ruleset",
                event,
            )
            LOG.exception(f"Unexpected error occured")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return JsonResponse(data['return_data'], status=data['return_code'])

    def delete(self, request):
        """
        Takes in json body containing uuid, returns message about whether the deletion was successful
        """
        model = VsupportApiRulesetModel()

        try:
            data = model.delete(request)
            return JsonResponse(data['return_data'], status=data['return_code'])
        except Exception:
            LOG.exception(f"Unexpected error occured")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return JsonResponse(data['return_data'], status=data['return_code'])


class VectraMatchAssignmentViewV2_5(APIV2ListView):
    """
    Handles the endpoint to deal with rules file assignment requests from the UI/API
    """

    permission = 'vectra_match_ruleset'
    allowed_methods = ('GET', 'POST', 'DELETE', 'OPTIONS')

    def get(self, request):
        """
        Takes in no information, returns data about what devices have which files, and which files are attributed to which devices (i.e. both the forward and reverse view)
        """
        try:
            model = VsupportApiAssignmentModel()

            try:
                data = model.get()
                return JsonResponse(data, status=status.HTTP_200_OK)

            except Exception:
                LOG.warning(f"Unexpected or error response from vsupport received: {data}")
                return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as exc:
            LOG.exception(f"Unexpected error occured: {repr(exc)}")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """
        Takes in a json body containing uuid and a list of devices that uuid should be assigned to, returns message indicating success/failure
        """
        event = {'action': audit_factory.EventActions.UPDATED, 'object': audit_factory.EventObjects.GENERAL_SETTING, 'data': request.data}

        model = VsupportApiAssignmentModel()

        try:
            data = model.post(request)
            return JsonResponse(data['return_data'], status=data['return_code'])

        except Exception:
            AUDIT.audit(
                get_audit_user_data(request),
                audit_factory.Result.FAILURE,
                f'Unexpected error has occurred while trying to assign Vectra Match ruleset',
                event,
            )
            LOG.exception(f"Unexpected error occured")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """
        Takes in body with fields uuid and device serial, deletes rules file from that device
        """
        event = {'action': audit_factory.EventActions.DELETED, 'object': audit_factory.EventObjects.GENERAL_SETTING, 'data': request.data}

        model = VsupportApiAssignmentModel()

        try:
            data = model.delete(request)
            return JsonResponse(data['return_data'], status=data['return_code'])

        except Exception:
            AUDIT.audit(
                get_audit_user_data(request),
                audit_factory.Result.FAILURE,
                "Unexpected error has occurred while trying to delete Vectra Match rules assignment",
                event,
            )
            LOG.exception(f"Unexpected error occured")
            return JsonResponse({"details": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AccountsV2_5(AccountsV2_4):
    """
    V2.5 API endpoint for Accounts.
    - API URL: /api/v2.5/accounts
    - Permission: accounts
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'fields'
        'page'
        'page_size'
        'ordering'
        'name'
        'state'
        't_score'
        't_score_gte'
        'c_score'
        'c_score_gte'
        'tags'
        'all',
        'min_id',
        'max_id',
        'note_modified_timestamp_gte',
        'privilege_level',
        'privilege_level_gte',
        'privilege_category'
    }
    """

    serializer_class = AccountSerializerV2_5


class HealthV2_5(HealthV2):
    """
    V2.5 API endpoint for System Health.
    - API URL: /api/v2.5/health or /api/v2.5/health/<check_type>
    - Permission: health
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'cache'
        'vlans'
    }
    """

    CHECK_TYPES = {'cpu', 'disk', 'memory', 'network', 'power', 'sensors', 'system', 'hostid', 'connectivity', 'trafficdrop', 'detection'}

    def get_health_check(self, check, use_cache=False):
        if check == "detection" and not flag_enabled(Flags.platform_algo_health):
            return {}
        return super().get_health_check(check, use_cache)


class AccountV2_5(AccountV2_4):
    """
    V2.5 API endpoint for Accounts/<id>.
    - API URL: /api/v2.5/account/<id>
    - Permission: account
    - Methods: GET
    - Required Parameters: None
    """

    serializer_class = AccountSerializerV2_5

    def get_serializer_class(self):
        return AccountSerializerV2_5


class VectraMatchDownloadRulesetViewV2_5(APIV2GenericView):
    """
    View class is responsible for processing GET requests to retrieve curated ruleset for Vectra Match.
    """

    permission = 'vectra_match_setting'
    allowed_methods = ('GET',)
    ALLOW_HTML = False

    def get(self, request):
        """
        Handle a GET request to retrieve curated ruleset for Vectra Match.

        Args:
            request: The HTTP request object.

        Returns:
            The .files file for the curated ruleset.
        """

        vectra_match = VectraMatch()

        try:
            match_license = vectra_match.get_license()

            if match_license.get("valid", False):
                vectra_ruleset_path = '/opt/vectra/threat_intel_feed/curated.rules'
                filename = os.path.basename(vectra_ruleset_path)
                response = StreamingHttpResponse(FileWrapper(open(vectra_ruleset_path, 'r')), content_type='text/plain')
                response['Content-Length'] = os.path.getsize(vectra_ruleset_path)
                response['Content-Disposition'] = f'attachment; filename={filename}'
                return response
            else:
                raise VectraMatchInvalidLicenseException

        except VectraMatchInvalidLicenseException:
            message = "You are not licensed to use Vectra Match, please contact support to enable this feature."
            LOG.error(message)
            return JsonResponse({"details": message}, status=status.HTTP_403_FORBIDDEN)
        except FileNotFoundError:
            message = "Vectra curated ruleset was not found. Is this system enabled for Vectra Match and connected to Vectra's cloud?"
            LOG.exception(message)
            return JsonResponse({"details": message}, status=status.HTTP_404_NOT_FOUND)
        except Exception:
            message = "Unexpected or error response from Vectra Match received."
            LOG.exception(message)
            return JsonResponse({"details": message}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GroupMembersV2_5(APIV2ListView, QueryParameterValueValidationMixin):
    """
    V2.5 API endpoint for groups/<id>/members.
    - API URL: /api/v2.5/groups/<id>/members
    - Permission: groups
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'page'
        'page_size'
        'ordering' (for hosts)
        'name'
        'is_key_asset' (for hosts)
        'uid'
    }

    Note that uid and name will both filter on uid and name for hosts and accounts respectively.

    Ordering is only available for hosts.
        - name
        - id
    """

    permission = 'groups'

    allowed_methods = ('GET',)
    pagination_class = StandardResultSetPagination
    filter_backends = (filters.OrderingFilter,)
    ordering_fields = ('name', 'id')

    _query_params = (
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
        'ordering',
        'name',
        'is_key_asset',
    )

    _gt_zero_params = (StandardResultSetPagination.page_query_param,)
    _str_params = (StandardResultSetPagination.page_size_query_param, 'ordering', 'name')
    _bool_params = ('is_key_asset',)

    def list(self, request, pk):
        group_obj = GroupCollection.objects.get(id=pk)

        # get query params
        self._validate_query_params(self.request.query_params.keys(), self._query_params)
        ordering = self.request.query_params.get('ordering') if self.request.query_params.get('ordering') else 'id'
        name = self.request.query_params.get('name')
        is_key_asset = self.request.query_params.get('is_key_asset')

        # get and filter queryset
        try:
            qs = self.get_queryset(group_obj, ordering)
            qs = self.filter_queryset(group_obj, qs, name, is_key_asset)
        except Exception as e:
            LOG.exception(f"Exception occurred when getting group members endpoint for {group_obj.id} type group of {group_obj.type}. {e}")
            return JsonResponse({"message": f"Error occurred while getting members. Please contact support."}, status=500)

        # Get page
        page = self.paginate_queryset(qs)

        # Process members on page that need processing
        if group_obj.type == GroupCollection.HOST:
            page = HostGroupAPIProcessor.process_members(request, page)
        elif group_obj.type == GroupCollection.ACCOUNT:
            page = AccountGroupAPIProcessor.process_members(request, page)

        return self.get_paginated_response(page)

    def get_queryset(self, group_obj, ordering):
        if group_obj.type == GroupCollection.HOST:
            return (
                group_obj.hostgroup.hosts.extra(select={'is_key_asset': 'key_asset'})
                .values('id', 'name', 'is_key_asset')
                .order_by(ordering)
            )
        if group_obj.type == GroupCollection.IP:
            return group_obj.ipgroup.ips
        if group_obj.type == GroupCollection.DOMAIN:
            return group_obj.externaldomaingroup.domains
        if group_obj.type == GroupCollection.ACCOUNT:
            if flag_enabled(Flags.linked_account_groups):
                return group_obj.accountgroup.linked_accounts.values('id', 'display_uid').order_by('display_uid')
            else:
                return group_obj.accountgroup.accounts.values('uid').order_by('uid')

    def filter_queryset(self, group_obj, qs, name, is_key_asset):
        """
        Applies filtering.
        hosts:
            - name
            - is_key_asset
        accounts:
            - uid
        IP:
            - name
        domains:
            - name
        """
        if group_obj.type == GroupCollection.HOST:
            if name:
                qs = qs.filter(name__contains=name)
            if is_key_asset is not None:
                is_key_asset = True if str(is_key_asset).lower() in ('true', '1') else False
                qs = qs.filter(key_asset=is_key_asset)
        elif group_obj.type == GroupCollection.ACCOUNT:
            if name:
                if flag_enabled(Flags.linked_account_groups):
                    qs = qs.filter(display_uid__contains=name)
                else:
                    qs = qs.filter(uid__contains=name)
        elif group_obj.type == GroupCollection.IP:
            if name:
                qs = [member for member in qs if name in member]
        elif group_obj.type == GroupCollection.DOMAIN:
            if name:
                qs = [member for member in qs if name in member]
        return qs


class DetectionV2_5(DetectionV2_3):
    """
    V2.5 API endpoint that represents a single detection
    - API URL: /api/v2.5/detection/<id>
    - Permission:
        - view: detection
        - edit: triage
    - Methods: GET, PATCH
    - Required Parameters: None
    """

    serializer_class = DetectionSerializerV2_5

    def __init__(self):
        super().__init__()
        self._query_params = self._query_params + tuple(['include_src_dst_groups'])
        self._bool_params = tuple(list(super()._bool_params) + ['include_src_dst_groups'])

    def get_serializer_class(self):
        return self.serializer_class


class DetectionsV2_5(DetectionsV2_3):
    """
    V2.5 API endpoint for Detections.
    - API URL: /api/v2.5/detection
    - Permission:
        - view: detection
        - edit: triage
    - Methods: GET, PATCH
    - Required Parameters: None
    """

    serializer_class = DetectionSerializerV2_5

    def __init__(self):
        super().__init__()

        self._query_params = self._query_params + tuple(['reason', 'include_src_dst_groups'])
        self._choices_params = {
            'fields': list(DetectionSerializerV2_5.Meta.fields) + self.added_fields,
            'exclude_fields': list(DetectionSerializerV2_5.Meta.fields),
            'reason': StateReasons.values(),
        }
        self._bool_params = self._bool_params + tuple(['include_src_dst_groups'])

    def get_serializer_class(self):
        return self.serializer_class

    def get_queryset(self):
        queryset = super().get_queryset()

        if flag_enabled(Flags.signal_efficacy_closed_as) and flag_enabled(Flags.signal_efficacy_public_preview):
            reason = self.request.query_params.get('reason')
            if reason is not None:
                queryset = queryset.filter(get_detection_status_filters(reason, 'SQL'))

        return queryset.distinct()


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionCloseV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for closing detections.
    - API URL: /api/v2.5/detections/<id>/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    edit_permission = 'triage'
    allowed_methods = ('PATCH',)

    def patch(self, request: HttpRequest, pk: int) -> JsonResponse:
        """
        Closes a detection with the given ID.

        Args:
            request: The HTTP request object.
            pk: The ID of the detection to close.
                - Example -> 1234
            reason: Reason the detection was closed
                - Example -> 'benign'

        Returns:
            Success (200)
            JSONDecodeError (400): Invalid Json
            MissingReasonException (400): Unable to access reason
            ObjectDoesNotExist (404): Detection Id does not exist
            InvalidStateReasonException (422): Invalid reason provided
            Exception (500): Any non-covered exception that gets thrown
        """
        try:
            request_body = json.loads(request.body)
            reason = request_body.get('reason')
            validate_reason(reason)

            detection_obj = detection.objects.filter(id=pk)
            validate_model_object_exists(detection_obj)

            if reason == StateReasons.REMEDIATED.value:
                close_detections(request, detection_obj, reason, service='api')
            else:
                filter_single_detection(request, detection_obj, reason, service='api')

            # Close detections
            log_audit_event(
                request=request,
                action=audit_factory.EventActions.CLOSED,
                object_type=audit_factory.EventObjects.DETECTION,
                data={'detection': {'id': pk}},
                message=f'Closed detection id {pk} as {reason}',
            )

            return JsonResponse(
                {'_meta': {'level': 'success', 'message': f'Successfully closed detection as {reason}'}},
                status=status.HTTP_200_OK,
            )

        except json.decoder.JSONDecodeError:
            return JsonResponse({'_meta': {'level': 'error', 'message': 'Payload must be valid JSON'}}, status=status.HTTP_400_BAD_REQUEST)
        except MissingReasonException:
            LOG.exception('Unable to access reason')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': {'reason': 'Reason is required'}}}, status=status.HTTP_400_BAD_REQUEST
            )
        except ObjectDoesNotExist:
            return JsonResponse({'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND)
        except InvalidStateReasonException:
            LOG.exception('Invalid reason value provided')
            return JsonResponse(
                {
                    '_meta': {
                        'level': 'error',
                        'message': {'reason': 'Invalid reason provided. Valid values are remediated or benign.'},
                    }
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )
        except Exception as e:
            LOG.exception(f'Unexpected error occurred: {e}')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionOpenV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for opening a detection.
    - API URL: /api/v2.5/detections/<id>/open
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    edit_permission = 'triage'
    allowed_methods = ('PATCH',)

    def patch(self, request: HttpRequest, pk: int) -> JsonResponse:
        """
        Opens a detection with the given ID.
        Args:
            request: The HTTP request object.
            pk: The ID of the detection to open.
                - Example -> 1234
        Returns:
            Success (200)
            ObjectDoesNotExist (404): Detection Id does not exist
            Exception (500): Any non-covered exception that gets thrown
        """
        try:
            detection_obj = detection.objects.filter(id=pk)
            validate_model_object_exists(detection_obj)

            single_detection = detection_obj[0]
            if single_detection.state == detection.FIXED:
                open_detections(request, detection_obj, service='api')
            if single_detection.is_filtered_by_user:
                delete_filters(request, [pk], reason='user_delete_singletons_api_v2.5', service='api')

            log_audit_event(
                request=request,
                action=audit_factory.EventActions.OPENED,
                object_type=audit_factory.EventObjects.DETECTION,
                data={'detection': {'id': pk}},
                message=f'Re-opened detection ids {pk}',
            )

            return JsonResponse(
                {'_meta': {'level': 'success', 'message': 'Successfully re-opened detection'}},
                status=status.HTTP_200_OK,
            )

        except ObjectDoesNotExist:
            return JsonResponse({'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            LOG.exception(f'Unexpected error occurred: {e}')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@view_requires_flag(Flags.signal_efficacy_public_preview)
class AccountCloseV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for closing an account.
    - API URL: /api/v2.5/accounts/<id>/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    edit_permission = 'triage'
    allowed_methods = ('PATCH',)

    def patch(self, request: HttpRequest, pk: int) -> JsonResponse:
        """
        Closes an account with the given ID.

        Args:
            request: The HTTP request object.
            pk: The ID of the account to close.
                - Example -> 1234
            reason: Reason the account was closed
                - Example -> 'benign'

        Returns:
            Success (200)
            JSONDecodeError (400): Invalid Json
            MissingReasonException (400): Unable to access reason
            Http404 (404): Detection Id does not exist
            InvalidStateReasonException (422): Invalid reason provided
            Exception (500): Any non-covered exception that gets thrown
        """
        try:
            account_obj = get_object_or_404(LinkedAccount, id=pk)
            payload = json.loads(request.body.decode('utf-8'))
            reason = payload.get('reason')
            validate_reason(reason)

            affected_ids = close_entity(request, payload, entity_obj=account_obj, entity_type='linked_account', reason=reason)
            if affected_ids:
                LOG.info(f'Detections for account {pk} closed as {reason}')
                log_audit_event(
                    request=request,
                    action=audit_factory.EventActions.CLOSED,
                    object_type=audit_factory.EventObjects.ACCOUNT_DETECTIONS,
                    data={'account_detections': {'account_id': pk, 'affected_detection_ids': affected_ids}},
                    message=f'close detection ids {affected_ids} as {reason} for account {pk}',
                )

            return JsonResponse(
                {
                    '_meta': {'level': 'success', 'message': f'Successfully closed account as {reason}'},
                    "detections_closed": affected_ids if affected_ids else [],
                },
                status=status.HTTP_200_OK,
            )

        except json.decoder.JSONDecodeError:
            return JsonResponse({'_meta': {'level': 'error', 'message': 'Payload must be valid JSON'}}, status=status.HTTP_400_BAD_REQUEST)
        except MissingReasonException:
            LOG.exception('Unable to access reason')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': {'reason': 'Reason is required'}}}, status=status.HTTP_400_BAD_REQUEST
            )
        except Http404:
            return JsonResponse({'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND)
        except InvalidStateReasonException:
            LOG.exception('Invalid reason value provided')
            return JsonResponse(
                {
                    '_meta': {
                        'level': 'error',
                        'message': {'reason': 'Invalid reason provided. Valid values are remediated or benign.'},
                    }
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )
        except Exception as e:
            LOG.exception(f'Unexpected error occurred: {e}')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionsOpenV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for opening detections.
    - API URL: /api/v2.5/detections/open
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: detectionIdList
    """

    edit_permission = 'triage'
    allowed_methods = ('PATCH',)
    api_version_reason = 'api_v2_5'

    def patch(self, request: HttpRequest) -> JsonResponse:
        """
        Opens detections given a list of detection IDs.
        """
        try:
            request_body = json.loads(request.body)
            serializer = DetectionIdListSerializerV2_5(data=request_body)
            serializer.is_valid(raise_exception=True)
            validated_id_list = serializer.validated_data['detectionIdList']

            detections = detection.objects.filter(id__in=validated_id_list)
            detection_ids = detections.values_list('id', flat=True)

            missing_ids = set(validated_id_list) - set(detection_ids)
            if missing_ids:
                LOG.error(f'Detection IDs not found: {list(missing_ids)}')
                return JsonResponse(
                    {
                        '_meta': {
                            'level': 'error',
                            'message': {'detectionIdList': 'Some detection IDs were not found.'},
                        },
                        'errors': {'invalid_ids': list(missing_ids)},
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            open_detections(request, detections, service='api')
            delete_filters(request, detection_ids, reason=f'user_delete_singletons_api_{self.api_version_reason}', service='api')

            return JsonResponse({'_meta': {'level': 'success', 'message': 'Successfully re-opened detections'}}, status=status.HTTP_200_OK)

        except json.JSONDecodeError:
            LOG.exception('Unable to decode JSON')
            return JsonResponse({'_meta': {'level': 'error', 'message': 'Payload must be valid JSON'}}, status=status.HTTP_400_BAD_REQUEST)
        except serializers.ValidationError as e:
            LOG.exception(f'Validation error occurred: {e}')
            return self._handle_validation_error(e, request_body.get('detectionIdList', []))
        except Exception as e:
            LOG.exception(f'Unexpected error occurred: {e}')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _handle_validation_error(self, error: ErrorDetail, unvalidated_id_list):
        errors = error.detail.get('detectionIdList')
        if isinstance(errors, list) and len(errors) == 1 and any(err.code in ['required', 'not_a_list', 'empty'] for err in errors):
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': {'detectionIdList': errors[0]}}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        invalid_items = []
        for key in error.detail.get('detectionIdList', []):
            if isinstance(key, int) and 0 <= key < len(unvalidated_id_list):
                invalid_items.append(unvalidated_id_list[key])
            else:
                LOG.error(f'Invalid index detected: {key}')
                return JsonResponse(
                    {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        return JsonResponse(
            {
                '_meta': {'level': 'error', 'message': {'detectionIdList': 'detectionIdList must contain only non-negative integers'}},
                'errors': {'invalid_ids': invalid_items},
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


@view_requires_flag(Flags.signal_efficacy_public_preview)
class HostCloseV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for closing a host.
    - API URL: /api/v2.5/hosts/<id>/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    edit_permission = 'triage'
    allowed_methods = ('PATCH',)

    def patch(self, request: HttpRequest, pk: int) -> JsonResponse:
        """
        Closes a host with the given ID.

        Args:
            request: The HTTP request object.
            pk: The ID of the host to close.
                - Example -> 1234
            reason: Reason the host was closed
                - Example -> 'benign'

        Returns:
            A JSON response indicating the success or failure of the operation.
            JSONDecodeError (400): Invalid Json
            MissingReasonException (400): Unable to access reason
            ObjectDoesNotExist (404): Host Id does not exist
            InvalidStateReasonException (422): Invalid reason provided
            Exception (500): Any non-covered exception that gets thrown
        """
        try:
            host_obj = host.objects.get(id=pk)
            request_body = json.loads(request.body.decode('utf-8'))
            reason = request_body.get('reason')
            validate_reason(reason)

            affected_ids = close_entity(request, request_body, entity_obj=host_obj, entity_type='host', reason=reason)
            if affected_ids:
                LOG.info(f'All detections for host {pk} closed as {reason}')
                AUDIT.audit(lib_tv.get_audit_user_data(request), True, f'close detection ids {affected_ids} as {reason} for host {pk}')
                host_scoring.score_host(pk, reason=f'Detection {str(affected_ids)} closed as {reason}')

            return JsonResponse(
                {
                    '_meta': {'level': 'success', 'message': f'Successfully closed host as {reason}'},
                    'detections_closed': affected_ids if affected_ids else [],
                },
                status=status.HTTP_200_OK,
            )

        except json.decoder.JSONDecodeError:
            return JsonResponse({'_meta': {'level': 'error', 'message': 'Payload must be valid JSON'}}, status=status.HTTP_400_BAD_REQUEST)
        except MissingReasonException:
            LOG.exception('Unable to access reason')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': {'reason': 'Reason is required'}}}, status=status.HTTP_400_BAD_REQUEST
            )
        except host.DoesNotExist:
            return JsonResponse({"detail": "Not found."}, status=status.HTTP_404_NOT_FOUND)
        except InvalidStateReasonException:
            LOG.exception('Invalid reason value provided')
            return JsonResponse(
                {
                    '_meta': {
                        'level': 'error',
                        'message': {'reason': 'Invalid reason provided. Valid values are remediated or benign.'},
                    }
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )
        except Exception as e:
            LOG.exception(f'Unexpected error occurred: {e}')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionsCloseV2_5(APIV2GenericView):
    """
    V2.5 API endpoint for closing detections.
    - API URL: /api/v2.5/detections/close/
    - Methods: PATCH
    - Required Parameters: None
    """

    edit_permission = 'triage'
    allowed_methods = ('PATCH',)

    def patch(self, request: HttpRequest) -> JsonResponse:
        """
        Closes all detection with the given list of IDs.
        Args:
            request: The HTTP request object.
                detectioIdList: The list of IDs of the detections to close.
                    - Example -> [1234, 1235, 1236, ..]
                reason: Reason the detections were closed
                    - Example -> 'benign'
        Returns:
            A JSON response indicating the success or failure of the operation.
        Raises:
            JSONDecodeError (400): Invalid Json
            MissingDetectionIdListException (400): Unable to access detectionIdList
            InvalidDetectionIdException(400): Invalid Detection Id provided
            MissingReasonException (400): Unable to access reason
            ObjectDoesNotExist (404): Invalid detectionId provided (does not exist)
            InvalidDetectionIdListTypeException (422): detectionIdList is not of the type list
            InvalidStateReasonException (422): Invalid reason provided
            (500): Any non-covered exception that gets thrown
        """
        try:
            request_body = json.loads(request.body)
            reason = request_body.get('reason')
            validate_reason(reason)
            serializer = DetectionIdListSerializerV2_5(data=request_body)
            serializer.is_valid(raise_exception=True)
            validated_id_list = serializer.validated_data['detectionIdList']
            detection_objects = detection.objects.filter(id__in=validated_id_list)
            detection_ids = detection_objects.values_list('id', flat=True)

            missing_ids = set(validated_id_list) - set(detection_ids)

            if missing_ids:
                LOG.error(f'Detection IDs not found: {list(missing_ids)}')
                return JsonResponse(
                    {
                        '_meta': {
                            'level': 'error',
                            'message': {'detectionIdList': 'Some detection IDs were not found.'},
                        },
                        'errors': {'invalid_ids': list(missing_ids)},
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            if reason == StateReasons.REMEDIATED.value:
                close_detections(request, detection_objects, reason, service='api')
            else:
                filter_bulk_detections(request, detection_objects, reason, service='api')

            # Close detections
            log_audit_event(
                request=request,
                action=audit_factory.EventActions.CLOSED,
                object_type=audit_factory.EventObjects.DETECTIONS,
                data={'detections': {'ids': detection_objects}},
                message=f'Closed detection ids {detection_objects} as {reason}',
            )
            return JsonResponse(
                {'_meta': {'level': 'success', 'message': f'Successfully closed detections as {reason}'}},
                status=status.HTTP_200_OK,
            )
        except json.decoder.JSONDecodeError:
            LOG.exception('Unable to decode JSON')
            return JsonResponse({'_meta': {'level': 'error', 'message': 'Payload must be valid JSON'}}, status=status.HTTP_400_BAD_REQUEST)
        except serializers.ValidationError as e:
            LOG.exception(f'Validation error occurred: {e}')
            return self._handle_validation_error(e, request_body.get('detectionIdList', []))
        except MissingReasonException:
            LOG.exception('Unable to access reason')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': {'reason': 'Reason is required'}}}, status=status.HTTP_400_BAD_REQUEST
            )
        except InvalidStateReasonException:
            LOG.exception('Invalid reason value provided')
            return JsonResponse(
                {
                    '_meta': {
                        'level': 'error',
                        'message': {'reason': 'Invalid reason provided. Valid values are remediated or benign.'},
                    }
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY,
            )
        except Exception as e:
            LOG.exception(f'Unexpected error occurred: {e}')
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _handle_validation_error(self, error: ErrorDetail, unvalidated_id_list):
        errors = error.detail.get('detectionIdList')
        if isinstance(errors, list) and len(errors) == 1 and any(err.code in ['required', 'not_a_list', 'empty'] for err in errors):
            return JsonResponse(
                {'_meta': {'level': 'error', 'message': {'detectionIdList': errors[0]}}},
                status=status.HTTP_400_BAD_REQUEST,
            )

        invalid_items = []
        for key in error.detail.get('detectionIdList', []):
            if isinstance(key, int) and 0 <= key < len(unvalidated_id_list):
                invalid_items.append(unvalidated_id_list[key])
            else:
                LOG.error(f'Invalid index detected: {key}')
                return JsonResponse(
                    {'_meta': {'level': 'error', 'message': 'Internal Server Error'}}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        return JsonResponse(
            {
                '_meta': {'level': 'error', 'message': {'detectionIdList': 'detectionIdList must contain only non-negative integers'}},
                'errors': {'invalid_ids': invalid_items},
            },
            status=status.HTTP_400_BAD_REQUEST,
        )


class NotificationReceiversV2_5(NotificationReceiversBaseView, APIV2ListView):
    pass


class HostsV2_5(HostsV2):
    """
    V2.5 API endpoint for Hosts.
    - API URL: /api/v2.5/hosts
    - Permission: view_hosts
    - Methods: GET
    - Required Parameters: None
    """

    serializer_class = HostSerializerV2_5

    def get_serializer_class(self):
        return self.serializer_class
