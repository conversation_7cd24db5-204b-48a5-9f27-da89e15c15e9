# Copyright (c) 2023 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

"""
Urls for REST API for endpoints behind /api/v2.5
"""

from django.conf.urls import url

from base_tvui.rest.api_v2.api_v2_4.api_v2_4_urls import urlpatterns as v2_4_urls
from base_tvui.rest.api_v2.api_v2_5 import api_v2_5_views

urlpatterns = [
    url(r'^groups/?$', api_v2_5_views.GroupsV2_5.as_view(), name='api-groups-v2.5'),
    url(r'^groups/(?P<pk>\d+)/?$', api_v2_5_views.GroupV2_5.as_view(), name='api-group-v2.5'),
    url(
        r'^settings/active_directory/groups/?$',
        api_v2_5_views.ActiveDirectoryGroupsV2_5.as_view(),
        name='api-settings-groups-active-directory-v2.5',
    ),
    url(
        r'^vectra-match/enablement/?$',
        api_v2_5_views.VectraMatchEnablementViewV2_5.as_view(),
        name='api-vectra-match-enablement-v2.5',
    ),
    url(
        r'^vectra-match/stats/?$',
        api_v2_5_views.VectraMatchStatsViewV2_5.as_view(),
        name='api-vectra-match-stats-v2.5',
    ),
    url(
        r'^vectra-match/status/?$',
        api_v2_5_views.VectraMatchStatusViewV2_5.as_view(),
        name='api-vectra-match-status-v2.5',
    ),
    url(
        r'^vectra-match/available-devices/?$',
        api_v2_5_views.VectraMatchAvailableDevicesViewV2_5.as_view(),
        name='api-vectra-match-available-devices-v2.5',
    ),
    url(
        r'^vectra-match/rules/?$',
        api_v2_5_views.VectraMatchRulesViewV2_5.as_view(),
        name='api-vectra-match-rules-v2.5',
    ),
    url(
        r'^vectra-match/assignment/?$',
        api_v2_5_views.VectraMatchAssignmentViewV2_5.as_view(),
        name='api-vectra-match-assignment-v2.5',
    ),
    url(
        r'^vectra-match/alert-stats/?$',
        api_v2_5_views.VectraMatchAlertStatsViewV2_5.as_view(),
        name='api-vectra-match-alert-stats-v2.5',
    ),
    url(r'^accounts/?$', api_v2_5_views.AccountsV2_5.as_view(), name='api-accounts-v2.5'),
    url(r'^accounts/(?P<pk>\d+)/?$', api_v2_5_views.AccountV2_5.as_view(), name='api-account-v2.5'),
    url(r'^accounts/(?P<pk>\d+)/close?$', api_v2_5_views.AccountCloseV2_5.as_view(), name='api-account-close-v2.5'),
    url(
        r'^vectra-match/download-vectra-ruleset',
        api_v2_5_views.VectraMatchDownloadRulesetViewV2_5.as_view(),
        name='api-vectra-match-download-ruleset-v2.5',
    ),
    url(r'^health/?$', api_v2_5_views.HealthV2_5.as_view(), name="api-health-v2.5"),
    url(r'^health/(?P<check_type>\w+)/?$', api_v2_5_views.HealthV2_5.as_view(), name='api-health-check-v2.5'),
    url(r'^groups/(?P<pk>\d+)/members/?$', api_v2_5_views.GroupMembersV2_5.as_view(), name='api-group-members-v2.5'),
    url(r'^detections/?$', api_v2_5_views.DetectionsV2_5.as_view(), name='api-detections-v2.5'),
    url(r'^detections/(?P<pk>\d+)/?$', api_v2_5_views.DetectionV2_5.as_view(), name='api-detection-v2.5'),
    url(r'^detections/(?P<pk>\d+)/close/?$', api_v2_5_views.DetectionCloseV2_5.as_view(), name='api-detection-close-v2.5'),
    url(r'^detections/open/?$', api_v2_5_views.DetectionsOpenV2_5.as_view(), name='api-detections-open-v2.5'),
    url(r'^detections/close/?$', api_v2_5_views.DetectionsCloseV2_5.as_view(), name='api-detections-close-v2.5'),
    url(r'^detections/(?P<pk>\d+)/open/?$', api_v2_5_views.DetectionOpenV2_5.as_view(), name='api-detection-open-v2.5'),
    url(
        r'^notification/recievers/?$',
        api_v2_5_views.NotificationReceiversV2_5.as_view({'get': 'list', 'post': 'post'}),
        name='notification-receivers-v2.5',
    ),
    url(
        r'^notification/recievers/(?P<pk>\w+)/?$',
        api_v2_5_views.NotificationReceiversV2_5.as_view({'get': 'get', 'patch': 'patch', 'delete': 'delete'}),
        name='notification-receiver-v2.5',
    ),
    url(r'^hosts/(?P<pk>\d+)/close/?$', api_v2_5_views.HostCloseV2_5.as_view(), name='api-host-close-v2.5'),
    url(r'^hosts/?$', api_v2_5_views.HostsV2_5.as_view(), name='api-hosts-v2.5'),
] + v2_4_urls
