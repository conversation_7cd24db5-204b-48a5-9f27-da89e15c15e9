# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import json
import base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils as health_utils
import timeout_decorator

from rest_framework import filters, status
from rest_framework.generics import ListAPIView
from rest_framework.response import Response
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.exceptions import ValidationError
from django.utils import timezone
from django.shortcuts import render
from django_celery_results.models import TaskResult
from urllib.parse import urljoin
from datetime import date, datetime, timedelta
from django.db.models import F, Q

from django.http import Http404, JsonResponse
from django.shortcuts import get_object_or_404

from dateutil.relativedelta import relativedelta

from base_tvui.bin_utils import decompress_list, ListDecompressionError
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import (
    AccountSerializerV3_4,
    UserSerializerV3_4,
    UserRolesSerializerV3_4,
    EntityScoringEventsSerializerV3_4,
    EntitySerializerV3_4,
    LockdownSerializerV3_4,
    DetectionEventSerializerV3_4,
    DetectionSerializerV3_4,
    HealthGenericIntegrationSerializerV3_4,
    ActiveDirectoryHealthDetailsSerializerV3_4,
    AzureAdLockdownHealthDetailsSerializerV3_4,
    AWSHealthDetailsSerializerV3_4,
    AzureHealthDetailsSerializerV3_4,
    GCPHealthDetailsSerializerV3_4,
    DNSLookupHealthDetailsSerializerV3_4,
    SiemHealthDetailsSerializerV3_4,
    VcenterHealthDetailsSerializerV3_4,
    WindowsEventLogHealthDetailsSerializerV3_4,
    ZPAHealthDetailsSerializerV3_4,
    HostLockdownSerializerV3_4,
    WindowsDefenderSerializerV3_4,
    CarbonBlackSerializerV3_4,
    CarbonBlackCloudSerializerV3_4,
    CloudDataSourceSerializerV3_4,
    FireEyeEndpointSecuritySerializerV3_4,
    SentinelOneSerializerV3_4,
    CybereasonSerializerV3_4,
    CrowdStrikeSerializerV3_4,
    UniqueHostAuditSerializerV3_4,
    GroupSerializerV3_4,
)
from base_tvui.feature_flipper import Flags, view_requires_flag, flag_enabled
from base_tvui.rest.api_v2.api_v2_0_3.api_v2_views import parse_tz_offset
from base_tvui.rest.api_v3.api_v3_2.api_v3_2_views import (
    AccountV3_2,
    AccountsV3_2,
    parse_time_string,
    QueryParameterValueValidationMixin,
)
from base_tvui.rest.api_v3.api_v3_0.api_v3_views import APIV3ListView, ApiV3View
from base_tvui.rest.api_v3.api_v3_3.api_v3_3_views import (
    CloudbridgeRouter,
    DetectionsV3_3,
    TaggingV3_3,
    UsersV3_3,
    HealthV3_3,
    EntityScoringEventsV3_3,
    EntitiesV3_3,
    EntityV3_3,
    DetectionEventsV3_3,
    EntityLockdownV3_3,
    BulkTaggingV3_3,
    NotesV3_3ViewSet,
    DetectionV3_3,
    UniqueHostCountMonthlyV3_3,
    UniqueHostAuditMonthlyV3_3,
    GroupsV3_3,
    GroupV3_3,
    BulkNotesV3_3Create,
)
from base_tvui.rest.api_v2.api_v2_5.api_v2_5_views import (
    AccountCloseV2_5,
    DetectionCloseV2_5,
    DetectionOpenV2_5,
    DetectionsOpenV2_5,
    DetectionsCloseV2_5,
    HealthV2_5,
    GroupsV2_5,
    GroupV2_5,
    GroupMembersV2_5,
    HostCloseV2_5,
    ActiveDirectoryGroupsV2_5,
)
from base_tvui.rest.custom_exceptions import (
    InvalidRequestField,
    InvalidQueryParams,
    InvalidQueryParamValues,
    MissingQueryParamValues,
    HealthTaskTimeout,
    CouldNotGetTaskResult,
    UnexpectedException,
)
from base_tvui.rest.pagination import StandardResultSetPagination
from base_tvui.validator_utils import validate_email
from base_tvui import validator_utils
from tvui.cloud_user_management_views import UserDetail, UsersList
from tvui.models import User, VUIGroup, HostAudit, HostAuditSnapshot, GroupCollection, StateReasons
from tvui.group_collections.lib_group_collection import GroupCollectionFacilitator
from tvui.group_collections.account_groups.lib_account_group import AccountGroupsFacilitator
from tvui.async_tasks.celery_tasks import refresh_external_connector_fields, refresh_edr_health_fields
from tvui.group_collections.host_groups.lib_host_group import HostGroupsFacilitator
from base_tvui.lib_cloudbridge import make_get_request
from base_tvui.providers.lib_cloud_sensor import CloudSensorManagement
from base_tvui.providers.lib_sensors import annotate_sensor_status
from base_tvui.lib_entity import get_detection_status_filters
from tvui.events.destinations.notification_receiver_views import NotificationReceiversBaseView

from celery.exceptions import TimeoutError

from base_tvui import audit_factory

from pure_utils import log_utils

LOG = log_utils.get_vectra_logger(__name__)
AUDIT = audit_factory.get_audit_object()


class InvalidContentTypeError(Exception):
    """Exception to raise when content-type is not passed in."""

    pass


class AccountsV3_4(AccountsV3_2):
    """
    V3.4 API endpoint for Accounts.
    - API URL: /api/v3.4/accounts
    - Permission: accounts
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'fields'
        'page'
        'page_size'
        'ordering'
        'name'
        'state'
        't_score'
        't_score_gte'
        'c_score'
        'c_score_gte'
        'tags'
        'all',
        'min_id',
        'max_id',
        'note_modified_timestamp_gte',
        'privilege_level',
        'privilege_level_gte',
        'privilege_category'
    }
    """

    serializer_class = AccountSerializerV3_4


class EntityV3_4(EntityV3_3):
    """
    V3.4 API endpoint for Entity.
    - API URL: /api/v3.4/entities/<id>
    - Permission: account,host
    - Methods: GET
    - Required Parameters: None
    - Required Query Parameters: {
        'type'
    }
    """

    serializer_class = EntitySerializerV3_4

    def __init__(self):
        super().__init__()
        self._choices_params = {'type': ('account', 'host')}
        self._query_params = 'type'

    def get_serializer_class(self):
        return EntitySerializerV3_4


class EntitiesV3_4(EntitiesV3_3):
    """
    V3.4 API endpoint for Entities.
    - API URL: /api/v3.4/entities
    - Permission: account,host
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'id'
        'is_prioritized'
        'last_modified_timestamp_lte'
        'last_modified_timestamp_gte'
        'type'
        'ordering'
        'last_detection_timestamp_gte'
        'last_detection_timestamp_lte'
        'note_modified_timestamp_gte'
        'name'
        'fields',
        'exclude_fields',
        'state'
        'page'
        'page_size'
        'tags'
    }
    """

    serializer_class = EntitySerializerV3_4

    def __init__(self):
        super().__init__()
        self._query_params = (
            'id',
            'is_prioritized',
            'last_modified_timestamp_lte',
            'last_modified_timestamp_gte',
            'type',
            'ordering',
            'last_detection_timestamp_gte',
            'last_detection_timestamp_lte',
            'note_modified_timestamp_gte',
            'name',
            'fields',
            'exclude_fields',
            'state',
            StandardResultSetPagination.page_query_param,
            StandardResultSetPagination.page_size_query_param,
            'tags',
        )


class EntityScoringEventsV3_4(EntityScoringEventsV3_3):
    """
    V3.4 API endpoint for Entity Scoring.
    - API URL: /api/v3.4/events/entity_scoring
    - Permission: account,host
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'event_timestamp_gte',
        'event_timestamp_lte',
        'from',
        'include_score_decreases',
        'limit',
        'ordering',
        'type',
    }
    """

    serializer_class = EntityScoringEventsSerializerV3_4

    def __init__(self):
        super().__init__()
        self.deprecated_query_params = ['entity_type']
        self._query_params = tuple([param for param in list(self._query_params) if param not in self.deprecated_query_params])

    def get_serializer_class(self):
        return EntityScoringEventsSerializerV3_4


class DetectionEventsV3_4(DetectionEventsV3_3):
    """
    V3.4 API endpoint for Detection Events.
    - API URL: /api/v3.4/events/detections
    - Permission: detection
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'event_timestamp_gte'
        'from'
        'include_triaged'
        'include_info_category'
        'limit'
        'detection_id'
        'type'
        'entity_uid' # Cannot be supported for hosts at this time
        'ordering'
    }
    """

    serializer_class = DetectionEventSerializerV3_4

    def __init__(self):
        super().__init__()
        self.deprecated_query_params = ['entity_type']
        self._query_params = tuple([param for param in list(self._query_params) if param not in self.deprecated_query_params])

    def get_serializer_class(self):
        return self.serializer_class


class EntityLockdownV3_4(EntityLockdownV3_3):
    """
    V3.4 API endpoint for Lockdown.
    - API URL: /api/v3.4/lockdown
    - Permission: account_lockdown,host_lockdown
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'type'
    }
    """

    serializer_class = LockdownSerializerV3_4

    def __init__(self):
        super().__init__()
        self.deprecated_query_params = ['entity_type']
        self._query_params = tuple([param for param in list(self._query_params) if param not in self.deprecated_query_params])

    def get_serializer_class(self):
        return LockdownSerializerV3_4


class BulkNotesV3_4Create(BulkNotesV3_3Create):
    """
    V3.4 API endpoint for bulk note creation.
    Valid API URLs:
    - API URL: /api/v3.4/entities/notes
        - Permission: notes
        - Methods: POST
        - Required Parameters: type
        - Possible Query Parameters: {
            'type'
        }
    - API URL: /api/v3.4/<entity_name>/notes
        - Permission: notes
        - Methods: POST
    """

    def __init__(self):
        super().__init__()
        self.deprecated_query_params = ['entity_type']
        self._query_params = tuple([param for param in list(self._query_params) if param not in self.deprecated_query_params])


class NotesV3_4ViewSet(NotesV3_3ViewSet):
    """
    V3.4 API endpoint for notes.
    Valid API URLs:
    - API URL: /api/v3.4/entities/<id>/notes
        - Permission: notes
        - Methods: GET, POST
        - Required Parameters: entity_type or type
        - Possible Query Parameters: {
            'entity_type'
            'type'
        }
    - API URL: /api/v3.4/<entity_name>/<id>/notes
        - Permission: notes
        - Methods: GET, POST
    - API URL: /api/v3.4/entities/<id>/notes/<note_id>
        - Permission: notes
        - Methods: GET, PATCH, DELETE
        - Required Parameters: entity_type or type
        - Possible Query Parameters: {
            'entity_type'
            'type'
        }
    - API URL: /api/v3.4/<entity_name>/<id>/notes/<note_id>
        - Permission: notes
        - Methods: GET, PATCH, DELETE
    """

    def initial(self, request, *args, **kwargs):
        super(ApiV3View, self).initial(request, *args, **kwargs)
        self.format_kwarg = self.get_format_suffix(**kwargs)
        if self.kwargs['tvui_type'] == "entitie":
            if entity_type := self.request.query_params.get('type'):
                self.kwargs['tvui_type'] = entity_type
            else:
                raise MissingQueryParamValues(['type'], request_id=self.request.headers.get('X-Amzn-Requestid'))


NotesV3_4Detail = NotesV3_4ViewSet.as_view(actions={'get': 'retrieve', 'patch': 'partial_update', 'delete': 'destroy'})
NotesV3_4ListCreate = NotesV3_4ViewSet.as_view(actions={'get': 'list', 'post': 'create'})


class AccountV3_4(AccountV3_2):
    """
    V3.4 API endpoint for Accounts/<id>.
    - API URL: /api/v3.4/account/<id>
    - Permission: account
    - Methods: GET
    - Required Parameters: None
    """

    serializer_class = AccountSerializerV3_4

    def get_serializer_class(self):
        return AccountSerializerV3_4


@view_requires_flag(Flags.signal_efficacy_public_preview)
class AccountCloseV3_4(ApiV3View, AccountCloseV2_5):
    """
    V3.4 API endpoint for Account Close
    - API URL: /api/v3.4/accounts/<id>/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    def __init__(self):
        super().__init__()


class UserV3_4(ApiV3View, UserDetail):
    """
    V3.4 API endpoint for single User.
    - API URL: /api/v3.4/users/<id>
    - Permission: users
    - Methods: GET, PATCH, DELETE
    - Required Parameters: None
    - Possible Query Parameters: None
    """

    permission = 'users'

    serializer_class = UserSerializerV3_4
    allowed_methods = ('GET', 'PATCH', 'DELETE')
    renderer_classes = (JSONRenderer,)

    def get(self, request, *args, **kwargs):
        user_id = kwargs['pk']
        user = get_object_or_404(User.objects.conditional_users(), id=int(user_id))
        serialized_data = self.serializer_class(user).data
        return JsonResponse(serialized_data)

    def delete(self, request, *args, **kwargs):
        user_id = kwargs['pk']
        super_user_role = 'Super Admin'
        super_users = User.objects.conditional_users().filter(groups__group_extend__vname=super_user_role).exclude(first_login=None)
        super_users_ids = [super_user.id for super_user in super_users]
        if len(super_users_ids) == 1 and int(user_id) in super_users_ids:
            return Response(data={'error': 'Cannot delete last remaining verified Super Admin user.'}, status=status.HTTP_403_FORBIDDEN)
        try:
            resp = super().delete(request, user_id, *args, **kwargs)
            if resp.status_code != 200:
                return JsonResponse({'error': 'Error deleting user, please retry.'}, status=resp.status_code)
        except Http404 as error:
            raise error
        except Exception as err:
            LOG.exception(f'Error verifying user deletion: {err}.')
            return JsonResponse({'error': f'Unable to verify user deletion. Please retry.'}, status=500)
        return Response(status=204)

    def patch(self, request, *args, **kwargs):
        try:
            json.loads(request.body)
            # Ensure correct content type is in request
            if request.content_type != "application/json":
                raise InvalidContentTypeError
        except json.decoder.JSONDecodeError:
            return JsonResponse({"error": "Payload must be valid JSON"}, status=400)
        except InvalidContentTypeError:
            return JsonResponse({"error": "Unsupported Media Type"}, status=415)

        user_id = kwargs['pk']
        user = get_object_or_404(User.objects.conditional_users(), id=int(user_id))

        user_payload = request.data

        invalid_keys = []
        for key in user_payload.keys():
            if key not in ['name', 'role']:
                invalid_keys.append(key)
        if len(invalid_keys) > 0:
            raise InvalidRequestField(invalid_keys)

        name = user_payload.get('name')
        role = user_payload.get('role')
        changed_user_attributes = {}
        saml_enabled = user.saas_saml_profiles.filter(enabled=True).exists()

        if role:
            try:
                group = VUIGroup.objects.get(name=role)
            except Exception as error:
                return JsonResponse(
                    {'error': f'Invalid role provided. Query api/v{request.api_version}/users/roles for the role\'s standardized_name.'},
                    status=400,
                )
            # Ensure that active saml users cannot update role
            # If the requested role matches the user's current role, allow the request
            if saml_enabled:
                try:
                    current_role = user.groups.all().first().name
                except Exception as err:
                    LOG.exception(f'Error getting the user role : {err}.')
                    return JsonResponse({'error': f"Unable to get the user's role."}, status=500)
                if role != current_role:
                    return JsonResponse(
                        {'error': 'Cannot modify the role of a currently active SAML user.'},
                        status=400,
                    )

            changed_user_attributes['role'] = {'id': group.id}

        if name:
            parsed_name = name.rstrip().split(" ", 1)
            if len(parsed_name) != 2:
                return JsonResponse({'error': 'The name field requires space separated first and last name.'}, status=400)

            # Ensure that active saml users cannot update name
            # If the requested name matches the user's current name, allow the request
            if saml_enabled:
                try:
                    current_name = user.full_name
                    request_name = " ".join(parsed_name)
                except Exception as err:
                    LOG.exception(f'Error getting the user name : {err}.')
                    return JsonResponse({'error': f"Unable to get the user's name."}, status=500)
                if current_name != request_name:
                    return JsonResponse(
                        {'error': f'Cannot modify the name of a currently active SAML user.'},
                        status=400,
                    )

            changed_user_attributes['name'] = name

        request.data['user'] = changed_user_attributes

        try:
            resp = super().patch(request, user_id, *args, **kwargs)
        except Exception:
            raise
        try:
            if resp.status_code != 200:
                return JsonResponse({'error': 'Error patching user, please retry.'}, status=resp.status_code)
            else:
                resp_obj = resp.content.decode('utf-8')
                user_obj = json.loads(resp_obj).get('user')
                user_id = user_obj.get('id')
                user = get_object_or_404(User.objects.conditional_users(), id=int(user_id))
                serialized_data = self.serializer_class(user).data
                return JsonResponse(serialized_data, status=200)
        except Exception as err:
            LOG.exception(f'Error verifying user patching: {err}.')
            return JsonResponse({'error': f'Unable to verify user patching. Please retry.'}, status=500)


class UsersV3_4(UsersV3_3, UsersList):
    """
    V3.4 API endpoint for Users.
    - API URL: /api/v3.4/users
    - Permission: users
    - Methods: GET, POST
    - Required Parameters: None
    - Possible Query Parameters: {
        'page'
        'page_size'
        'email'
        'role'
        'last_login_gte'
    }
    """

    _api_version = 'v3.4'
    serializer_class = UserSerializerV3_4
    allowed_methods = ('GET', 'POST')
    is_valid_timestamp = lambda self, timestamp: validator_utils.validate_timestamp_no_space(timestamp)

    def __init__(self):
        self._query_params = (
            'email',
            'role',
            'last_login_gte',
            StandardResultSetPagination.page_query_param,
            StandardResultSetPagination.page_size_query_param,
        )
        self._str_params = ('email', 'role')
        self._timestamp_params = ('last_login_gte',)

    def get_queryset(self):
        role = self.request.query_params.get('role')
        if role is not None:
            try:
                VUIGroup.objects.get(name=role)
            except VUIGroup.DoesNotExist:
                raise ValidationError(
                    'Invalid role provided. Query api/v3.4/users/roles for a list of valid roles. Filter with a role\'s standardized_name.'
                )

        queryset = super().get_queryset()
        email = self.request.query_params.get('email')
        if email is not None:
            queryset = queryset.filter(username=email)
        return queryset.distinct()

    def post(self, request, *args, **kwargs):

        if request.content_type != "application/json":
            return JsonResponse({"error": "Unsupported Media Type"}, status=415)

        try:
            json.loads(request.body)
        except json.decoder.JSONDecodeError:
            return JsonResponse({"error": "Payload must be valid JSON"}, status=400)

        user_payload = request.data
        required_keys = [
            'name',
            'email',
            'role',
        ]
        invalid_keys = []
        for key in user_payload.keys():
            if key not in required_keys:
                invalid_keys.append(key)
        if len(invalid_keys) > 0:
            raise InvalidRequestField(invalid_keys)

        missing_keys = []
        for key in required_keys:
            if key not in user_payload.keys():
                missing_keys.append(key)

        if len(missing_keys) > 0:
            return JsonResponse({"error": f"Missing required request body params: {missing_keys}"}, status=400)

        email = user_payload.get('email')
        name = user_payload.get('name')
        role = user_payload.get('role')

        if not validate_email(email):
            return JsonResponse({'error': 'Invalid email address.'}, status=422)

        try:
            user_check = User.objects.conditional_users().get(username=email)
            if user_check:
                return JsonResponse({'error': 'A user profile with this email already exists.'}, status=400)
        except User.DoesNotExist:
            pass

        try:
            group = VUIGroup.objects.get(name=role)
        except Exception as error:
            return JsonResponse(
                {'error': f'Invalid role provided. Query api/v{request.api_version}/users/roles for the role\'s standardized_name.'},
                status=400,
            )

        parsed_name = name.rstrip().split(" ", 1)
        if len(parsed_name) != 2:
            return JsonResponse({'error': 'The name field requires space separated first and last name.'}, status=400)

        request.data['user'] = {'username': email, 'name': name, 'role': {'id': group.id}}

        try:
            resp = super(UsersV3_4, self).post(request, *args, **kwargs)
        except Exception:
            raise
        try:
            if resp.status_code != 200:
                return JsonResponse({'error': 'Error creating user, please check your payload and retry.'}, status=resp.status_code)
            else:
                resp_obj = resp.content.decode('utf-8')
                user_obj = json.loads(resp_obj).get('user')
                new_user_id = user_obj.get('id')
                new_user = get_object_or_404(User.objects.conditional_users(), id=int(new_user_id))
                serialized_data = self.serializer_class(new_user).data
                return JsonResponse(serialized_data, status=200)
        except Exception as err:
            LOG.exception(f'Error verifying user creation: {err}.')
            return JsonResponse({'error': f'Unable to verify user creation. Please retry.'}, status=500)


class UserRolesV3_4(APIV3ListView):
    """
    V3.4 API endpoint for User Roles
    - API URL: /api/v3.4/users/roles
    - Permission: roles
    - Methods: GET
    - Required Parameters: None
    """

    permission = 'roles'

    serializer_class = UserRolesSerializerV3_4
    allowed_methods = ('GET',)
    renderer_classes = (JSONRenderer,)

    def list(self, request, *args, **kwargs):
        queryset = VUIGroup.objects.conditional_roles()
        serialized_data = self.serializer_class(queryset, many=True).data
        return JsonResponse(serialized_data, safe=False)


class DetectionV3_4(DetectionV3_3):
    """
    V3 API endpoint that represents a single detection
    """

    serializer_class = DetectionSerializerV3_4

    def __init__(self):
        super().__init__()
        self._choices_params = {
            'fields': list(DetectionSerializerV3_4.Meta.fields) + self.added_fields,
            'exclude_fields': list(DetectionSerializerV3_4.Meta.fields),
        }
        self._query_params = self._query_params + tuple(['include_src_dst_groups'])
        self._bool_params = tuple(list(super()._bool_params) + ['include_src_dst_groups'])

    def get_serializer_class(self):
        return DetectionSerializerV3_4


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionCloseV3_4(ApiV3View, DetectionCloseV2_5):
    """
    V3.4 API endpoint for Detection Close
    - API URL: /api/v3.4/detections/<id>/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    def __init__(self):
        super().__init__()


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionsCloseV3_4(ApiV3View, DetectionsCloseV2_5):
    """
    V3.4 API endpoint for Multiple Detections Close
    - API URL: /api/v3.4/detections/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    def __init__(self):
        super().__init__()


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionOpenV3_4(ApiV3View, DetectionOpenV2_5):
    """
    V3.4 API endpoint for Detection Open
    - API URL: /api/v3.4/detection/<id>/open
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    def __init__(self):
        super().__init__()


@view_requires_flag(Flags.signal_efficacy_public_preview)
class HostCloseV3_4(ApiV3View, HostCloseV2_5):
    """
    V3.4 API endpoint for Host Close
    - API URL: /api/v3.4/hosts/<id>/close
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: None
    """

    def __init__(self):
        super().__init__()


@view_requires_flag(Flags.signal_efficacy_public_preview)
class DetectionsOpenV3_4(ApiV3View, DetectionsOpenV2_5):
    """
    V3.4 API endpoint for Detections Open
    - API URL: /api/v3.4/detections/open
    - Permission: triage
    - Methods: PATCH
    - Required Parameters: detectionIdList
    """

    api_version_reason = 'api_v3_4'

    def __init__(self):
        super().__init__()


class DetectionsV3_4(DetectionsV3_3):
    """
    V3.4 API endpoint for Detections
    NOTE: Adds 'include_info_category' query param
    - API URL: /api/v3.4/detections
    - Permission: detection
    - Methods: GET, PATCH
    - Possible Query Parameters: {
        certainty,
        certainty_gte,
        description,
        detection,
        detection_category,
        detection_type,
        entity_id,
        exclude_fields,
        fields,
        host_id,
        id,
        include_info_category
        last_timestamp,
        last_timestamp_gte,
        last_timestamp_lte,
        max_id,
        min_id,
        note_modified_timestamp_gte,
        ordering,
        page,
        page_size,
        src_account_id,
        src_ip,
        state,
        tags,
        threat,
        threat_gte,
        type,
        created_timestamp_gte,
        created_timestamp_lte
        include_src_dst_groups
    }
    """

    serializer_class = DetectionSerializerV3_4

    def __init__(self):
        super().__init__()
        self.serializer_class = DetectionSerializerV3_4
        self.deprecated_query_params = ['t_score', 'c_score', 'targets_key_asset', 't_score_gte', 'c_score_gte', 'category']
        self._query_params = tuple(
            [
                param
                for param in (list(self._query_params) + ['include_info_category', 'reason', 'include_src_dst_groups'])
                if param not in self.deprecated_query_params
            ]
        )
        self._bool_params = tuple(list(super()._bool_params) + ['include_info_category', 'include_src_dst_groups'])
        self._choices_params = {'fields': list(DetectionSerializerV3_4.Meta.fields), 'reason': StateReasons.values()}
        self.ordering_fields = ('created_datetime', 'last_timestamp', 'threat', 'certainty', 'id')
        self.ordering = (
            '-last_timestamp',
            '-threat',
            '-certainty',
        )

    def get_queryset(self):
        queryset = super().get_queryset().annotate(threat=F('t_score'), certainty=F('c_score'))

        include_info_category = self.request.query_params.get('include_info_category')
        category = self.request.query_params.get('detection_category') or self.request.query_params.get('category')

        if include_info_category is None or include_info_category in ('False', 'false', '0'):
            if category is None:
                # Filter INFO detections by default unless explicitly requested
                queryset = queryset.exclude(category='INFO')

        if flag_enabled(Flags.signal_efficacy_closed_as) and flag_enabled(Flags.signal_efficacy_public_preview):
            reason = self.request.query_params.get('reason')
            if reason is not None:
                queryset = queryset.filter(get_detection_status_filters(reason, 'SQL'))

        return queryset.distinct()

    def get_serializer_class(self):
        return DetectionSerializerV3_4


class TaggingV3_4(TaggingV3_3):
    """
    V3.4 API endpoint for Tagging
    - API URL: /api/v3.4/tagging/<object_type>/<entity_id>
    - Permission: tag
    - Methods: GET, PATCH
    - Possible Query Parameters: {
        'type'
    }
    """

    def __init__(self):
        super().__init__()
        self.deprecated_query_params = ['entity_type']
        self._query_params = tuple([param for param in list(self._query_params) if param not in self.deprecated_query_params])

    def get(self, request, object_type, entity_id, *args, **kwargs):
        resp = super().get(request, object_type, entity_id, *args, **kwargs)
        tag_obj = json.loads(resp.content.decode('utf-8'))
        if tag_obj['status'] == 'success':
            tag_obj['entity_id'] = tag_obj.pop('tag_id', entity_id)
        return JsonResponse(tag_obj, status=resp.status_code)

    def patch(self, request, object_type, entity_id, *args, **kwargs):
        resp = super().patch(request, object_type, entity_id, *args, **kwargs)
        tag_obj = json.loads(resp.content.decode('utf-8'))
        if tag_obj['status'] == 'success':
            tag_obj['entity_id'] = tag_obj.pop('tag_id', entity_id)
        return JsonResponse(tag_obj, status=resp.status_code)


class BulkTaggingV3_4(BulkTaggingV3_3):
    """
    V3.4 API endpoint for Bulk Tagging
    - API URL: /api/v3.4/tagging/<object_type>/
    - Permission: tag
    - Methods: POST, DELETE
    - Possible Query Parameters: {
        'type'
    }
    """

    def __init__(self):
        super().__init__()
        self.deprecated_query_params = ['entity_type']
        self._query_params = tuple([param for param in list(self._query_params) if param not in self.deprecated_query_params])


class ExternalConnectorsHealthV3_4(ApiV3View):
    """
    V3.4 API endpoint for
    - API URL: /api/v3.4/health/external_connectors
    - Permission: health
    - Methods: GET
    - Possible Query Parameters: {
        'connector_type',
        'data_type',
        'live'
    }
    """

    permission = 'health'
    allowed_methods = ('GET',)
    _query_params = (
        'connector_type',
        'data_type',
        'live',
    )

    def get(self, request, *args, **kwargs):
        # Get query params
        connector_types = request.query_params.get('connector_type')
        data_types = request.query_params.get('data_type')
        live = request.query_params.get('live')

        self._validate_params()
        if connector_types:
            connector_types = self._get_connector_types(connector_types)
        if data_types:
            data_types = ExternalConnectorsHealthV3_4._get_data_types(request, data_types)

        # if live, run update synchoronously and return results
        result, updated_at = self._get_task_results(live)

        results = {}

        # if no connector_type qp, show all
        if not connector_types:
            connector_types = health_utils.EXTERNAL_CONNECTOR_TYPES
        if not data_types:
            data_types = health_utils.DATA_TYPES
        for conn in connector_types:
            results[conn] = HealthGenericIntegrationSerializerV3_4(**result.get(conn)).model_dump(
                mode='json', exclude_defaults=True, include=data_types
            )

        response = {'results': results, 'updated_at': updated_at}
        return JsonResponse(response)

    def _validate_params(self):
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params, request_id=self.request.headers.get('X-Amzn-Requestid'))

    def _get_connector_types(self, connector_types):
        connector_types = connector_types.replace(' ', '').split(",")
        bad_connector_types = [conn for conn in connector_types if conn not in health_utils.EXTERNAL_CONNECTOR_TYPES]
        if bad_connector_types:
            raise InvalidQueryParamValues(
                {
                    'connector_type': f'Following type(s) are invalid: {bad_connector_types}. Please use any of the following connector types: '
                    + str(health_utils.EXTERNAL_CONNECTOR_TYPES)
                },
                request_id=self.request.headers.get('X-Amzn-Requestid'),
            )
        return connector_types

    @staticmethod
    def _get_data_types(request, data_types):
        data_types = data_types.replace(' ', '').split(",")
        bad_data_types = [dt for dt in data_types if dt not in health_utils.DATA_TYPES]
        if bad_data_types:
            raise InvalidQueryParamValues(
                {
                    'data_type': f'Following type(s) are invalid: {bad_data_types}. Please use any of the following data types: '
                    + str(health_utils.DATA_TYPES)
                },
                request_id=request.headers.get('X-Amzn-Requestid'),
            )
        return data_types

    def _get_task_results(self, live):
        if live and live in ('True', 'true', '1', 'TRUE'):
            try:
                task = refresh_external_connector_fields.apply_async()
                result = task.get(timeout=25.0)
                updated_at = timezone.now()
            except TimeoutError:
                raise HealthTaskTimeout(task)
            except Exception as e:
                raise UnexpectedException(e)
        else:
            task = (
                TaskResult.objects.filter(task_name__contains="refresh_external_connector_fields", status='SUCCESS')
                .order_by('-date_created')
                .first()
            )
            if not task:
                raise CouldNotGetTaskResult('refresh_external_connector_fields')
            result = json.loads(task.result)
            updated_at = task.date_done
        return result, updated_at


class ExternalConnectorsHealthDetailsV3_4(ExternalConnectorsHealthV3_4):
    """
    V3.4 API endpoint for
    - API URL: /api/v3.4/health/external_connectors/details
    - Permission: health
    - Methods: GET
    - Possible Query Parameters: {
        'connector_type',
        'live'
    }
    """

    permission = 'health'
    allowed_methods = ('GET',)
    _query_params = (
        'connector_type',
        'live',
    )

    def get(self, request, *args, **kwargs):
        # Get query params
        connector_types = request.query_params.get('connector_type')
        live = request.query_params.get('live')

        self._validate_params()
        if connector_types:
            connector_types = self._get_connector_types(connector_types)

        # if live, run update synchoronously and return results
        result, updated_at = self._get_task_results(live)

        results = {}

        EXTERNAL_CONNECTOR_SERIALIZER_MAP = {
            health_utils.TYPE_ACTIVE_DIRECTORY: ActiveDirectoryHealthDetailsSerializerV3_4,
            health_utils.TYPE_AZURE_AD_LOCKDOWN: AzureAdLockdownHealthDetailsSerializerV3_4,
            health_utils.TYPE_AWS: AWSHealthDetailsSerializerV3_4,
            health_utils.TYPE_AZURE: AzureHealthDetailsSerializerV3_4,
            health_utils.TYPE_GOOGLE_CLOUD: GCPHealthDetailsSerializerV3_4,
            health_utils.TYPE_RDNS: DNSLookupHealthDetailsSerializerV3_4,
            health_utils.TYPE_SIEM: SiemHealthDetailsSerializerV3_4,
            health_utils.TYPE_VCENTER: VcenterHealthDetailsSerializerV3_4,
            health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION: WindowsEventLogHealthDetailsSerializerV3_4,
            health_utils.TYPE_ZPA: ZPAHealthDetailsSerializerV3_4,
        }

        # if no connector_type qp, show all
        if not connector_types:
            connector_types = health_utils.EXTERNAL_CONNECTOR_TYPES
        for conn in connector_types:
            results[conn] = EXTERNAL_CONNECTOR_SERIALIZER_MAP[conn](**result.get(conn)).model_dump(
                mode='json',
                exclude_defaults=True,
                exclude={health_utils.AUTH_STATUS, health_utils.CONNECTOR_TYPE, health_utils.ERROR_STATES, health_utils.LOCKDOWN_STATUS},
            )

        response = {'results': results, 'updated_at': updated_at}
        return JsonResponse(response)


class EDRHealthV3_4(ApiV3View):
    """
    V3.4 API endpoint for
    - API URL: /api/v3.4/health/edr
    - Permission: health
    - Methods: GET
    - Possible Query Parameters: {
        'edr_type',
        'data_type',
        'live'
    }
    """

    permission = 'health'
    allowed_methods = ('GET',)
    _query_params = (
        'edr_type',
        'data_type',
        'live',
    )

    def get(self, request, *args, **kwargs):
        # Get query params
        edr_types = request.query_params.get('edr_type')
        data_types = request.query_params.get('data_type')
        live = request.query_params.get('live')

        self._validate_params()
        if edr_types:
            edr_types = self._get_edr_types(edr_types)
        if data_types:
            data_types = ExternalConnectorsHealthV3_4._get_data_types(request, data_types)

        # if live, run update synchronously and return results
        result, updated_at = self._get_task_results(live)

        results = {}

        # if no edr_type param, show all
        if not edr_types:
            edr_types = health_utils.EDR_TYPES
        if not data_types:
            data_types = health_utils.DATA_TYPES
        for conn in edr_types:
            results[conn] = HealthGenericIntegrationSerializerV3_4(**result.get(conn)).model_dump(
                mode='json', exclude_defaults=True, include=data_types
            )

        response = {'results': results, 'updated_at': updated_at}
        return JsonResponse(response)

    def _validate_params(self):
        bad_query_params = [param for param in self.request.query_params.keys() if param not in self._query_params]
        if bad_query_params:
            raise InvalidQueryParams(bad_query_params, request_id=self.request.headers.get('X-Amzn-Requestid'))

    def _get_edr_types(self, edr_types):
        edr_types = edr_types.replace(' ', '').split(",")
        bad_edr_types = [conn for conn in edr_types if conn not in health_utils.EDR_TYPES]
        if bad_edr_types:
            raise InvalidQueryParamValues(
                {
                    'edr_type': f'Following type(s) are invalid: {bad_edr_types}. Please use any of the following EDR types: '
                    + str(health_utils.EDR_TYPES)
                },
                request_id=self.request.headers.get('X-Amzn-Requestid'),
            )
        return edr_types

    def _get_task_results(self, live):
        if live and live in ('True', 'true', '1', 'TRUE'):
            try:
                task = refresh_edr_health_fields.apply_async()
                result = task.get(timeout=25.0)
                updated_at = timezone.now()
            except TimeoutError:
                raise HealthTaskTimeout(task)
            except Exception as e:
                raise UnexpectedException(e)
        else:
            task = (
                TaskResult.objects.filter(task_name__contains="refresh_edr_health_fields", status='SUCCESS')
                .order_by('-date_created')
                .first()
            )
            if not task:
                raise CouldNotGetTaskResult('refresh_edr_health_fields')
            result = json.loads(task.result)
            updated_at = task.date_done
        return result, updated_at


class EDRHealthDetailsV3_4(EDRHealthV3_4):
    """
    V3.4 API endpoint for
    - API URL: /api/v3.4/health/edr/details
    - Permission:
    - Methods: GET
    - Possible Query Parameters: {
        'edr_type',
        'live'
    }
    """

    permission = 'health'
    allowed_methods = ('GET',)

    _query_params = (
        'edr_type',
        'live',
    )

    def get(self, request, *args, **kwargs):
        # Get query params
        edr_types = request.query_params.get('edr_type')
        live = request.query_params.get('live')

        self._validate_params()
        if edr_types:
            edr_types = self._get_edr_types(edr_types)

        # if live, run update synchoronously and return results
        result, updated_at = self._get_task_results(live)

        results = {}

        EDR_SERIALIZER_MAP = {
            health_utils.TYPE_HOST_LOCKDOWN: HostLockdownSerializerV3_4,
            health_utils.TYPE_WINDOWS_DEFENDER: WindowsDefenderSerializerV3_4,
            health_utils.TYPE_CARBON_BLACK: CarbonBlackSerializerV3_4,
            health_utils.TYPE_CARBON_BLACK_CLOUD: CarbonBlackCloudSerializerV3_4,
            health_utils.TYPE_FIREEYE: FireEyeEndpointSecuritySerializerV3_4,
            health_utils.TYPE_SENTINELONE: SentinelOneSerializerV3_4,
            health_utils.TYPE_CYBEREASON: CybereasonSerializerV3_4,
            health_utils.TYPE_CROWDSTRIKE: CrowdStrikeSerializerV3_4,
        }

        # if no connector_type qp, show all
        if not edr_types:
            edr_types = tuple(EDR_SERIALIZER_MAP.keys())
        for edr in edr_types:
            results[edr] = EDR_SERIALIZER_MAP[edr](**result.get(edr)).model_dump(
                mode='json',
                exclude_defaults=True,
                exclude={health_utils.AUTH_STATUS, health_utils.CONNECTOR_TYPE, health_utils.ERROR_STATES, health_utils.LOCKDOWN_STATUS},
            )

        response = {'results': results, 'updated_at': updated_at}
        return JsonResponse(response)


class NetworkBrainHealthPingV3_4(HealthV3_3):
    """
    V3.4 API endpoint for
    - API URL: /api/v3.4/health/network_brain/ping
    - Permission: health
    - Methods: GET
    - Possible Query Parameters: None
    """

    permission = 'health'
    allowed_methods = ('GET',)

    def get(self, request, *args, **kwargs):
        if resp := self._cloudbridge_check():
            return resp
        checks_to_do = ['system']

        use_cache = 'false'

        try:
            health_check_result, call_latency = self.get_health_check(check=checks_to_do[0], use_cache=use_cache)
            if health_check_result.get('uptime', False):
                status = 'Brain enabled'
            else:
                status = 'Brain connection failed'
            response = {'results': {'ping': status, 'latency': call_latency}}
            return Response(response)
        except Exception as err:
            LOG.exception(f"Encountered an error running health check: {err}")
            return Response({'error': 'Encountered an error running health check'}, status=500)

    def get_health_check(self, check, use_cache=False):
        time_before = datetime.now()
        resp = make_get_request(urljoin(self.PLATFORM_URL, check), timeout=60)
        time_after = datetime.now()
        resp.raise_for_status()

        result = resp.json().get(check)
        latency = time_after - time_before
        latency_ms = "{:.2f}ms".format(latency.total_seconds() * 1000)
        return result, latency_ms


class UniqueHostCountMonthlyV3_4(UniqueHostCountMonthlyV3_3):
    """
    DEPRECATED (VLCN-401):
    Endpoint replaced by the v3.4 unique host audit timespan implmentation.
    """

    def __init__(self):
        super().__init__()


class UniqueHostAuditMonthlyV3_4(UniqueHostAuditMonthlyV3_3):
    """
    DEPRECATED (VLCN-401):
    Endpoint replaced by the v3.4 unique host audit timespan implmentation.
    """

    def __init__(self):
        super().__init__()


@view_requires_flag(Flags.v3_4_unique_hosts_timespan)
class UniqueHostCountTimespanV3_4(ApiV3View, ListAPIView, QueryParameterValueValidationMixin, CloudbridgeRouter):
    """
    V3.4 API endpoint for listing counts of unique hosts observed.
    - API URL: /api/v3.4/unique_hosts_observed_timespan
    - Permission: view_unique_host_count
    - Methods: GET
    - Required Parameters: None
    - Possible Query Params: {
        start
        end
        offset
    }
    """

    permission = 'unique_host_metric'
    allowed_methods = ('GET', 'OPTIONS')
    renderer_classes = (JSONRenderer,)

    _query_params = ('start', 'end', 'offset')
    _timestamp_params = ('start', 'end')
    _tz_offset_params = ('offset',)
    _bool_params = ()

    def get(self, request, *args, **kwargs):
        if non_cb := self._cloudbridge_check():
            return non_cb

        try:
            self._validate_query_params(self.request.query_params.keys(), self._query_params)
        except Exception as e:
            LOG.exception(e, extra={"AWS Request ID": self.request.headers.get("X-Amzn-Requestid")})
            raise e

        start = parse_time_string(self.request.query_params.get('start'))
        end = parse_time_string(self.request.query_params.get('end'))
        offset = parse_tz_offset(self.request.query_params.get('offset'))

        if (start and end) and start > end:
            return JsonResponse({"message": f"start must be before end. start: {start}, end: {end}"}, status=400)
        try:
            start, end = self._get_start_and_end_date(start, end, offset)
            response = self.get_queryset(start, end)
        except Exception as e:
            LOG.error(f'ERROR: {e}')
            return JsonResponse({'ERROR': f'{e}'}, status=500)

        if not response:
            return JsonResponse({"error": "Invalid timerange provided"}, status=400)
        if response['unique_hosts_observed'] == 0:
            error_message = f'No host data available for given timerange. Start: {start.date()}, End: {end.date()}'
            LOG.info(error_message)
            return JsonResponse({'error': error_message}, status=409)

        return JsonResponse(response, safe=False)

    def _get_start_and_end_date(self, start, end, offset):

        hours = 0  # if offset is zero, utc
        if offset:
            hours = offset.utcoffset().total_seconds() / 3600

        now = datetime.now().replace(tzinfo=timezone.timezone(timedelta(hours=hours)))

        # Start date should be the 1st day of the year prior, by default
        start_date = (now).replace(day=1, hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.timezone(timedelta(hours=hours)))
        # End date should be current date (so we reflect the current month count is in progress)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.timezone(timedelta(hours=hours)))

        if start is not None:
            start_date = start.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.timezone(timedelta(hours=hours)))
        if end is not None:
            end_date = end.replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.timezone(timedelta(hours=hours)))

        return start_date, end_date

    def get_queryset(self, start_date, end_date):

        # Count source of truth switched from HostAudit to HostAuditSnapshot in October 2024
        start_of_october_2024 = date(2024, 10, 1)
        if start_date.date() < start_of_october_2024 and end_date.date() >= start_of_october_2024:
            return None

        return self.compute_count(start_date, end_date)

    def compute_count(self, start, end):
        """
        Compute counts of unique hosts between provided start & end dates.
        """
        # Compute current count based on input
        # Include sessions that started in or before range, and are still going
        hs_filter = Q(start__lt=end) & (Q(end__gt=start) | Q(end__isnull=True))

        start_of_october_2024 = date(2024, 10, 1)
        if start.date() < start_of_october_2024:
            queryset = HostAudit.objects.filter(hs_filter)
            distinct_columns = queryset.values("host_luid", "ip_address").distinct()
            count = distinct_columns.count()
        else:
            queryset = HostAuditSnapshot.objects.filter(day__lte=end.date()).filter(day__gte=start.date())
            count = max([daily_snapshot.actual_count for daily_snapshot in queryset]) if queryset else 0

        # Create response record for month
        count_meta = {
            'start_date': start.strftime("%Y-%m-%d"),
            'end_date': end.strftime("%Y-%m-%d"),
            'unique_hosts_observed': int(count),
        }

        return count_meta


@view_requires_flag(Flags.v3_4_unique_hosts_timespan)
class UniqueHostAuditTimespanV3_4(ApiV3View, ListAPIView, QueryParameterValueValidationMixin, CloudbridgeRouter):
    """
    V3.3 API endpoint for listing unique hosts observed.
    - API URL: /api/v3.4/unique_hosts_observed_timespan/audit
    - Permission: unique_host_metric
    - Methods: GET
    - Required Parameters: None
    - Possible Query Params: {
        start
        end
        offset
        page
        page_size
    }
    """

    permission = 'unique_host_metric'
    allowed_methods = ('GET', 'OPTIONS')
    renderer_classes = (JSONRenderer,)

    _query_params = (
        'start',
        'end',
        'offset',
        StandardResultSetPagination.page_query_param,
        StandardResultSetPagination.page_size_query_param,
    )
    serializer_class = UniqueHostAuditSerializerV3_4
    pagination_class = StandardResultSetPagination

    _str_params = {}
    _tz_offset_params = ('offset',)

    def list(self, request, *args, **kwargs):
        if non_cb := self._cloudbridge_check():
            return non_cb

        # Query param validation
        try:
            self._validate_query_params(self.request.query_params.keys(), self._query_params)
        except Exception as e:
            LOG.exception(e, extra={"AWS Request ID": self.request.headers.get("X-Amzn-Requestid")})
            raise e

        try:
            start, end = (request.query_params.get(param) for param in ('start', 'end'))
            start, end = parse_time_string(start), parse_time_string(end)
            offset = parse_tz_offset(self.request.query_params.get('offset'))

            if (start and end) and start > end:
                return Response({"message": f"start must be before end. start: {start}, end: {end}"}, status=400)

            start_date, end_date = self._get_start_and_end_date(start, end, offset)

            start_of_october_2024 = date(2024, 10, 1)
            if start_date.date() < start_of_october_2024 and end_date.date() >= start_of_october_2024:
                return JsonResponse({"error": f"Invalid timerange provided"}, status=400)

            if start_date.date() < start_of_october_2024 and end_date.date() < start_of_october_2024:
                # Continue with pre-existing behavior
                return super().list(request, *args, **kwargs)

            try:
                # Audit source of truth switched from HostAudit to HostAuditSnapshot in October 2024
                snapshot = (
                    HostAuditSnapshot.objects.filter(day__lte=end_date.date())
                    .filter(day__gte=start_date.date())
                    .order_by('-actual_count')
                    .first()
                )
                if not snapshot:
                    error_message = f'No host data available for given timerange. Start: {start_date.date()}, End: {end_date.date()}'
                    LOG.info(error_message)
                    return JsonResponse({'error': error_message}, status=409)
                # Decompress data if necessary (data can be uncompressed)
                if snapshot.compressed_audit_data:
                    try:
                        audit_data = decompress_list(snapshot.compressed_audit_data, snapshot.compression_type)
                    except ListDecompressionError as e:
                        LOG.error(f'{e}')
                        return JsonResponse({'ERROR': f'{e}'}, status=500)
                else:
                    audit_data = snapshot.audit_data
                # Paginate results
                paginator = self.pagination_class()
                paginated_data = paginator.paginate_list(audit_data, request)
                return paginator.get_paginated_response(paginated_data)
            except Exception as e:
                LOG.error(f'ERROR: {e}')
                return JsonResponse({'ERROR': f'{e}'}, status=500)

        except Exception as e:
            LOG.error(f'ERROR: {e}')
            return JsonResponse({'ERROR': f'{e}'}, status=500)

    def get_queryset(self):
        start = parse_time_string(self.request.query_params.get('start'))
        end = parse_time_string(self.request.query_params.get('end'))
        offset = parse_tz_offset(self.request.query_params.get('offset'))

        # TODO: repeat logic to clean up later
        start_date, end_date = self._get_start_and_end_date(start, end, offset)

        # Include sessions that started in or before range, and are still going
        hs_filter = Q(start__lt=end_date) & (Q(end__gt=start_date) | Q(end__isnull=True))

        queryset = HostAudit.objects.filter(hs_filter)

        # Deduplicate the queryset (MySQL does not support distinct on fields)
        seen, duplicate_ids = set(), []
        for obj in queryset:
            host_luid = obj.host_luid if obj.host_luid else 'NULL'
            ip_address = obj.ip_address if obj.ip_address else 'NULL'
            start = str(obj.start) if obj.start else 'NULL'
            unique_key = host_luid + ip_address + start
            if unique_key in seen:
                duplicate_ids.append(obj.id)
            else:
                seen.add(unique_key)

        queryset = queryset.exclude(id__in=duplicate_ids)
        return queryset

    def _get_start_and_end_date(self, start, end, offset):
        hours = 0  # if offset is zero, utc
        if offset:
            hours = offset.utcoffset().total_seconds() / 3600

        now = datetime.now().replace(tzinfo=timezone.timezone(timedelta(hours=hours)))

        # Start date should be the 1st day of the month prior, by default
        start_date = (now).replace(day=1, hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.timezone(timedelta(hours=hours)))
        # End date should be current date (so we reflect the current month count is in progress)
        end_date = now.replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.timezone(timedelta(hours=hours)))

        if start is not None:
            start_date = start.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.timezone(timedelta(hours=hours)))
        if end is not None:
            end_date = end.replace(hour=23, minute=59, second=59, microsecond=999999, tzinfo=timezone.timezone(timedelta(hours=hours)))

        return start_date, end_date


class HealthV3_4(ApiV3View, CloudbridgeRouter, HealthV2_5):
    """
    V3.4 API endpoint for System Health.
    - API URL: /api/v3.4/health or /api/v3.4/health/<check_type>
    - Permission: health
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'cache'
        'vlans'
    }
    """

    permission = 'health'

    def get(self, request, *args, **kwargs):
        return self._cloudbridge_check() or super().get(request, *args, **kwargs)

    def _get_vlan_information(self):
        try:
            return self.get_v2_vlan_information()
        except timeout_decorator.TimeoutError:
            return {'vlan_ids': None}

    @timeout_decorator.timeout(10)
    def get_v2_vlan_information(self):
        return super()._get_vlan_information()


class GroupsV3_4(ApiV3View, GroupsV2_5):
    """
    V3.4 API endpoint for Groups.
    - API URL: /api/v3.4/groups
    - Permission: groups
    - Methods: GET, POST
    - Required Parameters: None
    - Possible Query Parameters: {
        'account_names'
        'domains'
        'host_ids'
        'host_names'
        'importance'
        'ips'
        'description'
        'last_modified_timestamp'
        'last_modified_by'
        'name'
        'type'
        'page'
        'page_size'
        'is_regex'
        'is_membership_evaluation_ongoing'
        'include_members'
        'is_ad_group'
        'ad_group_dn
    }
    """

    def __init__(self):
        super().__init__()

    def _validate_keys(self, group_payload):
        invalid_keys = []
        for key in group_payload.keys():
            if key not in ['name', 'type', 'members', 'importance', 'description', 'regex']:
                invalid_keys.append(key)
        if len(invalid_keys) > 0:
            raise InvalidRequestField(invalid_keys)

    def get_serializer_class(self):
        if flag_enabled(Flags.dynamic_groups):
            return GroupSerializerV3_4
        else:
            return super().serializer_class


class GroupV3_4(ApiV3View, GroupV2_5):
    """
    View for v3.4 of the /api/v3.4/groups/<id> endpoint.
    - Permission: groups
    - Methods: GET, PATCH, DELETE
    """

    def __init__(self):
        super().__init__()
        self.serializer_class = GroupSerializerV3_4
        self._query_params = 'include_members'
        self._bool_params = 'include_members'

    def get_serializer_class(self):
        return GroupSerializerV3_4

    def _validate_keys(self, group_payload):
        invalid_keys = []
        for key in group_payload.keys():
            if key not in ['name', 'type', 'members', 'importance', 'description', 'regex']:
                invalid_keys.append(key)
        if len(invalid_keys) > 0:
            raise InvalidRequestField(invalid_keys)

    def get(self, request, pk, *args, **kwargs):
        group_obj = self.get_object()
        if group_obj.type == GroupCollection.ACCOUNT:
            serializer = self.get_serializer(group_obj)
            return Response(serializer.data)
        else:
            return super().get(request, pk)


class GroupMembersV3_4(ApiV3View, GroupMembersV2_5):
    """
    V3.4 API endpoint for groups/<id>/members.
    - API URL: /api/v3.4/groups/<id>/members
    - Permission: groups
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'page'
        'page_size'
        'ordering' (for hosts)
        'name'
        'is_key_asset' (for hosts)
        'uid'
    }

    Note that uid and name will both filter on uid and name for hosts and accounts respectively.

    Ordering is only available for hosts.
        - name
        - id
    """


class NotificationReceiversV3_4(NotificationReceiversBaseView, APIV3ListView):
    pass


class ActiveDirectoryGroupsV3_4(ApiV3View, ActiveDirectoryGroupsV2_5):
    """
    V3.4 API endpoint for Active Directory Groups.
    - API URL: /api/v3.4/settings/settings/active_directory/groups/
    - Permission: groups
    - Methods: GET
    - Required Parameters: None
    """

    pass

class CloudDataSourcesV3_4(APIV3ListView, ApiV3View):
    """
    V3.4 API endpoint for Cloud Data Sources.
    - API URL: /api/v3.4/data-sources
    - Permission: data_sources
    - Methods: GET
    - Required Parameters: None
    - Possible Query Parameters: {
        'page',
        'page_size'
    }

    Returns all active cloud connectors (CloudSensor) using Option 3 schema format.
    Schema includes high-level connector fields with nested properties dictionary.
    Future-proofed for Nexus integration. Excludes deleted connectors.
    """

    permission = 'data_sources'
    serializer_class = CloudDataSourceSerializerV3_4
    allowed_methods = ('GET',)
    renderer_classes = (JSONRenderer,)
    pagination_class = StandardResultSetPagination

    def get_queryset(self):
        """
        Retrieve all active cloud connectors from CloudSensor.
        Filters out deleted connectors and annotates with health status.
        """
        try:
            all_sensors = CloudSensorManagement.get_all_sensors()

            active_sensors = []
            for sensor in all_sensors:
                if sensor.get('status') != 'DELETED':
                    try:
                        annotate_sensor_status(sensor)
                    except Exception:
                        # Continue without status annotation if it fails
                        pass
                    active_sensors.append(sensor)

            return active_sensors

        except Exception as exc:
            LOG.exception("Error retrieving cloud data sources: %s", exc)
            raise ValidationError("Unable to retrieve cloud data sources") from exc
