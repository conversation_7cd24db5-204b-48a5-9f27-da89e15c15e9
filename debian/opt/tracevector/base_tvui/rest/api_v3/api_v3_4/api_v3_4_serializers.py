# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
import json
from django.urls import reverse
from urllib.parse import urlencode
from rest_framework import serializers

from base_tvui.feature_flipper import flag_enabled, Flags
from base_tvui.lib_tv import get_absolute_url
from base_tvui.rest.api_v3.api_v3_3.api_v3_3_serializers import (
    AccountSerializerV3_3,
    UserSerializerV3_3,
    DetectionSerializerV3_3,
    EntityScoringEventsSerializerV3_3,
    EntitySerializerV3_3,
    DetectionEventSerializerV3_3,
    LockdownSerializerV3_3,
    HostSerializerV3_3,
    GroupSerializerV3_3,
)
from base_tvui.rest.serializers import DynamicFieldsModelSerializer
from base_tvui.rest.api_v3 import api_v3_utils
from tvui.models import User, VUIGroup, HostAudit, GroupCollection, detection, CloudSensor
from tvui.group_collections.account_groups.account_group_api_processor import AccountGroupAPIProcessor
from tvui.group_collections.config import API_MEMBER_LIMIT
from tvui.group_collections.group_ui_processors import AccountGroupUIProcessor, HostGroupUIProcessor
from tvui.group_collections.host_groups.host_group_api_processor import HostGroupAPIProcessor
from tvui.group_collections.ip_groups.ip_group_api_processor import IPGroupAPIProcessor
from tvui.group_collections.external_domain_groups.external_domain_group_api_processor import ExternalDomainGroupAPIProcessor
from tvui.detections.detection_pivots import build_pivot_query

from pure_utils import log_utils
from pydantic import BaseModel, conlist, conint, RootModel, ConfigDict, Field, root_validator
from typing import List, Literal, Dict, Optional
from datetime import datetime

LOG = log_utils.get_vectra_logger(__name__)


class AccountSerializerV3_4(AccountSerializerV3_3):
    detection_set = serializers.SerializerMethodField()
    url = serializers.SerializerMethodField()

    class Meta(AccountSerializerV3_3.Meta):
        fields = list(set(AccountSerializerV3_3.Meta.fields) - {'subaccounts'})

    def get_url(self, account_obj):
        return api_v3_utils.build_url(self.context['request'], 'accounts', account_obj.id)

    def get_detection_set(self, account_obj):
        detection_ids = list(account_obj.detection_set.values_list('id', flat=True))
        return api_v3_utils.build_url(self.context['request'], 'detections', detection_ids)


class EntityScoringEventsSerializerV3_4(EntityScoringEventsSerializerV3_3):
    def __init__(self, *args, **kwargs):
        super(EntityScoringEventsSerializerV3_4, self).__init__(*args, **kwargs)
        excluded_fields = {'entity_importance', 'entity_type', 'last_detection_id', 'last_detection_type', 'last_detection_url'}
        for field in excluded_fields:
            self.fields.pop(field)


class EntitySerializerV3_4(EntitySerializerV3_3):
    def __init__(self, *args, **kwargs):
        super(EntitySerializerV3_4, self).__init__(*args, **kwargs)
        excluded_fields = {
            'entity_importance',
            'entity_type',
        }
        for field in excluded_fields:
            self.fields.pop(field, None)


class DetectionEventSerializerV3_4(DetectionEventSerializerV3_3):
    class Meta(DetectionEventSerializerV3_3.Meta):
        fields = list(set(DetectionEventSerializerV3_3.Meta.fields) - {'entity_type'})


class LockdownSerializerV3_4(LockdownSerializerV3_3):
    def __init__(self, *args, **kwargs):
        super(LockdownSerializerV3_4, self).__init__(*args, **kwargs)
        excluded_fields = {
            'entity_type',
        }
        for field in excluded_fields:
            self.fields.pop(field)


class DetectionSerializerV3_4(DetectionSerializerV3_3):

    reason = serializers.SerializerMethodField()

    class Meta(DetectionSerializerV3_3.Meta):
        fields = list(set(DetectionSerializerV3_3.Meta.fields) - {'t_score', 'c_score', 'targets_key_asset', 'category'}) + ['reason']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if flag_enabled(Flags.detection_investigation_pivot):
            self.fields['investigation_pivot_link'] = serializers.SerializerMethodField(method_name='get_pivot_link')

        request = self.context.get('request', None)
        if request and request.query_params.get('include_src_dst_groups', 'False') in ('True', 'true', '1'):
            # If include_src_dst_groups is True, we need to include src and dst groups
            self.fields['src_groups'] = serializers.SerializerMethodField()
            self.fields['dst_groups'] = serializers.SerializerMethodField()

    def get_src_groups(self, det_obj):
        """
        Get the source groups for the detection object.
        """
        src_groups = list(det_obj.host.hostgroup_set.all() if det_obj.host else [])
        src_groups.extend(list(det_obj.account.accountgroup_set.all()) if det_obj.account else [])
        src_groups.extend(det_obj.src_ip_groups)
        return [
            {
                'id': grp.id,
                'name': grp.name,
                'description': grp.description,
                'type': grp.type,
                'last_modified': grp.last_modified_timestamp,
                'last_modified_by': grp.last_modified_by.username,
            }
            for grp in src_groups
        ]

    def get_dst_groups(self, det_obj):
        """
        Get the destination groups for the detection object.
        """
        dst_groups: List[GroupCollection] = detection.external_domain_groups_injectable(
            detection_domains=set(detail.dst_dns for detail in det_obj.details.all() if detail.dst_dns),
            external_domain_groups=self.context['domain_groups'],
        )
        dst_groups.extend(det_obj.dst_ip_groups)

        return [
            {
                'id': grp.id,
                'name': grp.name,
                'description': grp.description,
                'type': grp.type,
                'last_modified': grp.last_modified_timestamp,
                'last_modified_by': grp.last_modified_by.username,
            }
            for grp in dst_groups
        ]

    def matching_groups(self, det_obj):
        # Using GroupCollection type hint so that only shared attributes between ip and domain groups resolve.
        # NOTE: `det_obj.details.all()` is prefetched in our detections list views, so if you ever need to apply
        # any additional filtering/sorting to the details, make sure to update that prefetch.
        groups: List[GroupCollection] = detection.external_domain_groups_injectable(
            detection_domains=set(detail.dst_dns for detail in det_obj.details.all() if detail.dst_dns),
            external_domain_groups=self.context['domain_groups'],
        )
        groups.extend(self.fetch_ip_groups(det_obj))
        if det_obj.host:
            # If the detection is associated with a host, add its host groups
            groups.extend(det_obj.host.hostgroup_set.all())
        if det_obj.account:
            # If the detection is associated with an account, add its account groups
            groups.extend(det_obj.account.accountgroup_set.all())
        return [
            {
                'id': grp.id,
                'name': grp.name,
                'description': grp.description,
                'type': grp.type,
                'last_modified': grp.last_modified_timestamp,
                'last_modified_by': grp.last_modified_by.username,
            }
            for grp in groups
        ]

    def get_pivot_link(self, detection_obj):
        page = reverse('advanced_investigations')
        url = get_absolute_url(page)
        params = build_pivot_query(detection_obj)
        if params:
            for field, value in params.items():
                if isinstance(value, list) or isinstance(value, dict):
                    params[field] = json.dumps(value)
            encoded_params = urlencode(params)
            return f'{url}?{encoded_params}'
        else:
            return None

    def get_reason(self, det_obj):
        if flag_enabled(Flags.signal_efficacy_closed_as) and flag_enabled(Flags.signal_efficacy_public_preview):
            return det_obj.get_signal_efficacy_status()
        return None


class UserSerializerV3_4(UserSerializerV3_3):

    name = serializers.ReadOnlyField(source='full_name')
    email = serializers.ReadOnlyField(source='username')
    verified = serializers.SerializerMethodField(method_name='get_verified')
    identities = serializers.SerializerMethodField(method_name='get_identities')

    class Meta(UserSerializerV3_3.Meta):
        fields = list(set(UserSerializerV3_3.Meta.fields) - {'username', 'last_login'}) + ['name', 'verified', 'identities']

    def get_verified(self, user_obj):
        if user_obj.first_login:
            return True
        return False

    def get_role(self, user_obj):
        if group := user_obj.groups.first():
            return group.name

    def get_identities(self, user_obj):
        """Return list of local and saml profiles corresponding to the user"""
        identities = []
        if user_obj.saas_local_profile:
            identities.append({'type': 'LOCAL'})
        if saml_profiles := user_obj.saas_saml_profiles.get_queryset().order_by('id'):
            for profile in saml_profiles:
                identities.append({'type': 'SAML', 'saml_profile_id': profile.id})
        return identities


class UserRolesSerializerV3_4(DynamicFieldsModelSerializer):

    name = serializers.ReadOnlyField(source='group_extend.vname')
    standardized_name = serializers.ReadOnlyField(source='name')

    class Meta:
        model = VUIGroup
        fields = ('id', 'name', 'standardized_name')


class AdServerModel(BaseModel):
    status: Optional[str] = Field(None)  # Example constraint: non-empty string
    severity: Optional[str] = Field(None)  # Example constraint: non-empty string

    class Config:
        extra = "allow"


class ActiveDirectoryAdServerModel(RootModel):
    root: Optional[Dict[str, AdServerModel]] = Field(None)


class ActiveDirectoryMetricsModel(BaseModel):
    total_accounts_kerberos_last_30_days: Optional[conint(ge=0)] = Field(None)  # Must be a non-negative integer
    current_kerberos_ldap_overlap: Optional[conint(ge=0)] = Field(None)
    total_ad_accounts_found: Optional[conint(ge=0)] = Field(None)

    class Config:
        extra = "allow"


class ActiveDirectoryHealthDetailsSerializerV3_4(BaseModel):
    ad_servers: Optional[conlist(item_type=ActiveDirectoryAdServerModel)] = Field(None)
    metrics: Optional[ActiveDirectoryMetricsModel] = Field(None)
    account_lockdown: Optional[Literal['enabled', 'disabled']] = Field(None)

    class Config:
        extra = "allow"


class AzureAdLockdownHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class AWSHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class AzureHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class GCPHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class DNSLookupHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class SiemHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class VcenterHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class WindowsEventLogHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class ZPAHealthDetailsSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class HealthGenericIntegrationSerializerV3_4(BaseModel):
    connection_status: Literal['enabled', 'disabled', 'unknown']
    auth_status: Optional[dict] = Field(None)
    error_states: Optional[dict] = Field(None)
    lockdown_status: Optional[str] = Field(None)

    @root_validator(pre=True)
    @classmethod
    def conditional_nullification(cls, values):
        if values.get("connection_status") == "disabled":
            for field in cls.__fields__.keys():
                if field not in ("connection_status"):
                    values[field] = None
        return values

    class Config:
        extra = "ignore"


class HostLockdownSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class WindowsDefenderSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class CarbonBlackSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class CarbonBlackCloudSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class FireEyeEndpointSecuritySerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class SentinelOneSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class CybereasonSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class CrowdStrikeSerializerV3_4(BaseModel):
    model_config = ConfigDict(extra='allow')


class UniqueHostAuditSerializerV3_4(HostSerializerV3_3):
    """
    Serializer for V3.4 Unique Host Audit
    """

    last_seen_start = serializers.ReadOnlyField(source='start')
    last_seen_end = serializers.ReadOnlyField(source='end')

    class Meta:
        model = HostAudit
        fields = (
            'host_id',
            'host_luid',
            'ip_address',
            'host_artifact_value',
            'host_artifact_type',
            'last_seen_start',
            'last_seen_end',
        )


class GroupSerializerV3_4(GroupSerializerV3_3):
    """Serializer for Groups V3.4"""

    regex = serializers.SerializerMethodField()
    membership_evaluation_ongoing = serializers.SerializerMethodField()
    members = serializers.SerializerMethodField(method_name='_get_members')
    member_count = serializers.SerializerMethodField(method_name='_get_member_count')
    built_using = serializers.SerializerMethodField(method_name='_get_built_using')
    rules = serializers.SerializerMethodField(method_name='_get_rules')
    members_truncated = serializers.SerializerMethodField()
    ad_group_dn = serializers.ReadOnlyField()

    class Meta(GroupSerializerV3_3.Meta):
        fields = list(GroupSerializerV3_3.Meta.fields) + [
            'regex',
            'membership_evaluation_ongoing',
            'member_count',
            'built_using',
            'members_truncated',
            'ad_group_dn',
        ]

    def get_regex(self, group_obj):
        try:
            regex = None
            if group_obj.rule:
                if group_obj.type == GroupCollection.HOST:
                    regex = HostGroupUIProcessor(group_obj)._get_dynamic_rule_str(group_obj.rule)
                elif group_obj.type == GroupCollection.ACCOUNT:
                    regex = AccountGroupUIProcessor(group_obj)._get_dynamic_rule_str(group_obj.rule)
            return regex
        except Exception as e:
            LOG.exception(f'Unexpected error getting group rule: {e}')
            return None

    def get_membership_evaluation_ongoing(self, group_obj):
        if group_obj.member_eval_pending:
            return True
        else:
            return False

    def get_members_truncated(self, group_obj):
        include_members = self.context['request'].query_params.get('include_members', 'True') in ('True', 'true', '1')
        if group_obj.member_eval_pending or not include_members:
            return False
        else:
            if hasattr(group_obj, 'accountgroup'):
                try:
                    return group_obj.accountgroup.accounts.count() > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            elif hasattr(group_obj, 'hostgroup'):
                try:
                    return group_obj.hostgroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            elif hasattr(group_obj, 'externaldomaingroup'):
                try:
                    return group_obj.externaldomaingroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            elif hasattr(group_obj, 'ipgroup'):
                try:
                    return group_obj.ipgroup.members_count > API_MEMBER_LIMIT
                except Exception as e:
                    LOG.exception('Failed to get members for group with id ' + str(group_obj.id) + ' due to exception: ' + str(e))
                    return False
            else:
                LOG.error('Group does not have a valid member count attribute')
                return False

    def _get_members(self, group_obj):
        include_members = self.context['request'].query_params.get('include_members', 'True') in ('True', 'true', '1')
        if group_obj.member_eval_pending or not include_members:
            return []
        else:
            if group_obj.type == GroupCollection.HOST:
                return HostGroupAPIProcessor.get_group_members(self.context['request'], group_obj.hostgroup, limit=API_MEMBER_LIMIT)
            if group_obj.type == GroupCollection.IP:
                return group_obj.ipgroup.ips[:API_MEMBER_LIMIT]
            if group_obj.type == GroupCollection.DOMAIN:
                return group_obj.externaldomaingroup.domains[:API_MEMBER_LIMIT]
            if group_obj.type == GroupCollection.ACCOUNT:
                return AccountGroupAPIProcessor.get_group_members(self.context['request'], group_obj.accountgroup, limit=API_MEMBER_LIMIT)

    def _get_member_count(self, group_obj):
        if group_obj.type == GroupCollection.HOST:
            return group_obj.hostgroup.members_count
        elif group_obj.type == GroupCollection.ACCOUNT:
            return group_obj.accountgroup.members_count
        elif group_obj.type == GroupCollection.DOMAIN:
            return group_obj.externaldomaingroup.members_count
        elif group_obj.type == GroupCollection.IP:
            return group_obj.ipgroup.members_count
        else:
            return 0

    def _get_built_using(self, group_obj):
        if group_obj.member_type == GroupCollection.MEMBER_TYPE_DYNAMIC:
            return 'regex'
        elif group_obj.member_type == GroupCollection.MEMBER_TYPE_AD:
            return 'ad_group'
        else:
            return 'static_members'

    def _get_rules(self, group_obj):
        if group_obj.type == GroupCollection.HOST:
            return HostGroupAPIProcessor.get_rules(group_obj.hostgroup)
        elif group_obj.type == GroupCollection.ACCOUNT:
            return super()._get_rules(group_obj)
        elif group_obj.type == GroupCollection.DOMAIN:
            return ExternalDomainGroupAPIProcessor.get_rules(group_obj.externaldomaingroup)
        elif group_obj.type == GroupCollection.IP:
            return IPGroupAPIProcessor.get_rules(group_obj.ipgroup)


class CloudDataSourceSerializerV3_4(serializers.Serializer):
    """
    Serializer for Cloud Data Sources (CloudSensor) in API v3.4
    Maps CloudSensor data to the exact contract format specified
    """

    connector_id = serializers.SerializerMethodField()
    connector_name = serializers.SerializerMethodField()
    connector_type = serializers.SerializerMethodField()
    connector_state = serializers.SerializerMethodField()
    created_at = serializers.SerializerMethodField()
    error = serializers.SerializerMethodField()
    last_log_received = serializers.SerializerMethodField()
    updated_at = serializers.SerializerMethodField()
    properties = serializers.SerializerMethodField()

    def get_connector_id(self, obj):
        """Return the source_id as the primary identifier"""
        return obj.get('source_id') or obj.get('serial_number')

    def get_connector_name(self, obj):
        """Return the connector name"""
        return obj.get('sensor_name') or obj.get('name')

    def get_connector_type(self, obj):
        """Return the connector type"""
        return obj.get('sensor_type')

    def get_connector_state(self, obj):
        """Return the connector state mapped to Nexus values or sensor_data status"""
        return obj.get('status')

    def get_created_at(self, obj):
        """Return creation timestamp - will be null until Nexus"""
        return obj.get('created_at')

    def get_error(self, obj):
        """Return error information if present"""
        error_data = obj.get('error')
        if error_data and error_data != 'None':
            if isinstance(error_data, dict):
                return error_data.get('message') or str(error_data)
            return str(error_data)
        return None

    def get_last_log_received(self, obj):
        """Return last log received timestamp"""
        return obj.get('last_log_received') or obj.get('last_seen')

    def get_updated_at(self, obj):
        """Return updated timestamp - Nexus specific, will be null for now"""
        return obj.get('updated_at')

    def get_properties(self, obj):
        """
        Return connector-specific properties in a nested dictionary
        Provides flexibility for different connector types and future changes
        """
        properties = {}

        # Standard properties that apply to most connectors
        if region := obj.get('region') or obj.get('location'):
            properties['region'] = region

        # Extract connector-specific properties from sensor_data if available
        sensor_data = obj.get('sensor_data', {})
        if isinstance(sensor_data, dict):
            # Setup link for connectors that need authorization
            if setup_link := sensor_data.get('setup_link'):
                properties['setup_link'] = setup_link

            # License type information
            if license_type := sensor_data.get('license_type'):
                properties['license_type'] = license_type

            # API type for cloud connectors
            if api_type := sensor_data.get('api_type'):
                properties['api_type'] = api_type

            # Microsoft tenant ID for O365/Azure connectors
            if ms_tenant_id := sensor_data.get('ms_tenant_id') or sensor_data.get('tenant_id'):
                properties['ms_tenant_id'] = ms_tenant_id

            # Audit logs enabled flag
            if 'audit_logs_enabled' in sensor_data:
                properties['audit_logs_enabled'] = bool(sensor_data['audit_logs_enabled'])

        return properties
