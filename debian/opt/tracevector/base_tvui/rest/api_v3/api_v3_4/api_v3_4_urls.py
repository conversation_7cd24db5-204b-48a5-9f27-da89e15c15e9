# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

"""
Urls for REST V3.4 API for endpoints behind /api/v3.4
"""

from django.conf.urls import include, url
from base_tvui import lib_tv
from base_tvui.rest.api_v3.api_v3_3.api_v3_3_urls import urlpatterns as v3_3_urls
from base_tvui.rest.api_v3.api_v3_4 import api_v3_4_views

deprecated_pattern_names = [
    'api-account-scoring-events-v3.0',
    'api-account-scoring-v3.3',
    'api-detection-events-v3.0',
    'api-account-detection-v3.3',
]
filtered_v3_3_urls = [pattern for pattern in v3_3_urls if pattern.name not in deprecated_pattern_names]


urlpatterns = [
    url(r'^accounts/?$', api_v3_4_views.AccountsV3_4.as_view(), name='api-accounts-v3.4'),
    url(r'^accounts/(?P<pk>\d+)/?$', api_v3_4_views.AccountV3_4.as_view(), name='api-account-v3.4'),
    url(r'^accounts/(?P<pk>\d+)/close?$', api_v3_4_views.AccountCloseV3_4.as_view(), name='api-account-close-v3.4'),
    url(r'^users/(?P<pk>\d+)/?$', api_v3_4_views.UserV3_4.as_view(), name='api-user-v3.4'),
    url(r'^users/?$', api_v3_4_views.UsersV3_4.as_view(), name='api-users-v3.4'),
    url(r'^users/roles/?$', api_v3_4_views.UserRolesV3_4.as_view(), name='api-user-roles-v3.4'),
    url(r'^detections/?$', api_v3_4_views.DetectionsV3_4.as_view(), name='api-detections-v3.4'),
    url(r'^detections/(?P<pk>\d+)/?$', api_v3_4_views.DetectionV3_4.as_view(), name='api-detection-v3.4'),
    url(r'^detections/(?P<pk>\d+)/close/?$', api_v3_4_views.DetectionCloseV3_4.as_view(), name='api-detection-close-v3.4'),
    url(r'^detections/close/?$', api_v3_4_views.DetectionsCloseV3_4.as_view(), name='api-detections-close-v3.4'),
    url(r'^detections/open/?$', api_v3_4_views.DetectionsOpenV3_4.as_view(), name='api-detections-open-v3.4'),
    url(r'^detections/(?P<pk>\d+)/open/?$', api_v3_4_views.DetectionOpenV3_4.as_view(), name='api-detection-open-v3.4'),
    url(r'^tagging/(?P<object_type>[\w-]+)/(?P<entity_id>\d+)/?$', api_v3_4_views.TaggingV3_4.as_view(), name='api-tagging-v3.4'),
    url(r'^health/network_brain/ping/?$', api_v3_4_views.NetworkBrainHealthPingV3_4.as_view(), name='api-health-network-brain-ping-v3.4'),
    url(
        r'^health/external_connectors/?$', api_v3_4_views.ExternalConnectorsHealthV3_4.as_view(), name='api-health-external_connectors-v3.4'
    ),
    url(
        r'^health/external_connectors/details/?$',
        api_v3_4_views.ExternalConnectorsHealthDetailsV3_4.as_view(),
        name='api-health-external_connectors-details-v3.4',
    ),
    url(r'^health/edr/?$', api_v3_4_views.EDRHealthV3_4.as_view(), name='api-health-edr-v3.4'),
    url(r'^health/edr/details/?$', api_v3_4_views.EDRHealthDetailsV3_4.as_view(), name='api-health-edr-details-v3.4'),
    url(r'^(?P<tvui_type>\w+)s/notes/?$', api_v3_4_views.BulkNotesV3_4Create.as_view(), name='api-notes-v3.4-bulk-create'),
    url(r'^(?P<tvui_type>\w+)s/(?P<type_id>\d+)/notes/?$', api_v3_4_views.NotesV3_4ListCreate, name='api-notes-v3.4-listcreate'),
    url(r'^(?P<tvui_type>\w+)s/(?P<type_id>\d+)/notes/(?P<note_id>\d+)/?$', api_v3_4_views.NotesV3_4Detail, name='api-notes-v3.4-detail'),
    url(r'^entities/?$', api_v3_4_views.EntitiesV3_4.as_view(), name='api-entities-v3.4'),
    url(r'^entities/(?P<pk>\d+)/?$', api_v3_4_views.EntityV3_4.as_view(), name='api-entity-v3.4'),
    url(r'^events/entity_scoring/?$', api_v3_4_views.EntityScoringEventsV3_4.as_view(), name='api-entity-scoring-events-v3.4'),
    url(r'^events/detections/?$', api_v3_4_views.DetectionEventsV3_4.as_view(), name='api-detection-events-v3.4'),
    url(r'^lockdown/?$', api_v3_4_views.EntityLockdownV3_4.as_view(), name='api-entity-lockdown-v3.4'),
    url(r'^tagging/(?P<table>[\w-]+)/?$', api_v3_4_views.BulkTaggingV3_4.as_view(), name='api-bulk-tagging-v3.4'),
    url(r'^unique_hosts_observed/?$', api_v3_4_views.UniqueHostCountTimespanV3_4.as_view(), name='api-uniqueHostCountTimespan-v3.4'),
    url(
        r'^unique_hosts_observed/audit/?$',
        api_v3_4_views.UniqueHostAuditTimespanV3_4.as_view(),
        name='api-uniqueHostAuditTimespan-v3.4',
    ),
    url(r'^unique_hosts_observed_monthly/?$', api_v3_4_views.UniqueHostCountMonthlyV3_4.as_view(), name='api-uniqueHostCountMonthly-v3.4'),
    url(
        r'^unique_hosts_observed_monthly/audit/?$',
        api_v3_4_views.UniqueHostAuditMonthlyV3_4.as_view(),
        name='api-uniqueHostAuditMonthly-v3.4',
    ),
    url(r'^health/?$', api_v3_4_views.HealthV3_4.as_view(), name='api-health-v3.4'),
    url(r'^health/(?P<check_type>\w+)/?$', api_v3_4_views.HealthV3_4.as_view(), name='api-health-check-v3.4'),
    url(r'^groups/?$', api_v3_4_views.GroupsV3_4.as_view(), name='api-groups-v3.4'),
    url(r'^groups/(?P<pk>\d+)/?$', api_v3_4_views.GroupV3_4.as_view(), name='api-group-v3.4'),
    url(r'^groups/(?P<pk>\d+)/members/?$', api_v3_4_views.GroupMembersV3_4.as_view(), name='api-group-members-v3.4'),
    url(
        r'^settings/active_directory/groups/?$',
        api_v3_4_views.ActiveDirectoryGroupsV3_4.as_view(),
        name='api-settings-groups-active-directory-v3.4',
    ),
    url(
        r'^notification/recievers/?$',
        api_v3_4_views.NotificationReceiversV3_4.as_view(actions={'get': 'list', 'post': 'post'}),
        name='notification-receivers-v3.4',
    ),
    url(
        r'^notification/recievers/(?P<pk>\w+)/?$',
        api_v3_4_views.NotificationReceiversV3_4.as_view({'get': 'get', 'patch': 'patch', 'delete': 'delete'}),
        name='notification-receiver-v3.4',
    ),
    url(r'^hosts/(?P<pk>\d+)/close/?$', api_v3_4_views.HostCloseV3_4.as_view(), name='api-host-close-v3.4'),
    url(r'^data-sources/?$', api_v3_4_views.CloudDataSourcesV3_4.as_view(), name='api-cloud-data-sources-v3.4'),
] + filtered_v3_3_urls
