# Copyright (c) 2016-2019 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
import os
import copy
import json
from typing import List
from abc import abstractmethod, ABCMeta
from datetime import datetime, timedelta
from collections import defaultdict

import dateutil.parser
import jinja2
import pytz
from django.core.exceptions import ObjectDoesNotExist
from django.urls import reverse
from django.utils import dateformat
from django.utils.translation import ugettext
from django.template.loader import render_to_string
from markupsafe import Markup
from pure_utils.log_utils import get_vectra_logger

from base_tvui import models_utils, lib_tv, settings, lib_host_role
from base_tvui.feature_flipper import conditions
from base_tvui.lib_account import find_account_groups_containing_accounts
from base_tvui.lib_notifications import get_alert_settings, still_targets_key_asset
from base_tvui.lib_tv import calc_duration, get_device_name, get_device_serial
from base_tvui.settings import TIME_ZONE
from base_tvui.feature_flipper import flag_enabled, Flags
from vectra.collection_scoring.config import ARCHETYPE_MAPPING
from lockdown import lockdown_utils
from tvui.campaigns.campaign_views import CampaignBaseEndpoint
from tvui.models import (
    Account,
    Campaign,
    detection,
    host,
    setting,
    User,
    LinkedAccount,
    AccountGroup,
    CloudSensor,
    HostGroup,
    NetworkSensor,
)
from tvui.helpers import EDR_ARTIFACT_TYPES
from tvui.priority_entities.utils import get_priority_score

LOG = get_vectra_logger(__name__)

RUX_SCORE_USAGE = 'rux'
QUX_SCORE_USAGE = 'qux'


class BaseRenderer(object, metaclass=ABCMeta):
    """
    The BaseRenderer is an abstract class defining the implementations
    for the different notification delivery type renderings that may need to be supported
    by each renderer.

    Each delivery type has its own entry point into the render class that takes in the notification payload,
    and then returns the content that is needed to populate the notification as appropriate to
    the delivery method.
    """

    @abstractmethod
    def render_for_email(self, notification_data):
        """
        returns a dict containing the following:
            subject: str containing the subject line for the email
            contents: dict of { plaintext, html } containing the plaintext and html versions of the message,
                respectively. At least one entry should be provided.
        """
        pass

    @staticmethod
    def detection_is_papi(detection):
        return detection.type.lower().startswith('papi')

    @staticmethod
    def is_prioritized(entity_obj) -> str:
        is_prioritized = "Not Prioritized"
        priority_score = get_priority_score()
        if entity_obj.urgency_score >= priority_score:
            is_prioritized = 'Prioritized'
        return is_prioritized

    @staticmethod
    def get_groups(entity_obj, default_value):
        groupIDs = []
        if isinstance(entity_obj, LinkedAccount):
            accountsToFindGroupsFor = []
            if flag_enabled(Flags.linked_account_groups):
                accountsToFindGroupsFor.append(entity_obj)
            elif hasattr(entity_obj, 'subaccounts'):
                for sub in entity_obj.subaccounts.all():
                    accountsToFindGroupsFor.append(sub)  # In this case, we search for subaccounts
            else:
                # In this case, we search for linked accounts
                # This is a line bit confusing, because there is no such thing as a LinkedAccount without subaccount property
                # so this line should never activate
                accountsToFindGroupsFor.append(entity_obj)
            groups = find_account_groups_containing_accounts(accountsToFindGroupsFor).order_by('name')
        else:
            groupIDs.append(entity_obj.id)
            groups = HostGroup.objects.filter(hosts__id__in=groupIDs).order_by('name')

        g_importance = 'Medium (Default)'
        group_str = default_value
        if len(groups) > 0:
            group_str = ', '.join({group.name for group in groups})
            if groups.filter(importance='high').count() > 0:
                g_importance = 'High'
            elif groups.filter(importance='medium').count() > 0:
                g_importance = 'Medium'
            elif groups.filter(importance='low').count() > 0:
                g_importance = 'Low'
            elif groups.filter(importance='never_prioritize').count() > 0:
                g_importance = 'Never Prioritize'

        return group_str, g_importance

    @staticmethod
    def get_archetype(entity_obj, default_value):
        archetype_description = default_value
        attack_profile = default_value
        indicator_name_strs = []
        indicator_objs = []
        if isinstance(entity_obj, LinkedAccount):
            archetype = entity_obj.get_account_archetype_details()
        else:
            archetype = entity_obj.get_host_archetype_details()

        detection_set = entity_obj.detection_set

        if archetype:
            if archetype.get('name') == 'aws_threat_actor':
                archetype_description = 'An actor is executing suspicious behavior in the AWS cloud environment.'
            elif archetype.get('name') == 'multi_cloud':
                archetype_description = 'Attacker techniques observed across an identity provider, SaaS, and/or public cloud, indicating a multi-cloud service threat.'
            elif archetype.get('name') == 'hybrid_network':
                archetype_description = 'Attacker techniques observed across both network and cloud, indicating a multi-domain threat.'
            else:
                archetype_description = MITRE_SCHEMES[archetype['name']].get('description')

            attack_profile = archetype.get('vname')
            score_dets = archetype.pop('scoring_detections')
            indicator_name_strs = [pad_type_vname for pad_type, pad_type_vname in score_dets]

        for indicator in indicator_name_strs:
            ind = lambda: None
            ind.name = indicator
            ind.last_det_id = default_value
            ind.last_det_timestamp = default_value
            det = detection_set.filter(type_vname=indicator).order_by('-last_timestamp').first()
            if det:
                ind.last_det_id = det.id
                ind.last_det_timestamp = dateformat.format(det.last_timestamp, 'M. dS, Y H:i')

            indicator_objs.append(ind)
        return indicator_objs, archetype_description, attack_profile

    @staticmethod
    def get_up_email_subject(reason):
        return '{0} Alert'.format(reason.capitalize())


class TestRenderer(BaseRenderer):
    """
    Basic renderer that can be used to provide a basic helloworld implementation
    """

    def render_for_email(self, notification_data):
        text = 'Test Text Email content. Notification data: {0}'.format(json.dumps(notification_data))
        html = """\
            <html>
              <head></head>
              <body>
                <p>Test HTML Email content</p>
                <p>Notification data: {0}
              </body>
            </html>
        """.format(
            json.dumps(notification_data)
        )

        return {'subject': 'Test Render Email', 'contents': {'plaintext': text, 'html': html}}


def jinja_filter_datetime(dt, format='%b. %-d, %Y, %-I:%M %p %Z'):
    """Jinja filter for formatting datetimes in the brain's timezone"""
    if not isinstance(dt, datetime):
        return ''
    return dt.astimezone(pytz.timezone(TIME_ZONE)).strftime(format)


def jinja_filter_UP_datetime(dt, format='M. dS, Y H:i e'):
    """Jinja filter to format datetimes for unified prioritization templates"""
    if isinstance(dt, str):
        return dt
    if not isinstance(dt, datetime):
        return ''
    return dateformat.format(dt.astimezone(pytz.timezone(TIME_ZONE)), format)


class HTMLEmailRenderer:
    alerts_path = os.path.join(settings.SRC_ROOT, 'tracevector/tvui/tv_templates/emails/dist/alerts')
    jinja_env = jinja2.Environment(loader=jinja2.FileSystemLoader(alerts_path))
    jinja_env.globals.update({'CURRENT_YEAR': datetime.now().year, 'S3_IMG_URL': 'https://s3-us-west-1.amazonaws.com/vectra-images'})
    jinja_env.filters['braintime'] = jinja_filter_datetime
    jinja_env.filters['UP_timeformat'] = jinja_filter_UP_datetime

    # TODO: for i18n,
    #       https://stackoverflow.com/questions/8471455/how-to-enable-trans-tag-for-jinja-templates/********#********

    @classmethod
    def render(cls, template_name, alert_data):
        """
        Generic notification template renderer for emails
        """
        if flag_enabled(Flags.unified_prioritization) and template_name in UP_SUPPORT_NOTIFICATION:
            template_name += '-up'
        return cls.jinja_env.get_template("{template_name}-alert.html".format(template_name=template_name)).render(alert_data)


NEW_NOTIFICATION_UX_TYPES = [
    'host',
    'account',
    'system',
    'campaign',
    'host-lockdown-qux',
    'host-lockdown-failed-qux',
    'host-lockdown-rux',
    'host-lockdown-failed-rux',
    'ad-lockdown-qux',
    'ad-lockdown-failed-qux',
    'ad-lockdown-rux',
    'ad-lockdown-failed-rux',
    'azure-ad-auto-lockdown-rux',
    'azure-ad-auto-lockdown-revoke-rux',
    'azure-ad-auto-lockdown-failed-rux',
    'azure-ad-auto-lockdown-failed-revoke-rux',
]

MITRE_SCHEMES = {scheme['name']: scheme for scheme in ARCHETYPE_MAPPING['archetype_schemes']['mitre']}

UP_SUPPORT_NOTIFICATION = ['account', 'host']


class EmailRenderer(BaseRenderer):
    @staticmethod
    def render_email_template(template_type, alert_data, wrap_with_generic=True):
        """
        Generic notification template renderer for emails
        """
        render_data = copy.deepcopy(alert_data)
        render_data['device_address'] = lib_tv.get_hostname_and_default_ip()[0]

        # TODO: APP-9905 remove old rendering after all notification types transitioned to new UX
        if template_type in NEW_NOTIFICATION_UX_TYPES:
            html = HTMLEmailRenderer.render(template_type, render_data)
        else:
            body_html = render_to_string(f'notifications/{template_type}_alert_email.html', render_data)
            if wrap_with_generic:
                html = render_to_string("notifications/generic_alert_email.html", {**render_data, 'email_body': body_html})
            else:
                html = body_html

        render_data['text_email_body'] = render_to_string("notifications/{}_alert_text_email.html".format(template_type), render_data)

        text = render_to_string("notifications/generic_alert_text_email.html", render_data)
        if flag_enabled(Flags.unified_prioritization) and template_type in UP_SUPPORT_NOTIFICATION:
            subject = alert_data['brief']
        else:
            subject = alert_data['notification_subject']

        return {'contents': {'plaintext': text, 'html': html}, 'subject': subject}


class AlarmedAccountNotification(EmailRenderer):
    """
    Alarmed Account Notification
    """

    def __init__(self):
        self.default_value = '-'
        self.alert_data = {}
        self.alert_settings, self.notify_det_cats, self.notify_det_types = get_alert_settings()
        super(AlarmedAccountNotification, self).__init__()

    def _get_highlighted_det_index(self, detection_list):
        """Return index of detection to highlight as 'most recent' in email

        Args:
            detection_list(list) list of detection objects ordered by last timestamp

        Prioritize in the following order:
            - targets key asset
            - configured in notifications to trigger on
            - last_timestamp

        Returns: index of detection in list or 0
        """
        key_asset_indx = None
        configured_det_indx = None
        for indx, check_det in enumerate(detection_list):
            if check_det.targets_key_asset:
                key_asset_indx = indx
                break
            if configured_det_indx is None and (
                (self.notify_det_types and check_det.type in self.notify_det_types)
                or (self.notify_det_cats and check_det.category in self.notify_det_cats)
            ):
                configured_det_indx = indx
        return key_asset_indx or configured_det_indx or 0

    def render_for_email(self, notification_data):
        """
        Render email from notification data

        Args:
            notification_data (dict): notification dict created from Notification ORM object

        returns: HTML render for email
        """
        other_recent_detections_list = list(detection.objects.filter(id__in=notification_data['detections']).order_by('-last_timestamp'))
        v_account = _get_linked_account_instance(notification_data['account']['id'])
        total_account_active_detection_count = v_account.detection_set.filter(state='active').count()
        groups, importance = self.get_groups(v_account, self.default_value)
        profile_indicators, attack_name, attack_profile = self.get_archetype(v_account, self.default_value)
        brief = self.get_up_email_subject(notification_data['reason'])

        if other_recent_detections_list:
            highlighted_indx = self._get_highlighted_det_index(other_recent_detections_list)
            most_recent_detection = other_recent_detections_list.pop(highlighted_indx)
            most_recent_detection.sensor_name = self.default_value
            cloud_sensor = CloudSensor.objects.filter(luid=most_recent_detection.sensor_luid).first()
            if cloud_sensor:
                setattr(most_recent_detection, 'sensor_luid', cloud_sensor.name)
            else:
                setattr(most_recent_detection, 'sensor_luid', self.default_value)
        else:
            most_recent_detection = None
            LOG.warning('No recent detections found for email notification %s', notification_data)
            raise Exception('No recent detections found for related account for email notifications')

        if notification_data['reason'] == 'detection' and most_recent_detection:
            if self.detection_is_papi(most_recent_detection):
                reason = 'Privilege Anomaly - {}'.format(most_recent_detection.type_vname)
                notification_subject = 'Privilege Anomaly - {}'.format(most_recent_detection.type_vname)
            else:
                reason = 'Detection Alert - {}'.format(most_recent_detection.type_vname)
                notification_subject = 'Detection alert - {}'.format(most_recent_detection.type_vname)
        else:
            reason = 'Account Alert - {}'.format(v_account.uid)
            notification_subject = 'Account alert - {}'.format(v_account.uid)

        if self.detection_is_papi(most_recent_detection):
            try:
                papi_host = most_recent_detection.details.first().host_detection.host
                papi_host_session_id = papi_host.host_session_set.order_by('-start').first().id
                papi_host_source = AlarmedHostNotification.get_sensor_source(papi_host)
            except Exception as e:
                LOG.exception(f'Failed to get PAPI host data with error {e}')
                papi_host = None
                papi_host_session_id = None
                papi_host_source = None
        else:
            papi_host = None
            papi_host_session_id = None
            papi_host_source = None

        self.alert_data = {
            'notification_subject': notification_subject,
            'reason': reason,
            'orig_reason': notification_data['reason'],
            'account': v_account,
            'brief': brief,
            'most_recent_detection': most_recent_detection,
            'papi_host': papi_host,
            'papi_host_session_id': papi_host_session_id,
            'papi_host_source': papi_host_source,
            'other_recent_detections_list': other_recent_detections_list,
            'total_account_active_detection_count': total_account_active_detection_count,
            'url': 'settings/notifications/',
            'account_attack_name': attack_name,
            'account_attack_profile': attack_profile,
            'account_groups': groups,
            'account_importance': importance,
            'profile_indicators': profile_indicators,
        }
        if most_recent_detection.type in ["smb_ransomware"]:
            self.alert_data['extra_details'] = Markup(
                render_to_string("notifications/extra_details/alert_{}.html".format(most_recent_detection.type), self.alert_data)
            )
            self.alert_data['extra_text_details'] = render_to_string(
                "notifications/extra_details/alert_{}_text.html".format(most_recent_detection.type), self.alert_data
            )

        return self.render_email_template('account', self.alert_data)


class AlarmedHostNotification(EmailRenderer):
    """
    Alarmed Host Notification
    """

    def __init__(self):
        self.default_value = '-'
        self.alert_data = {}
        self.alert_settings, self.notify_det_cats, self.notify_det_types = get_alert_settings()
        super(AlarmedHostNotification, self).__init__()

    @staticmethod
    def get_sensor_source(host_check):
        """returns source (sensor name) of host

        Args:
            host_check (host object): host to get source

        Returns: name of source (str)
        """
        try:
            source = NetworkSensor.objects.get(luid=host_check.sensor_luid).alias
        except (ObjectDoesNotExist, AttributeError):
            # if no sensor  source exists then source was the headend
            source = get_device_name()
        return source

    def _get_highlighted_det_index(self, detection_list):
        """Return index of detection to highlight as 'most recent' in email

        Args:
            detection_list(list) list of detection objects ordered by last timestamp

        Prioritize in the following order:
            - targets key asset
            - configured in notifications to trigger on
            - last_timestamp

        Returns: index of detection in list or 0
        """
        key_asset_indx = None
        configured_det_indx = None
        for indx, check_det in enumerate(detection_list):
            if check_det.targets_key_asset:
                key_asset_indx = indx
                break
            if configured_det_indx is None and (
                (self.notify_det_types and check_det.type in self.notify_det_types)
                or (self.notify_det_cats and check_det.category in self.notify_det_cats)
            ):
                configured_det_indx = indx
        return key_asset_indx or configured_det_indx or 0

    def render_for_email(self, notification_data):
        """
        Render email from notification data

        Args:
            notification_data (dict): notification dict created from Notification ORM object

        returns: HTML render for email
        """
        other_recent_detections_list = list(detection.objects.filter(id__in=notification_data['detections']).order_by('-last_timestamp'))
        targets_key_asset_found = still_targets_key_asset(other_recent_detections_list)
        host_id = notification_data['host']['id']
        v_host = host.objects.get(id=host_id)
        total_host_active_detection_count = detection.objects.filter(host_session__host=v_host, state='active').count()
        p_owner = v_host.probable_owner.get('name') if v_host.probable_owner else None
        host_tscore = notification_data['host']['host_t_score']
        host_cscore = notification_data['host']['host_c_score']
        is_key_asset = v_host.key_asset
        targets_key_asset = v_host.targets_key_asset and targets_key_asset_found
        source = self.get_sensor_source(v_host)
        highlighted_indx = self._get_highlighted_det_index(other_recent_detections_list)
        brief = self.get_up_email_subject(notification_data['reason'])
        profile_indicators, attack_name, attack_profile = self.get_archetype(v_host, self.default_value)
        groups, importance = self.get_groups(v_host, self.default_value)

        try:
            host_session_id = v_host.host_session_set.order_by('-start').first().id
        except Exception:
            host_session_id = None
            LOG.warning("No host session attached to host %s", host_id)

        if other_recent_detections_list:
            most_recent_detection = other_recent_detections_list.pop(highlighted_indx)
            try:
                most_recent_det_detail_id = most_recent_detection.details.order_by('-last_timestamp').first().id
            except Exception:
                most_recent_det_detail_id = None
                LOG.warning("No detail id found for host %s and detection %s", host_id, most_recent_detection.id)
        else:
            most_recent_detection = None
            most_recent_det_detail_id = None
            LOG.warning('No recent detections found for email notification %s', notification_data)
            raise Exception('No recent detections found for related host for email notifications')

        try:
            host_severity = models_utils.get_severity(host_tscore, host_cscore)
        except Exception:
            host_severity = ''
            LOG.exception('Failed to get the host severity')

        # Add host role info
        role_names_str = None

        if v_host.host_luid:
            role_names = list(filter(None, lib_host_role.fetch_sorted_host_role_list(v_host)))
            role_names_str = ', '.join(role_names)

        if targets_key_asset:
            reason = 'Key Asset Targeted - {}'.format(v_host.name)
            notification_subject = 'Key asset targeted - {}'.format(v_host.name)
        elif is_key_asset:
            reason = 'Key Asset Activity - {}'.format(v_host.name)
            notification_subject = 'Key asset activity - {}'.format(v_host.name)
        elif notification_data['reason'] == "detection" and most_recent_detection:
            if self.detection_is_papi(most_recent_detection):
                reason = 'Privilege Anomaly - {}'.format(most_recent_detection.type_vname)
                notification_subject = 'Privilege Anomaly - {}'.format(most_recent_detection.type_vname)
            else:
                reason = 'Detection Alert - {}'.format(most_recent_detection.type_vname)
                notification_subject = 'Detection alert - {}'.format(most_recent_detection.type_vname)
        else:
            reason = 'Host Alert - {}'.format(v_host.name)
            notification_subject = 'Host alert - {}'.format(v_host.name)

        if self.detection_is_papi(most_recent_detection):
            papi_account = _ensure_correct_account_instance(most_recent_detection.details.first().account)
        else:
            papi_account = None

        self.alert_data = {
            'notification_subject': notification_subject,
            'reason': reason,
            'orig_reason': notification_data['reason'],
            'is_key_asset': is_key_asset,
            'targets_key_asset': targets_key_asset,
            'host': v_host,
            'brief': brief,
            'host_session_id': host_session_id,
            'host_roles': role_names_str,
            'source': source,
            'p_owner': p_owner,
            'host_tscore': host_tscore,
            'host_cscore': host_cscore,
            'host_severity': host_severity,
            'most_recent_detection': most_recent_detection,
            'most_recent_detail_id': most_recent_det_detail_id,
            'papi_account': papi_account,
            'other_recent_detections_list': other_recent_detections_list,
            'total_host_active_detection_count': total_host_active_detection_count,
            'url': 'settings/notifications/',
            'button_title': ugettext('Update Notification Settings'),
            'attack_name': attack_name,
            'attack_profile': attack_profile,
            'groups': groups,
            'importance': importance,
            'profile_indicators': profile_indicators,
        }
        if most_recent_detection.type in ["smb_ransomware"]:
            self.alert_data['extra_details'] = Markup(
                render_to_string("notifications/extra_details/alert_{}.html".format(most_recent_detection.type), self.alert_data)
            )
            self.alert_data['extra_text_details'] = render_to_string(
                "notifications/extra_details/alert_{}_text.html".format(most_recent_detection.type), self.alert_data
            )
        if is_key_asset or targets_key_asset:
            return self.render_email_template('key_asset', self.alert_data)
        else:
            return self.render_email_template('host', self.alert_data)

    @staticmethod
    def detection_is_papi(detection):
        return detection.type.lower().startswith('papi')


class CampaignNotification(EmailRenderer):
    """
    Campaign Notification

    Triggered when:
        - a new campaign is created
        - a campaign is closed
        - a host is added to an existing campaign it was previously not a part of
    """

    @staticmethod
    def _get_campaign_notification_data(notification_data):
        try:
            campaign = Campaign.objects.get(id=notification_data['campaign_id'])
        except ObjectDoesNotExist:
            LOG.error('Cannot render notification for campaign with id %s, campaign does not exist', notification_data['campaign_id'])
            return {}

        campaign_data = CampaignBaseEndpoint().get_campaign_data(campaign)
        campaign_data['duration'] = calc_duration(campaign_data['duration'])
        notification_subject = ""
        if notification_data['reason'] == 'added':
            notification_subject = "New Attack Campaign: {}".format(campaign_data['name'])
            reason = "Campaign Created"
        elif notification_data['reason'] == 'closed':
            notification_subject = "Attack Campaign: {} Closed".format(campaign_data['name'])
            reason = "Campaign Closed"
        campaign_notification_data = {
            'button_title': ugettext("Update Notification Settings"),
            'url': 'settings/notifications/',
            'notification_subject': ugettext(notification_subject),
            'notification_reason': notification_data['reason'],
            'campaign': campaign_data,
            'reason': reason,
        }

        return campaign_notification_data

    def render_for_email(self, notification_data):
        """
        Render email from notification data
        """
        return self.render_email_template('campaign', self._get_campaign_notification_data(notification_data))


class SystemNotification(EmailRenderer):
    """
    Generic notification renderer for "general" system alerts
    """

    def __init__(self):
        self.system_messages = {
            'sensor_autopair': {
                'preview': 'Sensor paired',
                'header': 'Sensor Paired',
                'body': 'The sensor %(sensor_name)s at %(sensor_ip)s in vCenter %(vcenter)s was automatically paired.',
                'button_title': 'Manage Sensors',
                'url': reverse('data_sources_network_sensors'),
            },
            'sensor_autodelete': {
                'preview': 'Sensor automatically deleted',
                'header': 'Sensor Automatically Deleted',
                'body': 'The sensor %(sensor_name)s at %(sensor_ip)s in vCenter %(vcenter)s was automatically deleted.',
                'button_title': 'Manage Sensors',
                'url': reverse('data_sources_network_sensors'),
            },
            'sensor_shutdown': {
                'preview': 'Sensor shut down',
                'header': 'Sensor Shut Down',
                'body': 'The sensor %(sensor_name)s at %(sensor_ip)s in vCenter %(vcenter)s was powered off.',
            },
            'sensor_moved': {
                'preview': 'Sensor moved',
                'header': 'Sensor Moved',
                'body': 'The sensor %(sensor_name)s in vCenter %(vcenter)s was moved from %(src_physical_host)s to %(dst_physical_host)s.',
            },
            'vm_not_covered': {
                'preview': 'Virtual machine no longer monitored',
                'header': 'Virtul Machine No Longer Monitored',
                'body': 'The virtual machine(s) in vCenter %(vcenter)s are no longer being monitored by a sensor: %(vms)s.',
            },
            'new_physical_host': {
                'preview': 'New host detected',
                'header': 'New Host Detected',
                'body': 'A new physical host %(physical_host_name)s in vCenter %(vcenter)s was added to datacenter %(datacenter_name)s.',
            },
        }
        super(SystemNotification, self).__init__()

    def render_for_email(self, notification_data):
        system_messages = self.system_messages

        try:
            msg_key = notification_data['message_key']
        except KeyError:
            msg_key = ''
            LOG.exception('Failed to get a message key')

        try:
            params = notification_data['message_parameters']
        except KeyError:
            params = ''

        alert_data = {'reason': 'System Alert', 'notification_subject': 'Vectra System Alert'}

        if msg_key and msg_key in system_messages:
            alert_data['message_header'] = ugettext(system_messages[msg_key]['header'])
            alert_data['message_body'] = ugettext(system_messages[msg_key]['body'])
            if params:
                msg_params = notification_data['message_parameters']
                try:
                    alert_data['message_body'] = alert_data['message_body'] % msg_params
                except KeyError:
                    LOG.error("Error formatting message body [%s] with parameters [%s]", alert_data['message_body'], msg_params)
                    raise
            if 'button_title' in system_messages[msg_key]:
                alert_data['button_title'] = ugettext(system_messages[msg_key]['button_title'])
                alert_data['url'] = system_messages[msg_key].get('url')
            if 'preview' in system_messages[msg_key]:
                alert_data['preview'] = ugettext(system_messages[msg_key]['preview'])
        elif notification_data.get('message_str'):
            alert_data['message_str'] = notification_data['message_str']
        else:
            LOG.exception('Failed to render a notification email')
            return

        return self.render_email_template('system', alert_data)


class SystemHealthNotification(EmailRenderer):
    """
    Notification renderer for system health alerts
    """

    def __init__(self):
        self._checks_with_button = {
            'sensor_connectivity': {'button_title': 'Manage Sensors', 'url': reverse('data_sources_network_sensors')},
            'colossus_packet_drop_rate': {'button_title': 'Manage Traffic', 'url': reverse('network_stats_ingested')},
            'capture_interface_link_status': {'button_title': 'Manage Traffic', 'url': reverse('network_stats_ingested')},
        }
        super(SystemHealthNotification, self).__init__()

    def render_for_email(self, notification_data):
        # `notification_data` is a forwared sensu event
        params = notification_data.get('message_parameters', {})

        # extract relevant fields
        check_title = params['check']['output']['title']
        message = params['check']['output']['message']
        status = params['check']['status']
        # optionally show contact customer support text when check is in CRITICAL status.
        contact_customer_support = params['check'].get('contact_customer_support', False) if status == 2 else False

        # validate fields
        if not check_title.strip():
            raise Exception('required field "check_title" is empty')
        if not message.strip():
            raise Exception('required field "message" is empty')

        alert_data = {
            'reason': 'System Alert',
            'notification_subject': 'Vectra system alert',
            'preview': ugettext('Vectra {}'.format(check_title)),
            'message_header': ugettext('Brain {}: {}'.format(get_device_serial(), check_title)),
            'message_body': ugettext(message),
            'contact_customer_support': contact_customer_support,
        }

        # add button
        check_name = params['check']['name']
        button_data = self._checks_with_button.get(check_name)
        if button_data:
            alert_data['button_title'] = ugettext(button_data['button_title'])
            alert_data['url'] = button_data.get('url')

        return self.render_email_template('system', alert_data)


class AssignmentNotificationRenderer(EmailRenderer):
    """
    Notification renderer for assignment alerts
    """

    NEW = 'new'
    REMOVED = 'removed'

    def get_base_assignment_data(self, notification_data, assignment_type):
        assignment_type = str(assignment_type).title()
        action = notification_data['action']
        if action == self.NEW:
            reason = '{} assigned to you'.format(assignment_type)
            remove = False
        elif action == self.REMOVED:
            reason = '{} unassigned from you'.format(assignment_type)
            remove = True
        else:
            raise NameError("Invalid assignment notification action [%s]", action)

        return {
            'reason': reason,
            'remove': remove,
            'notification_subject': reason,
            'assigned_by': notification_data['assigned_by'],
            'assigned_to': notification_data['assigned_to'],
        }

    def get_host_assignment_data(self, notification_data):
        # Get the assigned to user from assigned_to_id, since the assignment
        # user info may change after record is added to the notification queue
        assignment_type_id = notification_data['assignment_type_id']
        assignment_data = self.get_base_assignment_data(notification_data, 'Host')
        assignment_data.update(
            {'is_host_assignment': True, 'hostname': host.objects.get(id=assignment_type_id).name, 'host_id': assignment_type_id}
        )
        assignment_data['text_email_body'] = '{reason}\n Hostname: {hostname} Assigned by: {assigned_by} Assigned to: {assigned_to}'.format(
            reason=assignment_data['reason'],
            hostname=assignment_data['hostname'],
            assigned_by=assignment_data['assigned_by'],
            assigned_to=assignment_data['assigned_to'],
        )

        return assignment_data

    def get_account_assignment_data(self, notification_data):
        assignment_type_id = notification_data['assignment_type_id']
        assignment_data = self.get_base_assignment_data(notification_data, 'Account')
        account_uid = LinkedAccount.objects.get(id=assignment_type_id).uid
        assignment_data.update({'is_account_assignment': True, 'account_id': assignment_type_id, 'account_uid': account_uid})

        assignment_data['text_email_body'] = '{reason}\n Account: {uid} Assigned by: {assigned_by} Assigned to: {assigned_to}'.format(
            reason=assignment_data['reason'],
            uid=account_uid,
            assigned_by=assignment_data['assigned_by'],
            assigned_to=assignment_data['assigned_to'],
        )

        return assignment_data

    def get_assignment_data(self, notification_data):
        params = notification_data.get('message_parameters', {})
        status = params.get('check', {}).get('status', 1)
        assignment_obj_type = notification_data.get('assignment_obj_type')

        try:
            if assignment_obj_type == 'host':
                assignment_data = self.get_host_assignment_data(notification_data)
            elif assignment_obj_type == 'linked_account':
                assignment_data = self.get_account_assignment_data(notification_data)
        except Exception:
            LOG.exception('Error processing assignment with id %s', notification_data['assignment_id'])
            raise Exception('Error processing assignment')

        assignment_data['contact_customer_support'] = (
            params.get('check', {}).get('contact_customer_support', False) if status == 2 else False
        )

        return assignment_data

    def render_for_email(self, notification_data):
        assignment_data = self.get_assignment_data(notification_data)
        return self.render_email_template('assignment', assignment_data, wrap_with_generic=False)


class PasswordNotificationRenderer(EmailRenderer):
    """
    Render class for Password Change/Expiring/Expired Notifications.
    """

    PASSWORD_NOTIFICATION_TYPE = {
        'password_changed': {'notification_subject': 'Your Password Was Changed', 'reason': 'Your password was changed'},
        'change_password_soon': {'notification_subject': 'Change Your Password Soon', 'reason': 'Change your password soon'},
        'password_expired': {'notification_subject': 'Your Password Has Expired', 'reason': 'Your password has expired'},
    }

    def render_for_email(self, alert_data):
        """
        Render data for password notification template.

        @args:
            alert_data (dict) - must contain user_id and reason
        """
        try:
            user = User.objects.get(pk=alert_data['user_id'])
            reason = alert_data['reason']
            password_alert_data = copy.deepcopy(self.PASSWORD_NOTIFICATION_TYPE[reason])
            password_alert_data['notification_reason'] = reason
            password_alert_data['username'] = user.display_username

            if reason == 'password_changed':
                password_alert_data['timestamp'] = user.password_set_timestamp
            elif reason == 'change_password_soon':
                expiration = int(setting.objects.get(group='password_policy', key='expiration_in_days').value)
                password_alert_data.update(
                    {
                        'expiration_date': user.password_set_timestamp + timedelta(days=expiration),
                        'device_address': lib_tv.get_hostname_and_default_ip()[0],
                        'button_title': ugettext('My Profile'),
                        'url': reverse('profile_ember'),
                    }
                )

            return self.render_email_template('password', password_alert_data)
        except Exception:
            LOG.exception('Error processing password notification')


class ADLockdownNotificationRenderer(EmailRenderer):
    def render_for_email(self, alert_data):
        """
        alert_data: {
            'success': True or False,
            'action': 'enabled' or 'disabled',
            'account_id': id of the account,
            'linked_account_id': id of the linked account,
            'user_id': id of user who took action, or None if auto,
            'timestamp': time the action was taken,
            'timeout_timestamp': Timeout associated with the action, or None,
            'locked_duration_min': (int) time in minutes that the account was locked,
            'scores': (dict) scores of the entity when a lockdown action was taken.
        }
        """
        action = alert_data['action']
        linked_acct = _get_linked_account_instance(alert_data['linked_account_id'])
        user = User.objects.filter(id=alert_data['user_id']).first()
        auto = user is None  # convenient shorthand
        auto_settings = lockdown_utils.get_account_lockdown_settings()
        scores = alert_data['scores']

        timestamp = dateutil.parser.parse(alert_data['timestamp'])
        timeout_timestamp = alert_data['timeout_timestamp'] and dateutil.parser.parse(alert_data['timeout_timestamp'])

        # quick and dirty humanization of timespan to "X hours Y minutes" format
        hrs, mins = divmod(alert_data['locked_duration_min'], 60)
        duration_str = '{} hour{}'.format(hrs, 's' if hrs > 1 else '') if hrs else ''
        duration_str += ' {} minute{}'.format(mins, 's' if mins > 1 else '') if mins else ''

        score_usage = RUX_SCORE_USAGE if conditions.is_cloud() else QUX_SCORE_USAGE
        if alert_data['success']:
            template_name = f'ad-lockdown-{score_usage}'
            subject = 'Lockdown: Account '
            subject += 'Automatically ' if auto else 'Manually '
            subject += action.title()
        else:
            template_name = f'ad-lockdown-failed-{score_usage}'
            action = {'enabled': 're-enable', 'disabled': 'disable'}[action]
            subject = 'Lockdown: Failed to {} Account'.format(action.title())

        # linked_acct should primarily be a LinkedAccount and as a fallback to Account, this is to handle the reference
        # to uid which is named differenlty e.g. LinkedAccount - display_uid, Account - uid.
        if isinstance(linked_acct, LinkedAccount):
            formatted_account_uid = linked_acct.display_uid
        elif isinstance(linked_acct, Account):
            formatted_account_uid = linked_acct.uid
            LOG.warning("AD notification render received an Account object as a fallback.", extra={"linked_account_id": linked_acct.id})

        # TODO APP-10590 generalize this to a jinja filter
        # this prevents outlook, etc. from automatically making the account a mailto: hyperlink
        formatted_account_uid = Markup(formatted_account_uid.replace('@', '<span>@</span>'))

        render_data = {
            'notification_subject': subject,
            'formatted_account_uid': formatted_account_uid,
            'account': linked_acct,
            'scores': scores,
            'action': action,
            'auto': auto,
            'user': user,
            'timestamp': timestamp,
            'timeout_timestamp': timeout_timestamp,
            'auto_settings': auto_settings,
            'lock_duration': duration_str,
        }

        return self.render_email_template(template_name, render_data)


class AzureADAutoLockdownNotificationRenderer(EmailRenderer):
    def render_for_email(self, alert_data: List[dict]):
        """
        alert_data: {
            'success': True or False,
            'action': 'enabled' or 'disabled',
            'account_id': id of the linked account,
            'user_id': id of user who took action, or None if auto,
            'timestamp': time the action was taken,
            'timeout_timestamp': Timeout associated with the action, or None,
            'locked_duration_min': (int) time in minutes that the account was locked,
            'scores': (dict) scores of the entity when a lockdown action was taken.
        }
        """
        action = alert_data['action']
        account = _get_linked_account_instance(alert_data['linked_account_id'])
        scores = alert_data['scores']
        auto_settings = lockdown_utils.get_azure_account_lockdown_settings()

        timestamp = dateutil.parser.parse(alert_data['timestamp'])

        if alert_data['success'] == True and alert_data['action'] == 'disabled':
            template_name = f'azure-ad-auto-lockdown-{RUX_SCORE_USAGE}'
            subject = f'AD Auto Lockdown: Account Automatically {action.title()}'

        if alert_data['success'] == False and not alert_data['action'] == 'revoke_session':
            template_name = f'azure-ad-auto-lockdown-failed-{RUX_SCORE_USAGE}'
            action = {'enabled': 're-enable', 'disabled': 'disable'}[action]
            subject = f'AD Auto Lockdown: Account Failed to Automatically {action}'

        if alert_data['success'] == True and alert_data['action'] == 'revoke_session':
            template_name = f'azure-ad-auto-lockdown-revoke-{RUX_SCORE_USAGE}'
            subject = 'Successfully revoked sessions'

        if alert_data['success'] == False and alert_data['action'] == 'revoke_session':
            template_name = f'azure-ad-auto-lockdown-failed-revoke-{RUX_SCORE_USAGE}'
            subject = 'Failed to revoke sessions'

        # account should primarily be a LinkedAccount and as a fallback to Account, this is to handle the reference
        # to uid which is named differenlty e.g. LinkedAccount - display_uid, Account - uid.
        if isinstance(account, LinkedAccount):
            formatted_account_uid = account.display_uid
        elif isinstance(account, Account):
            formatted_account_uid = account.uid
            LOG.warning("AD notification render received an Account object as a fallback.", extra={"account_id": account.id})

        # TODO APP-10590 generalize this to a jinja filter
        # this prevents outlook, etc. from automatically making the account a mailto: hyperlink
        formatted_account_uid = Markup(formatted_account_uid.replace('@', '<span>@</span>'))

        render_data = {
            'notification_subject': subject,
            'formatted_account_uid': formatted_account_uid,
            'account': account,
            'scores': scores,
            'action': action,
            'auto': True,
            'user': None,
            'timestamp': timestamp,
            'auto_settings': auto_settings,
        }

        return self.render_email_template(template_name, render_data)


class HostLockdownNotificationRenderer(EmailRenderer):
    @staticmethod
    def verbs_for(edr_type, action):
        return defaultdict(
            lambda: {
                'locked': {'verb': 'isolated', 'verb_infinitive': 'isolate', 'inverse_verb': 'unisolated'},
                'unlocked': {'verb': 'unisolated', 'verb_infinitive': 'unisolate', 'inverse_verb': 'isolated'},
            }
        )[edr_type][action]

    def render_for_email(self, alert_data):
        """
        alert_data: {
            'success': True | False,
            'action': 'locked' | 'unlocked',
            'edr_type': 'windows_defender',
            'host_id': id of the host,
            'user_id': id of the user who initially locked | None if auto,
            'actor_id': id of user who took action | None if auto,
            'timestamp': time the action was taken,
            'timeout_timestamp': timeout associated with the action | None
            'locked_duration_min': (int) time in minutes that the host was - or is anticipated to be - locked,
        }
        """
        action = {'type': alert_data['action']}
        action.update(self.verbs_for(alert_data['edr_type'], alert_data['action']))

        actor = User.objects.filter(id=alert_data['actor_id']).first()
        is_auto = actor is None  # convenient shorthand
        edr_name = EDR_ARTIFACT_TYPES.get(alert_data['edr_type'])

        score_usage = RUX_SCORE_USAGE if conditions.is_cloud() else QUX_SCORE_USAGE
        if alert_data['success']:
            template_name = f'host-lockdown-{score_usage}'
            subject = 'Lockdown: {} Host '.format(edr_name)
            subject += 'Automatically ' if is_auto else 'Manually '
            subject += action['verb'].title()
        else:
            template_name = f'host-lockdown-failed-{score_usage}'
            subject = 'Lockdown: Failed to {} Host'.format(action['verb_infinitive'].title())

        # quick and dirty humanization of timespan to "X hours Y minutes" format
        hrs, mins = divmod(alert_data['locked_duration_min'], 60)
        duration_str = '{} hour{}'.format(hrs, 's' if hrs > 1 else '') if hrs else ''
        duration_str += ' {} minute{}'.format(mins, 's' if mins > 1 else '') if mins else ''

        render_data = {
            'notification_subject': subject,
            'host': host.objects.get(id=alert_data['host_id']),
            'action': action,
            'actor': actor,
            'user': User.objects.filter(id=alert_data['user_id']).first(),
            'is_auto': is_auto,
            'timestamp': dateutil.parser.parse(alert_data['timestamp']),
            'now_timestamp': dateutil.parser.parse(alert_data['now_timestamp']),
            'timeout_timestamp': alert_data['timeout_timestamp'] and dateutil.parser.parse(alert_data['timeout_timestamp']),
            'auto_settings': lockdown_utils.get_host_lockdown_settings(),
            'lock_duration': duration_str,
            'edr_name': edr_name,
        }

        return self.render_email_template(template_name, render_data)


def _ensure_correct_account_instance(account):
    """
    If provided an Account, returns its related LinkedAccount instance.
    If provided a LinkedAccount, returns it directly.
    """
    if isinstance(account, LinkedAccount):
        return account
    elif isinstance(account, Account):
        return account.linked_account
    else:
        # They have very similar interfaces so a bit of caution here won't hurt
        raise TypeError(
            'Argument to _ensure_correct_account_instance can only be %s.%s' % (LinkedAccount.__module__, LinkedAccount.__qualname__)
        )


def _get_linked_account_instance(linked_account_id):
    """
    Retrieves the correct LinkedAccount for id that is assumed to belong a LinkedAccount.
    """
    return _ensure_correct_account_instance(LinkedAccount.objects.get(id=linked_account_id))
