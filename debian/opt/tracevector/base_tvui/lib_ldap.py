# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

"""All LDAP related platform API code"""

import copy
import json

import requests
from base_tvui.cache_utils import conditional_ttl_cache
import urllib3

from base_tvui.lib_cloudbridge import (
    make_delete_request,
    make_get_request,
    make_post_request,
    make_put_request,
)
from base_tvui.settings import (
    CLOUD_ENV,
    PLATFORM_REST_URL,
)
from base_tvui import (
    audit_factory,
    string_utils,
)
from base_tvui.feature_flipper import Flags, flag_enabled
from base_tvui.timeouts import ON_PREM_LDAP_READ_TIMEOUT
from lockdown.lockdown_utils import get_account_lockdown_settings
from pure_utils.log_utils import get_vectra_logger
from cachetools.func import ttl_cache

AUDIT = audit_factory.get_audit_object()
LOG = get_vectra_logger(__name__)


class LdapClient:
    """Class for all LDAP related platform API functionality"""

    @staticmethod
    def get_ldap_service_config_multi(config_type, ad_profile_id):
        """
        Retrieves the LDAP configuration for a given config type and AD profile from the platform
        Returns an initialized data structure no matter what
        """
        config = {
            "enabled": False,
            "profile_name": config_type + "profile",
            "ad_profile_name": "unknown",
            "bind_dn": "",
            "bind_pwd": "",
            "server_uris": [],
            "basedns": [],
            "search_param": "sAMAccountName",
            "connection_timeout": 180,
            "query_timeout": 30,
            "starttls": False,
            "autobind": "NONE",
            "ldaps": False,
            "cache_update_speed": 1800,
        }

        if not ad_profile_id:
            LOG.error(f"[{ad_profile_id}] Please specify an AD profile for which configuration needs to be retrieved")
            return config

        url = "http://{platform_rest_url}/ldap_service/configuration_multi?ad_profile_id={ad_profile_id}&config_type={config_type}".format(
            platform_rest_url=PLATFORM_REST_URL, ad_profile_id=ad_profile_id, config_type=config_type
        )
        try:
            request = make_get_request(url, timeout=(2, 10))
            request.raise_for_status()
            if request.status_code != 204:
                config = request.json()
        except (requests.exceptions.ConnectionError, urllib3.exceptions.ProtocolError):
            LOG.warning(f'[{ad_profile_id}] Failed to connect to ldap_service')
        except Exception:
            LOG.exception(f'[{ad_profile_id}] Platform API call failed (ldap_service/configuration_multi)')

        return config

    @staticmethod
    def get_ldap_service_top_config_multi():
        """
        Retrieves the top level LDAP configuration from the platform. Returns an initialized structure no matter what
        """
        top_config = {"_id": "ldap_service_multi", "name": "ldap_service_multi", "profiles": {}}

        url = f"http://{PLATFORM_REST_URL}/ldap_service/top_configuration_multi"
        try:
            request = make_get_request(url, timeout=(2, 10))
            request.raise_for_status()
            if request.status_code != 204:
                top_config = request.json()
            else:
                top_config['error'] = True
        except (requests.exceptions.ConnectionError, urllib3.exceptions.ProtocolError):
            LOG.warning(f'Failed to connect to ldap_service during GET')
            top_config['error'] = True
        except Exception:
            LOG.exception(f'Platform API GET call failed (ldap_service/top_configuration_multi)')
            top_config['error'] = True

        return top_config

    @staticmethod
    def set_ldap_service_top_config_multi_global(enabled=False):
        """Set any global params in the top level LDAP configuration from the platform"""
        url = f"http://{PLATFORM_REST_URL}/ldap_service/top_configuration_multi"
        status = 500
        try:
            params = {'enabled': enabled}
            request = make_post_request(url, params=params, timeout=(2, 10))
            request.raise_for_status()
            status = request.status_code
        except (requests.exceptions.ConnectionError, urllib3.exceptions.ProtocolError):
            LOG.warning(f'Failed to connect to ldap_service during POST')
        except Exception:
            LOG.exception(f'Platform API POST call failed (ldap_service/top_configuration_multi)')

        return status

    @staticmethod
    def delete_ldap_service_config_multi(config_type, ad_profile_id):
        """
        Delete an LDAP configuration for a given type and AD profile/instance from the platform.
        Returns HTTP status code of delete request to ldap_service
        """

        # For now, only allow deleting config_type of `query` so we don't mess up sso
        assert config_type == 'query'
        status = 500

        if not ad_profile_id:
            LOG.error(f"[{ad_profile_id}] Please specify an AD profile for which configuration needs to be deleted")
            return status

        url = 'http://{platform_rest_url}/ldap_service/configuration_multi?ad_profile_id={ad_profile_id}&config_type={config_type}'.format(
            platform_rest_url=PLATFORM_REST_URL, ad_profile_id=ad_profile_id, config_type=config_type
        )
        try:
            request = make_delete_request(url, timeout=(2, 10))
            request.raise_for_status()
            status = request.status_code
        except (requests.exceptions.ConnectionError, urllib3.exceptions.ProtocolError):
            LOG.warning(f'[{ad_profile_id}] Failed to connect to ldap_service')
        except Exception as e:
            LOG.exception(f'[{ad_profile_id}] Platform API call failed (ldap_service/configuration_multi)')
        return status

    @staticmethod
    def get_ldap_service_details_multi_all(
        distinguishedName=None, userPrincipalName=None, servicePrincipalName=None, uid=None, force_pull=False
    ):
        """
        Queries the ldap_service for LDAP details of an account using provided search parameters and for all configured AD profiles
        Params:
            distinguishedName
            userPrincipalName
            servicePrincipalName
            uid
            force_pull: whether to issue a new query to active directory, bypassing the ldap_service cache
        Returns:
            dict with
              key: AD profile name
              value: (dict) LDAP context information
            (None) if none found
        """
        url = f'http://{PLATFORM_REST_URL}/ldap_service/query_multi_all'

        if distinguishedName:
            params = {'dn': distinguishedName}
        elif userPrincipalName:
            params = {'upn': userPrincipalName}
        elif servicePrincipalName:
            params = {'spn': servicePrincipalName}
        elif uid:
            params = {'uid': uid}
        else:
            LOG.error(
                f"ldap_service details called without identifying information: dn={distinguishedName}, upn={userPrincipalName}, spn={servicePrincipalName}, uid={uid}"
            )
            return None

        params['force_pull'] = force_pull

        update_lockdown_params(params)

        try:
            # Send the timeout for On-Prem/Cloud to ldap_service so that the service timesout within this time
            params['read_timeout'] = CLOUD_ENV.CBI_READ_TIMEOUT if CLOUD_ENV else ON_PREM_LDAP_READ_TIMEOUT
            resp = make_get_request(url, params=params)
            resp.raise_for_status()
            return resp.json()
        except requests.Timeout:
            LOG.warning(f'Timed out attempting to lookup ldap_service details from all AD profiles')
        except (requests.exceptions.ConnectionError, requests.exceptions.HTTPError, urllib3.exceptions.ProtocolError):
            LOG.warning(f'Failed to connect to ldap_service while retrieving details from all AD profiles')
        except Exception:
            LOG.exception(
                f'Failed to lookup ldap for: dn={distinguishedName}, upn={userPrincipalName}, spn={servicePrincipalName}, uid={uid} while retrieving details from all AD profiles'
            )

        return None

    @staticmethod
    def get_ldap_service_details_all(
        distinguishedName=None, userPrincipalName=None, servicePrincipalName=None, uid=None, force_pull=False, flatten=False
    ):
        """Queries the ldap_service for LDAP details of an account using provided search parameters and for
           all profiles for MultiAD or the default profile for single AD
        Params:
            distinguishedName
            userPrincipalName
            servicePrincipalName
            uid
            force_pull: whether to issue a new query to active directory, bypassing the ldap_service cache
            flatten: If True, return the details only from the default-ad profile or the first available
                     AD profile if no default exists.
                     If False, return data from all profiles in a 'profiles' hierarchy
                     Note: Once upper layers esp. VUI REST API can recognize data from multiple AD profiles, we can remove this flattening code
        Returns:
            (dict) LDAP all profiles context information or just for one profile
            (None) if none found
        """
        profiles = LdapClient.get_ldap_service_details_multi_all(
            distinguishedName=distinguishedName,
            userPrincipalName=userPrincipalName,
            servicePrincipalName=servicePrincipalName,
            uid=uid,
            force_pull=force_pull,
        )
        if profiles and flatten:
            # Find if a default-ad profile exists
            ad_default_profile_id = next((k for k, v in profiles.items() if v and v.get('ad_profile_name', None) == 'default-ad'), None)
            # Use data from default-ad profile if it exists otherwise use the first available AD profile
            profile = profiles[ad_default_profile_id] if ad_default_profile_id else next(iter(profiles.values()))
            profile.pop('ad_profile_name', None)
            # No 'profiles' hiearchy ie it is flattened
            return profile
        return {"profiles": profiles}

    @staticmethod
    def get_ldap_service_batch_details_multi_all(distinguishedNames=None, userPrincipalNames=None, servicePrincipalNames=None):
        """
        Queries the ldap_service for LDAP details of multiple accounts using provided search parameters and for all configured AD profiles
        Params:
            distinguishedNames: list
            userPrincipalNames: list
            servicePrincipalNames: list
            force_pull: whether to issue a new query to active directory, bypassing the ldap_service cache
        Returns:
            dict with
              key: AD profile name
              value: (list) LDAP context information
            (None) if none found
        """
        params = {}
        url = f'http://{PLATFORM_REST_URL}/ldap_service/query_multi_all'
        if not distinguishedNames and not userPrincipalNames and not servicePrincipalNames:
            LOG.error(
                f"ldap_service details called without identifying information: dns={distinguishedNames}, upns={userPrincipalNames}, spns={servicePrincipalNames}"
            )
            return None

        data = {
            'distinguishedNames': distinguishedNames,
            'userPrincipalNames': userPrincipalNames,
            'servicePrincipalNames': servicePrincipalNames,
        }
        try:
            # Send the timeout for On-Prem/Cloud to ldap_service so that the service timesout within this time
            params['read_timeout'] = CLOUD_ENV.CBI_READ_TIMEOUT if CLOUD_ENV else ON_PREM_LDAP_READ_TIMEOUT
            resp = make_post_request(url, json=data, params=params)
            resp.raise_for_status()
            return resp.json()
        except requests.Timeout:
            LOG.warning(f'Timed out attempting to lookup ldap_service batch details from all AD profiles')
        except (requests.exceptions.ConnectionError, requests.exceptions.HTTPError, urllib3.exceptions.ProtocolError):
            LOG.warning(f'Failed to connect to ldap_service while retrieving batch details from all AD profiles')
        except Exception:
            LOG.exception(
                f'Failed to lookup ldap for: dns={distinguishedNames}, upns={userPrincipalNames}, spns={servicePrincipalNames} while retrieving batch details from all AD profiles'
            )

        return None

    @staticmethod
    def get_ldap_associated_accounts(account_uid):
        """Queries the ldap_service for all accounts associated with 'account_uid'

        Args:
            account_uid (string): Unique identifier for ldap account query

        Returns:
            (list): list of account uids associated with 'account_uid'
        """
        url = f'http://{PLATFORM_REST_URL}/ldap_service/associated_accounts_multi_all'
        params = {'uid': account_uid}

        update_lockdown_params(params)

        try:
            LOG.info('Querying {} for accounts associated with : {}.'.format(url, account_uid))
            resp = make_get_request(url, params=params, timeout=10)
            resp.raise_for_status()
            if resp.status_code == 200:
                return resp.json()

        except requests.Timeout:
            LOG.warning('Timed out attempting while trying to query ldap_service')
        except (requests.exceptions.ConnectionError, requests.exceptions.HTTPError, urllib3.exceptions.ProtocolError):
            LOG.warning('Failed to query ldap_service')
        except Exception as e:
            LOG.exception('Failed to lookup ldap associated accounts for: uid={} | {}'.format(account_uid, str(e)))

        return None

    @staticmethod
    @conditional_ttl_cache(ttl=600, maxsize=100, condition=lambda x: x is not None)
    def get_ldap_associated_accounts_cached(account_uid):
        return LdapClient.get_ldap_associated_accounts(account_uid)

    @staticmethod
    def save_ldap_service_config_multi(config_type, ad_profile_id, cred_data, audit_user_info):
        """
        Persist the ldap configuration for a given config type and AD profile to the platform.
        Returns internal configuration saved if successful, False if there is a failure
        """

        if not ad_profile_id:
            LOG.error(f"[{ad_profile_id}] Please specify an AD profile for which configuration needs to be saved")
            return False

        config_doc = LdapClient.get_ldap_service_config_multi(config_type, ad_profile_id)
        old_config = copy.deepcopy(config_doc)

        config_doc['enabled'] = cred_data.get('enabled', False)
        config_doc['profile_name'] = cred_data.get('profile_name', config_type + "profile")
        config_doc['ad_profile_name'] = cred_data.get('ad_profile_name')
        config_doc['bind_dn'] = cred_data.get('bind_dn', '')
        config_doc['bind_pwd'] = cred_data.get('bind_pwd', '')
        config_doc['basedns'] = cred_data.get('basedns', [])
        config_doc['server_uris'] = cred_data.get('server_uris', [])
        config_doc['search_param'] = cred_data.get('search_param', '')
        config_doc['connection_timeout'] = cred_data.get('connection_timeout', 9999)
        config_doc['query_timeout'] = cred_data.get('query_timeout', 9999)
        config_doc['starttls'] = cred_data.get('starttls', False)
        config_doc['autobind'] = cred_data.get('autobind', 'NONE')
        config_doc['ldaps'] = cred_data.get('ldaps', False)

        ret_val = False
        config_saved = False

        try:
            url = 'http://{platform_rest_url}/ldap_service/configuration_multi'.format(platform_rest_url=PLATFORM_REST_URL)
            data = {'config_type': config_type, 'config': config_doc, 'ad_profile_id': ad_profile_id}
            resp = make_put_request(url, json=data)
            if resp.json().get('success'):
                config_saved = True
                ret_val = config_doc
                # Hide passwords from audit logging
                config_doc['bind_pwd'] = '******'
                LOG.info(f'[{ad_profile_id}] LDAP service configuration persisted. %s', config_doc)
        except Exception:
            LOG.exception(f'[{ad_profile_id}] Unexpected exception attempting to persist the LDAP service configuration')

        # Hide passwords from audit logging
        old_config['bind_pwd'] = '******'
        old_config = string_utils.truncate(json.dumps(old_config), AUDIT.VALUE_SIZE_LIMIT)
        new_config = string_utils.truncate(json.dumps(config_doc), AUDIT.VALUE_SIZE_LIMIT)
        AUDIT.audit(
            audit_user_info, config_saved, f'[{ad_profile_id}] change of ldap service configuration from {old_config} to {new_config}'
        )
        return ret_val

    def test_ldap_query_connection_multi_all(self, with_lockdown=False):
        """
        Test LDAP query connection using health endpoint for all AD profiles
        Params:
            with_lockdown (bool) - whether to also check for correct lockdown permissions
            timeout: in seconds that ldap service will wait for running the tests for all profiles
        Returns:
            dict with
              key: AD profile name
              value: list of ldap servers and their respective connection statuses:
                [{'server_uri': '1.2.3.4', 'status': 'success', 'message': 'Connection message here'}]
                None if the connector is disabled
        """
        try:
            url = f'http://{PLATFORM_REST_URL}/ldap_service/test_configurations_multi_all'
            params = {'config_type': 'query'}
            if with_lockdown:
                params['with_lockdown'] = True
            # Send the timeout for On-Prem/Cloud to ldap_service so that the service timesout within this time
            params['read_timeout'] = CLOUD_ENV.CBI_READ_TIMEOUT if CLOUD_ENV else ON_PREM_LDAP_READ_TIMEOUT
            response = make_get_request(url, params=params)
            response.raise_for_status()
            return response.json()

        except Exception:
            LOG.exception(f'Platform API call failed (ldap_service/test_configurations_multi_all)')
            # Exception in getting connection status of the configured AD profiles' AD servers (URIs)
            return None

    def get_ad_metrics_multi(self, ad_profile_id):
        """Get metrics regarding Active Directory eg. number of AD accounts found for the given AD profile"""
        if not ad_profile_id:
            LOG.error("Please specify an AD profile for which Active Directory metrics need to be retrieved")
            return None

        try:
            url = 'http://{}/ldap_service/ad_metrics_multi'.format(PLATFORM_REST_URL)
            params = {'ad_profile_id': ad_profile_id}
            response = make_get_request(url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception:
            LOG.error(f'[{ad_profile_id}] Platform API call failed (ldap_service/ad_metrics)')
            return {}

    def get_ad_metrics_bulk_multi_all(self, uids):
        """Get metrics of how many UIDs exist in cache for all AD profiles matching the UIDs list specified"""
        location = 'ldap_service/ad_metrics_bulk_multi_all'
        url = f'http://{PLATFORM_REST_URL}/{location}'

        params = {'uids': uids}
        try:
            response = make_get_request(url=url, params=params)
            response.raise_for_status()
            return response.json()
        except Exception:
            LOG.exception(f'Platform API call failed ({location})')
            return {}

    def test_ldap_config_connection_multi(self, config, ad_profile_id, with_lockdown=False):
        """Test an LDAP config connection without saving using the health endpoint"""
        if not ad_profile_id:
            LOG.error(f"[{ad_profile_id}] Please specify an AD profile for which configuration needs to be tested")
            return [{'server_uri': '', 'status': 'error', 'message': 'Could not check status. No AD profile specified'}]

        params = {'with_lockdown': with_lockdown, 'ad_profile_id': ad_profile_id}
        try:
            response = make_post_request(
                'http://' + PLATFORM_REST_URL + '/ldap_service/test_configurations_multi', params=params, json=config
            )
            response.raise_for_status()
            return response.json()
        except Exception:
            LOG.exception(f'[{ad_profile_id}] Platform API call failed (ldap_service/test_configurations_multi)')
            return [{'server_uri': '', 'status': 'error', 'message': 'Could not check status'}]

    @staticmethod
    @ttl_cache(ttl=3000)
    def get_ldap_groups_multi():
        """Query latest list of available groups for import for all ad_profiles

        Returns:
            dict: dictionary with keys as ad_profile_ids and values as lists of groups
        """
        params = {}
        url = f'http://{PLATFORM_REST_URL}/ldap_service/groups_list_multi'  # Endpoint defined above

        # attempt query
        try:
            # Send the timeout for On-Prem/Cloud to ldap_service so that the service timesout within this time
            params['read_timeout'] = CLOUD_ENV.CBI_READ_TIMEOUT if CLOUD_ENV else ON_PREM_LDAP_READ_TIMEOUT
            resp = make_get_request(url, params=params)
            resp.raise_for_status()
            return resp.json()
        except Exception:
            LOG.exception(f'Platform API call failed (ldap_service/groups_list_multi)')
            raise


def update_lockdown_params(params):
    """
    Updates request parameters for LDAP requests based on the account lockdown settings.

    Args:
    - params (dict): A dictionary containing parameters for a LDAP request.
    """
    if flag_enabled(Flags.account_lockdown_account_expires):
        lockdown_settings = get_account_lockdown_settings()

        if lockdown_settings.get('lockdown_account_expires_attribute_enabled') is not None:
            params['account_expires_attribute_enabled'] = lockdown_settings['lockdown_account_expires_attribute_enabled']
