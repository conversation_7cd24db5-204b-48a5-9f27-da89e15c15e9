from functools import wraps
from pure_utils import log_utils

from base_tvui import bin_utils

bin_utils.init_django()

from base_tvui.lib_cloud_metrics import MetricName, fail_success_metric
from distillation.common import is_distillation_enabled, DataSourceType
from distillation.distillation_framework import learner

module = "bin.build_learner_state"
LOG = log_utils.get_vectra_logger(module)


def fail_success_metric_learner_state(func):
    """Pass arguments to `build_learner_state` on to `fail_success_metric` to make labels dynamic"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        decorator = fail_success_metric(
            name=MetricName.DISTILLATION_BUILD_LEARNER_STATE_PROCESS_FAIL,
            labels={"data_source_type": kwargs["data_source_type"].value},
        )
        decorated = decorator(func)
        return decorated(*args, **kwargs)

    return wrapper


@fail_success_metric_learner_state
def build_learner_state(*, data_source_type: DataSourceType) -> None:
    if is_distillation_enabled(data_source_type):
        LOG.info(f'Building distillation learner state for {data_source_type}')
        learner.build_learner_state(data_source_type=data_source_type)


def main():
    """Run building learner state once for each data source type"""
    build_learner_state(data_source_type=DataSourceType.O365)
    build_learner_state(data_source_type=DataSourceType.AWS)
    build_learner_state(data_source_type=DataSourceType.AZURE)
    build_learner_state(data_source_type=DataSourceType.NETWORK)


if __name__ == '__main__':
    main()
