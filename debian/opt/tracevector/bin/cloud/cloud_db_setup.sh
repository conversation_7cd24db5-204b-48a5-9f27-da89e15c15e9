#!/bin/bash

#### DO NOT REMOVE: SCRIPT SHOULD FAIL IF ANY COMMAND FAILS ####
set -e
#### DO NOT REMOVE: SCRIPT SHOULD FAIL IF ANY COMMAND FAILS ####

log () {
    echo $(date) - MIGRATION - $*
}


function exit_function(){
  EXIT_CODE=$?
  log "Finish execution with exit code ${EXIT_CODE}"
  if [ $EXIT_CODE -eq 0 ]
  then
    log "Execution correct"
    metric db_setup_error 0
  else
    log "Execution incorrect"
    metric db_setup_error 1
  fi
}
trap exit_function EXIT


push_metric () {
  DATA=$1
  # echo "$DATA"
  PUSH_GATEWAY="prometheus-pushgateway.prometheus-stack.svc.cluster.local:9091"
  curl -s -X POST -H "Content-type: text/plain" --data "$DATA"$'\n' http://${PUSH_GATEWAY}/metrics/job/db-setup || {
    log "Error pushing metric, return code: $?"
  }
}

NAMESPACE=vui-${INTERNAL_BRAIN}

metric () {
  METRIC_NAME=$1
  VALUE=$2
  push_metric "${METRIC_NAME}"'{namespace="'"${NAMESPACE}"'"} '"${VALUE}"
}

clean_metrics() {
  push_metric "${CLEANING}"
}

MEASURES=`cat << EOF
# HELP db_setup_task_time_milliseconds_summary A summary of the task time
# TYPE db_setup_task_time_milliseconds_summary summary
EOF
`

CLEANING=`echo "${MEASURES}"$'\n'`

measure () {
  TASK=$1
  MEASURE=$2

  data=`cat << EOF
db_setup_task_time_milliseconds_summary_sum{namespace="${NAMESPACE}",task="${TASK}"} ${MEASURE}
db_setup_task_time_milliseconds_summary_count{namespace="${NAMESPACE}",task="${TASK}"} 1
EOF
`

  data_cleaning=`cat << EOF
db_setup_task_time_milliseconds_summary_sum{namespace="${NAMESPACE}",task="${TASK}"} 0
db_setup_task_time_milliseconds_summary_count{namespace="${NAMESPACE}",task="${TASK}"} 0
EOF
`

  MEASURES=`echo "${MEASURES}"$'\n'"$data"`
  CLEANING=`echo "${CLEANING}"$'\n'"$data_cleaning"`

  push_metric "$MEASURES"
}

log "Starting DB Migration script"
metric db_setup_ongoing 1


log "Checking if the setup should be skipped: SKIP_DB_SETUP should be 1 and is '${SKIP_DB_SETUP}'"
if [ "${SKIP_DB_SETUP}" = "1" ]; then
    log "SKIP_DB_SETUP is set to 1. Exiting immediately."
    exit 8
fi
log "Not to skip, proceed"

# Do not wait, but keep the metric for comparison
WAIT_TIME=0

metric db_setup_initial_wait_time $WAIT_TIME

before=$(date +%s%3N)
sleep $WAIT_TIME
measure initial_wait $(($(date +%s%3N) - before))

log "Proceeding with the migration"

# Start the time to measure
SECONDS=0

EXIT_CODE=0
EXISTING_VERSION="$2"

# Bypass SSL for local dev
if [ "$1" != "--insecure" ];
then
    MYSQL_SSL_OPTS="--ssl-ca /etc/ssl/rds-combined-ca-bundle.pem --ssl-mode=VERIFY_IDENTITY"
fi

MYSQL_BASE="mysql -u${MARIADB_USER} -p${MARIADB_PW} -h${MARIADB_HOSTNAME} ${MYSQL_SSL_OPTS} ${MARIADB_DB}"

check_connection() {
    local retries=0
    local delay=1
    # A max retry that is about 30 minutes
    local max_retry=40

    log "Looping until done validating connection to RDS."
    while true; do
      isvalid=$(${MYSQL_BASE} -se "SELECT 1;")
      if [ $isvalid = "1" ]; then
          log "Validated DNS OK"
          return 0
      else
          (( retries++ ))
          log "Connection failed $retries times. Backoff for $delay ."
          sleep $delay
          delay=$(( delay * 2 ))
          # Max out at a minute per retry
          if (( delay >= 60 )); then
              delay=60
          fi
          if (( retries >= max_retry )); then
              log "Aborting DB-Setup, reached max DNS retries."
              exit 1
          fi
      fi
    done
}

before=$(date +%s%3N)
check_connection
measure connection_check $(($(date +%s%3N) - before))

before=$(date +%s%3N)
if [ -z "${SKIP_NTC_MIGRATION_CHECK}" ];
then
    NTC_MIGRATION_IN_PROGRESS=$(${MYSQL_BASE} -sse "select operation_state from tvui_ntc_migration_state_log order by id desc limit 1;" || true)
    if [ "${NTC_MIGRATION_IN_PROGRESS}" == "in_progress" ];
    then
        log "Aborting DB-Setup, NTC migration is in progress."
        exit 1
    fi

    # If ntc migration flag is enabled or has executed in the past day (a rollback could be impending) we consider that a migration stage might be
    # triggered so require acquiring an advisory lock to prevent both db-setup and ntc migration running concurrently.
    # Note: these need to be separate queries as in certain fail cases for the ntc migration the tvui_setting table might not exist.
    NTC_MIGRATION_SCHEDULED_QUERY="select 1 where exists (select * from tvui_setting where \`group\` = 'feature'\
        and \`key\` = 'network_sql_migration_import' and \`value\` = 'on');"
    NTC_MIGRATION_SCHEDULED=$(${MYSQL_BASE} -sse "$NTC_MIGRATION_SCHEDULED_QUERY" || true)
    NTC_MIGRATION_RUN_RECENTLY_QUERY="select 1 where exists (select * from tvui_ntc_migration_state_log where created_at > Now() - INTERVAL 1 DAY)"
    NTC_MIGRATION_RUN_RECENTLY=$(${MYSQL_BASE} -sse "$NTC_MIGRATION_RUN_RECENTLY_QUERY" || true)

    if [ "${NTC_MIGRATION_SCHEDULED}" == "1" ] || [ "${NTC_MIGRATION_RUN_RECENTLY}" == "1" ];
    then
        log "NTC migration is scheduled or has run recently so rollback could be triggered. Attempting to acquire advisory lock."

        mkfifo /tmp/mysql.stdin.pipe /tmp/mysql.stdout.pipe
        ${MYSQL_BASE} -ss --unbuffered < /tmp/mysql.stdin.pipe > /tmp/mysql.stdout.pipe &

        PID_MYSQL=$!

        cleanup_proc_pipe() {
            kill $PID_MYSQL
            rm -rf /tmp/mysql.stdin.pipe /tmp/mysql.stdout.pipe
        }
        trap cleanup_proc_pipe EXIT

        exec 10>/tmp/mysql.stdin.pipe
        exec 11</tmp/mysql.stdout.pipe

        cleanup_fd() {
            exec 10>&-
            exec 11>&-
            cleanup_proc_pipe
        }
        trap cleanup_fd EXIT

        echo "SELECT GET_LOCK(\"$EXTERNAL_VUI-ntcmigration\", 10);" >&10

        release_lock() {
            echo "SELECT RELEASE_LOCK(\"$EXTERNAL_VUI-ntcmigration\");" >&10
        }

        if ! read -t 11 line <&11; then
            log "Aborting DB-Setup, failed to read from mysql process getting advisory lock"
            exit 1
        elif [[ $line == 1 ]]; then
            log "Advisory lock acquired successfully"
            trap release_lock EXIT
        else
            log "Aborting DB-Setup, timeout waiting for advisory lock"
            exit 1
        fi
    fi
fi
measure ntc_check $(($(date +%s%3N) - before))

before=$(date +%s%3N)
# Only run schema_empty.sql if it hasn't yet been run (it is very destructive if ran more than once).
# If this check fails, the whole script should exit due to '-e' flag, but we also check exit code below just in case.
DB_EXISTS=$(${MYSQL_BASE} -sse "select count(*) from information_schema.tables where table_name='auth_group' and table_schema='${MARIADB_DB}';")
if [ $? -ne 0 ];
then
    log "Failed to check whether schema_empty.sql has been run, failing!"
    exit 1
fi
measure check_schema_empty $(($(date +%s%3N) - before))

before=$(date +%s%3N)
if [ "${DB_EXISTS}" == "0" ];
then
    log "Running schema_empty.sql to initialize empty db"
    ${MYSQL_BASE} < /src/tracevector/schema_empty.sql
    log "We ran schema empty and got the return code $?"
else
    log "Database already initialized, skipping schema_empty.sql"
fi
measure run_schema_empty $(($(date +%s%3N) - before))

readonly VUI_SETTINGS='/etc/vectra_configs/settings.yaml'

before=$(date +%s%3N)
# We are getting the deployment type from the settings file on disk. This will either be set to "onprem" or "cloud".
# We then supply the deployment type value into the mysql --init-command so the vectra_deployment variable is available for execution.
# If a valid value is not passed in, the postinst will exit with an error. The migration scripts that follow will not be run.
# As a reminder, If scripts apply to OnPrem Vui, please add scripts execution to ./debian/debian/vectra-ui.postinst
DEPLOYMENT_TYPE=$(grep '^deployment:' $VUI_SETTINGS | cut -d : -f 2 | tr -d ' ')
log "Running schema migrations"
# Running the schema migrations, adding a message if it fails
pyvui /src/tracevector/schema_migrations/run_schema_migrations.py --environment ${DEPLOYMENT_TYPE} || { echo 'MIGRATION - Failed to apply schema migrations'; exit 1; }
measure run_schema_migrations $(($(date +%s%3N) - before))

log "Running rbac_setup.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/rbac_setup.py
measure rbac_setup $(($(date +%s%3N) - before))

log "Running cognito_user.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/cognito_user.py
measure cognito_user $(($(date +%s%3N) - before))

log "Running generate_detection_schemas.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/dynamic_detections/generate_detection_schemas.py
measure generate_detection_schemas $(($(date +%s%3N) - before))

log "Running migrate_assignments_v2.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_assignments_v2.py
measure migrate_assignments_v2 $(($(date +%s%3N) - before))

log "Running fix_aws_seqid.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/fix_aws_seqid.py
measure fix_aws_seqid $(($(date +%s%3N) - before))

log "Running migrate_azure_ad_disable_account_access.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_azure_ad_disabled_account_access.py
measure migrate_azure_ad_disable_account_access $(($(date +%s%3N) - before))

log "Running migrate_o365_spearphising_subject.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_o365_spearphishing_subject.py # CS-5694: Migrate sw_o365_spearphishing subject to not be null
measure migrate_o365_spearphising_subject $(($(date +%s%3N) - before))

log "Running migrate_o365_trusted_networks_modified_ips.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_o365_trusted_networks_modified_ips.py # SAASAPPS-1154: Migrate sw_o365_trustedNetworksModified ips to be in correct format
measure migrate_o365_trusted_networks_modified_ips $(($(date +%s%3N) - before))

log "Running generate_distillation_smart_rules.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/distillation_scripts/generate_distillation_smart_rules.py
measure generate_distillation_smart_rules $(($(date +%s%3N) - before))

log "Running generate_edr_schemas.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/dynamic_edrs/generate_edr_schemas.py # SAASAPPS-3115 Add schemas for Cloudbridge
measure generate_edr_schemas $(($(date +%s%3N) - before))

log "Running migrate_detection_data_source.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_detection_data_source.py # SAASAPPS-3350 Migrate data source type for old detections
measure migrate_detection_data_source $(($(date +%s%3N) - before))

log "Running generate_groups.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/predefined_groups/generate_groups.py --current_version  "$EXISTING_VERSION" # SAASAPPS-3564 Add schemas for Cloudbridge
measure generate_groups $(($(date +%s%3N) - before))

log "Running generate_triage_templates.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/triage_templates/generate_triage_templates.py # SAASAPPS-3564 Add schemas for Cloudbridge
measure generate_triage_templates $(($(date +%s%3N) - before))

log "Running migrate_o365_suspicious_sign_on_remote1_dns_to_flex7.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_o365_suspicious_sign_on_remote1_dns_to_flex7.py # CS-7820: Add flex7 field for Azure AD Suspicious Sign-On detection
measure migrate_o365_suspicious_sign_on_remote1_dns_to_flex7 $(($(date +%s%3N) - before))

log "Running configure_flags_for_db_setup.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/cloud/configure_flags_for_db_setup.py # TITAN-1955 Enable setting flags at provision time for cloud.
measure configure_flags_for_db_setup $(($(date +%s%3N) - before))

log "Running migrate_incorrect_detail_fields.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_incorrect_detail_fields.py
measure migrate_incorrect_detail_fields $(($(date +%s%3N) - before))

log "Running migrate_account_alarmed.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_account_alarmed.py # BRIDGE-576: move Account alarm* feilds to LinkedAccount
measure migrate_account_alarmed $(($(date +%s%3N) - before))

log "Running migrate_endpoint_blocklist.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_endpoint_blocklist.py # CAT-3234 Used to add blocked endpoints to the tvui_setting table
measure migrate_endpoint_blocklist $(($(date +%s%3N) - before))

log "Running entity_state_fix.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/entity_state_fix.py # CS-9302: [KEEP] set entity state to inactive if urgency score is 0
measure entity_state_fix $(($(date +%s%3N) - before))

log "Running fix_lockdown_settings.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/cloud/fix_lockdown_settings.py # CS-9427: fix entity importance mapping
measure fix_lockdown_settings $(($(date +%s%3N) - before))

log "Running migrate_aws_suspect_public_ebs_access_category.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_aws_suspect_public_ebs_access_category.py || true # BRIDGE-1184 Migrate aws_suspect_public_ebs_access category
measure migrate_aws_suspect_public_ebs_access_category $(($(date +%s%3N) - before))

log "Running migrate_azurecp.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_azurecp.py # BRIDGE-1191 Migrate detections with azurecp as data source type to azure-cp
measure migrate_azurecp $(($(date +%s%3N) - before))

log "Running migrate_info_detections_to_scored.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_info_detections_to_scored.py # BRIDGE-1166 Migrate info dets to scored
measure migrate_info_detections_to_scored $(($(date +%s%3N) - before))

log "Running populate_entra_first_party_apps.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/entity_context/populate_entra_first_party_apps.py # BRIDGE-1217 [KEEP]: Updates mapping of Microsoft first party app and app names if json is new
measure populate_entra_first_party_apps $(($(date +%s%3N) - before))

log "Running ensure_autotriage_job_contexts.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/bulk_autotriage_scripts/ensure_autotriage_job_contexts.py # CS-9565: [KEEP until QUX migration fixed] QUX to RUX migraiton incorrectly misses ai triage job context table
measure ensure_autotriage_job_contexts $(($(date +%s%3N) - before))

log "Running migrate_azure_detection_ip_field.py"
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_azure_detection_ip_field.py # BRIDGE-1367 Migrate azure detection ip fields from src_ip to flex_json.caller_ip_address
measure migrate_azure_detection_ip_field $(($(date +%s%3N) - before))

log "Running migrate_sw_o365_dllHijack_category.py "
before=$(date +%s%3N)
pyvui /src/tracevector/bin/migrate_sw_o365_dllHijack_category.py # BRIDGE-1483 Migrate sw_o365_dllHijack_category detection category
measure migrate_sw_o365_dllHijack_category $(($(date +%s%3N) - before))


log "MIGRATION - Cleanup "
before=$(date +%s%3N)
# TODO[APP-13704]: cleanup
# Don't delete deactivated users such as those migrated from appliance to cloud.
${MYSQL_BASE} -e "DELETE FROM auth_user_groups WHERE user_id IN (SELECT id FROM auth_user WHERE is_active = 1 AND account_type NOT IN ('JWT', 'API_CLIENT', 'SPECIAL'))"
${MYSQL_BASE} -e "DELETE FROM django_admin_log WHERE user_id IN (SELECT id FROM auth_user WHERE is_active = 1 AND account_type NOT IN ('JWT', 'API_CLIENT', 'SPECIAL'))"
${MYSQL_BASE} -e "DELETE FROM auth_user WHERE is_active = 1 AND account_type NOT IN ('JWT', 'API_CLIENT', 'SPECIAL')"
measure cleanup $(($(date +%s%3N) - before))

log "Migration finished. It took $SECONDS seconds, after a wait of $WAIT_TIME seconds."
metric db_setup_total_time_seconds $SECONDS

metric db_setup_ongoing 0
log "Waiting 60 seconds to launch clean metrics"
sleep 60
clean_metrics
log "Metrics clean, exiting"

command || EXIT_CODE=$?
echo $EXIT_CODE
