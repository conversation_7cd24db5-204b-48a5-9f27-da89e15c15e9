{"version": 1, "type": "sql_inject", "name": "SQL Injection Activity", "category": "lateral", "dataSourceType": "network", "attackGraphProcessor": {"fields": {"targets": {"handler": "internal_host_target", "field": "internal_web_server"}}}, "detectionTargetProcessor": {"fields": {"targets": {"handler": "detection_target_host_session", "field": "dst_session_luid"}}}, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "Internal Web Server", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_host": {"title": "Internal Target Host", "api_name": "internal_target_hostname", "detail_fields": ["dst_host_id"]}, "flex3": {"title": "SQL Fragment", "api_name": "sql_fragment", "detail_fields": ["flex3"]}, "flex4": {"title": "Response Code", "api_name": "response_code", "detail_fields": ["flex4"]}, "remote2_ip": {"title": "X-Forwarded-For", "api_name": "x_forwarded_for", "detail_fields": ["flex5"]}}}, "syslogProcessor": {"fields": {"http_segment": "flex1", "user_agent": "flex2", "sql_fragment": "flex3", "response_code": "flex4", "x_forwarded_for": "flex5", "x_forwarded_for_host_session_luid": "flex6"}}, "scoring": {"detection": {"method": "python", "threatLow": 30, "threatHigh": 80, "certaintyLow": 10, "certaintyHigh": 95}, "host": {"archetypeBucket": "lateralM", "activeTime": 21}}, "bucketing": {"extraTupleFields": []}}