{"version": 1, "type": "reverse_rat", "name": "External Remote Access", "category": "cnc", "dataSourceType": "network", "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_host"}}}, "syslogProcessor": {"fields": {"count": "count"}}, "scoring": {"detection": {"method": "python", "threatLow": 25, "threatHigh": 70, "certaintyLow": 10, "certaintyHigh": 95}, "host": {"archetypeBucket": "cncHC", "activeTime": 30}}, "bucketing": {"extraTupleFields": []}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "External Host", "field": "external_host", "format": "external_host"}, {"label": "Target", "field": "protocol_port", "format": "protocol_port"}, {"label": "<PERSON><PERSON>", "field": "total_bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "total_bytes_rcvd", "format": "bytes"}, {"label": "First Seen", "field": "first_timestamp", "format": "date"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}]}, "summaryDisplay": {"fields": [{"label": "External Hosts", "field": "external_hosts", "format": "concat_slice"}, {"label": "Unique Ports", "field": "num_unique_ports", "format": "basic"}, {"label": "Sessions", "field": "num_sessions", "format": "basic"}, {"label": "Active Time", "field": "active_time", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}]}, "onePagerDisplay": {"content": [{"title": "Triggers", "content": ["An internal host is connecting to an external server and the pattern looks reversed from normal client to server traffic; the client appears to be receiving instructions from the server and a human on the outside appears to be controlling the exchange", "The threat score is driven by the quantity of data exchanged and longevity of the connection", "The certainty score is driven by the ratio of data sent by the internal host compared to data received from the server and the longevity of the connection"], "content_up": ["An internal host is connecting to an external server and the pattern looks reversed from normal client to server traffic; the client appears to be receiving instructions from the server and a human on the outside appears to be controlling the exchange"]}, {"title": "Possible Root Causes", "content": ["A host includes malware with remote access capability (e.g. Meterpreter, Poison Ivy) that connects to its C&C server and receives commands from a human operator", "A user has intentionally installed and is using remote desktop access software and is accessing the host from the outside (e.g. GotoMyPC, RDP)", "This behavior can also be exhibited through very active use of certain types of chat software that exposes similar human-driven behavior"]}, {"title": "Business Impact", "content": ["Presence of malware with human-driven C&C is a property of targeted attacks", "Business risk associated with outside human control of an internal host is very high", "Provisioning of this style of remote access to internal hosts poses substantial risks as compromise of the service provides direct access into your network"]}, {"title": "Steps to Verify", "content": ["Look at the detection details and the PCAP to determine whether this may be traffic from chat software", "Check if a user has knowingly installed remote access software and decide whether the resulting risk is acceptable", "<PERSON>an the computer for known malware and potentially reimage it, noting that some remote access toolkits leave no trace on disk and reside entirely in memory"]}]}, "mitreDisplay": ["T1219", "T1065", "T1048", "T1041", "T1105", "T1061", "T1059", "T1108"], "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}}