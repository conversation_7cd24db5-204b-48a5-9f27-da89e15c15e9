{"version": 1, "type": "spa_tcp_cnc", "name": "Suspect TCP Activity", "category": "cnc", "dataSourceType": "network", "detailProcessor": {"fields": {"bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "protocol": "proto", "src_ip": "src_ip", "dst_ip": "dst_ip", "target_domains": "dst_dns", "dst_port": "dst_port", "distinct_packets_sent": "flex_json.unique_packets_sent", "distinct_packets_received": "flex_json.unique_packets_recvd", "last_timestamp": "last_timestamp"}}, "summaryProcessor": {"fields": {"target_domains": {"handler": "unique_list", "field": "target_domains"}, "protocols": {"field": "protocol", "handler": "unique_list"}, "distinct_packets_sent": {"handler": "sum_list", "field": "distinct_packets_sent"}, "distinct_packets_received": {"handler": "sum_list", "field": "distinct_packets_received"}, "src_ips": {"handler": "unique_list", "field": "src_ip"}, "dst_ports": {"handler": "unique_list", "field": "dst_port"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}}}, "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}, "dst_ports": {"handler": "transform_list", "field": "dst_port"}, "target_domains": {"handler": "transform_list", "field": "target_domains"}}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "C&C Server", "field": "external_target", "format": "external_host"}, {"label": "Destination Port", "field": "dst_port", "format": "basic"}, {"label": "Application Protocol", "field": "protocol", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}], "content": [{"definitions": [{"label": "Packets Sent", "field": "distinct_packets_sent", "format": "basic"}, {"label": "Packets Received", "field": "distinct_packets_received", "format": "basic"}]}]}, "summaryDisplay": {"fields": [{"label": "IPs When Detected", "field": "src_ips", "format": "concat_slice"}, {"label": "C&C Servers", "field": "target_domains", "format": "concat_slice"}, {"label": "Destination Ports", "field": "dst_ports", "format": "concat_slice"}, {"label": "Application Protocols", "field": "protocols", "format": "concat_slice"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "Packets Sent", "field": "distinct_packets_sent", "format": "basic"}, {"label": "Packets Received", "field": "distinct_packets_received", "format": "basic"}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "infographic": "infographic_spa_tcp", "onePagerDisplay": null, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "C&C Server IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_dns": {"title": "C&C Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_port": {"title": "Destination Port", "api_name": "remote1_port", "detail_fields": ["dst_port"]}, "remote1_proto": {"title": "Application Protocol", "api_name": "remote1_proto", "detail_fields": ["proto"]}, "flex1": {"title": "Detection Name", "api_name": "metadata_name", "detail_fields": ["flex_json.metadata.name"]}}}, "scoring": {"detection": {"method": "static_latest_detail", "threatLow": null, "threatHigh": null, "certaintyLow": null, "certaintyHigh": null}, "host": {"activeTime": 7}}, "learnMoreLink": "https://support.vectra.ai/s/article/KB-VS-1781", "bucketing": {"extraTupleFields": []}}