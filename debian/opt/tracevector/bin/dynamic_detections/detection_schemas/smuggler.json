{"version": 1, "type": "smuggler", "name": "Data Smuggler", "category": "exfil", "dataSourceType": "network", "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": [{"handler": "extract_value", "field": "events"}, {"handler": "extract_value", "field": "sessions"}, {"handler": "flatten_list"}, {"handler": "extract_value", "field": "target_host"}, {"handler": "internal_host_target"}]}}, "detectionTargetProcessor": {"fields": {"targets": {"handler": "detection_target_host_session", "field": "dst_session_luid"}}}, "syslogProcessor": {"fields": {"proxied_dst": "flex1", "push_ips": "flex2", "push_ports": "flex3", "push_domains": "flex4"}}, "scoring": {"detection": {"method": "python", "threatLow": 60, "threatHigh": 95, "certaintyLow": 10, "certaintyHigh": 95}, "host": {"archetypeBucket": "exfilLC", "activeTime": 30}}, "bucketing": {"extraTupleFields": []}}