{"version": 1, "type": "icmp_tunnel", "name": "ICMP Tunnel", "category": "cnc", "dataSourceType": "network", "detectionDescription": "A host was observed using ICMP in ways inconsistent with standard implementation of the protocol. More precisely, a host's ICMP traffic was observed to contain datagrams which vary in size more frequently than typical ICMP traffic would. An attacker may be using the host to communicate with or transfer data to an external host.", "scoring": {"detection": {"method": "static_latest_detail", "threatLow": 30, "threatHigh": 30, "certaintyLow": 80, "certaintyHigh": 80}, "host": {"archetypeBucket": "cncHC", "activeTime": 7}}, "detailProcessor": {"fields": {"dst_ip": "dst_ip", "target_domain": "dst_dns", "external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "first_timestamp": "first_timestamp", "last_timestamp": "last_timestamp", "distinct_packets_sent": "flex_json.unique_packets_sent", "distinct_packets_received": "flex_json.unique_packets_recvd", "packet_object": {"handler": "extract_fields", "distinct_packets_sent": "flex_json.unique_packets_sent", "distinct_packets_received": "flex_json.unique_packets_recvd", "last_timestamp": "last_timestamp", "tunnel_type": "flex_json.tunnel_type"}}}, "groupbyProcessor": {"groupingField": ["external_target"], "fields": {"dst_ip": {"handler": "unique_value", "field": "dst_ip"}, "external_target": {"handler": "unique_value", "field": "external_target"}, "distinct_packets_sent": {"handler": "sum_list", "field": "distinct_packets_sent"}, "distinct_packets_received": {"handler": "sum_list", "field": "distinct_packets_received"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "packet_list": "packet_object", "target_domains": {"handler": "unique_list", "field": "target_domain"}, "first_timestamp": {"handler": "min_value", "field": "first_timestamp"}, "last_timestamp": {"handler": "max_value", "field": "last_timestamp"}}}, "summaryProcessor": {"fields": {"distinct_packets_sent": {"handler": "sum_list", "field": "distinct_packets_sent"}, "distinct_packets_received": {"handler": "sum_list", "field": "distinct_packets_received"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}, "target_domains": [{"handler": "flatten_list", "field": "target_domains"}, {"handler": "unique_list"}]}}, "attackGraphProcessor": {"blastRadius": true, "reverseDirection": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "summaryDisplay": {"fields": [{"label": "Distinct Packets Sent", "field": "distinct_packets_sent", "format": "basic"}, {"label": "Distinct Packets Received", "field": "distinct_packets_received", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "C&C Server", "field": "target_domains", "format": "basic", "excludeSummary": true}]}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "C&C Server", "field": "external_target", "format": "external_host"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "First Seen", "field": "first_timestamp", "format": "date"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}], "content": [{"componentName": "form-statuses", "components": [{"type": "bytesSent", "primaryText": "Observed normal ICMP interaction do not exceed 2 distinct packets sent."}, {"type": "bytesReceived", "primaryText": "Observed normal ICMP interaction do not exceed 2 distinct packets received."}]}, {"componentName": "table-display", "valuePath": "packet_list", "displayProperties": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "Tunnel Type", "format": "basic", "field": "tunnel_type"}, {"label": "Distinct Packets Sent", "tooltip": "Number of distinct packets calculated based on unique byte size of each packet seen across that tunnel.", "format": "basic", "field": "distinct_packets_sent"}, {"label": "Distinct Packets Received", "tooltip": "Number of distinct packets calculated based on unique byte size of each packet seen across that tunnel.", "format": "basic", "field": "distinct_packets_received"}, {"label": "Timestamp", "field": "last_timestamp", "format": "date"}]}}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "bucketing": {"extraTupleFields": []}, "infographic": "infographic_icmp_tunnel", "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_dns": {"title": "C&C Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_ip": {"title": "IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "flex2": {"title": "Tunnel Type", "api_name": "match_value", "detail_fields": ["flex_json.tunnel_type"], "default_suggestions": {"values": [{"id": "Heartbeat", "label": "Heartbeat"}, {"id": "Reverse Shell", "label": "Reverse Shell"}]}}}}, "onePagerDisplay": {"content": [{"title": "Triggers", "content": ["A host was observed using ICMP in ways inconsistent with standard implementation of the protocol.", "More precisely, a host's ICMP traffic was observed to contain datagrams which vary in size more frequently than typical ICMP traffic would.", "An attacker may be using the host to communicate with or transfer data to an external host."]}, {"title": "Possible Root Causes", "content": ["An attacker is using ICMP as a staging and/or control channel.", "An attacker is using ICMP to exfiltrate data from the environment.", "An attacker has established persistence & has chosen ICMP as a backup channel."]}, {"title": "Business Impact", "content": ["The presence of an ICMP tunnel indicates the host was compromised & that an attacker has remote access to the machine.", "Recon, data exfiltration, lateral movement, privilege escalation, & establishing a tunnel over a more reliable protocol like HTTPS are all likely next steps.", "ICMP tunnels can be stealthy and are often used to evade sophisticated perimeter security controls."]}, {"title": "Steps to Verify", "content": ["Check the destination IP & determine if the observed traffic arrives at a trusted endpoint.", "Investigate the host for malware, there may be code present which establishes a C2 channel with another host."]}]}, "mitreDisplay": ["T1008", "T1048", "T1095"]}