{"version": 1, "type": "stealth_post", "name": "Stealth HTTP Post", "category": "cnc", "dataSourceType": "network", "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": [{"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, {"handler": "external_host_target"}]}}, "scoring": {"detection": {"method": "python", "threatLow": 10, "threatHigh": 50, "certaintyLow": 10, "certaintyHigh": 95}, "host": {"archetypeBucket": "cncLC", "activeTime": 7}}, "bucketing": {"extraTupleFields": []}}