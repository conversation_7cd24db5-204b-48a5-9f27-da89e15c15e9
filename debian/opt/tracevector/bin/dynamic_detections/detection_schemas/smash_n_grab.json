{"version": 1, "type": "smash_n_grab", "name": "Smash and Grab", "category": "exfil", "dataSourceType": "network", "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": {"handler": "generic_target", "field": "external_destination", "icon": "domain", "label": "External Domain"}}}, "syslogProcessor": {"fields": {"proxied_dst": "flex1", "duration_seconds": "flex2", "event_id": "flex3", "subnet": "flex4", "normal_bytes_sent": "flex5", "proxy_external_dest_ip": "flex6"}}, "scoring": {"detection": {"method": "python", "threatLow": 60, "threatHigh": 95, "certaintyLow": 10, "certaintyHigh": 95}, "host": {"archetypeBucket": "exfilLC", "activeTime": 30}}, "bucketing": {"extraTupleFields": []}}