{"version": 1, "type": "hidden_tunnel_cnc", "name": "Hidden Tunnel", "category": "cnc", "dataSourceType": "network", "detectionDescription": "This host communicated with an external destination using protocol tunneling. The host appeared to be under the control of the external destination.", "detailProcessor": {"fields": {"external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "src_ip": "src_ip", "dst_ip": "dst_ip", "dst_dns": "dst_dns", "dst_port": "dst_port", "num_sessions": "count", "bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "first_timestamp": "first_timestamp", "last_timestamp": "last_timestamp", "protocol": {"handler": "match_value", "field": "proto", "tcp": {"handler": "render_template", "template": "TCP"}}, "application_protocol": {"handler": "match_value", "field": "flex_json.application_protocol", "unknown": {"handler": "render_template", "template": "Unknown"}, "http": {"handler": "render_template", "template": "HTTP"}, "tls": {"handler": "render_template", "template": "HTTPS"}, "kerberos": {"handler": "render_template", "template": "<PERSON><PERSON><PERSON>"}, "smb": {"handler": "render_template", "template": "SMB"}, "dcerpc": {"handler": "render_template", "template": "DCE/RPC"}, "bittorrent": {"handler": "render_template", "template": "BitTorrent"}, "rdp": {"handler": "render_template", "template": "RDP"}, "ssh": {"handler": "render_template", "template": "SSH"}, "vnc": {"handler": "render_template", "template": "VNC"}, "ldap": {"handler": "render_template", "template": "LDAP"}, "dns": {"handler": "render_template", "template": "DNS"}, "smtp": {"handler": "render_template", "template": "SMTP"}, "ftp": {"handler": "render_template", "template": "FTP"}, "snmp": {"handler": "render_template", "template": "SNMP"}, "syslog": {"handler": "render_template", "template": "Syslog"}, "radius": {"handler": "render_template", "template": "RADIUS"}}, "tunnel_type": {"handler": "match_value", "field": "subtype", "type0": {"handler": "render_template", "template": "Multiple short TCP sessions - Abnormal Beacon"}, "type1": {"handler": "render_template", "template": "Multiple short TCP sessions"}, "type3-agent": {"handler": "render_template", "template": "Long TCP session - Command line"}, "type3-rdp": {"handler": "render_template", "template": "Long TCP session - Graphical interface"}, "dynamic": {"handler": "render_template", "template": "Dynamic TCP session"}, "ja3-matches": {"handler": "render_template", "template": "JA3/JA3S match"}, "type_beaconing": {"handler": "render_template", "template": "Beaconing channel"}, "type_non_beaconing": {"handler": "render_template", "template": "Non-beaconing channel"}}, "hosts_prior_24_hours": "flex_json.domain_context.client_connections.past_day.total", "hosts_prior_7_days": "flex_json.domain_context.client_connections.past_week.mean", "detail_object": {"handler": "extract_fields", "tunnel_type": {"handler": "match_value", "field": "subtype", "type0": {"handler": "render_template", "template": "Multiple short TCP sessions - Abnormal Beacon"}, "type1": {"handler": "render_template", "template": "Multiple short TCP sessions"}, "type3-agent": {"handler": "render_template", "template": "Long TCP session - Command line"}, "type3-rdp": {"handler": "render_template", "template": "Long TCP session - Graphical interface"}, "dynamic": {"handler": "render_template", "template": "Dynamic TCP session"}, "ja3-matches": {"handler": "render_template", "template": "JA3/JA3S match"}, "type_beaconing": {"handler": "render_template", "template": "Beaconing channel"}, "type_non_beaconing": {"handler": "render_template", "template": "Non-beaconing channel"}}, "bytes_sent": "total_bytes_sent", "bytes_received": "total_bytes_rcvd", "first_timestamp": "first_timestamp", "last_timestamp": "last_timestamp"}, "server_is_proxy": "flex_json.server_is_proxy", "dest_proxy_ip": "flex_json.dest_proxy_ip"}}, "groupbyProcessor": {"groupingField": ["external_target", "dst_port", "server_is_proxy", "protocol", "application_protocol"], "fields": {"external_target": {"handler": "unique_value", "field": "external_target"}, "src_ip": {"handler": "unique_value", "field": "src_ip"}, "dst_dns": {"handler": "unique_value", "field": "dst_dns"}, "dst_ip": {"handler": "unique_value", "field": "dst_ip"}, "dst_port": {"handler": "unique_value", "field": "dst_port"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}, "total_hosts_prior_24_hours": {"handler": "most_recent", "field": "hosts_prior_24_hours"}, "average_hosts_prior_7_days": {"handler": "most_recent", "field": "hosts_prior_7_days"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "server_is_proxy": {"handler": "unique_value", "field": "server_is_proxy"}, "dest_proxy_ip": {"handler": "unique_value", "field": "dest_proxy_ip"}, "protocol": {"handler": "unique_value", "field": "protocol"}, "application_protocol": {"handler": "unique_value", "field": "application_protocol"}, "first_timestamp": {"handler": "min_value", "field": "first_timestamp"}, "last_timestamp": {"handler": "max_value", "field": "last_timestamp"}, "num_sessions": {"handler": "sum_list", "field": "num_sessions"}, "detail_list": "detail_object"}}, "summaryProcessor": {"fields": {"src_ips": {"handler": "unique_list", "field": "src_ip"}, "target_domains": {"handler": "unique_list", "field": "dst_dns"}, "dst_ips": {"handler": "unique_list", "field": "dst_ip"}, "dest_proxy_ips": {"handler": "unique_list", "field": "dest_proxy_ip"}, "dst_ports": {"handler": "unique_list", "field": "dst_port"}, "application_protocols": {"handler": "unique_list", "field": "application_protocol"}, "protocols": {"handler": "unique_list", "field": "protocol"}, "num_sessions": {"handler": "sum_list", "field": "num_sessions"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}}}, "attackGraphProcessor": {"blastRadius": true, "reverseDirection": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}, "dst_ports": {"handler": "transform_list", "field": "dst_port"}, "target_domains": {"handler": "transform_list", "field": "dst_dns"}}}, "syslogProcessor": {"fields": {"tunnel_type": "subtype"}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "C&C Server", "field": "external_target", "format": "external_host"}, {"label": "Port", "field": "dst_port", "format": "basic"}, {"label": "Data Sent", "field": "bytes_sent", "format": "bytes", "tooltip": "Note that in some cases, data sent and received may be double-counted due to multiple algorithms making similar observations."}, {"label": "Data Received", "field": "bytes_received", "format": "bytes", "tooltip": "Note that in some cases, data sent and received may be double-counted due to multiple algorithms making similar observations."}, {"label": "First Seen", "field": "first_timestamp", "format": "date"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}], "content": [{"definitions": [{"singleColumnDisplay": true, "label": "Hosts connected 24 hrs prior", "field": "total_hosts_prior_24_hours", "format": "int", "tooltip": "Total hosts connecting from your network to the domain 24 hours prior to detection. If the domain has not been previously observed on the network, this learning will not be available."}, {"label": "Average hosts 7 days prior", "field": "average_hosts_prior_7_days", "format": "int", "tooltip": "Average number of hosts per day connecting from your network to the domain over the 7 days prior to detection. If the domain has not been previously observed on the network, this learning will not be available."}, {"label": "Is Proxy", "field": "server_is_proxy", "format": "basic"}, {"label": "Transport Protocol", "field": "protocol", "format": "basic"}, {"label": "Application Protocol", "field": "application_protocol", "format": "basic"}]}, {"componentName": "table-display", "valuePath": "detail_list", "displayProperties": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "Tunnel Type", "format": "basic", "field": "tunnel_type"}, {"label": "Data Sent", "format": "bytes", "field": "bytes_sent"}, {"label": "Data Received", "format": "bytes", "field": "bytes_received"}, {"label": "First Seen", "field": "first_timestamp", "format": "date"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}]}}]}, "summaryDisplay": {"fields": [{"label": "IP When Detected", "field": "src_ips", "format": "concat_slice"}, {"label": "Domains", "field": "target_domains", "format": "concat_slice", "excludeExpando": true}, {"label": "Destination IPs", "field": "dst_ips", "format": "concat_slice"}, {"label": "Proxies Identified", "field": "dest_proxy_ips", "format": "concat_slice"}, {"label": "Destination Ports", "field": "dst_ports", "format": "concat_slice"}, {"label": "Application Protocols", "field": "application_protocols", "format": "concat_slice"}, {"label": "Protocols", "field": "protocols", "format": "concat_slice"}, {"label": "Sessions", "field": "num_sessions", "format": "basic"}, {"label": "Data Sent", "field": "bytes_sent", "format": "bytes"}, {"label": "Data Received", "field": "bytes_received", "format": "bytes"}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "onePagerDisplay": {"content": [{"title": "Triggers", "content": ["An internal host is communicating with an external server using protocol tunneling to mask to contents of the communication channel"]}, {"title": "Possible Root Causes", "content": ["An attacker may utilize protocol tunneling to egress the network and establish a communication channel with a command-and-control server", "Software can use tunneling to simplify traversing corporate firewalls not requiring modification to existing rulesets"]}, {"title": "Business Impact", "content": ["The use of a hidden tunnel by some software may be benign, but it represents significant risk as the intention is to bypass security controls", "Hidden tunnels used as part of a targeted attack are meant to slip by your perimeter security controls and indicate a sophisticated attacker", "Hidden tunnels are rarely used by botnets, though more sophisticated bot herders with more ambitious goals may utilize them"]}, {"title": "Steps to Verify", "content": ["Check to see if the destination IP or domain of the tunnel is an entity you trust for your network", "Ask the user of the host whether they are using hidden tunnel software for any purpose", "Before removing the offending software via antivirus or reimaging, take a memory snapshot for future analysis of the incident", "If the behavior reappears shortly after a reimaging, this may be a hardware/BIOS tunnel"]}]}, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "C&C Server IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_dns": {"title": "C&C Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_port": {"title": "Destination Port", "api_name": "remote1_port", "detail_fields": ["dst_port"]}, "flex1": {"title": "Tunnel Type", "api_name": "tunnel_type", "detail_fields": ["subtype"], "default_suggestions": {"values": [{"id": "type0", "label": "Multiple short TCP sessions - Abnormal Beacon"}, {"id": "type1", "label": "Multiple short TCP sessions"}, {"id": "type3-agent", "label": "Long TCP session - Command line"}, {"id": "type3-rdp", "label": "Long TCP session - Graphical interface"}, {"id": "dynamic", "label": "Dynamic TCP session"}, {"id": "ja3-matches", "label": "JA3/JA3S match"}, {"id": "type_beaconing", "label": "Beaconing channel"}, {"id": "type_non_beaconing", "label": "Non-beaconing channel"}]}}, "flex2": {"title": "Is Proxy", "api_name": "match_value", "detail_fields": ["flex_json.server_is_proxy"], "default_suggestions": {"values": [{"id": "Yes", "label": "Yes"}, {"id": "No", "label": "No"}]}}, "flex3": {"title": "Application Protocol", "api_name": "match_value", "detail_fields": ["flex_json.application_protocol"], "default_suggestions": {"values": [{"id": "unknown", "label": "Unknown"}, {"id": "http", "label": "HTTP"}, {"id": "tls", "label": "HTTPS"}, {"id": "kerber<PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"id": "smb", "label": "SMB"}, {"id": "dcerpc", "label": "DCE/RPC"}, {"id": "bittorrent", "label": "BitTorrent"}, {"id": "rdp", "label": "RDP"}, {"id": "ssh", "label": "SSH"}, {"id": "vnc", "label": "VNC"}, {"id": "ldap", "label": "LDAP"}, {"id": "dns", "label": "DNS"}, {"id": "smtp", "label": "SMTP"}, {"id": "ftp", "label": "FTP"}, {"id": "snmp", "label": "SNMP"}, {"id": "syslog", "label": "Syslog"}, {"id": "radius", "label": "RADIUS"}]}}}}, "mitreDisplay": ["TA0011", "T1132", "T1001", "T1571", "T1572", "T1573"], "scoring": {"detection": {"method": "static_latest_detail", "threatLow": 50, "threatHigh": 50, "certaintyLow": 50, "certaintyHigh": 50}, "host": {"archetypeBucket": "cncHC", "activeTime": 7}}, "bucketing": {"extraTupleFields": []}, "infographic": "infographic_hidden_http_tunnel_cnc"}