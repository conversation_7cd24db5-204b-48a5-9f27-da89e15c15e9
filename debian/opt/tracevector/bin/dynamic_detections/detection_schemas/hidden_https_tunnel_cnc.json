{"version": 1, "type": "hidden_https_tunnel_cnc", "name": "Hidden HTTPS Tunnel", "category": "cnc", "dataSourceType": "network", "detectionDescription": "This host communicated with an external destination using HTTPS where another protocol was running over the top of the session. The host appeared to be under the control of the external destination.", "detailProcessor": {"fields": {"session": {"handler": "extract_fields", "tunnel_type": {"handler": "match_value", "field": "subtype", "type0": {"handler": "render_template", "template": "Multiple short TCP sessions - Abnormal Beacon"}, "type1": {"handler": "render_template", "template": "Multiple short TCP sessions"}, "type3-agent": {"handler": "render_template", "template": "Long TCP session - Command line"}, "type3-rdp": {"handler": "render_template", "template": "Long TCP session - Graphical interface"}, "dynamic": {"handler": "render_template", "template": "Dynamic TCP session"}, "ja3-matches": {"handler": "render_template", "template": "JA3/JA3S match"}, "type_beaconing": {"handler": "render_template", "template": "Beaconing channel"}, "type_non_beaconing": {"handler": "render_template", "template": "Non-beaconing channel"}}, "protocol": "proto", "app_protocol": "flex1", "dst_port": "dst_port", "dst_ip": "dst_ip", "bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "first_timestamp": "first_timestamp", "last_timestamp": "last_timestamp", "dst_geo": "dst_geo", "dst_geo_lat": "dst_geo_lat", "dst_geo_lon": "dst_geo_lon"}, "ja3_hash": "flex2", "ja3s_hash": "flex3", "external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "dst_ip": "dst_ip", "dst_dns": "dst_dns", "dst_port": "dst_port", "num_sessions": "count", "bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "first_timestamp": "first_timestamp", "last_timestamp": "last_timestamp"}}, "groupbyProcessor": {"groupingField": ["dst_ip", "dst_dns"], "fields": {"external_target": {"handler": "unique_value", "field": "external_target"}, "dst_ip": {"handler": "unique_value", "field": "dst_ip"}, "target_domain": {"handler": "unique_value", "field": "dst_dns"}, "dst_port": {"handler": "unique_list", "field": "dst_port"}, "num_sessions": {"handler": "sum_list", "field": "num_sessions"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "ja3_hashes": {"handler": "unique_list", "field": "ja3_hash"}, "ja3s_hashes": {"handler": "unique_list", "field": "ja3s_hash"}, "sessions": "session", "first_timestamp": {"handler": "min_value", "field": "first_timestamp"}, "last_timestamp": {"handler": "max_value", "field": "last_timestamp"}}}, "summaryProcessor": {"fields": {"dst_ips": {"handler": "unique_list", "field": "dst_ip"}, "num_sessions": {"handler": "sum_list", "field": "num_sessions"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}}}, "attackGraphProcessor": {"blastRadius": true, "reverseDirection": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}, "dst_ports": {"handler": "transform_list", "field": "dst_port"}, "target_domains": {"handler": "transform_list", "field": "target_domain"}}}, "syslogProcessor": {"fields": {"tunnel_type": "subtype"}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "C&C Server", "field": "external_target", "format": "external_host"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "First Seen", "field": "first_timestamp", "format": "date"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}], "content": [{"definitions": [{"label": "JA3", "field": "ja3_hashes", "tooltip": "SSL/TLS client fingerprint", "format": "basic"}, {"label": "JA3S", "field": "ja3s_hashes", "tooltip": "SSL/TLS server fingerprint", "format": "basic"}]}, {"componentName": "table-display", "valuePath": "sessions", "displayProperties": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "Tunnel Type", "field": "tunnel_type", "format": "basic"}, {"label": "Port", "field": "dst_port", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "First Seen", "field": "first_timestamp", "format": "date"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}]}}]}, "summaryDisplay": {"fields": [{"label": "Target IPs", "field": "dst_ips", "format": "concat_slice"}, {"label": "Sessions", "field": "num_sessions", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "onePagerDisplay": {"content": [{"title": "Triggers", "content": ["An internal host is communicating with an outside IP using HTTPS where another protocol is running over the top of the HTTPS sessions", "This represents a hidden tunnel involving one long session or multiple shorter sessions over a longer period of time mimicking normal encrypted Web traffic", "When it can be determined whether the tunneling software is console-based or driven via a graphical user interface, that indicator will be included in the detection", "The threat score is driven by the quantity of data sent via the tunnel", "The certainty score is driven by the combination of the persistence of the connection(s) and the degree to which the observed volume and timing of requests matches up with training samples"], "content_up": ["An internal host is communicating with an outside IP using HTTPS where another protocol is running over the top of the HTTPS sessions", "This represents a hidden tunnel involving one long session or multiple shorter sessions over a longer period of time mimicking normal encrypted Web traffic", "When it can be determined whether the tunneling software is console-based or driven via a graphical user interface, that indicator will be included in the detection"]}, {"title": "Possible Root Causes", "content": ["A targeted attack may use hidden tunnels to hide communication with command and control servers over SSL on port 443", "A user is utilizing tunneling software to communicate with Internet services which might not otherwise be accessible", "Intentionally installed software is using a hidden tunnel to bypass expected firewall rules"]}, {"title": "Business Impact", "content": ["The use of a hidden tunnel by some software may be benign, but it represents significant risk as the intention is to bypass security controls", "Hidden tunnels used as part of a targeted attack are meant to slip by your perimeter security controls and indicate a sophisticated attacker", "Hidden tunnels are rarely used by botnets, though more sophisticated bot herders with more ambitious goals may utilize them"]}, {"title": "Steps to Verify", "content": ["Check to see if the destination IP or domain of the tunnel is an entity you trust for your network", "Ask the user of the host whether they are using hidden tunnel software for any purpose", "Before removing the offending software via antivirus or reimaging, take a memory snapshot for future analysis of the incident", "If the behavior reappears shortly after a reimaging, this may be a hardware/BIOS tunnel"]}]}, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "C&C Server IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_dns": {"title": "C&C Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_port": {"title": "Destination Port", "api_name": "remote1_port", "detail_fields": ["dst_port"]}, "flex1": {"title": "Tunnel Type", "api_name": "tunnel_type", "validator": "hidden_https_tunnel_type_cnc", "detail_fields": ["subtype"], "default_suggestions": {"values": [{"id": "type0", "label": "Multiple short TCP sessions - Abnormal Beacon"}, {"id": "type1", "label": "Multiple short TCP sessions"}, {"id": "type3-agent", "label": "Long TCP session - Command line"}, {"id": "type3-rdp", "label": "Long TCP session - Graphical interface"}, {"id": "dynamic", "label": "Dynamic TCP session"}, {"id": "ja3-matches", "label": "JA3/JA3S match"}, {"id": "type_beaconing", "label": "Beaconing channel"}, {"id": "type_non_beaconing", "label": "Non-beaconing channel"}]}}, "flex2": {"title": "JA3", "api_name": "ja3_hash", "detail_fields": ["flex2"]}, "flex3": {"title": "JA3S", "api_name": "ja3s_hash", "detail_fields": ["flex3"]}}}, "mitreDisplay": ["T1043", "T1094", "T1024", "T1132", "T1001", "T1008", "T1071", "T1032", "T1105", "T1108"], "scoring": {"detection": {"method": "python", "threatLow": 10, "threatHigh": 70, "certaintyLow": 10, "certaintyHigh": 80}, "host": {"archetypeBucket": "cncHC", "activeTime": 7}}, "bucketing": {"extraTupleFields": []}, "advancedSearch": {"groupedDetailFields": {"ja3_hash": {"type": "text"}, "ja3s_hash": {"type": "text"}}, "summaryFields": {"ja3_hashes": {"type": "text"}, "ja3s_hashes": {"type": "text"}}}}