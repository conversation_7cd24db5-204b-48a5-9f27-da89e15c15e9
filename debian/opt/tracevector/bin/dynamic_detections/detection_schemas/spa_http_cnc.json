{"version": 1, "type": "spa_http_cnc", "name": "Suspect HTTP Activity", "category": "cnc", "dataSourceType": "network", "detailProcessor": {"fields": {"http_hostname": "flex_json.details.http.hostname", "http_url": "flex_json.details.http.url", "http_user_agent": "flex_json.details.http.http_user_agent", "http_content_type": "flex_json.details.http.http_content_type", "http_method": "flex_json.details.http.http_method", "http_protocol": "flex_json.details.http.protocol", "http_status": "flex_json.details.http.status", "bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "protocol": "proto", "target_domains": "dst_dns", "src_ip": "src_ip", "dst_ip": "dst_ip", "dst_port": "dst_port", "last_timestamp": "last_timestamp"}}, "summaryProcessor": {"fields": {"target_domains": {"handler": "unique_list", "field": "target_domains"}, "src_ips": {"handler": "unique_list", "field": "src_ip"}, "dst_ports": {"handler": "unique_list", "field": "dst_port"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}, "header_hostnames": {"handler": "unique_list", "field": "http_hostname"}, "urls": {"handler": "unique_list", "field": "http_url"}, "user_agents": {"handler": "unique_list", "field": "http_user_agent"}, "methods": {"handler": "unique_list", "field": "http_method"}, "content_types": {"handler": "unique_list", "field": "http_content_type"}, "statuses": {"handler": "unique_list", "field": "http_status"}}}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}, "dst_ports": {"handler": "transform_list", "field": "dst_port"}, "target_domains": {"handler": "transform_list", "field": "target_domains"}}}, "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "C&C Server", "field": "external_target", "format": "external_host"}, {"label": "Destination Port", "field": "dst_port", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "Timestamp", "field": "last_timestamp", "format": "date"}], "content": [{"definitions": [{"label": "HTTP Header Hostname", "field": "http_hostname", "format": "basic"}, {"label": "URL", "field": "http_url", "format": "basic"}, {"label": "User Agent", "field": "http_user_agent", "format": "basic"}, {"label": "Method", "field": "http_method", "format": "basic"}, {"label": "Content Type", "field": "http_content_type", "format": "basic"}, {"label": "Status", "field": "http_status", "format": "basic"}]}]}, "summaryDisplay": {"fields": [{"label": "IP When Detected", "field": "src_ips", "format": "concat_slice"}, {"label": "C&C Server", "field": "target_domains", "format": "concat_slice"}, {"label": "Destination Port", "field": "dst_ports", "format": "concat_slice"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "HTTP Header Hostname", "field": "header_hostnames", "format": "concat_slice"}, {"label": "URL", "field": "urls", "format": "concat_slice"}, {"label": "User Agent", "field": "user_agents", "format": "concat_slice"}, {"label": "Method", "field": "methods", "format": "concat_slice"}, {"label": "Content Type", "field": "content_types", "format": "concat_slice"}, {"label": "Status", "field": "statuses", "format": "concat_slice"}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "infographic": "infographic_spa_http", "onePagerDisplay": null, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "C&C Server IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_dns": {"title": "C&C Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_port": {"title": "Destination Port", "api_name": "remote1_port", "detail_fields": ["dst_port"]}, "flex1": {"title": "Detection Name", "api_name": "metadata_name", "detail_fields": ["flex_json.metadata.name"]}, "flex2": {"title": "HTTP Hostname", "api_name": "http_hostname", "detail_fields": ["flex_json.details.http.hostname"]}, "flex3": {"title": "HTTP URL", "api_name": "http_url", "detail_fields": ["flex_json.details.http.url"]}, "flex4": {"title": "HTTP User Agent", "api_name": "http_user_agent", "detail_fields": ["flex_json.details.http.http_user_agent"]}, "flex5": {"title": "HTTP Method", "api_name": "http_method", "detail_fields": ["flex_json.details.http.http_method"]}, "flex6": {"title": "HTTP Content Type", "api_name": "http_content_type", "detail_fields": ["flex_json.details.http.http_content_type"]}, "flex7": {"title": "HTTP Status", "api_name": "http_status", "detail_fields": ["flex_json.details.http.status"]}}}, "scoring": {"detection": {"method": "static_latest_detail", "threatLow": null, "threatHigh": null, "certaintyLow": null, "certaintyHigh": null}, "host": {"activeTime": 7}}, "learnMoreLink": "https://support.vectra.ai/s/article/KB-VS-1781", "bucketing": {"extraTupleFields": []}}