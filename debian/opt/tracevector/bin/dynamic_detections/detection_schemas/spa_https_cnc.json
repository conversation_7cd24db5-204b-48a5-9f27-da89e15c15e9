{"version": 1, "type": "spa_https_cnc", "name": "Suspect HTTPS Activity", "category": "cnc", "dataSourceType": "network", "detailProcessor": {"fields": {"tls_issuerdn": "flex_json.details.tls.issuerdn", "tls_fingerprint": "flex_json.details.tls.fingerprint", "tls_servername": "flex_json.details.tls.sni", "tls_version": "flex_json.details.tls.version", "tls_notbefore": "flex_json.details.tls.notbefore", "tls_notafter": "flex_json.details.tls.notafter", "tls_ja3_hash": "flex_json.details.tls.ja3.hash", "tls_ja3s_hash": "flex_json.details.tls.ja3s.hash", "bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "protocol": "proto", "src_ip": "src_ip", "dst_ip": "dst_ip", "target_domains": "dst_dns", "dst_port": "dst_port", "last_timestamp": "last_timestamp"}}, "summaryProcessor": {"fields": {"target_domains": {"handler": "unique_list", "field": "target_domains"}, "src_ips": {"handler": "unique_list", "field": "src_ip"}, "dst_ports": {"handler": "unique_list", "field": "dst_port"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}, "tls_servernames": {"handler": "unique_list", "field": "tls_servername"}, "tls_versions": {"handler": "unique_list", "field": "tls_version"}, "tls_issuerdns": {"handler": "unique_list", "field": "tls_issuerdn"}, "tls_fingerprints": {"handler": "unique_list", "field": "tls_fingerprint"}, "tls_notbefores": {"handler": "unique_list", "field": "tls_notbefore"}, "tls_notafters": {"handler": "unique_list", "field": "tls_notafter"}, "tls_ja3_hashes": {"handler": "unique_list", "field": "tls_ja3_hash"}, "tls_ja3s_hashes": {"handler": "unique_list", "field": "tls_ja3s_hash"}}}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}, "dst_ports": {"handler": "transform_list", "field": "dst_port"}, "target_domains": {"handler": "transform_list", "field": "target_domains"}}, "excludeFields": ["tls_notbefore", "tls_notbefores", "tls_notafter", "tls_notafters"]}, "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "C&C Server", "field": "external_target", "format": "external_host"}, {"label": "Destination Port", "field": "dst_port", "format": "basic"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "Protocol", "field": "protocol", "format": "basic"}, {"label": "Last Seen", "field": "last_timestamp", "format": "date"}], "content": [{"definitions": [{"label": "Server Name Identifier", "field": "tls_servername", "format": "basic"}, {"label": "TLS Version", "field": "tls_version", "format": "basic"}, {"label": "Issuer DN", "field": "tls_issuerdn", "format": "basic"}, {"label": "Fingerprint", "field": "tls_fingerprint", "format": "basic"}, {"label": "Not Before Date", "field": "tls_notbefore", "format": "basic"}, {"label": "Not After Date", "field": "tls_notafter", "format": "basic"}, {"label": "JA3", "field": "tls_ja3_hash", "format": "basic"}, {"label": "JA3S", "field": "tls_ja3s_hash", "format": "basic"}]}]}, "summaryDisplay": {"fields": [{"label": "IP When Detected", "field": "src_ips", "format": "concat_slice"}, {"label": "C&C Server", "field": "target_domains", "format": "concat_slice"}, {"label": "Destination Port", "field": "dst_ports", "format": "concat_slice"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}, {"label": "Server Name Identifier", "field": "tls_servernames", "format": "concat_slice"}, {"label": "TLS Version", "field": "tls_versions", "format": "concat_slice"}, {"label": "Issuer DN", "field": "tls_issuerdns", "format": "concat_slice"}, {"label": "Fingerprint", "field": "tls_fingerprints", "format": "concat_slice"}, {"label": "Not Before Date", "field": "tls_notbefores", "format": "concat_slice"}, {"label": "Not After Date", "field": "tls_notafters", "format": "concat_slice"}, {"label": "JA3", "field": "tls_ja3_hashes", "format": "concat_slice"}, {"label": "JA3S", "field": "tls_ja3s_hashes", "format": "concat_slice"}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "infographic": "infographic_spa_https", "onePagerDisplay": null, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "C&C Server IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_dns": {"title": "C&C Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_port": {"title": "Destination Port", "api_name": "remote1_port", "detail_fields": ["dst_port"]}, "flex1": {"title": "Detection Name", "api_name": "metadata_name", "detail_fields": ["flex_json.metadata.name"]}, "flex2": {"title": "Server Name Identifier", "api_name": "tls_servername", "detail_fields": ["flex_json.details.tls.sni"]}, "flex3": {"title": "TLS Version", "api_name": "tls_version", "detail_fields": ["flex_json.details.tls.version"]}, "flex4": {"title": "Issuer DN", "api_name": "tls_issuerdn", "detail_fields": ["flex_json.details.tls.issuerdn"]}, "flex5": {"title": "Fingerprint", "api_name": "tls_fingerprint", "detail_fields": ["flex_json.details.tls.fingerprint"]}, "flex6": {"title": "JA3", "api_name": "tls_ja3_hash", "detail_fields": ["flex_json.details.tls.ja3.hash"]}, "flex7": {"title": "JA3S", "api_name": "tls_ja3s_hash", "detail_fields": ["flex_json.details.tls.ja3s.hash"]}}}, "scoring": {"detection": {"method": "static_latest_detail", "threatLow": null, "threatHigh": null, "certaintyLow": null, "certaintyHigh": null}, "host": {"activeTime": 7}}, "learnMoreLink": "https://support.vectra.ai/s/article/KB-VS-1781", "bucketing": {"extraTupleFields": []}}