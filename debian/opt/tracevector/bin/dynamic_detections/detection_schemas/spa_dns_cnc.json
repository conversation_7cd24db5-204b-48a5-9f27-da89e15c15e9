{"version": 1, "type": "spa_dns_cnc", "name": "Suspect DNS Activity", "category": "cnc", "dataSourceType": "network", "detailProcessor": {"fields": {"bytes_received": "total_bytes_rcvd", "bytes_sent": "total_bytes_sent", "lookup_value": "flex_json.details.dns.query[0].rrname", "lookup_type": "flex_json.details.dns.query[0].rrtype", "external_target": {"handler": "extract_fields", "ip": "dst_ip", "name": "dst_dns"}, "protocol": "proto", "dst_ip": "dst_ip", "src_ip": "src_ip", "target_domains": "dst_dns", "dst_port": "dst_port", "last_timestamp": "last_timestamp"}}, "summaryProcessor": {"fields": {"target_domains": {"handler": "unique_list", "field": "target_domains"}, "src_ips": {"handler": "unique_list", "field": "src_ip"}, "dst_ports": {"handler": "unique_list", "field": "dst_port"}, "lookup_values": {"handler": "unique_list", "field": "lookup_value"}, "lookup_types": {"handler": "unique_list", "field": "lookup_type"}, "bytes_sent": {"handler": "sum_list", "field": "bytes_sent"}, "bytes_received": {"handler": "sum_list", "field": "bytes_received"}}}, "attackGraphProcessor": {"blastRadius": true, "fields": {"targets": {"handler": "external_host_target", "field": "external_target"}}}, "apiProcessor": {"fields": {"dst_ips": {"handler": "transform_list", "field": "dst_ip"}, "dst_ports": {"handler": "transform_list", "field": "dst_port"}, "target_domains": {"handler": "transform_list", "field": "target_domains"}}}, "tableDisplay": {"defaultSort": "last_timestamp.desc", "columns": [{"label": "Lookup Server", "field": "external_target", "format": "external_host"}, {"label": "Destination Port", "field": "dst_port", "format": "basic"}, {"label": "Lookup Value", "field": "lookup_value", "format": "basic"}, {"label": "Lookup Type", "field": "lookup_type", "format": "basic"}, {"label": "Timestamp", "field": "last_timestamp", "format": "date"}], "content": [{"definitions": [{"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}]}]}, "summaryDisplay": {"fields": [{"label": "IP When Detected", "field": "src_ips", "format": "concat_slice"}, {"label": "Lookup Server", "field": "target_domains", "format": "concat_slice"}, {"label": "Destination Port", "field": "dst_ports", "format": "concat_slice"}, {"label": "Lookup Value", "field": "lookup_values", "format": "concat_slice"}, {"label": "Lookup Type", "field": "lookup_types", "format": "concat_slice"}, {"label": "<PERSON><PERSON>", "field": "bytes_sent", "format": "bytes"}, {"label": "<PERSON><PERSON> Received", "field": "bytes_received", "format": "bytes"}]}, "timelineDisplay": {"title": "Events", "fields": [{"label": "Events", "format": "basic"}]}, "infographic": "infographic_spa_dns", "onePagerDisplay": null, "triage": {"source": ["ip", "host", "sensor"], "additional": {"remote1_ip": {"title": "Lookup Server IP", "api_name": "remote1_ip", "detail_fields": ["dst_ip"]}, "remote1_dns": {"title": "Lookup Server Domain", "api_name": "remote1_dns", "detail_fields": ["dst_dns"]}, "remote1_port": {"title": "Destination Port", "api_name": "remote1_port", "detail_fields": ["dst_port"]}, "flex1": {"title": "Detection Name", "api_name": "metadata_name", "detail_fields": ["flex_json.metadata.name"]}, "flex2": {"title": "Lookup Value", "api_name": "lookup_value", "detail_fields": ["flex_json.details.dns.query[0].rrname"]}, "flex3": {"title": "Lookup Type", "api_name": "lookup_type", "detail_fields": ["flex_json.details.dns.query[0].rrtype"]}}}, "scoring": {"detection": {"method": "static_latest_detail", "threatLow": null, "threatHigh": null, "certaintyLow": null, "certaintyHigh": null}, "host": {"activeTime": 7}}, "learnMoreLink": "https://support.vectra.ai/s/article/KB-VS-1781", "bucketing": {"extraTupleFields": []}}