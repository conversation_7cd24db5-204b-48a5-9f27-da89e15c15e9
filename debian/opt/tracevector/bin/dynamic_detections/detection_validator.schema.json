{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Detection Schema", "definitions": {"assign": {"title": "Assign", "description": "assign returns a dictionary with the key of the outbound_field and the value of the incoming value.", "type": "object", "properties": {"handler": {"type": "string", "const": "assign"}, "outbound_field": {"description": "The field to assign the incoming value to", "type": "string"}}, "required": ["handler", "outbound_field"]}, "cap_value": {"title": "Cap Value", "description": "cap value checks if the value is greater than the provided threshold, and if so, caps the value and preprends a geq symbol", "type": "object", "properties": {"handler": {"type": "string", "const": "cap_value"}, "field": {"description": "Field for value to check if is greater than cap", "type": "string"}, "cap": {"description": "Maximum value for field before capping it", "type": "integer"}}, "required": ["handler", "field", "cap"]}, "combineJsonFields": {"title": "Combine <PERSON> Fields", "description": "combine json fields will load one or more json string from detection detail fields", "type": "object", "properties": {"handler": {"type": "string", "const": "combine_json_fields"}}, "required": ["handler"], "additionalProperties": {"description": "the key of any additional properties will be what the field is called in outbound dictionary and the value will be the path used on the incoming data to retrieve the value", "type": "string"}}, "duration": {"title": "Duration", "description": "duration returns the number of seconds between two datetime objects.", "type": "object", "properties": {"handler": {"type": "string", "const": "duration"}, "start_time": {"description": "The field containing the starting time", "type": "string"}, "end_time": {"description": "The field containing the ending time", "type": "string"}}, "required": ["handler", "start_time", "end_time"]}, "extractValue": {"title": "Extract Value", "description": "extract_value is used to retrieve the value of a field and return the value itself. The same as just having the string path but can but used in lists of handlers", "type": "object", "properties": {"handler": {"type": "string", "const": "extract_value"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "extractFields": {"title": "Extract Fields", "description": "extract_fields is used to retrieve fields from the incoming object and place them in a dictionary with the keys matching the property name. Used interally by many of the handlers that deal with single dictionaries as opposed to a list of dictionaries.", "type": "object", "properties": {"handler": {"type": "string", "const": "extract_fields"}}, "required": ["handler"], "additionalProperties": {"description": "The key of any additional properties represents the name of the field to assign the valiue to. The value can either be the path to the field in the incoming data or reference a handler to apply to the field.", "anyOf": [{"description": "The key of any additional properties will be what the field is called in outbound dictionary and the value will be the path used on the incoming data to retrieve the value", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/cap_value"}, {"$ref": "#/definitions/duration"}, {"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/internalHost"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sumFields"}, {"$ref": "#/definitions/privilegeCategory"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/maxValue"}]}, {"description": "For the list of handlers the initial object sent in is each detail for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/cap_value"}, {"$ref": "#/definitions/duration"}, {"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/internalHost"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sumFields"}, {"$ref": "#/definitions/privilegeCategory"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/maxValue"}]}}]}}, "flattenList": {"title": "Flatten List", "description": "flatten_list takes in a list of lists and merges all inner lists together to make a single list containing all objects of the inner list. Useful when a detection that is grouped has a property that is already a list.", "type": "object", "required": ["handler"], "properties": {"handler": {"type": "string", "const": "flatten_list"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}}, "parseBooleanToYesNo": {"title": "<PERSON>rse <PERSON> to Yes No", "description": "parse_boolean_to_yes_no parse boolean to Yes or No string", "type": "object", "properties": {"handler": {"type": "string", "const": "parse_boolean_to_yes_no"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "groupField": {"title": "Group Field", "description": "group_field is used to apply our groupby logic to a field that is a list of dicts. Also allows specifying how to aggregate each of the fields in the context of the field being grouped", "type": "object", "properties": {"handler": {"type": "string", "const": "group_field"}, "grouping_field": {"description": "The grouping_field is used to determine which field or fields should be used to grouped the data sent in", "anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["handler"], "additionalProperties": {"anyOf": [{"description": "If the handler object is just a string then that string represents the field to call pluck_field on to get the value", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/uniqueList"}, {"$ref": "#/definitions/mostRecent"}, {"$ref": "#/definitions/simpleMath"}, {"$ref": "#/definitions/toDatetime"}, {"type": "string"}]}, {"description": "For the list of handlers the initial object sent in is each processed detail for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/assign"}, {"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/map"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/simpleMath"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/toDatetime"}, {"$ref": "#/definitions/uniqueList"}]}}]}}, "injectFields": {"title": "Inject Fields", "description": "inject fields is used to retrieve the value of a set of fields and add them as keys to a list of dictionaries", "type": "object", "properties": {"handler": {"type": "string", "const": "inject_fields"}, "field": {"description": "The field to add the keys to", "type": "string"}}, "required": ["handler", "field"], "additionalProperties": {"description": "The key of any additional properties will be what the field is called in outbound dictionary and the value will be the path used on the incoming data to retrieve the value", "type": "string"}}, "internalAccount": {"title": "Interal Account", "description": "interal_account takes in a uid for an account and attempts to locate an account with that uid in our database, if so it adds in the id for the account to the object. If the uid does not exist the field is set to just be the uid of the account", "type": "object", "properties": {"handler": {"type": "string", "const": "internal_account"}, "uid": {"type": "string"}, "type": {"type": "string"}}, "required": ["handler", "uid"]}, "bulkInternalAccount": {"title": "Bulk Internal Account", "description": "bulk_interal_account takes in a list of uids and attempts to locate each account with that uid in our database. If it exists, it adds in the id for the account along with the uid to the list of objects. If the uid does not exist the field is set to just be the uid of the account", "type": "object", "properties": {"handler": {"type": "string", "const": "bulk_internal_account"}, "type": {"type": "string"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "type"]}, "internalHost": {"title": "Interal Host", "description": "interal_account takes in an ip and host_session_luid for a host and attempts to locate it in our database, if so it adds in the id and dns name for the host to the object. If the host does not exist, just the ip passed in is returned", "type": "object", "properties": {"handler": {"type": "string", "const": "internal_host"}, "session_luid": {"type": "string"}, "ip": {"type": "string"}}, "required": ["handler", "ip", "session_luid"]}, "length": {"title": "Length", "description": "length can be used to either return the list of an array passed in or extract a field out of a dict and return the length of it's value.", "type": "object", "required": ["handler"], "properties": {"handler": {"type": "string", "const": "length"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}}, "listDifference": {"title": "List Difference", "description": "list_difference is used to find the difference between two sets of data", "type": "object", "properties": {"handler": {"type": "string", "const": "list_difference"}, "field_a": {"description": "The field to have the list of values subtracted from it.", "type": "string"}, "field_b": {"description": "The field to use to subtract from the other list of values.", "type": "string"}}, "required": ["handler", "field_a", "field_b"]}, "loadJson": {"title": "<PERSON><PERSON>", "description": "load_json will load a json string from a flex field", "type": "object", "required": ["handler"], "properties": {"handler": {"type": "string", "const": "load_json"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}}, "map": {"title": "Map", "description": "Map acts similar to the general data structure function, it applies a handler to every value in a field and returns the resulting list", "type": "object", "properties": {"handler": {"type": "string", "const": "map"}, "function": {"description": "Function follow the handler definition for top level fields and can be any of the ones listed below", "anyOf": [{"$ref": "#/definitions/assign"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/extractValue"}]}}, "required": ["handler", "function"]}, "matchValue": {"title": "Match Value", "description": "match_value gives the ability to use a different handler definition depending on the value of a specified field or from the incoming value in a chained handler. Will raise an error if there is no match", "type": "object", "properties": {"handler": {"type": "string", "const": "match_value"}, "field": {"description": "The field path to follow to get the value to compare to options listed as additionalProperties", "type": "string"}}, "required": ["handler"], "additionalProperties": {"description": "The key of any additional properties represents the different subtype options for the field, whichever is matched with the value for the detection detail will be used. If none match an error is thrown", "anyOf": [{"description": "If the handler object is just a string then that string represents the path in the detail to get the value for the key", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/internalHost"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sumFields"}]}, {"description": "For the list of handlers the initial object sent in is each detail for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/internalHost"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sumFields"}]}}]}}, "maxValue": {"title": "Max Value", "description": "max_value internally uses pluck_field to extract all values for a given field from a list of objects and returns the value of the max one", "type": "object", "properties": {"handler": {"type": "string", "const": "max_value"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "minValue": {"title": "Min Value", "description": "min_value internally uses pluck_field to extract all values for a given field from a list of objects and returns the value of the min one", "type": "object", "properties": {"handler": {"type": "string", "const": "min_value"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "pluckField": {"title": "<PERSON><PERSON> Field", "description": "pluck_field is used to extract a field out of a list of objects into a list of the values of that field. Primarily used interally by many of our aggregation based handlers.", "type": "object", "properties": {"handler": {"type": "string", "const": "pluck_field"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "popField": {"title": "Pop Field", "description": "pop_field is used to extract a field out of an object into a new value. Only used in the API Processor due to the other handlers creating new objects while the handler alters existing ones.", "type": "object", "properties": {"handler": {"type": "string", "const": "pop_field"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "privilegeCategory": {"title": "Privilege Category", "description": "privilege_category is used to take a number 0-9 and convert that to an object representing both the privilege and privilege category associated with that number.", "type": "object", "properties": {"handler": {"type": "string", "const": "privilege_category"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "renderTemplate": {"title": "Render Template", "description": "render_template has two primary use cases, the first is injecting the value of a field(s) into a string by including the field as an additional property in the handler definition and then surrounding it in double brackets inside the string value of the template field. The second is just displaying the string value without any fields injected into it.", "type": "object", "properties": {"handler": {"type": "string", "const": "render_template"}, "template": {"description": "The template to base the value of the field using the handler on. The value will be whatever the string is and anything inside of {{}} with be injected with the values of those keys in the handler object", "type": "string"}}, "required": ["handler", "template"], "additionalProperties": {"description": "The key of any additional properties represents a field to combine with all others. If cannot be combined, an error will be thrown", "type": "string"}}, "sumFields": {"title": "<PERSON><PERSON>", "description": "sum_fields is used to combine the values of multiple different fields together into one", "type": "object", "properties": {"handler": {"type": "string", "const": "sum_fields"}, "fields": {"description": "The list of fields containing number values to extract and sum with all others.", "type": "array", "items": {"type": "string"}}}, "required": ["handler", "fields"]}, "sort": {"title": "Sort", "description": "sort takes in a list of objects, the direction they should be sorted in, and an optional field to sort by and returns a sorted list of the data", "type": "object", "properties": {"handler": {"type": "string", "const": "sort"}, "direction": {"description": "The direction property is used to determine if the values should be ascending or descending", "type": "string", "enum": ["asc", "desc"]}, "field": {"description": "The field property when the values in the list are expected to be dictionaries and specifies which of the fields in it should be used to sort by", "type": "string"}}, "required": ["handler", "direction"]}, "sumList": {"title": "Sum List", "description": "sum_list combines all values in a list together into a single value. Takes in a field and uses pluck_field to extract all instances of it from the list of objects", "type": "object", "properties": {"handler": {"type": "string", "const": "sum_list"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "transformList": {"title": "Transform List", "description": "For a lot of our older detections we would turn all entity based fields into lists when we sent them through the api so this handler takes the place of that functionality. Not really used for newer detections. The handler itself takes in any field and transforms it into a list and if it is already a list it stays that way. Field may be excluded to use the the object sent in instead", "type": "object", "properties": {"handler": {"type": "string", "const": "transform_list"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler"]}, "truncateList": {"title": "Truncate List", "description": "Truncates any value from a list beyond a certain index to limit the length of the field", "type": "object", "properties": {"handler": {"type": "string", "const": "truncate_list"}, "length": {"description": "The max length a list is allowed to be before truncation", "type": "integer"}}, "required": ["handler", "length"]}, "uniqueList": {"title": "Unique List", "description": "unique_list is an aggregation handler that calls pluck_field on a list of objects with a given field and returns all unique objects from them", "type": "object", "properties": {"handler": {"type": "string", "const": "unique_list"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler"]}, "mostRecent": {"title": "Most Recent", "description": "most_recent is designed to, for a given field, grab the value that occured in the most recent detection detail", "type": "object", "properties": {"handler": {"type": "string", "const": "most_recent"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "toDatetime": {"title": "To Datetime", "description": "This handler will take in a list of dictionaries, and transform a given field to a datetime", "type": "object", "properties": {"handler": {"type": "string", "const": "to_datetime"}, "field": {"type": "string", "description": "The name of the field that we want to transform into a datetime"}}, "required": ["handler", "field"]}, "simpleMath": {"title": "Simple Math", "description": "simple_math is a handler that is able to apply simple math operations, eg. add and subtract a constant", "type": "object", "properties": {"handler": {"type": "string", "const": "simple_math"}, "value": {"type": "integer", "description": "The value that we want to perform the math operation on"}, "operation": {"description": "The math operation desired, eg +, -, x, /", "type": "string", "enum": ["*", "+", "-", "/"]}}, "required": ["handler", "operation", "value"]}, "uniqueValue": {"title": "Unique Value", "description": "unique_value is an aggregation handler that calls unique_list on a list of objects with a given field and assumes that only one value should remain. If only one does, the value is removed from the list and returned as the single value. If more than one is left in the list an error is thrown", "type": "object", "properties": {"handler": {"type": "string", "const": "unique_value"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "search": {"title": "Advanced Search Definition", "description": "How to define search for the summary and grouped details sections. All values must be objects other than a field `type` that says how to treat the field", "type": "object", "additionalProperties": {"type": "object", "properties": {"type": {"description": "type can be any of text, long, or date, or boolean. Types of text will be replaced with the verbose text definition before being added to the final mapping", "type": "string", "enum": ["text", "long", "date", "boolean"]}}, "additionalProperties": {"$ref": "#/definitions/search"}}}, "entityScoring": {"type": ["object"], "requird": ["activeTime", "archetypeBucket"], "properties": {"activeTime": {"description": "Number of days the detection should stay active", "type": "integer"}, "archetypeBucket": {"description": "The archetype  bucket a given detection type is categorized as", "type": ["string", "null"], "enum": ["brute", "cncLC", "cncHC", "discoveryW", "discoveryD", "lateralM", "lateralH", "lateralW", "exfilLC", "exfilHC", "monetization", "recon", "smb_ransomware", "botnet", "unk", "ransomware", null]}}}, "extractFieldsAttackGraph": {"title": "Extract Fields Attack Graph Version", "description": "extract_fields is used to construct an object using various fields from the incoming data", "type": "object", "properties": {"handler": {"type": "string", "const": "extract_fields"}}, "required": ["handler"], "additionalProperties": {"description": "The key of any additional properties will be what the field is called in outbound dictionary and the value will be the path used on the incoming data to retrieve the value", "type": "string"}}, "accountTarget": {"title": "Account Target", "description": "account_target constucts an attack graph target list based on a list of account uids", "type": "object", "properties": {"handler": {"type": "string", "const": "account_target"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler"]}, "genericTarget": {"title": "Generic Target", "description": "generic_target constucts an attack graph target list based on a list of any type of value", "type": "object", "properties": {"handler": {"type": "string", "const": "generic_target"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}, "icon": {"description": "Which icon should be used for displaying the nodes", "type": "string", "enum": ["account", "domain", "generic", "host", "ip", "service"]}, "label": {"description": "The label to use when including node in a tooltip", "type": "string"}}, "required": ["handler", "icon", "label"]}, "staticTarget": {"title": "Static Target", "description": "static_target constucts an attack graph node based on a static value not derived from the data", "type": "object", "properties": {"handler": {"type": "string", "const": "static_target"}, "value": {"description": "The value to populate the node with", "type": "string"}, "icon": {"description": "Which icon should be used for displaying the nodes", "type": "string", "enum": ["account", "domain", "generic", "host", "ip", "service"]}, "label": {"description": "The label to display for the node", "type": "string"}}, "required": ["handler", "value", "icon"]}, "internalHostTarget": {"title": "Internal Host Target", "description": "internal_host_target constucts an attack graph target list based on an internal host", "type": "object", "properties": {"handler": {"type": "string", "const": "internal_host_target"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler"]}, "externalHostTarget": {"title": "External Host Target", "description": "external_host_target constucts an attack graph target list based on an external host", "type": "object", "properties": {"handler": {"type": "string", "const": "external_host_target"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler"]}, "namedTarget": {"title": "Named Target", "description": "named_target constucts an attack graph target list based on a list objects with a `name` field", "type": "object", "properties": {"handler": {"type": "string", "const": "named_target"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}, "icon": {"description": "Which icon should be used for displaying the nodes", "type": "string", "enum": ["account", "domain", "generic", "host", "ip", "service"]}, "label": {"description": "The label to use when including node in a tooltip", "type": "string"}}, "required": ["handler", "icon", "label"]}, "papiTarget": {"title": "PAPI Target", "description": "papi_target constucts an attack graph node based on a papi detection that can be either account or host based", "type": "object", "properties": {"handler": {"type": "string", "const": "papi_target"}}, "required": ["handler"]}, "internalOrExternalHostTarget": {"title": "Internal or External Host Target", "description": "internal_or_external_host_target constucts an attack graph node based on if a host is internal or external in the data", "type": "object", "properties": {"handler": {"type": "string", "const": "internal_or_external_host_target"}}, "required": ["handler"]}, "detectionTargetHostSession": {"title": "Detection Target Host Session", "description": "detection_target_host_session creates an entries in the InternalDetectionTarget table based on one or multiple host_session_luids", "type": "object", "properties": {"handler": {"type": "string", "const": "detection_target_host_session"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}, "detectionTargetAccount": {"title": "Detection Target Account", "description": "detection_target_account creates entries in the InternalDetectionTarget table based on one or multiple singular account_uids", "type": "object", "properties": {"handler": {"type": "string", "const": "detection_target_account"}, "field": {"description": "The field property is used when the name of the field being used is not of importance to the handler", "type": "string"}}, "required": ["handler", "field"]}}, "type": "object", "required": ["version", "type", "name", "category", "bucketing", "scoring"], "properties": {"_comments": {"description": "Human-readable comments about the detection type and/or schema. Not to be used by any code!", "type": "array", "items": {"type": "string"}}, "version": {"description": "The current version the detection schema is at", "type": "integer", "minimum": 1}, "type": {"description": "The detection's unique type to be used a key throughout pipeline", "type": "string"}, "name": {"description": "The detection's unique name to be used as the display name", "type": "string"}, "detectionDescription": {"description": "Some plain-text to give more information about the detection", "type": "string"}, "dataSourceType": {"description": "The data source the detection was generated from", "type": "string"}, "category": {"description": "The detection's category in the shorthand form", "comment": "Went with the shorthand name here since this is what things are looked up with, display name can be mapped from this", "enum": ["botnet", "cnc", "custom", "exfil", "info", "lateral", "recon"]}, "detailProcessor": {"description": "The detail processor controls the flow from detection detail in the database to the initial object that begins the detection process", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "object", "additionalProperties": {"anyOf": [{"description": "If the handler object is just a string then that string represents the path in the detail to get the value for the key", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/cap_value"}, {"$ref": "#/definitions/duration"}, {"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/injectFields"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/internalHost"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/listDifference"}, {"$ref": "#/definitions/loadJson"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/privilegeCategory"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sumFields"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/transformList"}, {"$ref": "#/definitions/parseBooleanToYesNo"}]}, {"description": "For the list of handlers the initial object sent in is each detail for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/assign"}, {"$ref": "#/definitions/cap_value"}, {"$ref": "#/definitions/duration"}, {"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/internalAccount"}, {"$ref": "#/definitions/internalHost"}, {"$ref": "#/definitions/map"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/listDifference"}, {"$ref": "#/definitions/loadJson"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/privilegeCategory"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/sumFields"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/transformList"}]}}]}}}}, "groupbyProcessor": {"type": "object", "required": ["fields"], "properties": {"groupingField": {"anyOf": [{"description": "The field to use as an anchor point to group details together", "type": "string"}, {"description": "A list of fields can be used to create a composite key to group with", "type": "array", "items": {"type": "string"}}]}, "fields": {"type": "object", "additionalProperties": {"anyOf": [{"description": "If the handler object is just a string then that string represents the field to call pluck_field on to get the value", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/groupField"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/simpleMath"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/toDatetime"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/uniqueList"}, {"$ref": "#/definitions/mostRecent"}, {"$ref": "#/definitions/bulkInternalAccount"}, {"$ref": "#/definitions/duration"}]}, {"description": "For the list of handlers the initial object sent in is each processed detail for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/assign"}, {"$ref": "#/definitions/extractFields"}, {"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/groupField"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/map"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/simpleMath"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/toDatetime"}, {"$ref": "#/definitions/uniqueList"}, {"$ref": "#/definitions/bulkInternalAccount"}, {"$ref": "#/definitions/duration"}]}}]}}}}, "summaryProcessor": {"description": "The summary processor takes in the result of the groupby processor and aggregates the selected fields together to produce the summary object", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "object", "additionalProperties": {"description": "The summary handlers can either be a single handler or a list of handlers", "anyOf": [{"description": "If the handler object is just a string then that string represents the field to call pluck_field on to get the value", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/mostRecent"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/uniqueList"}, {"$ref": "#/definitions/parseBooleanToYesNo"}]}, {"description": "For the list of handlers the initial objects sent in is the list of details for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/assign"}, {"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/map"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/mostRecent"}, {"$ref": "#/definitions/renderTemplate"}, {"$ref": "#/definitions/sort"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/uniqueList"}]}}]}}}}, "syslogProcessor": {"description": "The syslog processor maps detection details to syslog fields", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "object", "additionalProperties": {"anyOf": [{"description": "if the handler object is just a string then that string represents the field to call pluck_field on to get the value", "type": "string"}, {"anyOf": [{"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/combineJsonFields"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/loadJson"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/sumFields"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/uniqueList"}]}, {"description": "For the list of handlers the initial objects sent in is the list of details for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/assign"}, {"$ref": "#/definitions/combineJsonFields"}, {"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/length"}, {"$ref": "#/definitions/loadJson"}, {"$ref": "#/definitions/pluckField"}, {"$ref": "#/definitions/map"}, {"$ref": "#/definitions/matchValue"}, {"$ref": "#/definitions/maxValue"}, {"$ref": "#/definitions/minValue"}, {"$ref": "#/definitions/sumList"}, {"$ref": "#/definitions/sumFields"}, {"$ref": "#/definitions/truncateList"}, {"$ref": "#/definitions/uniqueValue"}, {"$ref": "#/definitions/uniqueList"}]}}]}}}}, "apiProcessor": {"description": "The api processor takes in the result of the groupby and summary processors and applies any needed changes to the data before send to the api. Not Required.", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "object", "additionalProperties": {"anyOf": [{"anyOf": [{"$ref": "#/definitions/transformList"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/popField"}, {"$ref": "#/definitions/flattenList"}]}, {"description": "For the list of handlers the initial objects sent in is the list of details for the detection. Each additional handler gets the result of the last handler", "type": "array", "items": {"anyOf": [{"$ref": "#/definitions/transformList"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/popField"}, {"$ref": "#/definitions/flattenList"}]}}]}}, "excludeFields": {"description": "Fields to be excluded from the api response for the detection", "type": "array", "items": {"type": "string"}}}}, "attackGraphProcessor": {"description": "The attack graph processor takes in the result of the summary processor and constructs the list of target nodes from it", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "object", "additionalProperties": {"anyOf": [{"anyOf": [{"$ref": "#/definitions/accountTarget"}, {"$ref": "#/definitions/genericTarget"}, {"$ref": "#/definitions/staticTarget"}, {"$ref": "#/definitions/internalHostTarget"}, {"$ref": "#/definitions/externalHostTarget"}, {"$ref": "#/definitions/namedTarget"}, {"$ref": "#/definitions/internalOrExternalHostTarget"}]}, {"type": "array", "items": [{"anyOf": [{"$ref": "#/definitions/extractFieldsAttackGraph"}, {"$ref": "#/definitions/extractValue"}]}], "additionalItems": {"anyOf": [{"$ref": "#/definitions/accountTarget"}, {"$ref": "#/definitions/extractValue"}, {"$ref": "#/definitions/flattenList"}, {"$ref": "#/definitions/genericTarget"}, {"$ref": "#/definitions/internalHostTarget"}, {"$ref": "#/definitions/externalHostTarget"}, {"$ref": "#/definitions/namedTarget"}, {"$ref": "#/definitions/papiTarget"}, {"$ref": "#/definitions/internalOrExternalHostTarget"}]}}]}}, "reverseDirection": {"description": "Indicates if the direction of the edge should be reversed and to face the attributed entity", "type": "boolean"}, "blastRadius": {"description": "Indicates if the detection should considered when initiating a search for related detection instances", "type": "boolean"}}}, "detectionTargetProcessor": {"description": "The detection target processor takes in a field representing internal targets and creates attack graph nodes from it", "type": "object", "required": ["fields"], "properties": {"fields": {"type": "object", "additionalProperties": {"anyOf": [{"$ref": "#/definitions/detectionTargetHostSession"}, {"$ref": "#/definitions/detectionTargetAccount"}]}}}}, "tableDisplay": {"type": "object", "required": ["defaultSort", "columns"], "properties": {"defaultSort": {"description": "Field and order to sort by", "examples": ["last_seen.desc", "bytes_sent.asc"], "type": "string"}, "columns": {"type": "array", "items": {"type": "object", "required": ["field", "format", "label"], "not": {"required": ["columnWidth", "layoutWeight"]}, "properties": {"label": {"description": "Label to be used as the column header", "type": "string"}, "field": {"description": "Groupby field to display in the column", "type": "string"}, "format": {"description": "Formating method to apply to the grouby field", "type": "string", "enum": ["basic", "bytes", "count", "date", "duration", "number", "external_host", "internal_host", "internal_account", "protocol_port", "privilege", "template"]}, "tooltip": {"description": "Tooltip to display in the column header", "type": "string"}, "oneOf": {"columnWidth": {"description": "A string representing the fixed pixel width of the column", "type": "string"}, "layoutWeight": {"description": "A number corresponding to the layout weight for the column", "type": "number"}}}}}, "content": {"type": "array", "items": {"anyOf": [{"description": "Schema for key value pairing", "type": "object", "properties": {"definitions": {"description": "List of key (label) and value (field) pairings", "type": "array", "items": {"type": "object", "required": ["label", "field", "format"], "properties": {"label": {"description": "Label to be used as the key", "type": "string"}, "field": {"description": "Field to display as the value", "type": "string"}, "format": {"description": "Formating method to apply to the field", "type": "string", "enum": ["basic", "bytes", "external_host", "int"]}, "tooltip": {"description": "Tooltip to diplay as the key", "type": "string"}}}}, "parameters": {"description": "List of Name value pairs and created at timestamp", "type": "object", "items": {"type": "object", "required": ["label", "timestamp", "columns"], "properties": {"label": {"description": "Label to be used as the key", "type": "string"}, "timestamp": {"description": "Field to show when the parameters were read in", "type": "string"}, "columns": {"type": "array", "items": {"type": "object", "required": ["field", "format", "label"], "properties": {"label": {"description": "Label to be used as the column header", "type": "string"}, "field": {"description": "Groupby field to display in the column", "type": "string"}, "format": {"description": "Formating method to apply to the grouby field", "type": "string", "enum": ["basic", "bytes", "date", "number", "external_host", "internal_host", "internal_account", "privilege"]}, "tooltip": {"description": "Tooltip to be used as the column header", "type": "string"}}}}}}}, "command_arguments": {"description": "Name value pair and created at timestamp", "type": "object", "items": {"type": "object", "required": ["label", "timestamp", "columns"], "properties": {"label": {"description": "Label to be used as the key", "type": "string"}, "timestamp": {"description": "Field to show when the parameters were read in", "type": "string"}, "columns": {"type": "array", "items": {"type": "object", "required": ["field", "format", "label"], "properties": {"label": {"description": "Label to be used as the column header", "type": "string"}, "field": {"description": "Groupby field to display in the column", "type": "string"}, "format": {"description": "Formating method to apply to the grouby field", "type": "string", "enum": ["basic", "bytes", "date", "number", "external_host", "internal_host", "internal_account", "privilege"]}, "tooltip": {"description": "Tooltip field to display in the column", "type": "string"}}}}}}}}}, {"type": "object", "required": ["disposition", "label", "field", "table"], "properties": {"disposition": {"description": "Type of content to be shown. Either unusual or learned", "type": "string", "enum": ["unusual", "learned"]}, "label": {"description": "Text to show above the container", "type": "string"}, "field": {"description": "Field that houses the object to be displayed in the content", "type": "string"}, "tooltip": {"description": "Text to be shown in a tooltip to the right on the container", "type": "string"}, "table": {"description": "Table content supported for dynamic detection expandos", "type": "object", "required": ["defaultSort", "columns"], "properties": {"defaultSort": {"description": "Field and order to sort by", "examples": ["last_seen.desc", "bytes_sent.asc"], "type": "string"}, "columns": {"type": "array", "items": {"type": "object", "required": ["field", "format", "label"], "properties": {"label": {"description": "Label to be used as the column header", "type": "string"}, "field": {"description": "Groupby field to display in the column", "type": "string"}, "format": {"description": "Formating method to apply to the grouby field", "type": "string", "enum": ["basic", "bytes", "date", "number", "external_host", "internal_host", "internal_account", "privilege"]}}}}}}, "list": {"description": "List content supported for dynamic detection expandos", "type": "object", "required": ["format"], "properties": {"format": {"description": "The format the list items are presented in eg. basic (strings) or link", "type": "string", "enum": ["basic", "bytes", "date", "number", "external_host", "internal_host", "internal_account"]}}}}}]}}}}, "summaryDisplay": {"description": "The summary display schema describes how to take in and display the summary object created by the summary processor", "type": "object", "required": ["fields"], "properties": {"fields": {"description": "An ordered list of the fields to display in the summary and all detection expando", "type": "array", "items": {"oneOf": [{"type": "object", "required": ["label", "field", "format"], "properties": {"label": {"description": "Label to be used for the field", "type": "string"}, "field": {"description": "Summary field to display", "type": "string"}, "format": {"description": "Formating method to apply to the summary field", "type": "string", "enum": ["basic", "bytes", "concat_slice", "concat_count", "count", "date", "list", "privilege", "identity", "number"]}, "summaryLength": {"description": "This optional argument changes the number of elements concat_slice and concat_count list fields require before the alternative display method is used. Only applies to the summary sidebar", "type": "integer"}, "excludeExpando": {"description": "This optional argument makes it so that the field is only shown on the summary sidebar", "type": "boolean"}, "excludeSummary": {"description": "This optional argument makes it so that the field is only shown on the all detection expando", "type": "boolean"}}}, {"type": "object", "required": ["label", "replace_host_label", "excludeExpando", "excludeSummary"], "properties": {"label": {"description": "Label to be used for the field", "type": "string"}, "replace_host_label": {"description": "Indicates whether to replace the host label", "type": "boolean"}, "excludeExpando": {"description": "This optional argument makes it so that the field is only shown on the summary sidebar", "type": "boolean"}, "excludeSummary": {"description": "This optional argument makes it so that the field is only shown on the all detection expando", "type": "boolean"}}}]}}}}, "timelineDisplay": {"type": ["object", "null"], "required": [], "properties": {"title": {"description": "Title of the graph to convey what is being shown", "type": "string"}, "fields": {"type": "array", "items": {"type": "object", "required": ["format", "label"], "properties": {"label": {"type": "string"}, "field": {"type": "string"}, "format": {"description": "Formating method to apply to the field", "enum": ["basic"]}}}}}}, "detectionPivot": {"description": "Pivot information for going from the detection page to investigations", "type": "object", "properties": {"query": {"type": "array", "items": {"type": "array", "items": {"type": ["string", "array", "boolean"], "items": {"type": "string"}}}}}}, "infographic": {"description": "Name of the infographic to use", "type": ["string", "null"]}, "hidePCAPDownload": {"description": "If the PCAP download button should be hidden", "type": "boolean"}, "onePagerDisplay": {"description": "The one pager display schema describes the breakdown of the different sections of the page", "type": ["object", "null"], "required": [], "properties": {"content": {"description": "The different sections describing the detection", "type": "array", "items": [{"description": "The triggers for the detection", "type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "Triggers"}, "content": {"type": "array", "items": {"type": "string"}}}}, {"description": "The possible root causes for the detection", "type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "Possible Root Causes"}, "content": {"type": "array", "items": {"type": "string"}}}}, {"description": "The business impact of the detection", "type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "Business Impact"}, "content": {"type": "array", "items": {"type": "string"}}}}, {"description": "The steps to verify the detection", "type": "object", "required": ["title", "content"], "properties": {"title": {"type": "string", "const": "Steps to Verify"}, "content": {"type": "array", "items": {"type": "string"}}}}]}}}, "triage": {"description": "Defines triage for detection details.", "type": "object", "required": ["source", "additional"], "properties": {"source": {"description": "Which source fields this detection type can be triaged on", "type": "array", "minItems": 1, "items": {"type": "string", "enum": ["ip", "host", "account", "sensor", "ip_when_detected"]}}, "additional": {"description": "Triage of addl fields. Keys are triage filter field. IP, DNS, etc validation/matching logic implicit based on field name.", "type": "object", "propertyNames": {"enum": ["remote1_ip", "remote1_dns", "remote1_port", "remote1_proto", "remote1_kerb_account", "remote1_kerb_service", "remote2_ip", "remote2_dns", "remote2_port", "remote2_proto", "remote1_host", "remote2_host", "identity", "flex1", "flex2", "flex3", "flex4", "flex5", "flex6", "flex7", "flex8", "flex9", "flex10", "flex11"]}, "additionalProperties": {"type": "object", "required": ["title", "api_name", "detail_fields"], "properties": {"title": {"description": "Display name for triage condition on UI", "type": "string", "minLength": 1}, "api_name": {"description": "Customer-facing API name for triage condition", "type": "string", "minLength": 1}, "detail_fields": {"description": "Detection detail fields to match against. Can be column names like 'dst_ip' or flex_json paths like 'flex_json.foo.bar'", "type": "array", "items": {"type": "string", "minLength": 1}, "minItems": 1}, "placeholder_text": {"description": "Input placeholder text on UI", "type": "string"}, "validator": {"description": "Specific validator to be used on the field", "type": "string"}, "default_suggestions": {"description": "Default values to suggest to user in UI", "type": "object", "properties": {"values": {"type": "array", "items": {"type": "object", "properties": {"id": {"description": "Value to store in backend to match against", "type": ["string", "boolean"], "minLength": 1}, "label": {"description": "Value shown to user on UI", "type": "string", "minLength": 1}}, "required": ["id", "label"]}}}, "required": ["values"]}, "matching": {"description": "Allows specifying matching logic for rule flex1-n fields (rule field, NOT detail field). 'exact' is default if not specified. Should not be included on any other rule fields (e.g. remote1_ip)", "type": "object", "properties": {"method": {"description": "What match method to use.  'outer_wildcard' matches case-insensitively and only supports wildcards on the ends of the rule value, not in the middle.", "type": "string", "enum": ["exact", "outer_wildcard", "full_wildcard", "full_wildcard_all_match", "case_insensitive"]}}}}}}}}, "bucketing": {"type": "object", "properties": {"extraTupleFields": {"description": "Extra fields to bucket on, like 'subtype'. Values are also copied from detail to detection. Can be left empty in most cases.", "type": "array"}}, "required": ["extraTupleFields"]}, "scoring": {"type": "object", "description": "All information related to scoring detections, hosts, and accounts connected to this detection type", "anyOf": [{"required": ["detection", "account"]}, {"required": ["detection", "host"]}], "properties": {"account": {"$ref": "#/definitions/entityScoring"}, "host": {"$ref": "#/definitions/entityScoring"}, "detection": {"type": "object", "properties": {"method": {"description": "Method to use for detection scoring. 'static_latest_detail' uses the score from the latest detail in the detection bucket. 'python' uses the new python implementation.", "type": "string", "enum": ["static_latest_detail", "static_non_scoring", "python"]}, "threatLow": {"description": "The minimum threat score the detection can have", "anyOf": [{"type": "integer", "minimum": 0, "maximum": 100}, {"type": "null"}]}, "threatHigh": {"description": "The maximum threat score the detection can have", "anyOf": [{"type": "integer", "minimum": 0, "maximum": 100}, {"type": "null"}]}, "certaintyLow": {"description": "The minimum certainty score the detection can have", "anyOf": [{"type": "integer", "minimum": 0, "maximum": 100}, {"type": "null"}]}, "certaintyHigh": {"description": "The maximum certainty score the detection can have", "anyOf": [{"type": "integer", "minimum": 0, "maximum": 100}, {"type": "null"}]}}, "required": ["method", "threatLow", "threatHigh", "certaintyLow", "certaintyHigh"]}, "mitre_techniques": {"description": "A list of MITRE techniques for scoring", "type": "array", "items": {"type": "string"}}}}, "mitreDisplay": {"description": "The detection's list of MITRE Att&ck framework mappings", "type": "array", "items": {"description": "MITRE Att&ck Technique ID", "type": "string"}}, "advancedSearch": {"description": "Defines how to treat certain fields in elasticsearch. If there is a conflict between another detection schema or the base detection properties file the schema generation will fail.", "type": "object", "properties": {"groupedDetailFields": {"description": "Fields to include the in grouped details section of search", "$ref": "#/definitions/search"}, "summaryFields": {"description": "Fields to include the in summary section of search", "$ref": "#/definitions/search"}}, "additionalProperties": false}, "learnMoreLink": {"type": "string", "format": "uri"}}, "additionalProperties": false, "dependencies": {"detailProcessor": ["summaryProcessor", "apiProcessor"], "groupbyProcessor": ["detailProcessor", "summaryProcessor", "apiProcessor"], "apiProcessor": ["detailProcessor", "summaryProcessor"], "tableDisplay": ["timelineDisplay"], "timelineDisplay": ["tableDisplay"]}}