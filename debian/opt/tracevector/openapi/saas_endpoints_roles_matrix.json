{"/api/v3.4/detections/{id}/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionV3_4"}, "/api/v3.4/lockdown/": {"view_perms": ["view_account_lockdown", "view_host_lockdown"], "view_roles": ["auditor", "read_only", "restricted_admins"], "edit_perms": ["edit_account_lockdown", "edit_host_lockdown"], "edit_roles": ["restricted_admins"], "class_name": "EntityLockdownV3_4"}, "/api/v3.2/groups/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupsV3_2"}, "/api/v3.3/vectra-match/alert-stats/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchAlertStatsV3_3"}, "/api/v3.4/tagging/{table}/{entity_id}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "TaggingV3_4"}, "/api/v3.4/tagging/{table}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkTaggingV3_4"}, "/api/v3.3/vectra-match/rules/upload/{uuid}/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchRulesUploadV3_3"}, "/api/v3.3/vectra-match/assignment/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchAssignmentV3_3"}, "/api/v3.4/health/network_brain/ping/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "NetworkBrainHealthPingV3_4"}, "/api/v3.3/vectra-match/rules/upload/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchRulesUploadV3_3"}, "/api/v3.4/health/external_connectors/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "ExternalConnectorsHealthV3_4"}, "/api/v3.3/entities/": {"view_perms": ["view_account", "view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account", "edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntitiesV3_3"}, "/api/v3.3/entities/{id}/": {"view_perms": ["view_account", "view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account", "edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntityV3_3"}, "/api/v3.3/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsV3_3"}, "/api/v3.4/health/external_connectors/details/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "ExternalConnectorsHealthDetailsV3_4"}, "/api/v3.3/events/entity_scoring/": {"view_perms": ["view_account", "view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "EntityScoringEventsV3_3"}, "/api/v3.3/detections/{id}/pcap/": {"view_perms": ["view_pcap"], "view_roles": ["security_analyst"], "edit_perms": ["edit_pcap"], "edit_roles": [], "class_name": "DetectionPCAPV3_3"}, "/api/v3.4/health/edr/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "EDRHealthV3_4"}, "/api/v3.3/events/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "DetectionEventsV3_3"}, "/api/v3.3/detections/{id}/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionV3_3"}, "/api/v3.4/health/edr/details/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "EDRHealthDetailsV3_4"}, "/api/v3.4/{tvui_types}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkNotesV3_4Create"}, "/api/v3.3/{tvui_types}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkNotesV3_3Create"}, "/api/v3.4/{tvui_types}/{type_id}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3_4ViewSet"}, "/api/v3.3/lockdown/": {"view_perms": ["view_account_lockdown", "view_host_lockdown"], "view_roles": ["auditor", "read_only", "restricted_admins"], "edit_perms": ["edit_account_lockdown", "edit_host_lockdown"], "edit_roles": ["restricted_admins"], "class_name": "EntityLockdownV3_3"}, "/api/v3.4/{tvui_types}/{type_id}/notes/{note_id}/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3_4ViewSet"}, "/api/v3.3/health/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "HealthV3_3"}, "/api/v3.4/entities/": {"view_perms": ["view_account", "view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account", "edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntitiesV3_4"}, "/api/v3.3/health/{check_type}/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "HealthV3_3"}, "/api/v3.4/users/": {"view_perms": ["view_users"], "view_roles": ["restricted_admins", "security_analyst", "setting_admins"], "edit_perms": ["edit_users"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "UsersV3_4"}, "/api/v3.4/entities/{id}/": {"view_perms": ["view_account", "view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account", "edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntityV3_4"}, "/api/v3.3/threatFeeds/": {"view_perms": ["view_threat_feed"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_threat_feed"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ThreatFeedsV3_3"}, "/api/v3.3/hosts/": {"view_perms": ["view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "HostsV3_3"}, "/api/v3.2/groups/{id}/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupV3_2"}, "/api/v3/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsV3"}, "/api/v3.4/users/roles/": {"view_perms": ["view_roles"], "view_roles": ["restricted_admins", "security_analyst", "setting_admins"], "edit_perms": ["edit_roles"], "edit_roles": ["setting_admins"], "class_name": "UserRolesV3_4"}, "/api/v3.4/events/entity_scoring/": {"view_perms": ["view_account", "view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "EntityScoringEventsV3_4"}, "/api/v3.3/threatFeeds/{feed_id}/file/": {"view_perms": ["view_threat_feed"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_threat_feed"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ThreatFeedFileV3_3"}, "/api/v3.2/accounts/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountsV3_2"}, "/api/v3.3/hosts/{id}/": {"view_perms": ["view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "HostV3_3"}, "/api/v3.4/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsV3_4"}, "/api/v3.4/events/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "DetectionEventsV3_4"}, "/api/v3.2/accounts/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountV3_2"}, "/api/v3.3/threatFeeds/{feed_id}/": {"view_perms": ["view_threat_feed"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_threat_feed"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ThreatFeedV3_3"}, "/api/v3.3/events/account_scoring/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "AccountScoringEventsV3_3"}, "/api/v3/assignments/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3/assignments/{id}/resolve/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.3/events/account_detection/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountDetectionEventsV3_3"}, "/api/v3.3/unique_hosts_observed/": {"view_perms": ["view_unique_host_metric"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_unique_host_metric"], "edit_roles": [], "class_name": "UniqueHostCountMonthlyV3_3"}, "/api/v3/{tvui_types}/{type_id}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3ViewSet"}, "/api/v3.4/accounts/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountV3_4"}, "/api/v3.4/accounts/{id}/close": {"view_perms": [], "view_roles": [], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountCloseV3_4"}, "/api/v3.3/tagging/{table}/{entity_id}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "TaggingV3_3"}, "/api/v3/{tvui_types}/{type_id}/notes/{note_id}/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3ViewSet"}, "/api/v3.3/unique_hosts_observed/audit/": {"view_perms": ["view_unique_host_metric"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_unique_host_metric"], "edit_roles": [], "class_name": "UniqueHostAuditMonthlyV3_3"}, "/api/v3.3/groups/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupsV3_3"}, "/api/v3/assignment_outcomes/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.3/tagging/{table}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkTaggingV3_3"}, "/api/v3/assignment_outcomes/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.3/groups/{id}/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupV3_3"}, "/api/v3.3/accounts/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountsV3_2"}, "/api/v3/detections/{id}/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionV3"}, "/api/v3.4/detections/open/": {"view_perms": [], "view_roles": [], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsOpenV3_4"}, "/api/v3.3/users/": {"view_perms": ["view_users"], "view_roles": ["restricted_admins", "security_analyst", "setting_admins"], "edit_perms": ["edit_users"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "UsersV3_3"}, "/api/v3/events/account_detection/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "DetectionEventsV3"}, "/api/v3.1/entities/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntityV3_1"}, "/api/v3.3/accounts/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountV3_2"}, "/api/v3.3/{tvui_types}/{type_id}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3_3ViewSet"}, "/api/v3/events/account_scoring/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "AccountScoringEventsV3"}, "/api/v3/events/audits/": {"view_perms": ["view_audit_events"], "view_roles": ["auditor", "restricted_admins"], "class_name": "AuditEventsV3"}, "/api/v3/accounts/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountsV3"}, "/api/v3.3/vectra-match/enablement/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchEnablementV3_3"}, "/api/v3/accounts/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountV3"}, "/api/v3.3/vectra-match/stats/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchStatsV3_3"}, "/api/v3/rules/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RulesV3"}, "/api/v3.4/users/{id}/": {"view_perms": ["view_users"], "view_roles": ["restricted_admins", "security_analyst", "setting_admins"], "edit_perms": ["edit_users"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "UserV3_4"}, "/api/v3.1/entities/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntitiesV3_1"}, "/api/v3.4/accounts/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountsV3_4"}, "/api/v3.3/vectra-match/status/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchStatusV3_3"}, "/api/v3/rules/{rule_id}/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RuleV3"}, "/api/v3.3/vectra-match/available-devices/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchAvailableDevicesV3_3"}, "/api/v3/tagging/{table}/{entity_id}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "TaggingV3"}, "/api/v3.1/events/entity_scoring/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "EntityScoringEventsV3_1"}, "/api/v3.3/vectra-match/rules/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchRulesV3_3"}, "/api/v3/tagging/{table}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkTaggingV3"}, "/api/v3/assignments/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.3/{tvui_types}/{type_id}/notes/{note_id}/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3_3ViewSet"}, "/api/v3.4/groups/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupsV3_3"}, "/api/v3.4/settings/active_directory/groups/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ActiveDirectoryGroupsV3_4"}, "/api/v3.4/vectra-match/alert-stats/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchAlertStatsV3_3"}, "/api/v3.4/vectra-match/rules/upload/{uuid}/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchRulesUploadV3_3"}, "/api/v3.4/vectra-match/assignment/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchAssignmentV3_3"}, "/api/v3.4/vectra-match/rules/upload/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchRulesUploadV3_3"}, "/api/v3.4/detections/{id}/pcap/": {"view_perms": ["view_pcap"], "view_roles": ["security_analyst"], "edit_perms": ["edit_pcap"], "edit_roles": [], "class_name": "DetectionPCAPV3_3"}, "/api/v3.4/detections/{id}/close/": {"view_perms": [], "view_roles": [], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionCloseV3_4"}, "/api/v3.4/detections/{id}/open/": {"view_perms": [], "view_roles": [], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionOpenV3_4"}, "/api/v3.4/detections/close/": {"view_perms": [], "view_roles": [], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsCloseV3_4"}, "/api/v3.4/health/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "HealthV3_4"}, "/api/v3.4/health/{check_type}/": {"view_perms": ["view_health"], "view_roles": ["auditor", "restricted_admins"], "edit_perms": ["edit_health"], "edit_roles": [], "class_name": "HealthV3_4"}, "/api/v3.4/threatFeeds/": {"view_perms": ["view_threat_feed"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_threat_feed"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ThreatFeedsV3_3"}, "/api/v3.4/hosts/": {"view_perms": ["view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "HostsV3_3"}, "/api/v3.4/groups/{id}/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupV3_3"}, "/api/v3.4/groups/{id}/members/": {"view_perms": ["view_groups"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_groups"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "GroupV3_3"}, "/api/v3.1/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsV3"}, "/api/v3.2/detections/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionsV3"}, "/api/v3.4/threatFeeds/{feed_id}/file/": {"view_perms": ["view_threat_feed"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_threat_feed"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ThreatFeedFileV3_3"}, "/api/v3.4/hosts/{id}/": {"view_perms": ["view_host"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_host"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "HostV3_3"}, "/api/v3.4/hosts/{id}/close/": {"view_perms": [], "view_roles": [], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "HostCloseV3_4"}, "/api/v3.4/threatFeeds/{feed_id}/": {"view_perms": ["view_threat_feed"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_threat_feed"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "ThreatFeedV3_3"}, "/api/v3.1/assignments/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.2/assignments/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.3/assignments/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.4/assignments/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.1/assignments/{id}/resolve/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.2/assignments/{id}/resolve/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.3/assignments/{id}/resolve/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.4/assignments/{id}/resolve/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.4/unique_hosts_observed_monthly/": {"view_perms": ["view_unique_host_metric"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_unique_host_metric"], "edit_roles": [], "class_name": "UniqueHostCountMonthlyV3_4"}, "/api/v3.4/unique_hosts_observed/": {"view_perms": ["view_unique_host_metric"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_unique_host_metric"], "edit_roles": [], "class_name": "UniqueHostCountTimespanV3_4"}, "/api/v3.1/{tvui_types}/{type_id}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3ViewSet"}, "/api/v3.2/{tvui_types}/{type_id}/notes/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3ViewSet"}, "/api/v3.1/{tvui_types}/{type_id}/notes/{note_id}/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3ViewSet"}, "/api/v3.2/{tvui_types}/{type_id}/notes/{note_id}/": {"view_perms": ["view_note"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "NotesV3ViewSet"}, "/api/v3.4/unique_hosts_observed_monthly/audit/": {"view_perms": ["view_unique_host_metric"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_unique_host_metric"], "edit_roles": [], "class_name": "UniqueHostAuditMonthlyV3_4"}, "/api/v3.4/unique_hosts_observed/audit/": {"view_perms": ["view_unique_host_metric"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_unique_host_metric"], "edit_roles": [], "class_name": "UniqueHostAuditTimespanV3_4"}, "/api/v3.1/assignment_outcomes/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.2/assignment_outcomes/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.3/assignment_outcomes/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.4/assignment_outcomes/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.1/assignment_outcomes/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.2/assignment_outcomes/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.3/assignment_outcomes/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.4/assignment_outcomes/{id}/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentOutcomesV3ViewSet"}, "/api/v3.1/detections/{id}/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionV3"}, "/api/v3.2/detections/{id}/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_note"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "DetectionV3"}, "/api/v3.1/events/account_detection/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "DetectionEventsV3"}, "/api/v3.2/events/account_detection/": {"view_perms": ["view_detection"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "DetectionEventsV3"}, "/api/v3.2/entities/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntityV3_1"}, "/api/v3.1/events/account_scoring/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "AccountScoringEventsV3"}, "/api/v3.2/events/account_scoring/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "AccountScoringEventsV3"}, "/api/v3.1/events/audits/": {"view_perms": ["view_audit_events"], "view_roles": ["auditor", "restricted_admins"], "class_name": "AuditEventsV3"}, "/api/v3.2/events/audits/": {"view_perms": ["view_audit_events"], "view_roles": ["auditor", "restricted_admins"], "class_name": "AuditEventsV3"}, "/api/v3.3/events/audits/": {"view_perms": ["view_audit_events"], "view_roles": ["auditor", "restricted_admins"], "class_name": "AuditEventsV3"}, "/api/v3.4/events/audits/": {"view_perms": ["view_audit_events"], "view_roles": ["auditor", "restricted_admins"], "class_name": "AuditEventsV3"}, "/api/v3.1/accounts/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountsV3"}, "/api/v3.4/vectra-match/enablement/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchEnablementV3_3"}, "/api/v3.1/accounts/{id}/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AccountV3"}, "/api/v3.4/vectra-match/stats/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchStatsV3_3"}, "/api/v3.1/rules/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RulesV3"}, "/api/v3.2/rules/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RulesV3"}, "/api/v3.3/rules/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RulesV3"}, "/api/v3.4/rules/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RulesV3"}, "/api/v3.2/entities/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_account"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "EntitiesV3_1"}, "/api/v3.4/vectra-match/status/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchStatusV3_3"}, "/api/v3.1/rules/{rule_id}/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RuleV3"}, "/api/v3.2/rules/{rule_id}/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RuleV3"}, "/api/v3.3/rules/{rule_id}/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RuleV3"}, "/api/v3.4/rules/{rule_id}/": {"view_perms": ["view_triage"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_triage"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "RuleV3"}, "/api/v3.4/vectra-match/available-devices/": {"view_perms": ["view_vectra_match_setting"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_setting"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchAvailableDevicesV3_3"}, "/api/v3.1/tagging/{table}/{entity_id}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "TaggingV3"}, "/api/v3.2/tagging/{table}/{entity_id}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "TaggingV3"}, "/api/v3.2/events/entity_scoring/": {"view_perms": ["view_account"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "class_name": "EntityScoringEventsV3_1"}, "/api/v3.4/vectra-match/rules/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchRulesV3_3"}, "/api/v3.1/tagging/{table}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkTaggingV3"}, "/api/v3.2/tagging/{table}/": {"view_perms": ["view_tag"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_tag"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "BulkTaggingV3"}, "/api/v3.1/assignments/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.2/assignments/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.3/assignments/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.4/assignments/": {"view_perms": ["view_assignment"], "view_roles": ["auditor", "read_only", "restricted_admins", "security_analyst"], "edit_perms": ["edit_assignment"], "edit_roles": ["restricted_admins", "security_analyst"], "class_name": "AssignmentsV3ViewSet"}, "/api/v3.3/vectra-match/download-vectra-ruleset/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchCuratedRulesetDownloadV3_3"}, "/api/v3.4/vectra-match/download-vectra-ruleset/": {"view_perms": ["view_vectra_match_ruleset"], "view_roles": ["read_only", "restricted_admins", "setting_admins"], "edit_perms": ["edit_vectra_match_ruleset"], "edit_roles": ["restricted_admins", "setting_admins"], "class_name": "VectraMatchCuratedRulesetDownloadV3_3"}, "/api/v3.4/notification/recievers/": {"view_perms": ["view_event_receivers"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_event_receivers"], "edit_roles": ["restricted_admins"], "class_name": "NotificationReceiversV3_4"}, "/api/v3.4/notification/recievers/{id}/": {"view_perms": ["view_event_receivers"], "view_roles": ["restricted_admins"], "edit_perms": ["edit_event_receivers"], "edit_roles": ["restricted_admins"], "class_name": "NotificationReceiversV3_4"}}