# Copyright (c) 2021 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from dataclasses import dataclass
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from statistics import mean
from collections import defaultdict, namedtuple
import logging

from django.db.models import Q
from django.utils import timezone
from tvui.models import (
    CISOReportDef,
    PrioritizationTimings,
    Assignment,
    notes,
    host,
    LinkedAccount,
    host_session,
    score,
    LinkedAccountScoreHistory,
)
from .lib_reports import PDFReport
from base_tvui.host_api import HostAttributeAPI
from pure_utils import event_utils
from base_tvui.feature_flipper import flag_enabled, Flags
from base_tvui import lib_host_role

LOG = logging.getLogger(__name__)


class CISOReport(PDFReport):
    template = 'reports/operational_metrics/operational_metrics_report_pdf.html'
    page_format = 'letter_portrait_no_margin'

    def __init__(self, report_def_):
        self.report_def = report_def_
        self.end_date = timezone.now()
        self.report_start_date = timezone.now() - timedelta(days=30)
        self.start_of_prev_month = self.report_start_date - timedelta(days=30)
        self.data_cutoff_month = self.start_of_prev_month - relativedelta(months=5)

    def _get_total_prioritizations(self):
        prioritizations = list(
            PrioritizationTimings.objects.filter(time_prioritized__gte=self.data_cutoff_month, time_prioritized__lt=self.end_date).order_by(
                'time_prioritized'
            )
        )
        total_prioritizations_this_month = len([p for p in prioritizations if p.time_prioritized >= self.report_start_date])

        return prioritizations, total_prioritizations_this_month

    def _get_total_assignments(self, prioritizations):
        prioritized_host_ids = [p.entity_id for p in prioritizations if p.obj_type == 'host']
        prioritized_account_ids = [p.entity_id for p in prioritizations if p.obj_type == 'linked_account']
        ack_assignments = list(
            Assignment.objects.filter(
                (Q(obj_type='host') & Q(type_id__in=prioritized_host_ids))
                | (Q(obj_type='linked_account') & Q(type_id__in=prioritized_account_ids)),
                date_assigned__gte=self.start_of_prev_month,
                date_assigned__lt=self.end_date,
            )
            .select_related('outcome')
            .select_related('user')
            .order_by('date_assigned')
        )
        total_acknowledgments = len([a for a in ack_assignments if a.date_assigned >= self.report_start_date])

        non_ack_assignments = list(
            Assignment.objects.filter(date_assigned__gte=self.start_of_prev_month, date_assigned__lt=self.end_date)
            .exclude(id__in=(a.id for a in ack_assignments))
            .select_related('outcome')
            .select_related('user')
            .order_by('date_assigned')
        )

        return ack_assignments, total_acknowledgments, non_ack_assignments

    def _get_total_resolutions(self, assignments):
        resolved_assignments_this_month = [a for a in assignments if a.date_resolved and a.date_resolved >= self.report_start_date]
        count_outcome_category = lambda category: len([a for a in resolved_assignments_this_month if a.outcome.category == category])

        return {
            'total_resolutions': len(resolved_assignments_this_month),
            'total_resolutions_mtp': count_outcome_category('malicious_true_positive'),
            'total_resolutions_btp': count_outcome_category('benign_true_positive'),
            'total_resolutions_fp': count_outcome_category('false_positive'),
        }

    def _get_event_breakdown_stats(self, ttp_by_cat, tta_by_cat, ttr_by_cat):
        """

        :param ttp_by_cat: dict
        :param tta_by_cat: dict
        :param ttr_by_cat: dict
        :return:
        """
        totals = {}  # key = category
        mttp = {}  # key = category
        mtta = {}  # key = category
        mttr = {}  # key = category
        num_events = 0

        # count total events in each category
        for c in ('malicious_true_positive', 'benign_true_positive', 'false_positive'):
            totals[c] = len(ttr_by_cat.get(c, []))
            num_events += totals[c]

            # if there are resolved events
            if totals[c]:
                mttp[c] = mean(ttp_by_cat.get(c, []))
                mtta[c] = mean(tta_by_cat.get(c, []))
                mttr[c] = mean(ttr_by_cat.get(c, []))

        data = {
            'event_breakdown': {
                'mtp': totals['malicious_true_positive'],
                'btp': totals['benign_true_positive'],
                'fp': totals['false_positive'],
                'total': num_events,
            },
            'incident_timings': {
                'mtp': {
                    'name': 'Malicious True Positive',
                    'abbreviation': 'MTP',
                    'mttp': mttp.get('malicious_true_positive', 0),
                    'mtta': mtta.get('malicious_true_positive', 0),
                    'mttr': mttr.get('malicious_true_positive', 0),
                },
                'btp': {
                    'name': 'Benign True Positive',
                    'abbreviation': 'BTP',
                    'mttp': mttp.get('benign_true_positive', 0),
                    'mtta': mtta.get('benign_true_positive', 0),
                    'mttr': mttr.get('benign_true_positive', 0),
                },
                'fp': {
                    'name': 'False Positive',
                    'abbreviation': 'FP',
                    'mttp': mttp.get('false_positive', 0),
                    'mtta': mtta.get('false_positive', 0),
                    'mttr': mttr.get('false_positive', 0),
                },
            },
        }

        for e in data['incident_timings'].values():
            e['mtt_total'] = sum([e['mttp'], e['mtta'], e['mttr']])

        return data

    def _get_obs_privilege(self, _host, _acc):
        """
        Helper private method to handle getting host privilege level
        """
        if _host is None and _acc is None:
            return "No privilege found"

        return _host.priv_level if _host else _acc.priv_level

    def _get_impactful_event(self, event, _notes, _hosts, _linked_accs, _detections, _roles, host_max_scores, acc_max_scores):
        _note = _notes.get(event.assignment.id)
        _host = _hosts.get(event.assignment.type_id) if event.assignment.obj_type == 'host' else None
        _acc = _linked_accs.get(event.assignment.type_id) if event.assignment.obj_type == 'linked_account' else None

        event = {
            'entity_type': event.assignment.obj_type,
            'resolution_assignee': event.assignment.user.username,
            'time_to_prioritize': event.ttp,
            'timestamp_prioritized': event.ttp_date,
            'time_to_acknowledge': event.tta,
            'timestamp_acknowledged': event.tta_date,
            'time_to_respond': event.ttr,
            'timestamp_responded': event.ttr_date,
            'note': (
                {
                    'author': _note.created_by.username,
                    'timestamp': _note.date_created,
                    'note': _note.note,
                }
                if _note
                else None
            ),
            'observed_privilege': self._get_obs_privilege(_host, _acc),
            'detections': [
                {
                    'name': d.type_vname,
                    'timestamp': d.last_timestamp,
                }
                for d in _detections.get(_host or _acc, [])[:5]
            ],
            'tags': [_tag.name for _tag in (_host.tags if _host else _acc.tags).all()],
        }

        if _host:
            event['roles'] = _roles.get(_host, [])

            # Calculate detection profile AKA host archetype
            event['detection_profile'] = None
            archetype_data = _host.get_host_archetype_details()

            if archetype_data:
                event['detection_profile'] = archetype_data['vname']

            host_sessions = host_session.objects.filter(host=_host).order_by('-end')

            event['entity_name'] = _host.name
            event['last_seen_timestamp'] = (host_sessions[0].end or host_sessions[0].start) if host_sessions else None
            event['last_seen_ip'] = _host.last_source
            event['probable_account'] = _host.probable_owner['name'] if _host.probable_owner else None

            event['max_threat'] = host_max_scores.get(_host.id)[0] if _host.id in host_max_scores else None
            event['max_certainty'] = host_max_scores.get(_host.id)[1] if _host.id in host_max_scores else None
            event['host_groups'] = [{'name': hg.name, 'importance': hg.importance} for hg in _host.hostgroup_set.all()]
            event['max_urgency_score'] = host_max_scores.get(_host.id)[2] if _host.id in host_max_scores else None
        if _acc:
            event['last_seen_timestamp'] = _acc.last_seen
            event['entity_name'] = _acc.uid
            event['cloud_account'] = _acc.display_uid
            event['max_threat'] = acc_max_scores.get(_acc.id)[0] if _acc.id in acc_max_scores else None
            event['max_certainty'] = acc_max_scores.get(_acc.id)[1] if _acc.id in acc_max_scores else None
            event['max_urgency_score'] = acc_max_scores.get(_acc.id)[2] if _acc.id in acc_max_scores else None

        return event

    def _get_linked_acc_max_scores(self, acc_ids):
        _scores = LinkedAccountScoreHistory.objects.filter(
            (Q(score_date__gte=self.start_of_prev_month) & Q(score_date__lte=self.end_date)), account_id__in=acc_ids
        ).order_by('-t_score')

        max_scores = {}  # key = acc_id, value = (threat, confidence, urgency_score)

        for _s in _scores:
            if _s.account_id not in max_scores:
                max_scores[_s.account_id] = (_s.t_score, _s.c_score, _s.urgency_score)

        return max_scores

    def _get_host_max_scores(self, host_ids):
        _scores = score.objects.filter(
            (Q(timestamp__gte=self.start_of_prev_month) & Q(timestamp__lte=self.end_date))
            | (Q(end_timestamp__gte=self.start_of_prev_month) & Q(end_timestamp__lte=self.end_date)),
            host_id__in=host_ids,
        ).order_by('-threat_score')

        max_scores = {}  # key = host_id, value = (threat, confidence, urgency_score)

        for _s in _scores:
            if _s.host_id not in max_scores:
                max_scores[_s.host_id] = (_s.threat_score, _s.confidence_score, _s.urgency_score)

        return max_scores

    def _get_impactful_events(self, events):
        """
        :param events: list[ImpactfulEvent]
        :return: { 'impactful_events': list }
        """
        host_ids = [e.assignment.type_id for e in events if e.assignment.obj_type == 'host']
        acc_ids = [e.assignment.type_id for e in events if e.assignment.obj_type == 'linked_account']

        _notes = {
            _note.type_id: _note
            for _note in notes.objects.filter(type='assignment', type_id__in=[e.assignment.id for e in events]).select_related('created_by')
        }
        _hosts = {_host.id: _host for _host in host.objects.filter(id__in=host_ids)}
        _linked_accs = {_acc.id: _acc for _acc in LinkedAccount.objects.filter(id__in=acc_ids)}
        _detections = {
            entity: entity.detection_set.order_by('-last_timestamp') for entity in list(_hosts.values()) + list(_linked_accs.values())
        }
        _roles = {_host: list(dict.fromkeys(lib_host_role.fetch_sorted_host_role_list(_host))) for _host in _hosts.values()}
        linked_acc_max_scores = self._get_linked_acc_max_scores(acc_ids)
        host_max_scores = self._get_host_max_scores(host_ids)

        btp_events = [
            self._get_impactful_event(event, _notes, _hosts, _linked_accs, _detections, _roles, host_max_scores, linked_acc_max_scores)
            for event in events
            if event.assignment.outcome.category == 'benign_true_positive'
        ]
        mtp_events = [
            self._get_impactful_event(event, _notes, _hosts, _linked_accs, _detections, _roles, host_max_scores, linked_acc_max_scores)
            for event in events
            if event.assignment.outcome.category == 'malicious_true_positive'
        ]

        # sort first by descending threat score, second by ascending (chronological) response time
        btp_events = sorted(btp_events, key=lambda e: (-(e['max_threat'] or 0), e['timestamp_responded']))
        mtp_events = sorted(mtp_events, key=lambda e: (-(e['max_threat'] or 0), e['timestamp_responded']))

        return {
            'impactful_events': mtp_events + btp_events,
        }

    def _get_time_to_prioritize_stats(self, prioritizations):
        prioritizations_from_this_month = [p for p in prioritizations if p.time_prioritized >= self.report_start_date]
        prioritizations_from_last_month = [
            p for p in prioritizations if p.time_prioritized < self.report_start_date and p.time_prioritized >= self.start_of_prev_month
        ]

        mean_time_to_prioritize_this_month = None
        mean_time_to_prioritize_prev_month = None
        month_over_month_mttp = None
        ttp_this_month = [p.duration_seconds for p in prioritizations_from_this_month]

        if prioritizations_from_this_month:
            mean_time_to_prioritize_this_month = mean(ttp_this_month)

        if prioritizations_from_last_month:
            mean_time_to_prioritize_prev_month = mean(p.duration_seconds for p in prioritizations_from_last_month)

        if prioritizations_from_this_month and prioritizations_from_last_month:
            month_over_month_mttp = (mean_time_to_prioritize_this_month / mean_time_to_prioritize_prev_month - 1) * 100

        return {
            'mean_time_to_prioritize': mean_time_to_prioritize_this_month,
            'mean_ttp_month_over_month': month_over_month_mttp,
            'times_to_prioritize': ttp_this_month,
        }

    def _get_tta_stats_and_impactful_events(self, prioritizations, ack_assignments, non_ack_assignments):
        mean_time_to_ack_this_month = None
        mean_time_to_ack_prev_month = None
        month_over_month_mtta = None
        tta_list = []  # [(duration_seconds, timestamp), ...]
        ttp_by_category = defaultdict(list)  # key = category (i.e 'benign_true_positive'). value = list of durations
        tta_by_category = defaultdict(list)
        ttr_by_category = defaultdict(list)

        Event = namedtuple('Event', ['type', 'datetime', 'source'])
        TTA = namedtuple('TTA', ['duration', 'time_assigned'])

        @dataclass
        class ImpactfulEvent:
            assignment: Assignment
            ttr: int
            ttr_date: datetime
            ttp: int = None
            ttp_date: datetime = None
            tta: int = None
            tta_date: datetime = None

        impactful_events = []
        events_by_entity_id = defaultdict(list)  # key = [Event(), ...]

        for p in prioritizations:
            entity_id = ('a' if p.obj_type == 'linked_account' else 'h') + str(p.entity_id)
            events_by_entity_id[entity_id].append(Event('prioritize', p.time_prioritized, p))

        for a in ack_assignments:
            entity_id = ('a' if a.obj_type == 'linked_account' else 'h') + str(a.type_id)
            events_by_entity_id[entity_id].append(Event('acknowledge', a.date_assigned, a))

        for entity_id in events_by_entity_id:
            events = sorted(events_by_entity_id.get(entity_id), key=lambda e: e.datetime)

            # boundary condition: if we start with an Ack that has no prior prioritization
            if len(events) and events[0].type == 'acknowledge':
                non_ack_assignments.append(events[0].source)

            if len(events) <= 1:
                continue

            pairwise = lambda lst: zip(lst[:-1], lst[1:])

            for event1, event2 in pairwise(events):
                if event1.type == 'prioritize' and event2.type == 'acknowledge':
                    time_prioritized = event1.datetime
                    time_assigned = event2.datetime

                    tta = int((time_assigned - time_prioritized).total_seconds())
                    tta_list.append(TTA(tta, time_assigned))

                    if event2.source.outcome and event2.source.date_resolved > self.report_start_date:
                        assignment = event2.source
                        prioritization = event1.source
                        category = assignment.outcome.category
                        ttp = prioritization.duration_seconds
                        ttr = int((assignment.date_resolved - assignment.date_assigned).total_seconds())

                        ttp_by_category[category].append(ttp)
                        tta_by_category[category].append(tta)
                        ttr_by_category[category].append(ttr)
                        impactful_events.append(
                            ImpactfulEvent(
                                assignment=event2.source,
                                ttp=ttp,
                                ttp_date=prioritization.time_prioritized,
                                tta=tta,
                                tta_date=time_assigned,
                                ttr=ttr,
                                ttr_date=assignment.date_resolved,
                            )
                        )

                elif event2.type == 'acknowledge':
                    non_ack_assignments.append(event2.source)

        # Even for assignments that are not acknowledgements (i.e. they have no antecedent prioritization event),
        # make sure impactful events alwyays includes
        # - all MTP
        # - all BTP that have notes

        for assn in non_ack_assignments:
            if assn.outcome is None:
                continue

            if (assn.outcome.category == 'malicious_true_positive') or (assn.outcome.category == 'benign_true_positive' and assn.note):
                ttr = int((assn.date_resolved - assn.date_assigned).total_seconds())
                impactful_events.append(ImpactfulEvent(assignment=assn, ttr=ttr, ttr_date=assn.date_resolved))

        ttas_this_month = [tta.duration for tta in tta_list if tta.time_assigned >= self.report_start_date]
        ttas_prev_month = [
            tta.duration for tta in tta_list if tta.time_assigned < self.report_start_date and tta.time_assigned >= self.start_of_prev_month
        ]

        if ttas_this_month:
            mean_time_to_ack_this_month = mean(ttas_this_month)

        if ttas_prev_month:
            mean_time_to_ack_prev_month = mean(ttas_prev_month)

        if mean_time_to_ack_this_month and mean_time_to_ack_prev_month:
            month_over_month_mtta = (mean_time_to_ack_this_month / mean_time_to_ack_prev_month - 1) * 100

        stats = {
            'mean_time_to_acknowledge': mean_time_to_ack_this_month,
            'mean_tta_month_over_month': month_over_month_mtta,
            'times_to_acknowledge': ttas_this_month,
        }
        stats.update(self._get_event_breakdown_stats(ttp_by_category, tta_by_category, ttr_by_category))
        stats.update(self._get_impactful_events(impactful_events))

        return stats

    def _get_time_to_respond_stats(self, assignments):
        mean_time_to_respond_this_month = None
        mean_time_to_respond_prev_month = None
        month_over_month_mttr = None

        responses_this_month = [a for a in assignments if a.date_resolved and a.date_resolved >= self.report_start_date]
        responses_prev_month = [a for a in assignments if a.date_resolved and a.date_resolved < self.report_start_date]
        ttrs_this_month = [int((a.date_resolved - a.date_assigned).total_seconds()) for a in responses_this_month]

        if responses_this_month:
            mean_time_to_respond_this_month = mean(ttrs_this_month)

        if responses_prev_month:
            mean_time_to_respond_prev_month = mean([int((a.date_resolved - a.date_assigned).total_seconds()) for a in responses_prev_month])

        if responses_this_month and responses_prev_month:
            month_over_month_mttr = (mean_time_to_respond_this_month / mean_time_to_respond_prev_month - 1) * 100

        return {
            'mean_time_to_respond': mean_time_to_respond_this_month,
            'mean_ttr_month_over_month': month_over_month_mttr,
            'times_to_respond': ttrs_this_month,
        }

    def get_context(self):
        report_data = {
            'report': self.report_def,
            'created_datetime': timezone.now(),
            'from_datetime': self.report_start_date,
            'to_datetime': self.end_date,
            'datetime_format': 'M jS Y H:i',
            'unified_prioritization': flag_enabled(Flags.unified_prioritization),
        }

        try:
            prioritizations, total_prioritizations_this_month = self._get_total_prioritizations()
            ack_assignments, total_acknowledgments, non_ack_assignments = self._get_total_assignments(prioritizations)

            report_data.update(self._get_total_resolutions(ack_assignments + non_ack_assignments))
            report_data['total_ack_resolutions'] = self._get_total_resolutions(ack_assignments)['total_resolutions']

            report_data['total_prioritizations'] = total_prioritizations_this_month
            report_data['total_acknowledgments'] = total_acknowledgments
            report_data['percent_acknowledged'] = (
                total_acknowledgments / total_prioritizations_this_month * 100 if total_prioritizations_this_month else None
            )
            report_data['percent_responded'] = (
                report_data['total_resolutions'] / total_acknowledgments * 100 if total_acknowledgments else None
            )
        except Exception:
            LOG.exception('Unable to calculate data for CISO Report.')

        try:
            mttp_stats = self._get_time_to_prioritize_stats(prioritizations)
            report_data.update(mttp_stats)

            mtta_stats_impactful_events = self._get_tta_stats_and_impactful_events(prioritizations, ack_assignments, non_ack_assignments)
            report_data.update(mtta_stats_impactful_events)

            mttr_stats = self._get_time_to_respond_stats(ack_assignments + non_ack_assignments)
            report_data.update(mttr_stats)
        except Exception:
            LOG.exception('Unable to calculate means and month to month comparisons for CISO Report.')

        return report_data


def upload_ciso_workflow_telemetry():
    """
    Record workflow-related metrics as calculated by the CISO Report.
    This should be called daily by cloud_stats_push
    """
    try:
        # temporary CISOReport for past 30 days
        # (it doesn't actually use these dates right now, but it will in the future)
        end = timezone.now()
        start = end - timedelta(days=30)
        report_def = CISOReportDef(name='', frequency='on_demand', from_date=start, to_date=end)
        stats = CISOReport(report_def).get_context()

        # extract stats we want to upload to telemetry;
        # none of these should contain sensitive customer information

        extract_keys = [
            'total_resolutions',
            'total_resolutions_mtp',
            'total_resolutions_btp',
            'total_resolutions_fp',
            'total_prioritizations',
            'total_acknowledgments',
            'percent_acknowledged',
            'percent_responded',
            'times_to_prioritize',
            'times_to_acknowledge',
            'times_to_respond',
            'mean_time_to_respond',
            'mean_time_to_prioritize',
            'mean_time_to_acknowledge',
            'mean_ttp_month_over_month',
            'mean_tta_month_over_month',
            'mean_ttr_month_over_month',
        ]

        flattened_stats = {key: stats[key] for key in extract_keys}

        flattened_stats.update(
            {
                'mtp_mean_ttp': stats['incident_timings']['mtp']['mttp'],
                'mtp_mean_tta': stats['incident_timings']['mtp']['mtta'],
                'mtp_mean_ttr': stats['incident_timings']['mtp']['mttr'],
                #
                'btp_mean_ttp': stats['incident_timings']['btp']['mttp'],
                'btp_mean_tta': stats['incident_timings']['btp']['mtta'],
                'btp_mean_ttr': stats['incident_timings']['btp']['mttr'],
                #
                'fp_mean_ttp': stats['incident_timings']['fp']['mttp'],
                'fp_mean_tta': stats['incident_timings']['fp']['mtta'],
                'fp_mean_ttr': stats['incident_timings']['fp']['mttr'],
            }
        )

        event_utils.bundle_cloud_log(type='CISO_WORKFLOW_METRICS', doc=flattened_stats)

    except Exception:
        LOG.exception('Unable to upload CISO Report Telemetry')
