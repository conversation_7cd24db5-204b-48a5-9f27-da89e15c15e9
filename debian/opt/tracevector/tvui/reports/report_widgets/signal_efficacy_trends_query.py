import logging
from datetime import timezone

from base_tvui.sql_helper import execute_sql
from tvui.reports.report_widgets.constants import DEFAULT_NUM_MONTHS
from tvui.reports.report_widgets.query_base import QueryBase
from tvui.reports.report_widgets.signal_efficacy_trends_query_store import *

LOG = logging.getLogger(__name__)


class SignalEfficacyQuery(QueryBase):
    """
    Class to incorporate the signal efficacy metrics/Detection Resolution widget
    """

    def determine_query(self):
        """
        Determine the base SQL query to use based on RUX/QUX and attack surface filter
        """
        attack_valid = self.is_valid_attack_surface(self.params.get("attack_surface"))

        # there are only 4 cases
        if QueryBase.IS_RUX and attack_valid:
            return SF_ENABLED_RUX_AS_SQL
        elif QueryBase.IS_RUX and not attack_valid:
            return SF_ENABLED_RUX_NO_AS_SQL
        elif not QueryBase.IS_RUX and attack_valid:
            return SF_ENABLED_QUX_AS_SQL
        else:  # not QueryBase.IS_RUX and attack_valid
            return SF_ENABLED_QUX_NO_AS_SQL

    def query(self):
        final_sql = self.determine_query()
        return final_sql

    def execute(self):
        (self.params['from'], self.params['to']) = self.calculate_dates_months_ago(months_ago=DEFAULT_NUM_MONTHS)
        self.params['date_format'] = '%Y-%m'
        # This must be done as a param otherwise it will throw an exception.

        results = execute_sql(self.query(), self.params, translation={})
        return results
