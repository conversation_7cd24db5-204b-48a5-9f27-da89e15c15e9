from typing import Final, List
from collections import OrderedDict
from time import perf_counter

from django.utils.timezone import now

from base_tvui.lib_cloud_metrics import Metrics
from base_tvui.feature_flipper.flags import Flags
from base_tvui.feature_flipper.helpers import flag_enabled
from base_tvui.sql_helper import execute_sql
from tvui.reports.report_widgets.constants import DEFAULT_NUM_DAYS, REPORTING_DECIMAL_PRECISION
from tvui.reports.report_widgets.query_base import QueryBase

import logging

LOG = logging.getLogger(__name__)


class NoiseToSignalFunnelQuery(QueryBase):
    """
    This query finds number of detections created, entities track and entities prioritized.

    We also compare the results to the previous period and return the percentage change
    """

    DEFAULT_PARAMS = {'num_days': DEFAULT_NUM_DAYS}
    _return_keys: Final[List[str]] = ['label', 'sum']

    SQL = f"""-- Detections
        SELECT 'detections' as column_code, '# of Detections' as column_name, COALESCE(COUNT(*), 0) as cnt
        FROM tvui_detection A
        WHERE (A.created_datetime BETWEEN %(from)s AND %(to)s)
            AND A.category != 'INFO'
        ==attack_surface==
        UNION
        
        -- Unique Entities
            SELECT 'potential_attack_progressions' AS column_code, '# of Potential Attack Progressions' AS column_name, COALESCE(SUM(cnt), 0) FROM (
            SELECT COUNT(DISTINCT B.uid) AS cnt
            FROM tvui_detection A
            INNER JOIN tvui_account B ON A.account_id = B.id
            WHERE (A.created_datetime BETWEEN %(from)s AND %(to)s)
                AND A.category != 'INFO'
                ==attack_surface==
            GROUP BY A.account_id
        
            UNION ALL
        
            SELECT COUNT(DISTINCT B.host_id) AS cnt
            FROM tvui_detection A
            INNER JOIN tvui_host_session B ON A.host_session_id = B.id
            WHERE (A.created_datetime BETWEEN %(from)s AND %(to)s)
                ==attack_surface==
                AND A.category != 'INFO'
        ) as unique_entities
        
        UNION
        SELECT 'prioritized_alerts' AS column_code, '# of Prioritized Alerts' AS column_name, COALESCE(SUM(cnt), 0) AS cnt FROM (
            SELECT COUNT(DISTINCT tvui_linkedaccountscorehistory.linked_account_id) AS cnt
            FROM tvui_linkedaccountscorehistory
            JOIN tvui_account on tvui_linkedaccountscorehistory.linked_account_id = tvui_account.linked_account_id
            JOIN tvui_detection A on A.account_id = tvui_account.id
            WHERE (A.created_datetime BETWEEN %(from)s AND %(to)s)
            AND (tvui_linkedaccountscorehistory.created_date BETWEEN %(from)s AND %(to)s)
        ==attack_surface==
        {"AND tvui_linkedaccountscorehistory.is_prioritized = 1" if QueryBase.IS_RUX else "AND tvui_linkedaccountscorehistory.t_score >= 50"}
        GROUP BY tvui_linkedaccountscorehistory.linked_account_id
        UNION ALL
            SELECT COUNT(DISTINCT tvui_score.host_id) FROM tvui_host
            JOIN tvui_host_session on tvui_host_session.host_id = tvui_host.id
            JOIN tvui_detection A on A.host_session_id = tvui_host_session.id
            JOIN tvui_score on tvui_score.host_id = tvui_host.id
            WHERE tvui_score.timestamp BETWEEN %(from)s AND %(to)s
            AND  (A.created_datetime BETWEEN %(from)s AND %(to)s)
        ==attack_surface==
        {"AND tvui_score.is_prioritized = 1" if QueryBase.IS_RUX else "AND threat_score >= 50"}
        GROUP BY tvui_score.host_id
        ) as prioritised_entities
    """

    def query(self):
        completed_sql = self.SQL

        attack_sql = f"AND A.data_source_type =\'{self.params.get('attack_surface')}\'"

        # Return default query if not valid attack surface.
        attack_valid = self.is_valid_attack_surface(self.params.get("attack_surface"))
        completed_sql = completed_sql.replace("==attack_surface==", attack_sql if attack_valid else "")
        return completed_sql

    def execute(self):
        final_results = []

        ## find the results for the current period
        (self.params['from'], self.params['to']) = self.calculate_dates(num_days_ago=self.params['num_days'], now_datetime=now())
        start = perf_counter()
        current_period_results = execute_sql(self.query(), self.params, translation={})
        final_results += current_period_results

        ## find the results for the previous period
        # Determine the date range for the previous period
        self.previous_period_params = {'num_days': self.params['num_days']}
        (self.previous_period_params['from'], self.previous_period_params['to']) = self.calculate_dates(
            num_days_ago=self.params['num_days'], now_datetime=self.params['from']
        )

        end = perf_counter()
        metric_context = {
            **{
                'detections': int(final_results[0]['cnt']),
                'entities': int(final_results[1]['cnt']),
                'prioritised_entities': int(final_results[2]['cnt']),
            },
            **self.labels(),
        }
        Metrics.timing("vui_executive_report_funnel", end - start, metric_context)
        return final_results
