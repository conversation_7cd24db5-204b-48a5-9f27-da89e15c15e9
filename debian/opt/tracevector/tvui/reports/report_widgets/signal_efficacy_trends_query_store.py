"""
For the sake of readability, maintainability and preventing the redundant join used for getting the attack surface when we dont need it
all of the separate queries for signal efficacy will be stored here without any filtering logic, other than the date range
"""

# no attack surface param
SF_ENABLED_RUX_NO_AS_SQL = """
    SELECT
        DATE_FORMAT(closed_on, %(date_format)s) AS month,
        'All Prioritized Detections' AS column_code,
        COUNT(*) AS cnt
    FROM
        tvui_close_history
    WHERE
        closed_on BETWEEN %(from)s AND %(to)s
        AND is_prioritized = 1
    GROUP BY month

    UNION

    SELECT
        DATE_FORMAT(closed_on, %(date_format)s) AS month,
        'Closed as Remediated' AS column_code,
        COUNT(*) AS cnt
    FROM
        tvui_close_history
    WHERE
        closed_on BETWEEN %(from)s AND %(to)s
        AND is_prioritized = 1
        AND reason = 'remediated'
    GROUP BY month

    UNION

    SELECT
        DATE_FORMAT(closed_on, %(date_format)s) AS month,
        'Closed as Benign' AS column_code,
        COUNT(*) AS cnt
    FROM
        tvui_close_history
    WHERE
        closed_on BETWEEN %(from)s AND %(to)s
        AND is_prioritized = 1
        AND reason = 'benign'
    GROUP BY month

    ORDER BY month ASC, column_code;
"""

SF_ENABLED_RUX_AS_SQL = """
    SELECT
        DATE_FORMAT(close_history.closed_on, %(date_format)s) AS month,
        'All Prioritized Detections' AS column_code,
        COUNT(*) AS cnt
    FROM
        tvui_close_history close_history
    JOIN
        tvui_detection detection ON close_history.detection_id = detection.id
    WHERE
        close_history.closed_on BETWEEN %(from)s AND %(to)s
        AND close_history.is_prioritized = 1
        AND detection.data_source_type = %(attack_surface)s
    GROUP BY month

    UNION

    SELECT
        DATE_FORMAT(close_history.closed_on, %(date_format)s) AS month,
        'Closed as Remediated' AS column_code,
        COUNT(*) AS cnt
    FROM
        tvui_close_history close_history
    JOIN
        tvui_detection detection ON close_history.detection_id = detection.id
    WHERE
        close_history.closed_on BETWEEN %(from)s AND %(to)s
        AND close_history.is_prioritized = 1
        AND close_history.reason = 'remediated'
        AND detection.data_source_type = %(attack_surface)s
    GROUP BY month

    UNION

    SELECT
        DATE_FORMAT(close_history.closed_on, %(date_format)s) AS month,
        'Closed as Benign' AS column_code,
        COUNT(*) AS cnt
    FROM
        tvui_close_history close_history
    JOIN
        tvui_detection detection ON close_history.detection_id = detection.id
    WHERE
        close_history.closed_on BETWEEN %(from)s AND %(to)s
        AND close_history.is_prioritized = 1
        AND close_history.reason = 'benign'
        AND detection.data_source_type = %(attack_surface)s
    GROUP BY month

    ORDER BY month ASC, column_code;
"""

# https://jira.vectra.io/browse/NOVA-201
# no attack surface param
SF_ENABLED_QUX_NO_AS_SQL = """

"""

# https://jira.vectra.io/browse/NOVA-201
SF_ENABLED_QUX_AS_SQL = """

"""
