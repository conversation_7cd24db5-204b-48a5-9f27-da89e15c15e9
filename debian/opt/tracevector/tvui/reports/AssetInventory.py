# Copyright (c) 2021 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from datetime import timedelta
import logging
from netaddr import IPNetwork
from collections import defaultdict
from operator import itemgetter

from django.utils import timezone
import pytz

from base_tvui.host_api import HostAttributeAPI
from base_tvui.sql_helper import execute_sql
from tvui.helpers import EDR_ARTIFACT_TYPES
from tvui.models import host, HostRole

from .lib_reports import PDFReport, get_scheduled_date_range
from base_tvui.feature_flipper import flag_enabled, Flags

from pure_utils.lookup_mac_vendor import get_mac_vendor

LOG = logging.getLogger(__name__)


class AssetInventoryReport(PDFReport):
    """formerly "ExecutiveReport" """

    template = 'reports/asset_inventory/asset_inventory_pdf.html'
    page_format = 'letter_portrait_no_margin'

    def __init__(self, report_def_):
        if report_def_.frequency.lower() != 'on_demand':
            report_def_.from_date, report_def_.to_date = get_scheduled_date_range(report_def_)

        self.report_def = report_def_

    def _get_devices_observed(self):
        return get_devices_observed(self.report_def.from_date, self.report_def.to_date)

    def _get_general_data(self):
        general_data = {}

        general_data['report_name'] = self.report_def.name
        general_data['from_datetime'] = self.report_def.from_date
        general_data['to_datetime'] = self.report_def.to_date
        general_data['created_datetime'] = timezone.now()
        general_data['created_by'] = self.report_def.created_by.username if self.report_def.created_by else 'Unknown user'

        return general_data

    def _get_edr_ids(self):
        """
        Paginate through host_edr table using host API and return a list of hosts for each EDR solution.
        Also return a list of EDR types found
        :return: defaultdict
        {
            'host_luid1': ['Carbon Black', 'BitDefender', ...],
            ...
        },
        set(['Crowdstrike', 'Carbon Black', ...])
        """
        results = defaultdict(list)
        edr_types = set()

        # paginate through results and build results
        current_id = 0
        edr_attributes = HostAttributeAPI.get_attributes('host_edr', limit=1000, id_gt=current_id)

        while len(edr_attributes) > 0:
            for attr in edr_attributes:
                host_luid = attr.get('host_luid')
                edr_display_name = attr.get('edr_display_name', None)

                results[host_luid].append(edr_display_name)
                edr_types.add(edr_display_name)

            current_id = edr_attributes[-1]['id']
            edr_attributes = HostAttributeAPI.get_attributes('host_edr', limit=1000, id_gt=current_id)

        return results, edr_types

    def _get_endpoint_coverage(self):
        """
        Calculate host coverage by known EDRs
        Returns: [
           {
                'title': str,
                'count': int,
                'percent': float,
                'top_hosts': [
                    {
                        'name': str,
                        'priv_level': int,
                        't_score': int,
                        'c_score': int,
                        'urgency_score': int
                    }, ... ] }, ... ]
        """
        edr_types = set(EDR_ARTIFACT_TYPES.values())
        LIMIT_TOP = 10

        host_edr_ids, edr_id_types = self._get_edr_ids()  # get EDR from host API (EDR-id)
        edr_types = sorted(edr_types | edr_id_types, key=lambda at: at.lower())  # sort alphabetically

        sql_params = {
            'start_date': self.report_def.from_date,
            'end_date': self.report_def.to_date,
        }
        # This SQL query can take a while on larger customers.
        # If it takes too long, it causes "Endpoint request timed out" and a 500 error message.
        sql = """
            SELECT DISTINCT(tvui_host.`id`), tvui_host.`name`, tvui_host.`host_luid`
            FROM tvui_host
            JOIN (
            SELECT tvui_host_session.`host_id` FROM tvui_host_session WHERE start <= %(end_date)s AND end >= %(start_date)s
            UNION ALL
            SELECT tvui_host_session.`host_id` FROM tvui_host_session WHERE start <= %(end_date)s AND end IS NULL
            ) AS tvhs ON tvhs.`host_id` = tvui_host.`id`;
        """

        hosts = execute_sql(sql, sql_params)
        if not hosts:
            host_entries = []
        else:
            sql_params['host_ids'] = [h.id for h in hosts]

            sql = """
                SELECT * FROM `tvui_score`
                WHERE `timestamp` <= %(end_date)s AND `host_id` IN %(host_ids)s
                ORDER BY `timestamp` DESC;
            """

            scores = execute_sql(sql, sql_params)

            sql = """
                SELECT `host_id`, `priv_level` FROM `tvui_privhistory`
                WHERE `created_date` <= %(end_date)s AND `host_id` IN %(host_ids)s
                ORDER BY `created_date` DESC;
            """

            priv_entries = execute_sql(sql, sql_params)

            sql = """
                SELECT `type`, `host_id` FROM `tvui_host_artifact`
                WHERE `host_id` IN %(host_ids)s AND `type` IN %(edr_artifact_types)s;
            """

            sql_params['edr_artifact_types'] = list(EDR_ARTIFACT_TYPES.keys())
            edr_artifacts = execute_sql(sql, sql_params)

            # only iterate over these long result lists once each,
            # since they're sorted timestamp-descending, the first one we see per host will be its latest

            latest_score_by_host = {}
            for score_ in scores:
                if score_.host_id not in latest_score_by_host:
                    latest_score_by_host[score_.host_id] = score_

            latest_priv_by_host = {}
            for priv_ in priv_entries:
                if priv_.host_id not in latest_priv_by_host:
                    latest_priv_by_host[priv_.host_id] = priv_

            edr_types_by_host = defaultdict(set)
            for artf in edr_artifacts:
                edr_types_by_host[artf.host_id].add(artf.type)

            # gather hosts and record latest scores, priv, & EDR artifacts
            host_entries = []
            for host_ in hosts:
                score_ = latest_score_by_host.get(host_.id)
                priv = latest_priv_by_host.get(host_.id)

                # convert edr_artifacts from codename to displayable name
                artifacts_human_readable = [EDR_ARTIFACT_TYPES[edr_type] for edr_type in edr_types_by_host[host_.id]]
                edrs_from_edr_id = host_edr_ids.get(host_.host_luid, []) if host_.host_luid else []

                host_entries.append(
                    {
                        'name': host_.name,
                        't_score': score_.threat_score if score_ else 0,
                        'c_score': score_.confidence_score if score_ else 0,
                        'urgency_score': score_.urgency_score if score_ else 0,
                        'dfz_score': score_.dfz_score if score_ else 0,
                        'priv_level': priv.priv_level if priv else 0,
                        'edr_artifacts': list(set(artifacts_human_readable + edrs_from_edr_id)),
                    }
                )

        total_hosts = len(host_entries)

        sort_host_key = itemgetter('priv_level', 'dfz_score')

        endpoints = []

        # count hosts without EDR coverage first
        hosts_no_edrs = [h for h in host_entries if not h['edr_artifacts']]
        count = len(hosts_no_edrs)

        endpoints.append(
            {
                'title': 'No EDR Coverage',
                'top_hosts': sorted(hosts_no_edrs, key=sort_host_key, reverse=True)[:LIMIT_TOP],
                'count': count,
                'percent': (count / total_hosts) * 100 if total_hosts else 0,
            }
        )

        for title in edr_types:
            hosts_edr = [h for h in host_entries if title in h['edr_artifacts']]
            count = len(hosts_edr)

            if count:
                endpoints.append(
                    {
                        'title': title,
                        'top_hosts': sorted(hosts_edr, key=sort_host_key, reverse=True)[:LIMIT_TOP],
                        'count': count,
                        'percent': (count / total_hosts) * 100 if total_hosts else 0,
                    }
                )

        for e in endpoints:
            for h in e['top_hosts']:
                h.pop('edr_artifacts', None)  # not needed now that they're grouped

        return endpoints

    def _chunked_query(self, partial_sql, sql_params, chunk_size=10_000, pk='id', named=True):
        """
        @param partial_sql - must SELECT the pk and start its own WHERE clause and *not* include an ending semicolon
        @param named - whether to return namedtuples;
            if False, returns rows as normal tuples, and `partial_sql` must select the pk as the first column
        """

        sql = f"""
            {partial_sql}
            AND `{pk}` > %(cursor)s
            ORDER BY `{pk}` ASC
            LIMIT %(chunk_size)s;
        """

        sql_params = {**sql_params, 'cursor': 0, 'chunk_size': chunk_size}

        while True:
            results = execute_sql(sql, sql_params, named=named)
            if not results:
                return

            yield from results
            sql_params['cursor'] = getattr(results[-1], pk) if named else results[-1][0]

    def _get_privileged_entities(self):
        MAX_ENTITIES = 25  # Max 25 hosts/accounts/services
        priv_entities = {'accounts': [], 'hosts': [], 'services': []}

        sql = """
            SELECT `id`, `host_id`, `account_id`, `service_id`, `priv_level`, `created_date`
            FROM `tvui_privhistory`
            WHERE `created_date` BETWEEN %(start_date)s AND %(end_date)s
        """
        start_date = self.report_def.from_date - timedelta(hours=72)  # according to DS, privilege levels are stale after 72 hours
        sql_params = {'start_date': start_date, 'end_date': self.report_def.to_date}

        acct_last_privs = {}
        host_last_privs = {}
        srvc_last_privs = {}

        idx_host_id, idx_account_id, idx_service_id, idx_priv_level, idx_created_date = 1, 2, 3, 4, 5

        cmp_key = itemgetter(idx_created_date)

        for priv in self._chunked_query(sql, sql_params, named=False):
            if acct_id := priv[idx_account_id]:
                if acct_id not in acct_last_privs:
                    acct_last_privs[acct_id] = priv
                else:
                    last_priv = acct_last_privs[acct_id]
                    acct_last_privs[acct_id] = max(last_priv, priv, key=cmp_key)

            elif host_id := priv[idx_host_id]:
                if host_id not in host_last_privs:
                    host_last_privs[host_id] = priv
                else:
                    last_priv = host_last_privs[host_id]
                    host_last_privs[host_id] = max(last_priv, priv, key=cmp_key)

            elif srvc_id := priv[idx_service_id]:
                if srvc_id not in srvc_last_privs:
                    srvc_last_privs[srvc_id] = priv
                else:
                    last_priv = srvc_last_privs[srvc_id]
                    srvc_last_privs[srvc_id] = max(last_priv, priv, key=cmp_key)

        def get_top_entities(entity_last_privs, table_name, name_attr):
            top_entities = sorted(
                ((entity_id, priv) for entity_id, priv in entity_last_privs.items() if priv[idx_priv_level] > 0),
                key=lambda item: item[1][idx_priv_level],
                reverse=True,
            )[:MAX_ENTITIES]

            sql = f""" SELECT `id`, `{name_attr}` FROM `{table_name}` WHERE `id` IN %(entity_ids)s; """
            entities = execute_sql(sql, {'entity_ids': [id_ for id_, _ in top_entities] or [-1]})
            entity_name_map = dict(entities)

            return sorted(
                (
                    {
                        name_attr: entity_name_map[entity_id],
                        'priv_level': entity_last_privs[entity_id][idx_priv_level],
                        'priv_level_date': entity_last_privs[entity_id][idx_created_date].replace(tzinfo=pytz.UTC),
                    }
                    for entity_id, _ in top_entities
                    if entity_id in entity_name_map
                ),
                key=lambda entity: (-entity['priv_level'], entity[name_attr]),  # break ties by name alphabetically
            )

        priv_entities['accounts'] = get_top_entities(acct_last_privs, 'tvui_account', 'uid')
        priv_entities['hosts'] = get_top_entities(host_last_privs, 'tvui_host', 'name')
        priv_entities['services'] = get_top_entities(srvc_last_privs, 'tvui_service', 'uid')

        return priv_entities

    def _get_roles_observed(self):
        """

        :return:
        [
            {
                name: string,
                count: number
                hosts: [{
                    name: string,
                    t_score: number,
                    c_score: number,
                    urgency_score: number
                }],
            },
            ...
        ]
        """
        observed_roles = defaultdict(lambda: {'hosts': []})
        if flag_enabled(Flags.use_host_role_table):
            host_roles = HostRole.objects.filter(date_last_seen__gte=self.report_def.from_date).values("host_id", "role_name")
            _hosts = {
                _host.get("id"): _host
                for _host in host.objects.filter(id__in=set([_hr.get("host_id") for _hr in host_roles])).values(
                    "name", "t_score", "c_score", "urgency_score", "id"
                )
            }

        else:
            host_roles = HostAttributeAPI.get_attributes('host_role_id', last_seen_gte=self.report_def.from_date)
            _hosts = {
                _host.get("host_luid"): _host
                for _host in host.objects.filter(host_luid__in=set([_hr['host_luid'] for _hr in host_roles])).values(
                    "name", "t_score", "c_score", "urgency_score", "host_luid"
                )
            }
        result = []

        for _hr in host_roles:
            if flag_enabled(Flags.use_host_role_table):
                _h = _hosts[_hr.get("host_id")]
                _role = _hr.get("role_name")
            else:
                _h = _hosts[_hr['host_luid']]
                _role = _hr.get('role_display_name')

            if _h and _role:
                observed_roles[_role]['hosts'].append(
                    {
                        'name': _h.get("name"),
                        't_score': _h.get("t_score"),
                        'c_score': _h.get("c_score"),
                        'urgency_score': _h.get("urgency_score"),
                    }
                )

        for role, v in observed_roles.items():
            result.append({'name': role, 'count': len(v['hosts']), 'hosts': sorted(v['hosts'], key=lambda kv: kv['t_score'], reverse=True)})

        result = sorted(result, key=lambda kv: kv['count'], reverse=True)

        return result

    def get_context(self):
        report_data = {}
        report_data['unified_prioritization'] = flag_enabled(Flags.unified_prioritization)
        try:
            report_data['general_data'] = self._get_general_data()
        except Exception:
            LOG.exception('Unable to get general data for Asset Inventory Report')
            report_data['general_data'] = {}

        try:
            report_data['endpoints'] = self._get_endpoint_coverage()
        except Exception:
            LOG.exception('Unable to get endpoints for Asset Inventory Report')
            report_data['endpoints'] = []

        try:
            report_data['devices_observed'] = self._get_devices_observed()
        except Exception:
            LOG.exception('Unable to get devices observed for Asset Inventory Report')
            report_data['devices_observed'] = []

        try:
            priv_entities = self._get_privileged_entities()
            report_data['privileged_accounts'] = priv_entities['accounts']
            report_data['privileged_hosts'] = priv_entities['hosts']
            report_data['privileged_services'] = priv_entities['services']
        except Exception:
            LOG.exception('Unable to get privileged entities for Asset Inventory Report')
            report_data['privileged_accounts'] = []
            report_data['privileged_hosts'] = []
            report_data['privileged_services'] = []

        try:
            report_data['roles_observed'] = self._get_roles_observed()
        except Exception:
            LOG.exception('Unable to get roles observed for Asset Inventory Report')
            report_data['roles_observed'] = []

        return report_data


def get_devices_observed(from_date, to_date):
    UNKNOWN_VENDOR = 'Unknown OUI vendor'
    NO_MAC_ARTIFACTS = 'No MAC artifacts'
    from_date = from_date
    to_date = to_date

    # This query collects all hosts along with their artifacts
    query = """
    SELECT tvui_host.id, tvui_host.last_source, tvui_host_artifact.type, tvui_host_artifact.value
        FROM tvui_host
        LEFT JOIN tvui_host_artifact ON tvui_host.id=tvui_host_artifact.host_id
        WHERE (
            ((tvui_host.first_timestamp >= %(from_date)s AND tvui_host.first_timestamp <= %(to_date)s)
            OR (tvui_host.last_detection_timestamp >= %(from_date)s AND tvui_host.last_detection_timestamp <= %(to_date)s))
            AND tvui_host.last_source is not NULL
        );
    """

    query = execute_sql(query, {'from_date': from_date, 'to_date': to_date})

    # This data structure helps collapse duplicate host rows due to multiple artifacts
    _hosts = {}  # {id: {vendor, subnet, artifacts: ['mac', 'dhcp', ...]}, ...}
    subnet_mask = 24

    # massage data and convert mac addresses to vendor
    for i, item in enumerate(query):
        query[i] = dict(item._asdict())

        host_id = query[i]['id']
        artifact_type = query[i]['type']
        artifact_value = query[i]['value']

        if host_id not in _hosts:
            _hosts[host_id] = {
                'vendor': None,
                'subnet': IPNetwork('{}/{}'.format(query[i]['last_source'], subnet_mask)) if query[i]['last_source'] else None,
                'artifacts': [],
            }

        if artifact_type:
            _hosts[host_id]['artifacts'].append(artifact_type)

        if artifact_type == 'mac':
            vendor = get_mac_vendor(artifact_value)
            _hosts[host_id]['vendor'] = vendor if vendor else UNKNOWN_VENDOR

    # Sort hosts into buckets
    # - no artifacts
    # - no MAC artifacts
    # - vendors
    rows_dict = defaultdict(lambda: {'count': 0, 'subnets': set()})  # key = title

    for host_id in _hosts:
        _host = _hosts[host_id]
        _key = _host['vendor'] if _host['vendor'] else NO_MAC_ARTIFACTS

        rows_dict[_key]['count'] += 1
        rows_dict[_key]['subnets'].add(_host['subnet'])

    num_unknown_mac_vendor_hosts = None
    num_no_mac_address = None

    if UNKNOWN_VENDOR in rows_dict:
        num_unknown_mac_vendor_hosts = rows_dict[UNKNOWN_VENDOR]['count']
        rows_dict[UNKNOWN_VENDOR]['count'] = -1  # setting count to -1 to push to 2nd from bottom after sorting

    if NO_MAC_ARTIFACTS in rows_dict:
        num_no_mac_address = rows_dict[NO_MAC_ARTIFACTS]['count']
        rows_dict[NO_MAC_ARTIFACTS]['count'] = -2  # setting count to -2 to push to bottom after sorting

    # Create final data structure [ {title, count, subnets}, ... ]
    devices_observed = [
        {'title': key, 'count': value['count'], 'subnets': [str(ip.cidr) for ip in sorted(value['subnets'])]}
        for key, value in rows_dict.items()
    ]

    devices_observed.sort(key=lambda row: row['count'], reverse=True)

    if num_unknown_mac_vendor_hosts:
        devices_observed[-2]['count'] = num_unknown_mac_vendor_hosts  # restore true count

    if num_no_mac_address:
        devices_observed[-1]['count'] = num_no_mac_address  # restore true count

    return devices_observed
