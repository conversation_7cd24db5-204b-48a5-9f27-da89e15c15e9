# Copyright (c) 2020 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
import json
from typing import Dict, List, Tuple

from base_tvui import dict_utils
from django.forms import model_to_dict
from tvui.detections.dynamic_detections import schema_handlers
from tvui.detections.dynamic_detections.attack_graph_handlers import AttackGraphHandler
from tvui.detections.dynamic_detections.detection_target_handlers import DetectionTargetHandler
from tvui.models import detection_detail

from pure_utils.log_utils import get_vectra_logger

LOG = get_vectra_logger(__name__)


class DetailProcessor(object):
    """
    Constructs the detail list using the detail objects of a detection
    """

    def __init__(self, schema, detection_details):
        self.schema = schema
        self.detection_details = detection_details

    def process_details(self):
        handled_details = []
        handler = schema_handlers.DetailsHandler()
        for detail_object in self.detection_details:
            detail = {}
            for outbound_field, field_schema in self.schema.get('fields', {}).items():
                detail[outbound_field] = handler.get_result(detail_object, field_schema, outbound_field)
            handled_details.append(detail)
        return handled_details


class GroupbyProcessor(object):
    """
    Constructs the groupby list using the detail list of a detection
    """

    def __init__(self, schema, details_list):
        self.schema = schema
        self.details_list = details_list

    def group_and_aggregate_details(self, detection_type):
        aggregated_grouped_details = []
        handler = schema_handlers.GroupbyHandler()
        grouping_field = self.schema.get('groupingField')
        grouped_details = dict_utils.group_dict_list(self.details_list, grouping_field)

        for grouped_row in grouped_details:
            aggregated_grouped_row = {}
            for outbound_field, field_schema in self.schema.get('fields', {}).items():
                aggregated_grouped_row[outbound_field] = handler.get_result(grouped_row, field_schema, outbound_field, detection_type)
            aggregated_grouped_details.append(aggregated_grouped_row)
        return aggregated_grouped_details


class SummaryProcessor(object):
    """
    Constructs the summary object using the groupby list
    """

    def __init__(self, schema, groupby_data):
        self.schema = schema
        self.groupby_data = groupby_data

    def summarize_groupby(self, detection_type):
        summary_object = {}
        handler = schema_handlers.SummaryHandler()
        for outbound_field, field_schema in self.schema.get('fields', {}).items():
            summary_object[outbound_field] = handler.get_result(self.groupby_data, field_schema, outbound_field, detection_type)
        return summary_object


class AttackGraphProcessor(object):
    """
    Constructs the target nodes using the grouped detail data
    """

    def __init__(self, schema, detection_data, source_entity_type):
        self.schema = schema
        self.detection_data = detection_data
        self.source_entity_type = source_entity_type

    @staticmethod
    def _dedupe_nodes(nodes: list):
        seen = set()
        results = []
        for node in nodes:
            hashable_node = json.dumps(node, sort_keys=True, default=str)
            if hashable_node not in seen:
                seen.add(hashable_node)
                results.append(node)
        return results

    def process_nodes(self):
        node_list = []
        handler = AttackGraphHandler()
        for detail_object in self.detection_data:
            detail_object['source_entity_type'] = self.source_entity_type
            if self.schema:
                for outbound_field, field_schema in self.schema.get('fields', {}).items():
                    target = handler.get_result(detail_object, field_schema, outbound_field)
                    if isinstance(target, dict):
                        node_list.append(target)
                    elif isinstance(target, list):
                        node_list.extend(target)
        filtered_node_list = [node for node in node_list if node is not None]
        unique_node_list = self._dedupe_nodes(filtered_node_list)
        return unique_node_list

    def process_nodes_and_edges(self, attributed_node_id, detection_edge_data) -> Tuple[List[Dict], List[Dict]]:
        """
        For a given detection, generate the list of nodes and list of edges for the attack graph

        params:
            attributed_node_id: NodeId of the entity the detetction is attributed to
            detection_edge_data: Data to be used to constuct each of the edges alongside the Ids of the source and target nodes

        returns:
            List of nodes for the detection
            List of edges for the detection
        """
        edge_list = []
        unique_node_list = self.process_nodes()
        for node in unique_node_list:
            if not self.schema.get('reverseDirection'):
                edge_list.append(
                    {
                        **detection_edge_data,
                        'sourceId': attributed_node_id,
                        'targetId': node['nodeId'],
                    }
                )
            else:
                edge_list.append(
                    {
                        **detection_edge_data,
                        'sourceId': node['nodeId'],
                        'targetId': attributed_node_id,
                    }
                )
        return unique_node_list, edge_list


class APIProcessor(object):
    """
    Does any needed sanitation before sending the detection data to the API
    """

    def __init__(self, schema, detection_data):
        self.schema = schema
        self.detection_data = detection_data

    def _exclude_field(self, section):
        for field_to_exclude in self.schema.get('excludeFields', []):
            section.pop(field_to_exclude, None)
        return section

    def _process_section(self, section):
        handler = schema_handlers.APIHandler()
        section = self._exclude_field(section)
        for outbound_field, field_schema in self.schema.get('fields', {}).items():
            handler_result = handler.get_result(section, field_schema, outbound_field)
            # Don't add a field to the section if it doesn't exist
            if handler_result is not None:
                section[outbound_field] = handler_result
        return section

    def process_detection(self):
        self.detection_data['grouped_details'] = [
            self._process_section(grouped_detail) for grouped_detail in self.detection_data.pop('grouped_details')
        ]
        self.detection_data['summary'] = self._process_section(self.detection_data['summary'])
        return self.detection_data


class DetectionEventDetailProcessor(object):
    """
    Constructs the detail for a detection event given a detection detail. Used to populate detail information for the detection details history table, which is used for the detection event api.
    """

    EXCLUDE_FIELDS = ['src_ip', 'aws_account_id', 'src_account_uid']

    def __init__(self, schema, detection_detail):
        self.schema = schema
        self.detection_detail = detection_detail

    def process_detail(self):
        detail = {}
        handler = schema_handlers.DetailsHandler()
        if self.schema is not None:
            for outbound_field, field_schema in self.schema.get('fields', {}).items():
                if outbound_field not in DetectionEventDetailProcessor.EXCLUDE_FIELDS:
                    detail[outbound_field] = handler.get_result(self.detection_detail, field_schema, outbound_field)
        return detail


class DetectionTargetProcessor(object):
    """
    Adds one or more entries in the InternalDetectionTarget table based on one detection detail
    """

    def __init__(
        self,
        schema: dict,
        detection_detail: detection_detail,
    ):
        self.schema = schema
        self.detection_detail = model_to_dict(detection_detail)
        self.entity_is_host = self.detection_detail['is_host_detail']
        self.entity_identifier = self.detection_detail['src_session_luid'] if self.entity_is_host else self.detection_detail['account_uid']

    def process_detail(self):
        out = []
        handler = DetectionTargetHandler()
        if self.schema:
            for outbound_field, field_schema in self.schema.get('fields', {}).items():
                detail_id = self.detection_detail['id']
                LOG.info(f"Getting InternalDetectionTarget table with detection detail: {detail_id}")
                try:
                    out.extend(
                        handler.get_entries(
                            self.detection_detail, field_schema, outbound_field, self.entity_is_host, self.entity_identifier
                        )
                    )
                except TypeError:
                    # no need to log since the handler already did
                    continue
        return out
