from django.db.models import Q
from typing import List, <PERSON><PERSON>

from base_tvui.feature_flipper.conditions import is_cloud
from base_tvui.schema_utils import SchemaType, load_schemas_from_disk
from tvui.helpers import IMPORTANCE_MAPPING
from tvui.models import detection, detection_detail, host, AccountGroup, LinkedAccount

from pure_utils.log_utils import get_vectra_logger

LOG = get_vectra_logger(__name__)


BLAST_RADIUS_DETECTION_TYPES = []

for schema in load_schemas_from_disk(schema_type=SchemaType.DETECTION):
    _attack_graph = schema.get('attackGraphProcessor', {})
    if _attack_graph:
        if _attack_graph.get('blastRadius'):
            BLAST_RADIUS_DETECTION_TYPES.append(schema['type'])


def get_related_blast_radius_details(
    detection_id: int, dns_list: List[str], ip_list: List[str]
) -> Tuple[List[detection_detail], List[detection_detail]]:
    """
    Return list of details found by either dns or ip matching any from a provided list of each

    params:
        detection_id: ID of detection that the DNSs and ips came from
        dns_list: list of DNSs to query for details with
        dns_list: list of ips to query for details with

    returns:
        A tuple of a list of details matching the dns and a list of details matching the ip
    """
    base_filter = Q(
        type__in=BLAST_RADIUS_DETECTION_TYPES, host_detection__state=detection.ACTIVE, host_detection__smart_rule_id__isnull=True
    ) & ~Q(host_detection_id=detection_id)
    dns_details = detection_detail.objects.select_related('host_detection').filter(base_filter, dst_dns__in=dns_list)
    ip_details = detection_detail.objects.select_related('host_detection').filter(base_filter, dst_ip__in=ip_list)

    return dns_details, ip_details


def get_host_attack_graph_info(host_obj: host):
    """
    Returns information to populate entity graph
        Parameter:
            host_obj (host): host model

        Returns:
            host_info (list): json containing all info to pass for the frontend
    """

    host_info = {
        'id': host_obj.id,
        'name': host_obj.name,
        'lastSeenIP': host_obj.last_source,
        'tags': list(host_obj.tags.names()),
        'groups': list(host_obj.hostgroup_set.all().values_list('name', flat=True)) + host_obj.ip_groups_name,
    }

    if is_cloud():
        host_info['isPrioritized'] = host_obj.is_prioritized

    #  TODO https://jira.vectra.io/browse/BRIDGE-1484 all info about azure, vcenter, aws and gcp will be hard to test, Catamaran will add each of them as separated tickets

    return [{'key': k, 'value': v} for k, v in host_info.items()]


def get_linked_account_attack_graph_info(linked_account_obj: LinkedAccount) -> list:
    """
    Returns information to populate the attack graph
        Parameter:
            linked_account_obj (LinkedAccount): linked account model

        Returns:
            account_info (list): list containing all info in key value objects
    """
    try:
        account_info = {
            'id': linked_account_obj.id,
            'displayName': linked_account_obj.display_uid,
            'entityType': 'Account',
            'lastSeen': linked_account_obj.last_seen,
            'tags': list(linked_account_obj.tags.names()),
        }

        if is_cloud():
            account_info['isPrioritized'] = linked_account_obj.is_prioritized
            account_info['urgencyScore'] = linked_account_obj.urgency_score
            account_info['attackRating'] = linked_account_obj.attack_rating
            account_info['entityImportance'] = IMPORTANCE_MAPPING[int(linked_account_obj.entity_importance)]
        else:
            account_info['threatScore'] = linked_account_obj.t_score

        if account_archetype := linked_account_obj.get_account_archetype_details():
            account_info['attackProfile'] = account_archetype['vname']

        if network_account := linked_account_obj.network_subaccount:
            account_info['networkAccount'] = network_account.uid
            account_info['networkLockdownStatus'] = network_account.lockdown_state

        if o365_account := linked_account_obj.cloud_subaccount:
            account_info['o365Account'] = o365_account.uid
            account_info['o365LockdownStatus'] = o365_account.lockdown_state

        if aws_account := linked_account_obj.aws_subaccount:
            account_info['awsAccount'] = aws_account.uid

        if entra_account := linked_account_obj.entra_principal_subaccount:
            account_info['entraAccount'] = entra_account.clean_uid
            account_info['entraLockdownStatus'] = entra_account.lockdown_state

        account_info['groups'] = list(
            AccountGroup.objects.filter(accounts__linked_account__display_uid=linked_account_obj.uid).values_list('name', flat=True)
        )

        account_info['subaccounts'] = list(linked_account_obj.subaccounts.all().values_list('uid', flat=True))
        return [{'key': k, 'value': v} for k, v in account_info.items()]
    except Exception:
        LOG.exception('could not load account data:')
        return []
