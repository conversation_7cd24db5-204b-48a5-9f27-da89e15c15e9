from tvui.models import detection_detail, InternalDetectionTarget
from typing import List

from pure_utils.log_utils import get_vectra_logger

LOG = get_vectra_logger(__name__)


def get_details_targeting_entity(entity_identifier: str, is_host: bool) -> List[detection_detail]:
    """
    Returns information a list of detection_detail objects that target the host session / account denoted by entity identifier
        Parameter:
            entity_identifier: either host_session_luid or account_uid of the entity
            is_host: true if the identifier is a host session

        Returns:
            dets_to_create: a list of detection details
    """
    targets = []
    if is_host:
        targets = InternalDetectionTarget.objects.filter(target_host_session_luid=entity_identifier)
    else:
        targets = InternalDetectionTarget.objects.filter(target_account_uid=entity_identifier)
    seen_ids = set()
    out = []
    for target in targets:
        if target.detection_detail_id not in seen_ids:
            out.append(target.detection_detail)
            seen_ids.add(target.detection_detail_id)
    return out
