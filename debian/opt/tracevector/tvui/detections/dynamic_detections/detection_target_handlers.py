from tvui.detections.dynamic_detections.schema_handlers import <PERSON><PERSON>and<PERSON>
from tvui.models import InternalDetectionTarget
from base_tvui import dict_utils

from pure_utils.log_utils import get_vectra_logger

LOG = get_vectra_logger(__name__)


def detection_target(data_dict):
    # return one unsaved instance of InternalDetectionTarget
    return InternalDetectionTarget(**data_dict)


class DetectionTargetHandler(BaseHandler):
    # The same handler is used because the behavior is essentially the same. The reason to have both is for clarity in the dynamic schema
    _HANDLERS = {"detection_target_host_session": detection_target, "detection_target_account": detection_target}
    entries_to_create = []

    def get_entries(self, detail: dict, schema: dict, outbound_field: str, is_host: bool, entity_identifier: str):
        """
        Check the config under DetectionTargetProcessor and send to correct handler function

        params:
            detail: detection_detail dictionary to be used
            schema: denotes the handler to use and the field to look in
            outbound_field: top level field of dynamic schema, containing sub fields to be processed
            is_host: true if source entity is a host
            entity_identifier: either the source host session luid or the source account uid


        returns:
            list of unsaved InternalDetectionTarget entries
        """
        try:
            params = schema.copy()
            # get value of field passed to the dynamic detection schema in the detail
            target = dict_utils.get_value_at_path(detail, params.pop('field', None))
            # return early if target is null
            if not target:
                return []
            handler_name = params.pop(self._HANDLER_KEY)

            # since this always returns the same function, this is only for error checking
            handler_func = self._get_handler(handler_name)
            detail_id = detail['id']

            out = []
            # data to be placed in table
            split_target = target.split(',')
            for t in split_target:
                data_dict = {
                    'source_host_session_luid': entity_identifier if is_host else None,
                    'source_account_uid': entity_identifier if not is_host else None,
                    'target_host_session_luid': t if handler_name == 'detection_target_host_session' else None,
                    'target_account_uid': t if handler_name == 'detection_target_account' else None,
                    'detection_detail_id': detail_id,
                }
                out.append(handler_func(data_dict))
            return out
        except Exception:
            LOG.exception('Issue getting result for Detection Target field {}'.format(outbound_field))
            return None
