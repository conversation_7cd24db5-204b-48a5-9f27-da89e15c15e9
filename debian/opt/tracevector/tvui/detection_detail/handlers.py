# Copyright (c) 2020 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
"""
handlers.py
Classes that handle couch detection notes to create detection detail mysql records.
"""
import copy
import itertools
import json
import re

from pure_utils.log_utils import get_vectra_logger
import time

from collections import namedtuple, defaultdict
from dateutil import parser
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import django.db
from django.db.models import Q
from django.forms import model_to_dict

from base_tvui.lib_tv import calc_duration
from base_tvui.feature_flipper import flag_enabled, Flags
from base_tvui.models_utils import get_privilege_level_to_category
from tvui.detections.dynamic_detections import dynamic_processors
from tvui.models import detection_detail, host, ThreatFeedUpload, host_session, DetectionSchema, aws_account_attrs, InternalDetectionTarget
from tvui.helpers import DETECTION_CATEGORY_SHORTNAME_MAP, HEADERS
from tvui.detections.detection_types import DetectionType
from base_tvui import account_type, lib_account
from base_tvui.settings import PCAP_FILE_PATH
from tvui.triage import lib_triage
from pure_utils import event_utils, net_utils
from tvui.detection_detail import handlers_schema_foundry, handlers_publish as publish_handlers


LOG = get_vectra_logger(__name__)

# wait time for mysql errors and unexpected py_blish errors
RECONNECT_WAIT_TIME = 10
MAX_NUM_TRIES = 5


def _cloud_log_dropped_note(couch_doc, type='DETAIL_HANDLER_EXCEPTION'):
    event_utils.bundle_cloud_log(
        type=type,
        doc={
            'algo': couch_doc.get('algorithm'),
            'note_id': couch_doc.get('_id'),
            'sensor_luid': (couch_doc.get('published', {}) or {}).get('location'),
        },
    )


def check_for_timeout_error(note):
    """
    Checks that the note has not timed out otherwise checks if the couch notes have been created
    """
    if 'published' in note:
        if "timedout" not in note['published']:
            return True

    return not detection_detail.objects.filter(couch_note_id=note["_id"]).exists()


class KeySequenceDict(dict):
    """
    dict subclass used to help parse detection notes to details. Supports setting key to value of a path in another (possibly nested) dictionary
    """

    def _get_value_at_key_sequence(
        self, json_doc: Dict[str, Any], key_sequence: Union[str, List[str]], enforce_path_exists: bool = False
    ) -> Tuple[bool, Any]:
        """Gets value from json_doc (which is a possibly nested dictionary) at path

        Args:
        json_doc (Dict[str, Any]): dict to retrieve value at path from
            key_sequence (str or List[str]): path of the form "key1" or ["key1, "key2", ...]
            enforce_path_exists (bool): raises key error if not able to resolve path

        Returns:
            True or False: indicates whether value was able to be found
            value: value retrieved from json_doc. None if not found
        """
        if isinstance(key_sequence, str):
            key_sequence = [key_sequence]

        value = json_doc
        for key in key_sequence:
            if key not in value:
                if enforce_path_exists:
                    raise KeyError(f'Key {key} not found')
                return False, None
            value = value[key]
        return True, value

    def set_from_key_sequence(
        self,
        output_key: str,
        key_sequence: Union[str, List[str]],
        input_dict: Dict[str, Any],
        transformation: Optional[Callable[[Any], Any]] = None,
        enforce_path_exists: bool = False,
    ):
        """Sets key output_key of this dictionary based on input_path, a path to the key in a separate dictionary

        Args:
            output_key (str): key to set in this PathDict
            key_sequence (str or List[str]): path of the form "key1" or ["key1, "key2", ...]
            input_dict (Dict[str, Any]): dictionary to resolve input_path on obtain value from
            transformation (Optional[Callable[[Any], Any]]): unary function that is applied to value from input_dict prior to insertion to this dictionary
            enforce_path_exists (bool): raises key error if not able to resolve path
        """

        found_value, output_value = self._get_value_at_key_sequence(input_dict, key_sequence, enforce_path_exists=enforce_path_exists)

        if found_value:
            if transformation is not None:
                output_value = transformation(output_value)
            self[output_key] = output_value

    def update_from_key_sequence(
        self,
        output_key_to_input_sequence: Dict[str, Union[str, List[str]]],
        input_dict: Dict[str, Any],
        transformation: Optional[Callable[[Any], Any]] = None,
        enforce_path_exists: bool = False,
    ):
        """Sets key output_key of this dictionary based on input_path, a path to the key in a separate dictionary

        Args:
            output_key_to_input_sequence (Dict): key - key to set in this dictionary, value - sequence to find value in input dictionary
            input_dict (Dict[str, Any]): dictionary to resolve input_path on obtain value from
            transformation (Optional[Callable[[Any], Any]]): unary function that is applied to ALL values from input_dict prior to insertion to this dictionary
            enforce_path_exists (bool): raises key error if not able to resolve path
        """
        existing_kvs = dict()

        for k, path in output_key_to_input_sequence.items():
            exists, v = self._get_value_at_key_sequence(input_dict, path, enforce_path_exists=enforce_path_exists)
            if exists:
                if transformation is not None:
                    v = transformation(v)
                existing_kvs[k] = v

        self.update(existing_kvs)


class BaseDetectionHandler:
    """
    Base class of detection detail handlers.
    """

    _ip_geo_tuple = namedtuple('ip_geo_tuple', 'country_code longitude latitude')

    @staticmethod
    def extract_common_fields(
        couch_doc: Dict[str, Any], mandatory_fields: List[str] = [], excluded_fields: List[str] = []
    ) -> Dict[str, Any]:
        """Parses fields from couch_doc that are common to a significant amount of detection notes

        Args:
            couch_doc: couch doc passed to detection handler
            mandatory_fields: fields that if are absent from the detection note (and parsed in common handler) should result in an exception
            excluded_fields: fields that should be removed from common fields outputted if present (no error thrown if not present)

        Returns:
            Dict[str, Any]: Dict mapping string to value parsed for that key from input couch_doc
        """
        base_mandatory_fields = ['type', 'couch_note_id', 'sensor_luid']
        mandatory_fields = base_mandatory_fields + mandatory_fields
        mandatory_fields = [field for field in mandatory_fields if field not in excluded_fields]

        # Strictly speaking not all fields present in here are in 100% of handlers, common essentially means significant portion
        common_fields = KeySequenceDict()

        # Should be present in all notes
        common_fields.update_from_key_sequence(
            {
                'type': 'algorithm',
                'couch_note_id': '_id',
                'sensor_luid': ['published', 'location'],
                'sequence_id': '$sequence',
            },
            couch_doc,
        )

        # Timing-related fields used primarily for latency metrics

        common_fields.update_from_key_sequence(
            {
                'date_couch': ['commit', 'gmt'],
                'date_publish': ['published', 'start'],
                'date_s3': '$s3EventTime',
            },
            couch_doc,
        )

        common_fields.update_from_key_sequence(
            {
                'first_timestamp': ['required', 'start_time'],
                'last_timestamp': ['required', 'end_time'],
            },
            couch_doc,
            parser.parse,
        )

        # Tries both ways, but will prefer infected_host_session_ip if both exist
        common_fields.set_from_key_sequence('src_ip', ['required', 'infected_host_ip'], couch_doc)
        common_fields.set_from_key_sequence('src_ip', ['required', 'infected_host_session_ip'], couch_doc)

        common_fields.set_from_key_sequence('src_session_luid', ['required', 'infected_host_luid'], couch_doc)
        common_fields.set_from_key_sequence('src_session_luid', ['required', 'infected_host_session_luid'], couch_doc)

        common_fields.update_from_key_sequence(
            {
                'dst_ip': ['required', 'dest_ip'],
                'dst_dns': ['required', 'domain_name'],
                'dst_port': ['required', 'dest_port'],
            },
            couch_doc,
        )

        common_fields.set_from_key_sequence(
            'pcap_file',
            ['published', 'pcap_filename'],
            couch_doc,
            transformation=lambda s: s[len(PCAP_FILE_PATH) :] if s is not None else None,
        )

        # A few detections output what would be considered common fields into a flex field b/c of different formatting
        for key in excluded_fields:
            if key in common_fields:
                del common_fields[key]

        missing_fields = []
        for key in mandatory_fields:
            if key not in common_fields:
                missing_fields.append(key)

        if len(missing_fields) > 0:
            algo = couch_doc['algorithm'] if 'algorithm' in couch_doc else 'undefined'
            raise ValueError(f'Detection handler of note for {algo} type unabled to be parsed due to missing fields: {missing_fields}')

        return common_fields

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type. For each detection handler to implement.
        """
        raise NotImplementedError('.can_handle_detection() must be implemented')

    @classmethod
    def get_detection_target_table_entries(cls, detail, schema):
        target_processor_schema = schema.get('detectionTargetProcessor')
        if not target_processor_schema:
            return []

        dtp = dynamic_processors.DetectionTargetProcessor(target_processor_schema, detail)
        return dtp.process_detail()

    @classmethod
    def post_process_note(cls, note):
        # get newly created detection details from current note

        try:
            query = None
            if note.get('_id'):
                query = Q(couch_note_id=note['_id'])
            elif note.get('$sequence'):
                query = Q(sequence_id=note["$sequence"])
            else:
                LOG.error(f"processed note does not have an identifier")
                return
            dets = detection_detail.objects.filter(query)

            schema = None
            entries = []
            for detail in dets:
                if schema is None:
                    schema = DetectionSchema.objects.get(type=detail.type).schema
                entries.extend(cls.get_detection_target_table_entries(detail, schema))

            # create sql entries
            InternalDetectionTarget.objects.bulk_create(entries)
        except Exception as e:
            if note.get('_id'):
                LOG.exception(f"error in post processing of detection details from couch note {note['_id']} with exception {e}")
            else:
                # this note has a sequence ID since it would have been caught earlier otherwise
                LOG.exception(f"error in post processing of detection details from couch note {note['$sequence']} with exception {e}")

    @classmethod
    def dispatch_detection(cls, note):
        """
        Redirect each detection document to its handler based on the detection type.
        """
        handled = False
        error = None

        handled = handlers_schema_foundry.dispatch(note)

        if handled:
            return

        for handler in cls.__subclasses__():
            if handler.can_handle_detection(note):
                # Close MySQL connection periodically, so ORM can reopen it
                if check_for_timeout_error(note):
                    # TODO why are we closing the connection everytime a detection is to be inserted from anywhere?
                    django.db.connection.close()
                    error = handler.process_detection_detail(note)
                handled = True

        if not handled:
            error = 'No handlers found for couch doc: id={}, algo={}'.format(note.get('_id'), note.get('algorithm'))
            LOG.warning(error)

        cls.post_process_note(note)

        return error

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Creates a sql detection detail record off of a couch detection doc. For each detection handler to implement.
        """
        raise NotImplementedError('.create_sql_row() must be implemented')

    @classmethod
    def process_detection_detail(cls, note):
        """
        General handler of retries for a single detection detail note. Delegates sql record creation to
        create_sql_row.
        """
        error = None
        num_tries = 0
        while num_tries < MAX_NUM_TRIES:
            try:
                cls.create_sql_row(note)
                break
            except django.db.utils.OperationalError:
                # Keep re-trying if the issue is MySQL connectivity
                LOG.exception('MySQL error. Sleeping for a while and retrying.')
                time.sleep(RECONNECT_WAIT_TIME)
            except KeyError as excpt:
                LOG.exception('Detection detail has missing required keys')
                return repr(excpt)
            except Exception as excpt:
                num_tries += 1
                error = repr(excpt)

                field_id = ""
                if note_id := note.get("_id", None):
                    field_id = f'couch_doc id {note_id}'
                elif sequence_id := note.get("$sequence", None):
                    field_id = f'sequence id {sequence_id}'
                msg = f'Error [{type(excpt)}] while processing detection {field_id}. Algorithm {note.get("algorithm")}. Try number {num_tries}'

                if num_tries < MAX_NUM_TRIES:
                    LOG.warning(msg)
                else:
                    LOG.exception(msg)
        return error

    @classmethod
    def sanitize_ip_address(cls, ip_address):
        """
        Determines if an ip is ipv4 or ipv6 and if so attempts to parse out port data that may be sent along with ipv4 addresses
        """
        if not ip_address:
            return None

        # Parse ipv4 addresses *******:8080
        # Port is optional and can be separated by a colon or hyphen. Hyphen is not preferred, but is rarely used.
        if '.' in ip_address:
            ip_address = re.split(r'[:-]', ip_address)[0]

        # Parse ipv6 addresses [2001:0db8:85a3:0000:0000:8a2e:0370:7334]:8080
        if '[' in ip_address:
            ip_address = ip_address[ip_address.index('[') + 1 : ip_address.index(']')]

        return ip_address

    @classmethod
    def get_geo_data(cls, ip_address):
        """
        Look up an ip address' country code, latitude, and longitude.

        NOTE: This geoip feature has been disabled due to licensing concerns.
              Return NO geoip information while this feature is dissabled.
        """
        return cls._ip_geo_tuple(country_code=None, longitude=None, latitude=None)


class ExfilEventsHandler(BaseDetectionHandler):
    """
    Process detection notes for exfil events such as smash and grab (smash_n_grab)
    and Cloud Smuggler (cloud_smuggler) detections
    """

    @classmethod
    def can_handle_detection(cls, couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in ['smash_n_grab', 'cloud_smuggler']

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create detection details from the corresponding couch detection note.
        """
        first_timestamp = couch_doc['required']['start_time']
        last_timestamp = couch_doc['required']['end_time']
        new_det_details = []
        for exfil_dest in couch_doc['required']['exfil_destinations']:
            # If the timestamp is missing from exfil_dest, default to using the timestamp
            # that this note was created
            start_time = exfil_dest.get('start_time') or first_timestamp
            end_time = exfil_dest.get('end_time') or last_timestamp
            fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['first_timestamp', 'last_timestamp']),
                'dst_ip': exfil_dest['dest_ip'],
                'dst_dns': exfil_dest['domain_name'],
                'proto': exfil_dest['protocol'],
                'dst_port': exfil_dest['dest_port'],
                'total_bytes_sent': exfil_dest['bytes_sent'],
                'first_timestamp': parser.parse(start_time),
                'last_timestamp': parser.parse(end_time),
                'flex1': ','.join(couch_doc['required']['site_wide_popular_destinations']),
                'flex2': str(exfil_dest['duration_seconds']),
                'flex3': exfil_dest.get('event_id', None),  # only for ismash (new smash_n_grab)
                'flex4': couch_doc['required'].get('subnet', None),
                'flex5': couch_doc['required'].get('normal_bytes_sent', None),
                'flex6': exfil_dest.get('proxy_external_dest_ip'),
            }

            new_dd = detection_detail(**fields)
            new_det_details.append(new_dd)

        if new_det_details:
            det_detail = new_det_details[0]
            det_detail.skip_bytes_sent = couch_doc['required']['skip']['bytes_sent']

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class SuspiciousHTTPHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'http_cnc' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'http_cnc'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a suspicious http detection detail from the corresponding couch detection note.
        """
        first_timestamp = parser.parse(couch_doc['required']['start_time'])
        new_det_details = []
        for detail in couch_doc['required']['details']:
            dst_ip = detail.get('dest_ip')
            fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['first_timestamp', 'last_timestamp']),
                'subtype': detail['type'],
                'dst_dns': detail.get('domain_name'),
                'dst_ip': dst_ip,
                'reason': str(detail['reason']),
                'total_bytes_sent': detail['bytes_sent'],
                'total_bytes_rcvd': detail['bytes_recvd'],
                'last_timestamp': parser.parse(detail['end_time']) if detail.get('end_time') else first_timestamp,
                'first_timestamp': first_timestamp,
                'count': 1,
                'description': detail.get('user_agent'),
                'flex1': detail.get('http_method'),
                'flex2': detail.get('url'),
                'flex3': detail.get('referer'),
                'flex4': detail.get('host'),
                'flex5': detail.get('reply_cache_control'),
                'flex6': str(detail['score']) if detail.get('score') else None,
            }

            new_dd = detection_detail(**fields)
            new_det_details.append(new_dd)
        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for httpcnc detection %s', couch_doc['_id'])


class SMBRansomwareHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'smb_ransomware' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'smb_ransomware'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a ransomware detection detail from the corresponding couch detection note.
        """
        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_port', 'last_timestamp']),
            'total_bytes_sent': couch_doc['required']['write_bytes'],
            'total_bytes_rcvd': couch_doc['required']['read_bytes'],
            'dst_session_luid': couch_doc['required'].get('dest_luid'),
            'flex2': ','.join(couch_doc['required'].get('ransom_notes')),
        }

        if 'files_grouped_by_share' in couch_doc['required']:
            # newer notes ransomware v3) use files_grouped_by_share, and may have multiple shares in a single note
            new_det_details = []
            for share_group in sorted(couch_doc['required']['files_grouped_by_share'], key=lambda x: x.get('share_path')):
                enc_files = [
                    (efile['encrypted_path'] + "\\" if 'encrypted_path' in efile and efile['encrypted_path'] != '' else '')
                    + efile['encrypted_file']
                    for efile in sorted(share_group['encrypted_files'], key=lambda x: x.get('encrypted_file'))
                ]
                new_fields = copy.deepcopy(fields)
                new_fields.update(
                    {
                        'count': share_group['count_encrypted_docs'],
                        'description': share_group['share_path'],
                        'flex1': ','.join(share_group['extensions']),
                        'reason': ','.join(enc_files),
                        'total_bytes_sent': share_group['write_bytes'],
                        'total_bytes_rcvd': share_group['read_bytes'],
                    }
                )
                new_dd = detection_detail(**new_fields)
                new_det_details.append(new_dd)
            dd_cnt = len(new_det_details)
            detection_detail.objects.bulk_create(new_det_details)
        else:
            # This is a legacy note, either v1 or v2
            fields.update(
                {
                    'reason': ','.join(couch_doc['required'].get('encrypted_files')),
                    'count': couch_doc['required']['count_encrypted_docs'],
                    'description': couch_doc['required']['mounted_share'],
                    # comma-join if 'extensions' is a list (ransomware v2), otherwise anticipate it has already been comma-joined (ransomware v1)
                    'flex1': (
                        ','.join(couch_doc['required']['extensions'])
                        if isinstance(couch_doc['required']['extensions'], list)
                        else couch_doc['required']['extensions']
                    ),
                }
            )
            dd_cnt = 1
            new_dd = detection_detail(**fields)
            new_dd.save()
        LOG.info('Created %s detection detail record(s) for smb_ransomware detection %s', dd_cnt, couch_doc['_id'])


class SqlInjectHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'sql-inject' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'sql_inject'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a sql injection detection detail from the corresponding couch detection note.
        """
        first_timestamp = parser.parse(couch_doc['required']['start_time'])
        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_port', 'first_timestamp']),
            'total_bytes_sent': couch_doc['required']['bytes_sent'],
            'total_bytes_rcvd': couch_doc['required']['bytes_recvd'],
            'skip_bytes_sent': couch_doc['required']['skip']['bytes_sent'],
            'skip_bytes_rcvd': couch_doc['required']['skip']['bytes_recvd'],
            'skip_count': couch_doc['required']['skip']['count'],
            'last_timestamp': parser.parse(couch_doc['required']['end_time']) if couch_doc['required'].get('end_time') else first_timestamp,
            'dst_session_luid': couch_doc['required'].get('dest_luid'),
            'flex1': couch_doc['required'].get('http_segment'),
            'flex2': couch_doc['required'].get('user_agent'),
            'flex3': couch_doc['required'].get('sql_frag'),
            'flex4': str(couch_doc['required']['response_code']) if couch_doc['required'].get('response_code') else None,
            'flex5': couch_doc['required'].get('x_forwarded_for'),
            'flex6': couch_doc['required'].get('x_forwarded_for_host_session_luid'),
        }

        new_dd = detection_detail(**fields)
        new_dd.save()
        LOG.info('Create detection details for sql_inject detection %s', couch_doc['_id'])


class ShellKnockerHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'shell_knocker_c2s' and 'shell_knocker_s2c' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in ('shell_knocker_s2c', 'shell_knocker_c2s')

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a shell knocker detection detail from the corresponding couch detection note.
        """
        if couch_doc['algorithm'].endswith('c2s'):
            bytes_sent = couch_doc['required']['client_bytes']
            bytes_rcvd = couch_doc['required']['server_bytes']
            dst_ip = couch_doc['required']['dest_ip']
            dst_session_luid = couch_doc['required']['dest_luid']
            dst_port = couch_doc['required']['dest_port']
            flex6 = None
        else:
            bytes_sent = couch_doc['required']['server_bytes']
            bytes_rcvd = couch_doc['required']['client_bytes']
            dst_ip = couch_doc['required']['src_ip']
            dst_session_luid = couch_doc['required']['src_luid']
            dst_port = None
            flex6 = str(couch_doc['required']['dest_port'])
        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['last_timestamp']),
            'dst_ip': dst_ip,
            'dst_port': dst_port,
            'total_bytes_sent': bytes_sent,
            'total_bytes_rcvd': bytes_rcvd,
            'dst_session_luid': dst_session_luid,
            'flex1': ','.join(couch_doc['required']['anomalous_client']),
            'flex2': ','.join(couch_doc['required']['normal_client']),
            'flex3': ','.join(couch_doc['required']['anomalous_server']),
            'flex4': ','.join(couch_doc['required']['normal_server']),
            'flex5': str(couch_doc['required'].get('next_learn_in_time_hrs')),
            'flex6': flex6,
        }

        new_dd = detection_detail(**fields)
        new_dd.save()
        LOG.info('Created detection detail record for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class WatchmenHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'watchmen' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'watchmen'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a watchmen detection detail form the corresponding couch detection note.
        """

        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_port']),
            'dst_session_luid': couch_doc['required']['dest_luid'],
            'first_timestamp': parser.parse(couch_doc['required']['start_time']),
            'last_timestamp': parser.parse(couch_doc['required']['start_time']),
            'proto': couch_doc['required']['protocol'],
            'total_bytes_sent': couch_doc['required']['bytes_sent'],
            'total_bytes_rcvd': couch_doc['required']['bytes_recvd'],
            'flex1': couch_doc['required'].get('via_luid'),
            'flex2': couch_doc['required'].get('learning_timeout'),
            'flex_json': {
                'normal_access': couch_doc['required']['normal_access'],
                'normal_admins': couch_doc['required']['normal_admins'],
            },
        }

        new_dd = detection_detail(**fields)
        new_dd.save()
        LOG.info('Created detection detail record for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class KerberosUserAccountScanHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'kerberos_user_account_scan' to create a detection detail per realm.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'kerberos_user_account_scan'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create kerberos account scan detection details from the corresponding couch detection note.
        """
        realms = defaultdict(lambda: {'total_attempts': 0, 'server_luids': set(), 'accounts': set(), 'server_ips': set()})
        for kdc in couch_doc.get('required', {}).get('servers', []):
            for acct in kdc.get('accounts', []):
                realm = acct['realm']
                realms[realm]['total_attempts'] += acct['count']
                realms[realm]['accounts'].add(acct['account'])
                realms[realm]['server_luids'].add(kdc['dest_luid'])
                realms[realm]['server_ips'].add(kdc['dest_ip'])
        new_det_details = []
        for realm, realm_vals in realms.items():
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['last_timestamp']),
                'count': realm_vals['total_attempts'],
                'flex1': realm,
                'flex2': ','.join(dest_ip for dest_ip in realm_vals['server_ips']),
                'flex3': ','.join(dest_luid for dest_luid in realm_vals['server_luids']),
                'flex4': ','.join(acct for acct in realm_vals['accounts']),
            }

            new_dd = detection_detail(**fields)
            new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for kerberos_user_account_scan detection %s', couch_doc['_id'])


class KerberosClientAccountAnomalyHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'kerberos_client_anomaly' to create a detection detail
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'kerberos_client_anomaly'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create kerberos account anomaly details from the corresponding couch detection note.
        """
        new_det_details = []
        for rezn in couch_doc['required']['reason']:
            reason = dict(kv for kv in rezn.items())
            krb_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['last_timestamp']),
                'subtype': "reason_detail",
                'count': couch_doc['required']['count'],
                'reason': reason['reason_code'],
                'identity': couch_doc['required'].get('kerberos_id', ''),
            }

            if reason['reason_code'] == "unusual_dc":
                krb_fields['flex1'] = ','.join(kv['id'] for kv in reason['reason_baseline'])
                krb_fields['flex2'] = ','.join(kv['id'] for kv in reason['reason_list'])
                krb_fields['flex3'] = ','.join(kv['dest_luid'] for kv in reason['reason_baseline'] if 'dest_luid' in kv)
                krb_fields['flex4'] = ','.join(kv['dest_luid'] for kv in reason['reason_list'] if 'dest_luid' in kv)
            elif reason['reason_code'] == "unusual_acct" or reason['reason_code'] == "unusual_serv":
                krb_fields['flex1'] = ','.join(kv["id"] for kv in reason['reason_baseline'])
                krb_fields['flex2'] = ','.join(kv["id"] for kv in reason['reason_list'])
            else:
                krb_fields['flex1'] = reason['reason_baseline'][0]['id']
                krb_fields['flex2'] = reason['reason_list'][0]['id']

            new_dd = detection_detail(**krb_fields)
            new_det_details.append(new_dd)

        for s in couch_doc.get('required', {}).get('servers', []):
            for a in s['accounts']:
                sa_fields = {
                    **cls.extract_common_fields(couch_doc),
                    'subtype': "account_detail",
                    'dst_ip': s['dest_ip'],
                    'dst_session_luid': s.get('dest_luid'),
                    'src_session_luid': krb_fields['src_session_luid'],
                    'src_ip': krb_fields['src_ip'],
                    'first_timestamp': krb_fields['first_timestamp'],
                    'last_timestamp': krb_fields['last_timestamp'],
                    'flex1': a['account'],
                    'flex2': a['anomalous'] if 'anomalous' in a else 'false',
                    'flex3': a['count'],
                    'flex4': parser.parse(a['last_seen']),
                    'flex5': a['services'],
                }
                new_dd = detection_detail(**sa_fields)
                new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for kerberos_client_account_anomaly detection %s', couch_doc['_id'])


class KerberosServerAccessHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'kerberos_server_access' to create detection detail records.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'kerberos_server_access'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create Kerberos Server Access detection details from the corresponding couch detection note.

        The couch doc has a list of reasons under the reason_code field. Each reason_code has a list of reason_ids,
        which are generated differently for each reason.
        Given the client-account data instances, we determine whether an account is an instance of the various reasons.

        unusual_acct: if account exists: reason_id="account['account'],account['service']" otherwise: reason_id="account['service']"
        unusual_client: reason_id="client['dest_ip']"
        unusual_serv: reason_id="account['service']"
        denied_volume: if account has a first_seen field, it is a denied volume instance
        """
        new_det_details = []
        krb_base_fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['last_timestamp']),
            'subtype': "reason_detail",
            'count': couch_doc['required']['count'],
            'reason': '',
            'identity': couch_doc['required'].get('kerberos_id', ''),
        }

        clients = couch_doc['required']['hosts']
        reasons = [reason['reason_code'] for reason in couch_doc['required']['reason']]
        unusual_acct_inst = 'unusual_acct' in reasons
        unusual_client_inst = 'unusual_client' in reasons
        unusual_serv_inst = 'unusual_serv' in reasons
        denied_volume_inst = 'denied_volume' in reasons

        baselines = {'unusual_acct': 0, 'unusual_client': 0, 'unusual_serv': 0, 'denied_volume': 0}
        for reason in couch_doc['required']['reason']:
            if reason['reason_code'] == 'denied_volume':
                for baseline in reason['reason_baseline']:
                    baselines[reason['reason_code']] += int(baseline['id'])
            else:
                baselines[reason['reason_code']] = reason['reason_baseline_length']

        reason_ids = [reason_id['id'] for reason in couch_doc['required']['reason'] for reason_id in reason['reason_list']]
        for client in clients:
            for account in client['accounts']:
                client_acct_reasons = []
                if unusual_acct_inst:
                    if account['account']:
                        unusual_acct_id = account['account'] + "," + account['service']
                    else:
                        unusual_acct_id = account['service']
                    if unusual_acct_id in reason_ids:
                        client_acct_reasons.append('unusual_acct')

                if unusual_client_inst and client['dest_ip'] in reason_ids:
                    client_acct_reasons.append('unusual_client')

                if unusual_serv_inst and account['service'] in reason_ids:
                    client_acct_reasons.append('unusual_serv')

                if denied_volume_inst and 'first_seen' in account:
                    client_acct_reasons.append('denied_volume')

                if client_acct_reasons:
                    client_acct_fields = copy.deepcopy(krb_base_fields)
                    client_acct_fields['dst_ip'] = client['dest_ip']
                    client_acct_fields['dst_session_luid'] = client.get('dest_luid')
                    client_acct_fields['count'] = account['count']
                    client_acct_fields['flex1'] = account['account']
                    client_acct_fields['flex2'] = account['service']
                    client_acct_fields['flex3'] = account['error_type']

                    client_acct_reason_baselines = []
                    for reason in client_acct_reasons:
                        client_acct_reason_baselines.append(str(baselines[reason]))

                    client_acct_fields['flex4'] = ",".join(client_acct_reasons)
                    client_acct_fields['flex5'] = ",".join(client_acct_reason_baselines)

                    client_acct_fields['last_timestamp'] = parser.parse(account['last_seen'])

                    if 'denied_volume' in client_acct_reasons and 'first_seen' in account:
                        client_acct_fields['first_timestamp'] = parser.parse(account['first_seen'])

                    new_dd = detection_detail(**client_acct_fields)
                    new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for kerberos_server_access detection %s', couch_doc['_id'])


class SMBBruteForceHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'smb_brute_force' to create a detection detail per reason code.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'smb_brute_force'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create smb brute force detection details from the corresponding couch detection note.
        """
        new_det_details = []
        for err_code, err_count in couch_doc.get('required', {}).get('count_response', {}).items():
            users = couch_doc['required'].get('users')
            shares = couch_doc['required'].get('shares')
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_port', 'last_timestamp']),
                'description': 'SMB Brute-Force',
                'dst_session_luid': couch_doc['required']['dest_luid'],
                'reason': err_code,
                'count': err_count,
                'flex1': users if users is None or isinstance(users, str) else ','.join(users),
                'flex2': shares if shares is None or isinstance(shares, str) else ','.join(shares),
            }

            new_dd = detection_detail(**fields)
            new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for smb_brute_force detection %s', couch_doc['_id'])


class SMBPSExecHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'smb_psexec' to create one detection detail per note
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'smb_psexec'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create smb_psexec detection detail record for corresponding couch detection note.
        """
        BASELINE_LIMIT = 100
        anomalous_fields = [field for field in ['user', 'src_host', 'dst_host'] if couch_doc['required'].get('anomalous_' + field, False)]

        baseline_lengths = []
        baseline_details = {}
        for field in anomalous_fields:
            baseline_lengths.append(couch_doc['required']['baseline_{}_length'.format(field)])
            baseline_values = couch_doc['required'].get('baseline_{}s'.format(field)) or []
            baseline_details['baseline_{}s'.format(field)] = baseline_values[:BASELINE_LIMIT] if baseline_values else baseline_values

        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_port', 'last_timestamp']),
            'description': 'Suspicious Remote Execution',
            'dst_session_luid': couch_doc['required']['dest_luid'],
            'reason': ','.join(anomalous_fields),
            'flex1': couch_doc['required'].get('uuid'),
            'flex2': couch_doc['required'].get('user'),
            'flex3': couch_doc['required'].get('named_pipe'),
            'flex4': ','.join(str(baseline) for baseline in baseline_lengths),
            'flex5': ','.join(couch_doc['required']['functions']) if couch_doc['required'].get('functions') else None,
            'flex6': json.dumps(baseline_details) if baseline_details else None,
        }

        detection_detail.objects.create(**fields)
        LOG.info('Created detection detail record for smb_psexec detection %s', couch_doc['_id'])


class InternalDataGathererHandler(BaseDetectionHandler):
    """
    Key Data Gathering
    Process detection notes of type 'idg' to create a detection detail per reason and destination.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'idg'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create idg detection details from the corresponding couch detection note.
        """
        new_det_details = []
        common_fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_port', 'last_timestamp']),
            'proto': couch_doc['required']['protocol'],
            'flex6': couch_doc['required']['time_window_update_hours'],
        }

        for reason_group in couch_doc['required']['reasons']:
            for reason_label, reasons in reason_group.items():
                # This loop should only be entered into once, using loop for safety
                for instance in reasons:
                    if reason_label not in ('neighbors', 'neighbors_heuristics', 'activity'):
                        fields = {'dst_ip': instance['host_ip'], 'dst_session_luid': instance['host_session_luid'], 'reason': reason_label}
                    else:
                        fields = {'reason': reason_label}

                    if reason_label == 'download':
                        fields.update(
                            {
                                'count': instance.get('nb_connections', 0),
                                'total_bytes_rcvd': instance['recv_bytes'],
                                'flex1': instance['recv_bytes_baseline'],
                            }
                        )
                    elif reason_label == 'download_other':
                        fields.update(
                            {
                                'count': instance.get('nb_connections', 0),
                                'total_bytes_rcvd': instance['recv_bytes'],
                                'flex1': instance['recv_bytes_baseline'],
                                'flex2': ','.join(instance['seen_hosts_session_luid']),
                                'flex3': ','.join(instance['seen_hosts_ip']),
                            }
                        )
                    elif reason_label == 'download_heuristic':
                        fields.update({'count': instance.get('nb_connections', 0), 'total_bytes_rcvd': instance['recv_bytes']})
                    elif reason_label == 'connection':
                        fields.update({'count': instance['nb_connections'], 'flex1': instance['nb_connections_baseline']})
                    elif reason_label == 'connection_other':
                        fields.update(
                            {
                                'count': instance['nb_connections'],
                                'flex1': instance['nb_connections_baseline'],
                                'flex2': ','.join(instance['seen_hosts_session_luid']),
                                'flex3': ','.join(instance['seen_hosts_ip']),
                            }
                        )
                    elif reason_label == 'connection_heuristic':
                        fields.update({'count': instance['nb_connections']})
                    elif reason_label == 'neighbors':
                        fields.update(
                            {
                                'count': couch_doc['required']['attempts'],
                                'flex1': instance['neighbors_baseline'],
                                'flex2': ','.join(instance['list_neighbors_session_luid']),
                                'flex3': ','.join(instance['list_neighbors_ip']),
                                'flex4': ','.join(instance['training_neighbors_session_luid']),
                                'flex5': ','.join(instance['training_neighbors_ip']),
                            }
                        )
                    elif reason_label == 'neighbors_heuristics':
                        fields.update(
                            {
                                'count': couch_doc['required']['attempts'],
                                'flex2': ','.join(instance['list_neighbors_session_luid']),
                                'flex3': ','.join(instance['list_neighbors_ip']),
                            }
                        )
                    elif reason_label == 'activity':
                        fields.update({'flex1': instance['baseline'], 'flex2': instance['value']})
                    else:
                        LOG.error('Unknown reason %s in couch note %s of type %s', reason_label, couch_doc['_id'], couch_doc['algorithm'])

                    fields.update(common_fields)
                    new_dd = detection_detail(**fields)
                    new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for idg detection %s', couch_doc['_id'])


class SMBEnumShareHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'smb_enum_share' to create detection details per unique dest_luid
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'smb_enum_share'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create smb_enum_share detection detail records for the corresponding couch detection note for each unique dest_luid.
        """
        # generate shared common detection details (common_dd)
        common_dd = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_port', 'last_timestamp']),
            'description': 'File Share Enumeration',
            'flex1': couch_doc['required'].get('users'),  # flex1 is csv of users
        }

        # create detection details based off of unique dest_luid
        aggregate_by_dest_luid = defaultdict(lambda: {'dst_session_luid': None, 'flex2': set(), 'count': 0, 'dst_ip': None})
        for share in couch_doc['required']['shares']:
            aggregate_by_dest_luid[share['dest_luid']]['dst_session_luid'] = share['dest_luid']
            aggregate_by_dest_luid[share['dest_luid']]['dst_ip'] = share['dest_ip']
            aggregate_by_dest_luid[share['dest_luid']]['flex2'].add(share['mounted_share'])  # flex2 is csv of mounted_shares
            aggregate_by_dest_luid[share['dest_luid']]['count'] += 1

        new_dd = []
        for adl_vals in aggregate_by_dest_luid.values():
            # Combine each
            adl_vals = {**common_dd, **adl_vals}
            adl_vals['flex2'] = ','.join(adl_vals['flex2'])
            new_dd.append(detection_detail(**adl_vals))

        detection_detail.objects.bulk_create(new_dd)
        LOG.info('Created %s detection detail records for smb_enum_share detection %s', len(aggregate_by_dest_luid), couch_doc['_id'])


class USBSmugglerHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'usb_smuggler' to create a detection detail for the corresponding couch note
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'usb_smuggler'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a usb_smuggler detection detail from the corresponding couch detection note.
        """
        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip'], excluded_fields=['first_timestamp', 'last_timestamp']),
            'dst_dns': couch_doc['required'].get('domain_name'),
            'description': 'USB Device Insertion',
            'first_timestamp': parser.parse(couch_doc['required']['timestamp']),
            'last_timestamp': parser.parse(couch_doc['required']['timestamp']),
            'flex1': couch_doc['required']['usb_device'],
            'flex2': couch_doc['required']['usb_type'],
            'flex3': couch_doc['required']['usb_vendor'],
        }

        detection_detail.objects.create(**fields)
        LOG.info('Created all detection details for usb_smuggler detection %s', couch_doc['_id'])


class SuspiciousRemoteDesktopHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'rdp_anomaly' to create a detection detail for the corresponding couch note
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'rdp_anomaly'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a rdp_anomaly detection detail from the corresponding couch detection note.
        """
        new_det_details = []

        reason_dic = {
            'new_keyboard_layout_cookie': 'client_keyboard',
            'new_clientDigProductId_cookie': 'client_product_id',
            'new_keyboard_layout_server': 'server_keyboard',
            'new_keyboard_layout_global': 'global_keyboard',
        }
        for reason in couch_doc['required']['detection_description']:
            fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['dst_ip']),
                'dst_ip': couch_doc['required']['dst_ip'],
                'dst_session_luid': couch_doc['required']['dst_host_luid'],
                'dst_dns': couch_doc['required']['infected_rdp_session'].get('dstDisplayName'),
                'description': 'Suspicious Remote Desktop',
                'first_timestamp': parser.parse(couch_doc['required']['infected_rdp_session']['timestamp_datetime']),
                'last_timestamp': parser.parse(couch_doc['required']['infected_rdp_session']['timestamp_datetime']),
                'dst_port': couch_doc['required']['infected_rdp_session']['dstPort'],
                'reason': reason_dic[reason],
                'flex1': couch_doc['required']['infected_rdp_session']['clientName'],
                'flex2': couch_doc['required']['infected_rdp_session']['cookie'],
                'flex3': couch_doc['required']['infected_rdp_session']['clientDigProductId'],
                'flex4': couch_doc['required']['infected_rdp_session']['keyboard_country'],
                'flex5': couch_doc['required']['infected_rdp_session']['keyboard_id'],
                'flex6': json.dumps(
                    {
                        'clientDigProductIds': couch_doc['required']['state'].get('clientDigProductIds'),
                        'last_seen': couch_doc['required']['state']['last_seen'],
                        'first_seen': couch_doc['required']['state']['first_seen'],
                        'keyboard_info': couch_doc['required']['state'].get('keyboard_info'),
                    }
                ),
                'flex_json': {'cookie_type': couch_doc['required']['infected_rdp_session'].get('cookieType')},
            }
            new_dd = detection_detail(**fields)
            new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for rdp_anomaly detection %s', couch_doc['_id'])


class SMBAccountScanHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'smb_enum_user' to create detection details for each unique dest_luid
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'smb_enum_user'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create smb_enum_user detection detail records for the corresponding couch detection note for each unique dest_luid.
        """
        # shared field values across detection detail records from this couch doc
        common_dd_fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_port', 'last_timestamp']),
            'description': 'SMB Account Scan',
            'dst_port': couch_doc['required']['dest_port'],
            'last_timestamp': parser.parse(couch_doc['required']['end_time']),
        }

        # aggregate users for each unique dest_luid
        dest_luid_details = defaultdict(lambda: {'dst_session_luid': None, 'dst_ip': None, 'flex1': set()})
        for user in couch_doc['required']['users']:
            dest_luid_details[user['dest_luid']]['dst_session_luid'] = user['dest_luid']
            dest_luid_details[user['dest_luid']]['dst_ip'] = user['dest_ip']
            dest_luid_details[user['dest_luid']]['flex1'].add(user['user'])

        new_dd = []
        for dest_luid_fields in dest_luid_details.values():
            dest_luid_fields['count'] = len(dest_luid_fields['flex1'])
            dest_luid_fields['flex1'] = ','.join(dest_luid_fields['flex1'])
            dest_luid_fields = {**common_dd_fields, **dest_luid_fields}
            new_dd.append(detection_detail(**dest_luid_fields))

        detection_detail.objects.bulk_create(new_dd)
        LOG.info('Created %s detection detail records for smb_enum_user detection %s', len(new_dd), couch_doc['_id'])


class PortScanHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'port_scan' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'port_scan'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create port_scan detection detail records for the corresponding couch detection note.
        """
        fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp'], excluded_fields=['dst_ip']),
            'count': couch_doc['required']['scan_attempt'],
            'count_pos': couch_doc['required']['scan_success'],
            'description': 'Port Scan',
            'proto': couch_doc['required']['transport'],
            'dst_session_luid': couch_doc['required'].get('dest_luid'),
            'dst_ip': couch_doc['required']['networks'],
            'flex1': ','.join(net_utils.delta_port_decode(couch_doc['required']['ports'])),
            'flex2': bool(couch_doc['required'].get('is_slowscan', False)),
            'flex_json': {'is_smallscan': bool(couch_doc['required'].get('is_smallscan', False))},
        }

        detection_detail.objects.create(**fields)
        LOG.info('Created a detection detail record for port_scan detection %s', couch_doc['_id'])


class RDPReconHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'rdp_recon' to create a detection detail for the corresponding couch note
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'rdp_recon'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a rdp_recon detection detail from the corresponding couch detection note.
        """
        new_det_details = []
        for target in couch_doc['required']['brute_force_details']['servers_counts']:
            fields = {
                **cls.extract_common_fields(
                    couch_doc, excluded_fields=['dst_ip', 'dst_port', 'dst_dns', 'first_timestamp', 'last_timestamp']
                ),
                'dst_ip': target['dstIp'],
                'dst_session_luid': target.get('dstLuid'),
                'description': 'RDP Recon',
                'first_timestamp': parser.parse(couch_doc['required']['brute_force_details']['start_timestamp']),
                'last_timestamp': parser.parse(couch_doc['required']['brute_force_details']['end_timestamp']),
                'count': target['hits'],
                'dst_port': couch_doc['required']['infected_rdp_session']['dstPort'],
                'dst_dns': couch_doc['required']['infected_rdp_session'].get('dstDisplayName'),
                'flex1': couch_doc['required']['infected_rdp_session']['clientName'],
                'flex2': couch_doc['required']['infected_rdp_session']['cookie'],
                'flex3': 'encrypted' if couch_doc['required']['infected_rdp_session']['encrypted'] else 'unencrypted',
                'flex4': 'normal' if target['normal'] else 'abnormal',
            }
            new_dd = detection_detail(**fields)
            new_det_details.append(new_dd)

        detection_detail.objects.bulk_create(new_det_details)
        LOG.info('Created all detection details for rdp_recon detection %s', couch_doc['_id'])


class LDAPReconHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'ldap_recon' to create a detection detail for its couch note
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'ldap_recon'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a ldap_recon detection detail for the given couch note
        """
        dd_fields = {
            **cls.extract_common_fields(
                couch_doc,
                mandatory_fields=['first_timestamp', 'last_timestamp'],
                excluded_fields=['dst_ip', 'dst_port'],
            ),
            'description': 'Suspicious LDAP Query',
            'dst_ip': couch_doc['required'].get('dst_ip') or couch_doc['required']['dest_ip'],
            'dst_session_luid': couch_doc['required'].get('dst_luid') or couch_doc['required'].get('dest_luid'),
            'dst_port': couch_doc['required'].get('dst_port') or couch_doc['required']['dest_port'],
            'count': couch_doc['required']['result_count'],
            'total_bytes_rcvd': couch_doc['required']['response_bytes'],
            'flex1': couch_doc['required']['base_object'],
            'flex2': couch_doc['required']['filter'],
            'flex3': couch_doc['required']['code'],
        }

        detection_detail.objects.create(**dd_fields)
        LOG.info('Created all detection details for ldap_recon detection %s', couch_doc['_id'])


class VectraThreatIntelHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'vectra_threat_intel' to create a detection detail for its couch note

    """

    THREAT_FEED_NAME = 'Vectra Threat Intel'

    @classmethod
    def _get_connection_events(cls, match, first_timestamp, last_timestamp, connection_url):
        """
        generate connection data for a detection_detail (dd)

        @Args:
            match (dict): detection details to process through

        @Return (dict):
            A dictionary of the connection data
        """
        duration_int = int((last_timestamp - first_timestamp).total_seconds()) if first_timestamp else 0
        if duration_int > 60:
            duration = calc_duration(duration_int, hr_text='hour', min_text="minute", sec_text="second", include_seconds=True)
        else:
            duration = '{} {}'.format(duration_int, 'second' if duration_int == 1 else 'seconds')
        connection = {
            'total_bytes_sent': match['bytes_sent'],
            'total_bytes_rcvd': match['bytes_recvd'],
            'threat_feeds': [cls.THREAT_FEED_NAME],
            'first_timestamp': first_timestamp,
            'last_timestamp': last_timestamp,
            'protocol': match['protocol'],
            'target_host': {'ip': match.get('dest_ip'), 'dst_dns': match.get('domain_name')},
            'dst_port': match.get('dest_port'),
            'duration': duration,
            'duration_int': duration_int,
            'url': connection_url,
        }

        dns_request = match.get('source_context')
        if dns_request:
            connection['dns_rcode'] = int(dns_request['dns_rcode'])
            connection['dns_resolved_ips'] = dns_request['dns_resolved_ip']

        connection['is_external'] = not match.get('dest_luid')
        if match.get('dest_luid'):
            internal_targeted_hs = host_session.objects.filter(session_luid=match.get('dest_luid')).values('host_id', 'host__name').first()
            if internal_targeted_hs:
                connection['target_host'].update({'id': internal_targeted_hs['host_id'], 'name': internal_targeted_hs['host__name']})

        return connection

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'vectra_threat_intel'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a vectra_threat_intel detection detail for the given couch note
        """
        threat_intel_dd = []

        groupkeyfunc = lambda match: match['session_guid']
        oid_matches = itertools.groupby(sorted(couch_doc['required']['matches'], key=groupkeyfunc), key=groupkeyfunc)
        for oid, matches in oid_matches:
            matches = list(matches)

            # find the highest rule match, the rule priority order is listed below: user_agent, url, domain, user_account, ip
            highest_rule_match = None

            highest_rule_match = next(
                (
                    match
                    for rule_type in ('user_agent', 'url', 'domain', 'user_account', 'ip', 'dns_request')
                    for match in matches
                    if rule_type in [rule['type'] for rule in match['rule']]
                ),
                None,
            )

            if not highest_rule_match:
                LOG.warning('No valid rule matches encountered for oid %s in couch note %s', oid, couch_doc['_id'])
                continue

            # Gather the data needed to form the connection data
            first_timestamp = min(match['start_time'] for match in matches)
            first_timestamp = parser.parse(first_timestamp) if first_timestamp else None
            last_timestamp = parser.parse(max(match['end_time'] for match in matches))
            connection_url = next((match['url'] for match in matches if match.get('url')), None)
            connection_event = cls._get_connection_events(highest_rule_match, first_timestamp, last_timestamp, connection_url)

            # Determine the primary match, and other matches (if any)
            match_rank = ('ip', 'domain', 'url', 'user_agent', 'user_account', 'dns_request')

            other_matches = [{'type': rule['type'], 'value': rule['value']} for match in matches for rule in match['rule']]
            primary_match = max(other_matches, key=lambda m: match_rank.index(m['type']))
            other_matches.remove(primary_match)

            # generate most of the detail data based on the match object that had the highest priority rule match
            # incorporate other metadata (rule types, values, threat feed names, timestamps) from other match objects with the same oid
            match_dd_fields = {
                **cls.extract_common_fields(
                    couch_doc, excluded_fields=['dst_ip', 'dst_port', 'dst_dns', 'first_timestamp', 'last_timestamp']
                ),
                'category': 'Command & Control',
                'description': 'Vectra Threat Intelligence Match',
                'dst_ip': highest_rule_match['dest_ip'],
                'dst_session_luid': highest_rule_match.get('dest_luid'),
                'dst_port': highest_rule_match['dest_port'],
                'dst_dns': highest_rule_match.get('domain_name'),
                'proto': highest_rule_match['protocol'],
                'count': len(other_matches) + 1,
                'total_bytes_sent': highest_rule_match['bytes_sent'],
                'total_bytes_rcvd': highest_rule_match['bytes_recvd'],
                'first_timestamp': first_timestamp,
                'last_timestamp': last_timestamp,
                'reason': ','.join(rule['type'] for match in matches for rule in match['rule']),
                'flex1': oid,
                'flex2': ','.join(rule['value'] for match in matches for rule in match['rule']),
                'flex_json': {
                    'connection_event': connection_event,
                    'primary_match': [primary_match['type'], primary_match['value']],
                    'primary_dict': {'type': primary_match['type'], 'value': primary_match['value']},
                    'other_matches': other_matches,
                    'attacker_detail': highest_rule_match['attacker_detail'],
                    'indicators': [match['indicator_type'] for match in matches],
                    'dest_ips': [match['dest_ip'] for match in matches],
                    'dest_domains': [match['domain_name'] for match in matches],
                    'certainties': [match['certainty'] for match in matches],
                    'type': 'vectra_threat_intel',
                },
            }

            threat_intel_dd.append(detection_detail(**match_dd_fields))

        detection_detail.objects.bulk_create(threat_intel_dd)

        LOG.info('Created all detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class ThreatIntelHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'threat_intel_*' to create a detection detail for its couch note

    Depending on the algorithm field of the couch note, the created details will be of type
        1) 'threat_intel_cnc'
        2) 'threat_intel_exfil'
        3) 'threat_intel_lateral'
    """

    TYPE_TO_CATEGORY_MAP = {
        'threat_intel_cnc': 'Command & Control',
        'threat_intel_exfil': 'Exfiltration',
        'threat_intel_lateral': 'Lateral Movement',
    }

    # Add Vectra Internal Threat Feeds to threat_feed_map, not currently in SQL ThreatFeed table
    VECTRA_INTERNAL_THREAT_FEED_MAP = {'threat_feed_1578867732': 'Vectra Intel Threat Feed'}

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in ThreatIntelHandler.TYPE_TO_CATEGORY_MAP.keys()

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a threat_intel_* detection detail for the given couch note
        """
        threat_intel_dd = []
        threat_feed_doc_ids = [match['stix_doc_id'] for match in couch_doc['required']['matches']]
        threat_feed_map = {
            threat_feed.couch_id: threat_feed.name for threat_feed in ThreatFeedUpload.objects.filter(couch_id__in=threat_feed_doc_ids)
        }
        # Add Vectra Internal Threat Feeds to threat_feed_map
        threat_feed_map.update(cls.VECTRA_INTERNAL_THREAT_FEED_MAP)

        groupkeyfunc = lambda match: match['session_guid']
        oid_matches = itertools.groupby(sorted(couch_doc['required']['matches'], key=groupkeyfunc), key=groupkeyfunc)
        det_category = cls.TYPE_TO_CATEGORY_MAP[couch_doc['algorithm']]
        for oid, matches in oid_matches:
            matches = list(matches)

            # find the highest rule match, the rule priority order is listed below: user_agent, url, domain, user_account, ip
            highest_rule_match = None
            for match_rule_type in ('user_agent', 'url', 'domain', 'user_account', 'ip', 'dns_request'):
                for match in matches:
                    if match_rule_type in set(rule['type'] for rule in match['rule']):
                        highest_rule_match = match
                        break

                if highest_rule_match:
                    break

            if not highest_rule_match:
                LOG.warning('No valid rule matches encountered for oid %s in couch note %s', oid, couch_doc['_id'])
                continue

            first_timestamp = min(match['start_time'] for match in matches)
            last_timestamp = max(match['end_time'] for match in matches)

            # generate most of the detail data based on the match object that had the highest priority rule match
            # incorporate other metadata (rule types, values, threat feed names, timestamps) from other match objects with the same oid
            match_dd_fields = {
                **cls.extract_common_fields(
                    couch_doc,
                    excluded_fields=['dst_ip', 'dst_port', 'first_timestamp', 'last_timestamp'],
                ),
                'description': 'Threat Intelligence Match - {}'.format(det_category),
                'dst_ip': highest_rule_match['dest_ip'],
                'dst_session_luid': highest_rule_match.get('dest_luid'),
                'dst_port': highest_rule_match['dest_port'],
                'dst_dns': highest_rule_match.get('domain_name'),
                'proto': highest_rule_match['protocol'],
                'count': len([rule['type'] for match in matches for rule in match['rule']]),
                'total_bytes_sent': highest_rule_match['bytes_sent'],
                'total_bytes_rcvd': highest_rule_match['bytes_recvd'],
                'first_timestamp': parser.parse(first_timestamp) if first_timestamp else None,
                'last_timestamp': parser.parse(last_timestamp),
                'reason': ','.join(rule['type'] for match in matches for rule in match['rule']),
                'flex1': oid,
                'flex2': ','.join(rule['value'] for match in matches for rule in match['rule']),
                'flex3': ','.join(
                    threat_feed_map.get(match['stix_doc_id']) for match in matches if threat_feed_map.get(match['stix_doc_id'])
                ),
                'flex4': ','.join(match['indicator_type'] for match in matches),
                'flex5': ','.join(match.get('url') for match in matches if match.get('url')),
                'flex6': json.dumps(highest_rule_match.get('source_context')) if highest_rule_match.get('source_context') else None,
            }

            threat_intel_dd.append(detection_detail(**match_dd_fields))

        detection_detail.objects.bulk_create(threat_intel_dd)

        LOG.info('Created all detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class SmugglerHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'smuggler' (fired by 'ismuggler' algorithm)
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'smuggler'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """

        Create a smuggler detection_detail for each pull and push session in the couch note
        """
        if 'pull_sessions' not in couch_doc['required'] or 'exfil_sessions' not in couch_doc['required']:
            LOG.warning("Missing 'pull_sessions' or 'exfil_sessions' for smuggler detection %s", couch_doc['_id'])
            return

        smuggler_dd_details = []
        common_dd_fields = {
            **cls.extract_common_fields(couch_doc),
            'description': 'Data Smuggler',
        }

        pull_sessions = couch_doc['required']['pull_sessions'] or []
        push_sessions = couch_doc['required']['exfil_sessions'] or []

        TRIAGE_CONTEXT_LIMIT = 100
        push_ips = ','.join(
            list(set(str(push_session['exfil_ip']) for push_session in push_sessions if push_session['exfil_ip']))[:TRIAGE_CONTEXT_LIMIT]
        )
        push_ports = ','.join(
            list(set(str(push_session['exfil_dst_port']) for push_session in push_sessions if push_session['exfil_dst_port'] is not None))[
                :TRIAGE_CONTEXT_LIMIT
            ]
        )
        push_domains = ','.join(
            list(set(str(push_session['exfil_domain_name']) for push_session in push_sessions if push_session['exfil_domain_name']))[
                :TRIAGE_CONTEXT_LIMIT
            ]
        )
        for pull_session in pull_sessions:
            pull_session_dd_fields = {
                'dst_ip': pull_session['pull_ip'],
                'dst_dns': pull_session['pull_domain_name'],
                'dst_port': pull_session['pull_dst_port'],
                'proto': pull_session['pull_transport_protocol'],
                'dst_session_luid': pull_session['pull_host_session_luid'],
                'first_timestamp': parser.parse(pull_session['start_time']) if pull_session.get('start_time') else None,
                'last_timestamp': parser.parse(pull_session['end_time']),
                'total_bytes_rcvd': pull_session['bytes_recvd'],
                'total_bytes_sent': pull_session['bytes_sent'],
                'subtype': 'pull',
                'flex1': pull_session['pull_app_protocol'] if pull_session['pull_app_protocol'] != 'unknown' else None,
                # push context in pull details for triage
                'flex2': push_ips,
                'flex3': push_ports,
                'flex4': push_domains,
            }
            pull_session_dd_fields = {**common_dd_fields, **pull_session_dd_fields}
            smuggler_dd_details.append(detection_detail(**pull_session_dd_fields))

        pull_ips = ','.join(
            list(set(str(pull_session['pull_ip']) for pull_session in pull_sessions if pull_session['pull_ip']))[:TRIAGE_CONTEXT_LIMIT]
        )
        pull_ports = ','.join(
            list(set(str(pull_session['pull_dst_port']) for pull_session in pull_sessions if pull_session['pull_dst_port'] is not None))[
                :TRIAGE_CONTEXT_LIMIT
            ]
        )
        pull_domains = ','.join(
            list(set(str(pull_session['pull_domain_name']) for pull_session in pull_sessions if pull_session['pull_domain_name']))[
                :TRIAGE_CONTEXT_LIMIT
            ]
        )
        for push_session in push_sessions:
            push_session_dd_fields = {
                **common_dd_fields,
                'dst_ip': push_session['exfil_ip'],
                'dst_dns': push_session['exfil_domain_name'],
                'dst_port': push_session['exfil_dst_port'],
                'proto': push_session['exfil_transport_protocol'],
                'first_timestamp': parser.parse(push_session['start_time']) if push_session.get('start_time') else None,
                'last_timestamp': parser.parse(push_session['end_time']),
                'total_bytes_rcvd': push_session['bytes_recvd'],
                'total_bytes_sent': push_session['bytes_sent'],
                'subtype': 'push',
                'flex1': push_session['exfil_app_protocol'] if push_session['exfil_app_protocol'] != 'unknown' else None,
                # pull context in push details for triage
                'flex2': pull_ips,
                'flex3': pull_ports,
                'flex4': pull_domains,
                'flex5': push_session.get('exfil_proxy_external_dest_ip'),
            }
            smuggler_dd_details.append(detection_detail(**push_session_dd_fields))

        detection_detail.objects.bulk_create(smuggler_dd_details)
        LOG.info('Created all detection_detail details for smuggler detection %s', couch_doc['_id'])


class RPCReconHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'rpc-recon' (fired by 'rpc-recon' algorithm)
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'rpc_recon'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a rpc_recon detail, one per UUID
        """
        MAX_TARGETS = 100

        rpc_recon_details = []
        common_dd_fields = {
            **cls.extract_common_fields(
                couch_doc,
                mandatory_fields=['first_timestamp', 'last_timestamp'],
                excluded_fields=['dst_ip', 'dst_port'],
            ),
            'description': 'RPC Recon',
        }

        for uuid_key, uuid_targets in couch_doc['required']['uuid_info'].items():
            uuid_targets_truncated = uuid_targets[:MAX_TARGETS]
            uuid_dd_fields = {
                'flex1': uuid_key,
                'flex2': ','.join(target['dest_luid'] for target in uuid_targets_truncated),
                'flex3': ','.join(target['dest_ip'] for target in uuid_targets_truncated),
                'flex4': ','.join(str(target['dest_port']) for target in uuid_targets_truncated),
                'flex5': ','.join(str(target['count']) for target in uuid_targets_truncated),
                # total number of calls per UUID
                'count': sum(target['count'] for target in uuid_targets_truncated),
                # total number of targets per UUID
                'count_pos': len(list(target['dest_luid'].split(',') for target in uuid_targets_truncated)),
            }
            dd_fields = {**common_dd_fields, **uuid_dd_fields}
            rpc_recon_details.append(detection_detail(**dd_fields))

        detection_detail.objects.bulk_create(rpc_recon_details)
        LOG.info('Created all detection details for rpc_recon detection %s', couch_doc['_id'])


class SuspiciousRelayHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'conn_relay'
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'conn_relay'

    @staticmethod
    def get_external_fields(couch_doc):
        """
        Creates fields which are unique to an external subtype detection detail
        """
        couch_external = couch_doc['required']['out']
        couch_internal = couch_doc['required']['in']

        conn_relay_external_dd_fields = {
            'dst_ip': couch_external['dest_ip'],
            'dst_dns': couch_external.get('domain_name'),
            'dst_port': couch_external['dest_port'],
            'proto': couch_external['transport'],
            'total_bytes_rcvd': couch_external['bytes_recvd'],
            'total_bytes_sent': couch_external['bytes_sent'],
            'first_timestamp': parser.parse(couch_external['start_time']) if couch_external.get('start_time') else None,
            'last_timestamp': parser.parse(couch_external['end_time']),
            'subtype': 'external',
            # flex fields used for triage
            'flex1': couch_internal['dest_ip'],
            'flex2': couch_internal.get('domain_name'),
            'flex3': couch_internal['transport'],
            'flex4': couch_internal['dest_port'],
        }
        return conn_relay_external_dd_fields

    @staticmethod
    def get_internal_fields(couch_doc):
        """
        Creates fields which are unique to an internal subtype detection detail
        """
        couch_external = couch_doc['required']['out']
        couch_internal = couch_doc['required']['in']

        conn_relay_internal_dd_fields = {
            'dst_ip': couch_internal['dest_ip'],
            'dst_dns': couch_internal.get('domain_name'),
            'dst_port': couch_internal['dest_port'],
            'proto': couch_internal['transport'],
            'dst_session_luid': couch_internal['dest_luid'],
            'total_bytes_rcvd': couch_internal['bytes_recvd'],
            'total_bytes_sent': couch_internal['bytes_sent'],
            'first_timestamp': parser.parse(couch_internal['start_time']) if couch_internal.get('start_time') else None,
            'last_timestamp': parser.parse(couch_internal['end_time']),
            'subtype': 'internal',
            # flex fields used for triage
            'flex1': couch_external['dest_ip'],
            'flex2': couch_external.get('domain_name'),
            'flex3': couch_external['transport'],
            'flex4': couch_external['dest_port'],
        }
        return conn_relay_internal_dd_fields

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a conn_relay detection_detail for the external and internal session in the couch note
        """
        try:
            conn_relay_common_dd_fields = {
                **cls.extract_common_fields(couch_doc),
                'description': 'Suspicious Relay',
            }

            conn_relay_external_dd_fields = cls.get_external_fields(couch_doc)
            conn_relay_external_dd_fields = {**conn_relay_common_dd_fields, **conn_relay_external_dd_fields}

            conn_relay_internal_dd_fields = cls.get_internal_fields(couch_doc)
            conn_relay_internal_dd_fields = {**conn_relay_common_dd_fields, **conn_relay_internal_dd_fields}

            conn_relay_dd_details = [detection_detail(**conn_relay_external_dd_fields), detection_detail(**conn_relay_internal_dd_fields)]
            detection_detail.objects.bulk_create(conn_relay_dd_details)
            LOG.info(f'Created all detection_detail details for conn_relay detection {couch_doc["_id"]}')
        except KeyError:
            LOG.exception(f'conn_relay detection note {couch_doc["_id"]} has missing required keys')

        except ValueError:
            LOG.exception(f'conn_relay detection note {couch_doc["_id"]} has encountered a JSON decode error')

        except Exception:
            LOG.exception(f'Bulk create failed from conn_relay detection {couch_doc["_id"]}')

    @classmethod
    def rebuild_detection_detail(cls, couch_doc, missing_detail):
        """
        Creates a conn_relay detection_detail for either an external or internal session
        in the case where only 1 of the detection details exist.
        """
        conn_relay_common_dd_fields = {
            **cls.extract_common_fields(couch_doc),
            'sequence_id': couch_doc.get('$sequence'),
            'description': 'Suspicious Relay',
            'sensor_luid': couch_doc['published']['location'],
            'subtype': missing_detail,
        }

        if missing_detail == "external":
            conn_relay_additional_dd_fields = cls.get_external_fields(couch_doc)

        else:
            conn_relay_additional_dd_fields = cls.get_internal_fields(couch_doc)

        try:
            conn_relay_dd_fields = {**conn_relay_common_dd_fields, **conn_relay_additional_dd_fields}
            detection_detail.objects.create(**conn_relay_dd_fields)
            LOG.info(f'Successfully rebuilt {missing_detail} conn_relay detection detail for {couch_doc["_id"]}')

        except KeyError:
            LOG.exception(f'{missing_detail} conn_relay detection note has missing required keys from Couch Note {couch_doc["_id"]}')

        except ValueError:
            LOG.exception(
                f'{missing_detail} conn_relay detection note has encountered a JSON decode error from Couch Note {couch_doc["_id"]}'
            )

        except Exception:
            LOG.exception(f'Failed attempt to rebuild {missing_detail} conn_relay detection from for note {couch_doc["_id"]}')


class HiddenHTTPSTunnelHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'hidden_https_tunnel_*' to create a detection detail for its couch note

    Depending on the algorithm field of the couch note, the created details will be of type
        1) 'hidden_https_tunnel_cnc'
        2) 'hidden_https_tunnel_exfil'
    """

    TYPE_TO_CATEGORY_MAP = {'hidden_https_tunnel_cnc': 'Command & Control', 'hidden_https_tunnel_exfil': 'Exfiltration'}

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in HiddenHTTPSTunnelHandler.TYPE_TO_CATEGORY_MAP

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a hidden_https_tunnel_* detection_detail for the given couch note
        """
        dd_fields = {
            **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_dns', 'dst_port', 'last_timestamp']),
            'description': 'Hidden HTTPS Tunnel - {}'.format(cls.TYPE_TO_CATEGORY_MAP[couch_doc['algorithm']]),
            'proto': couch_doc['required']['transport'],
            'total_bytes_rcvd': couch_doc['required']['bytes_recvd'],
            'total_bytes_sent': couch_doc['required']['bytes_sent'],
            'count': couch_doc['required']['count'],
            'subtype': couch_doc['required'].get('tunnel_type', 'type1'),
            'flex1': couch_doc['required']['protocol'],
            'flex2': couch_doc['required'].get('ja3'),
            'flex3': couch_doc['required'].get('ja3s'),
        }

        detection_detail.objects.create(**dd_fields)
        LOG.info('Created all detection_detail details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class DomainFrontingHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'front_watch' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'frontwatch'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a domain fronting detection detail from the corresponding couch detection note.
        """

        try:
            required = couch_doc['required']
            cdn_info = {'cdn_ips': required['dest_ip_list'], 'cdn_domain_stats': required['domain_details']}

            # Here is the mapping from couch fields to sql fields
            # The flex fields are as follows
            # flex1: List of destination IPs
            # flex2: List of session numbers and external domains and reached via fronting
            # flex3: JA3 hash
            # flex4: The name of the CDN used
            # flex5: Frequency of contanct

            fields = {
                **cls.extract_common_fields(
                    couch_doc, mandatory_fields=['dst_port', 'first_timestamp', 'last_timestamp', 'src_ip'], excluded_fields=['dst_ip']
                ),
                'total_bytes_sent': required['total_bytes_sent'],
                'total_bytes_rcvd': required['total_bytes_recv'],
                'flex1': json.dumps(cdn_info),
                'flex2': required['ja3_hash'],
                'flex3': required['dest_cdn'],
                'flex4': required['effective_session_interval_seconds'],
            }

            detection_detail.objects.create(**fields)
            LOG.info('Create detection details for frontwatch detection %s', couch_doc['_id'])

        except KeyError:
            LOG.exception('Frontwatch detection note has missing required keys: {}'.format(couch_doc))
        except ValueError:
            LOG.exception("Frontwatch detection note has encountered a JSON decode error: {}".format(couch_doc))


class StageLoaderHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'stage_loader' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'stage_loader'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a stage loader detection detail from the corresponding couch detection note.
        """

        try:
            required = couch_doc['required']

            # Here is the mapping from couch fields to sql fields
            # flex1: callback port
            # flex2: bytes sent and recieved in phase 1
            # flex3: bytes sent and recieved in phase 2
            phase1_bytes = {'sent': required['phase1_bytes_sent'], 'recv': required['phase1_bytes_recv']}
            phase2_bytes = {'sent': required['phase2_bytes_sent'], 'recv': required['phase2_bytes_recv']}

            fields = {
                **cls.extract_common_fields(
                    couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp', 'src_ip'], excluded_fields=['dst_ip', 'dst_port']
                ),
                'dst_ip': required['target_ip'],
                'dst_session_luid': required['target_luid'],
                'dst_port': required['target_port'],
                'flex1': required['callback_port'],
                'flex2': json.dumps(phase1_bytes),
                'flex3': json.dumps(phase2_bytes),
            }

            detection_detail.objects.create(**fields)
            LOG.info('Create detection details for stage_loader detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('Stage loader detection note has missing required keys: {}'.format(couch_doc))


class CustomModelHandler(BaseDetectionHandler):
    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'custom_model'

    @classmethod
    def create_sql_row(cls, couch_doc):
        required = couch_doc['required']

        dd_fields = {
            **cls.extract_common_fields(couch_doc, excluded_fields=['sensor_luid']),
            'type': 'cm_{}_{}'.format(required['index'], required['category']),
            'subtype': required['custom_model_id'],
            'description': required['name'],
            'category': DETECTION_CATEGORY_SHORTNAME_MAP[required['category']],
            'dst_session_luid': required.get('resp_sluid'),
            'total_bytes_sent': required.get('bytes_sent', 0),
            'total_bytes_rcvd': required.get('bytes_received', 0),
            'count': 1,
            'flex1': required.get('flex1'),
            'flex2': required.get('flex2'),
            'flex3': required.get('flex3'),
            'flex4': required.get('flex4'),
            'flex5': required.get('flex5'),
            'flex6': required.get('flex6'),
        }

        detection_detail.objects.create(**dd_fields)
        LOG.info('Created all detection_detail details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class ICMPTunnelHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'icmp_tunnel' and 'internal_icmp_tunnel' to create a detection detail for its couch note

    Depending on the algorithm field of the couch note, the created details will be of type
        1) 'icmp_tunnel'
        2) 'internal_icmp_tunnel'
        3) 'internal_icmp_tunnel_c2s'
        4) 'internal_icmp_tunnel_s2c'
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        supported_algos = [
            DetectionType.ICMP_TUNNEL,
            DetectionType.INTERNAL_ICMP_TUNNEL,
            DetectionType.ICMP_TUNNEL_EXFIL,
            DetectionType.INTERNAL_ICMP_TUNNEL_C2S,
            DetectionType.INTERNAL_ICMP_TUNNEL_S2C,
        ]
        return couch_doc.get('algorithm') in supported_algos

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a 'icmp_tunnel', 'internal_icmp_tunnel', internal_icmp_tunnel_s2c, internal_icmp_tunnel_c2s, or 'icmp_tunnel_exfil' detection_detail for the given couch note
        """

        dd_fields = {
            **cls.extract_common_fields(
                couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp', 'src_ip', 'dst_port', 'dst_dns', 'dst_ip']
            ),
            'type': (
                DetectionType.INTERNAL_ICMP_TUNNEL_C2S
                if couch_doc['algorithm'] == DetectionType.INTERNAL_ICMP_TUNNEL
                else couch_doc['algorithm']
            ),
            'flex_json': {
                'threat_score': couch_doc['scoring']['threat'],
                'certainty_score': couch_doc['scoring']['certainty'],
            },
            'dst_session_luid': couch_doc['required']['dest_luid'],
            'total_bytes_rcvd': couch_doc['required']['bytes_recvd'],
            'total_bytes_sent': couch_doc['required']['bytes_sent'],
        }

        if couch_doc.get('algorithm') in (
            DetectionType.ICMP_TUNNEL,
            DetectionType.INTERNAL_ICMP_TUNNEL,
            DetectionType.INTERNAL_ICMP_TUNNEL_C2S,
            DetectionType.INTERNAL_ICMP_TUNNEL_S2C,
        ):
            dd_fields['flex_json']['unique_packets_sent'] = couch_doc['required']['unique_packets_sent']
            dd_fields['flex_json']['unique_packets_recvd'] = couch_doc['required']['unique_packets_recvd']
            dd_fields['flex_json']['tunnel_type'] = 'Heartbeat' if couch_doc['required']['tunnel_type'] == 'heartbeat' else 'Reverse Shell'

        detection_detail.objects.create(**dd_fields)
        LOG.info('Created all detection_detail details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])


class BasePAPIHandler(BaseDetectionHandler):
    """

    Process detection notes of type 'unusual_admin_console', 'breach', 'rogue_admin', 'admin_peer_console', and 'papi_admin_second_console' to create a detection detail record.

    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """

        supported_algos = [
            DetectionType.PAPI_ADMIN_PEER_CONSOLE,
            DetectionType.PAPI_ADMIN_SECOND_CONSOLE,
            DetectionType.PAPI_BREACH,
            DetectionType.PAPI_INSIDER_ATTACK,
            DetectionType.PAPI_ROGUE_ADMIN,
            DetectionType.PAPI_UNUSUAL_ADMIN_CONSOLE,
        ]
        return couch_doc.get('algorithm') in supported_algos

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a PAPI detection detail from the corresponding couch detection note.
        """

        try:
            required = couch_doc['required']

            normal_account_behavior = required['account']['normal_behavior']
            normal_service_behavior = required['service']['normal_behavior']
            normal_host_behavior = required['host']['normal_behavior']
            normal_behavior = {'account': normal_account_behavior, 'service': normal_service_behavior, 'host': normal_host_behavior}

            account_privilege_level = None if 'privilege_level' not in required['account'] else required['account']['privilege_level']
            service_privilege_level = None if 'privilege_level' not in required['service'] else required['service']['privilege_level']
            host_privilege_level = None if 'privilege_level' not in required['host'] else required['host']['privilege_level']
            privileges = {'account': account_privilege_level, 'service': service_privilege_level, 'host': host_privilege_level}

            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])

            # Use the account service to create these accounts on disk
            accounts_to_create = [
                {
                    'uid': required['account']['uid'],
                    'account_type': account_type.AccountType.KERBEROS,
                    'first_seen': start_time,
                    'last_seen': end_time,
                }
            ]
            for nb in normal_host_behavior:
                try:
                    if 'account_uid' in nb and not lib_account.is_machine_account(nb.get('account_uid')):
                        accounts_to_create.append(
                            {
                                'uid': nb['account_uid'],
                                'account_type': account_type.AccountType.KERBEROS,
                                'first_seen': start_time,
                                'last_seen': end_time,
                            }
                        )
                except ValueError:
                    LOG.exception("PAPI detection note has encountered a machine account: {}".format(nb.get('account_uid')))
            for nb in normal_service_behavior:
                try:
                    if 'account_uid' in nb and not lib_account.is_machine_account(nb.get('account_uid')):
                        accounts_to_create.append(
                            {
                                'uid': nb['account_uid'],
                                'account_type': account_type.AccountType.KERBEROS,
                                'first_seen': start_time,
                                'last_seen': end_time,
                            }
                        )
                except ValueError:
                    LOG.exception("PAPI detection note has encountered a machine account: {}".format(nb.get('account_uid')))
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            # Here is the mapping from couch fields to sql fields
            # The flex fields are as follows
            # flex1: Account name
            # flex2: Service name
            # flex3: Expected behaviors
            # flex4: Privilege levels
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp', 'src_ip']),
                'is_host_detail': required['host_detection'],
                'is_account_detail': required['account_detection'],
                'account_uid': required['account']['uid'],
                'account_id': account_map[required['account']['uid']],
                'flex1': couch_doc['detection_source'],
                'flex2': required['service']['name'],
                'flex3': json.dumps(normal_behavior),
                'flex4': json.dumps(privileges),
            }

            detection_detail.objects.create(**fields)
            LOG.info('Create detection details for PAPI detection %s: %s', couch_doc['algorithm'], couch_doc['_id'])

            # Apply retractions if necessary
            if not fields.get('is_host_detail'):
                if fields.get('account_uid') and len(required.get('account_seen_host_sessions', [])) > 0:
                    # Retract any host_sessions in the note
                    lib_account.retract_host_detection_details(
                        fields['account_uid'], required['account_seen_host_sessions'], fields['type']
                    )
            elif not fields.get('is_account_detail'):
                if fields.get('src_session_luid') and len(required.get('host_seen_accounts', [])) > 0:
                    # Retract any account uids in the note
                    lib_account.retract_account_detection_details(
                        fields['src_session_luid'], required['host_seen_accounts'], fields['type']
                    )

        except KeyError:
            LOG.exception('PAPI detection note has missing required keys: {}'.format(couch_doc))
        except ValueError:
            LOG.exception("PAPI detection note has encountered a JSON decode error: {}".format(couch_doc))


class RPCRecon1to1(BaseDetectionHandler):
    """
    Process detection notes of type 'rpc_recon_1to1' to create a detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'rpc_recon_1to1'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create an rpc recon 1 to 1 detection detail from the corresponding couch detection note.
        """

        try:
            required = couch_doc['required']
            fields = {
                **cls.extract_common_fields(
                    couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp', 'src_ip', 'dst_ip', 'dst_port']
                ),
                'dst_session_luid': required.get('dest_host_session_luid'),
                'flex_json': {
                    'anomalous_function_call': {
                        'service': required['anomalous_function_call']['service'],
                        'function_call': required['anomalous_function_call']['function_call'],
                        'count': required['anomalous_function_call_count'],
                        'account': required.get('account'),
                        'first_seen': parser.parse(required['first_seen']),
                        'last_seen': parser.parse(required['last_seen']),
                    },
                    'src_profiles': [
                        {
                            'count': src_profile.get('count'),
                            'function_call': src_profile.get('function_call'),
                            'function_uuid': src_profile.get('service'),
                        }
                        for src_profile in required.get('source_profile', [])
                    ],
                    'dst_profiles': [
                        {
                            'count': dst_profile.get('count'),
                            'function_call': dst_profile.get('function_call'),
                            'function_uuid': dst_profile.get('service'),
                        }
                        for dst_profile in required.get('dest_profile', [])
                    ],
                },
            }

            detection_detail.objects.create(**fields)
            LOG.info('Create detection details for rpc_recon_1to1 detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('rpc_recon_1to1 detection note has missing required keys: {}'.format(couch_doc))


class KerberosPasswordSpray(BaseDetectionHandler):
    """
    Process detection notes of type 'kerberos_password_spray' to create multiple detection detail records.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == 'kerberos_password_spray'

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create multiple kerberos password spray detection details from the corresponding couch detection note.
        """

        try:
            required = couch_doc['required']

            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['src_ip']),
            }

            accounts_to_create = []
            for account in required['accounts']:
                accounts_to_create.append(
                    {
                        'uid': account['account'],
                        'account_type': account_type.AccountType.KERBEROS,
                        'first_seen': parser.parse(account['start_time']),
                        'last_seen': parser.parse(account['end_time']),
                    }
                )
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            for account in required['accounts']:
                account_uid = account['account']
                fields = {
                    **base_fields,
                    'account_uid': account_uid,
                    'account_id': account_map[account_uid],
                    'count': account['count'],
                    'first_timestamp': parser.parse(account['start_time']),
                    'last_timestamp': parser.parse(account['end_time']),
                }

                detection_detail.objects.create(**fields)

            LOG.info('Create detection details for kerberos_password_spray detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('kerberos_password_spray detection note has missing required keys: {}'.format(couch_doc))


def sanity_check_dynamic_detail(detail):
    """
    Run a detail through some of our pipeline before saving it to the database, in order to
    have some sanity checking on the shape of the note matching its schema.
    If the sanity check fails, and exception will be thrown.
    :param detail: detection_detail object
    """
    # triage
    filter_conditions_suggestor = lib_triage.TriageFilterConditionsSuggestionsHandler()
    for _, condition_metadata in HEADERS[detail.type].items():
        filter_conditions_suggestor.get_detail_field_values(detail, condition_metadata['detail_field'])

    schema = DetectionSchema.objects.get(type=detail.type).schema

    # details
    detection_details = dynamic_processors.DetailProcessor(
        # exclude tags due to issue comparing model_to_dict() output in unit tests, due to tags being a QuerySet
        schema.get('detailProcessor'),
        [model_to_dict(detail, exclude=['tags'])],
    ).process_details()

    # group by
    group_by = (
        dynamic_processors.GroupbyProcessor(schema['groupbyProcessor'], detection_details).group_and_aggregate_details(detail.type)
        if schema.get('groupbyProcessor')
        else detection_details
    )

    # summary
    summary = dynamic_processors.SummaryProcessor(schema['summaryProcessor'], group_by).summarize_groupby(detail.type)

    # api
    dynamic_processors.APIProcessor(schema.get('apiProcessor', {}), {'grouped_details': group_by, 'summary': summary}).process_detection()


class SWDetections(BaseDetectionHandler):
    # SaasWatch Subtypes
    SW_O365_EXCHANGE_HIGH_RISK_OPS = 'sw_o365_exchangeHighRiskOps'
    SW_O365_EXCHANGE_MAILBOX_ADMIN = 'sw_o365_exchangeMailboxAdmin'
    SW_O365_EXCHANGE_POLICY_TAMPERING = 'sw_o365_exchangePolicyTampering'
    SW_O365_MAILBOX_NON_OWNER = 'sw_o365_mailboxNonOwner'
    SW_O365_PAA_ADMIN_ANOMALY = 'sw_o365_paaAdminAnomaly'
    SW_O365_PAA_USER_ANOMALY = 'sw_o365_paaUserAnomaly'

    # Mapping of algo to detection type
    SW_ALGO_MAPPING = {
        # Algo is type
        DetectionType.O365_SUSPECTED_COMPROMISED_ACCESS: DetectionType.O365_SUSPECTED_COMPROMISED_ACCESS,
        DetectionType.O365_SUSPICIOUS_MAILBOX_RULE: DetectionType.O365_SUSPICIOUS_MAILBOX_RULE,
        DetectionType.O365_SUSPICIOUS_SIGN_ON_MFA_FAILED: DetectionType.O365_SUSPICIOUS_SIGN_ON_MFA_FAILED,
        DetectionType.O365_ADD_COMPANY_PARTNER: DetectionType.O365_ADD_COMPANY_PARTNER,
        DetectionType.O365_EXT_TEAMS_USER_LINK: DetectionType.O365_EXT_TEAMS_USER_LINK,
        DetectionType.O365_LOGIN_SUSP_LOCATION: DetectionType.O365_LOGIN_SUSP_LOCATION,
        DetectionType.O365_NEW_CERT_AUTHORITY: DetectionType.O365_NEW_CERT_AUTHORITY,
        DetectionType.O365_PHISHING_SIM_CONFIG_CHANGE: DetectionType.O365_PHISHING_SIM_CONFIG_CHANGE,
        DetectionType.O365_SECOPS_MAILBOX_CHANGE: DetectionType.O365_SECOPS_MAILBOX_CHANGE,
        DetectionType.O365_DOMAIN_SETTINGS_MODIFIED: DetectionType.O365_DOMAIN_SETTINGS_MODIFIED,
        DetectionType.O365_CROSS_TENANT_ACCESS_CHANGE: DetectionType.O365_CROSS_TENANT_ACCESS_CHANGE,
        DetectionType.SW_O365_3RD_PARTY_APP: DetectionType.SW_O365_3RD_PARTY_APP,
        DetectionType.SW_O365_ACCOUNT_BRUTE_FORCE: DetectionType.SW_O365_ACCOUNT_BRUTE_FORCE,
        DetectionType.SW_O365_ACCOUNT_CREATE: DetectionType.SW_O365_ACCOUNT_CREATE,
        DetectionType.SW_O365_ACCOUNT_CREATE_NEW_ACCOUNT: DetectionType.SW_O365_ACCOUNT_CREATE_NEW_ACCOUNT,
        DetectionType.SW_O365_ACCOUNT_MANIPULATION: DetectionType.SW_O365_ACCOUNT_MANIPULATION,
        DetectionType.SW_O365_ATTACK_TOOL_RULER: DetectionType.SW_O365_ATTACK_TOOL_RULER,
        DetectionType.SW_O365_COMPLIANCE_SEARCH: DetectionType.SW_O365_COMPLIANCE_SEARCH,
        DetectionType.SW_O365_DISABLED_ACCOUNT_SIGNIN_ATTEMPT: DetectionType.SW_O365_DISABLED_ACCOUNT_SIGNIN_ATTEMPT,
        DetectionType.SW_O365_DISABLE_MFA: DetectionType.SW_O365_DISABLE_MFA,
        DetectionType.SW_O365_DLL_HIJACK: DetectionType.SW_O365_DLL_HIJACK,
        DetectionType.SW_O365_E_DISCOVERY_EXFIL: DetectionType.SW_O365_E_DISCOVERY_EXFIL,
        DetectionType.SW_O365_E_DISCOVERY_SEARCH: DetectionType.SW_O365_E_DISCOVERY_SEARCH,
        DetectionType.SW_O365_EXCHANGE_LOG_DISABLE: DetectionType.SW_O365_EXCHANGE_LOG_DISABLE,
        DetectionType.SW_O365_EXCHANGE_NEW_TRANSPORT_RULE: DetectionType.SW_O365_EXCHANGE_NEW_TRANSPORT_RULE,
        DetectionType.SW_O365_EXFIL_DELETE_ACCOUNT: DetectionType.SW_O365_EXFIL_DELETE_ACCOUNT,
        DetectionType.SW_O365_EXTERNAL_TEAMS_ACCESS: DetectionType.SW_O365_EXTERNAL_TEAMS_ACCESS,
        DetectionType.SW_O365_MALWARE_STAGING: DetectionType.SW_O365_MALWARE_STAGING,
        DetectionType.SW_O365_MULTI_HOP_PROXY: DetectionType.SW_O365_MULTI_HOP_PROXY,
        DetectionType.SW_O365_MULTIPLE_MAIL_FORWARD: DetectionType.SW_O365_MULTIPLE_MAIL_FORWARD,
        DetectionType.SW_O365_PAA_AZURE_AD_ANOMALY: DetectionType.SW_O365_PAA_AZURE_AD_ANOMALY,
        DetectionType.SW_O365_PRIV_MISMATCH_AAD: DetectionType.SW_O365_PRIV_MISMATCH_AAD,
        DetectionType.SW_O365_RANSOMWARE: DetectionType.SW_O365_RANSOMWARE,
        DetectionType.SW_O365_REDUNDANT_ACCESS: DetectionType.SW_O365_REDUNDANT_ACCESS,
        DetectionType.SW_O365_SCRIPTING_ENGINE_ACCESS: DetectionType.SW_O365_SCRIPTING_ENGINE_ACCESS,
        DetectionType.SW_O365_SEC_TOOLS_DISABLE: DetectionType.SW_O365_SEC_TOOLS_DISABLE,
        DetectionType.SW_O365_SPEARPHISHING: DetectionType.SW_O365_SPEARPHISHING,
        DetectionType.SW_O365_SUSPICIOUS_DOWNLOAD_ACTIVITY: DetectionType.SW_O365_SUSPICIOUS_DOWNLOAD_ACTIVITY,
        DetectionType.SW_O365_SUSPICIOUS_SIGN_ON: DetectionType.SW_O365_SUSPICIOUS_SIGN_ON,
        DetectionType.SW_O365_SUSPICIOUS_SHARING_ACTIVITY: DetectionType.SW_O365_SUSPICIOUS_SHARING_ACTIVITY,
        DetectionType.SW_O365_SUSPICIOUS_TEAMS_APP: DetectionType.SW_O365_SUSPICIOUS_TEAMS_APP,
        DetectionType.SW_O365_TRUSTED_NETWORKS_MODIFIED: DetectionType.SW_O365_TRUSTED_NETWORKS_MODIFIED,
        DetectionType.SW_O365_VALID_ACCOUNT: DetectionType.SW_O365_VALID_ACCOUNT,
        DetectionType.SW_O365_POWER_AUTOMATE_C2: DetectionType.SW_O365_POWER_AUTOMATE_C2,
        DetectionType.SW_O365_UNUSUAL_POWER_AUTOMATE_FLOW_CREATION: DetectionType.SW_O365_UNUSUAL_POWER_AUTOMATE_FLOW_CREATION,
        # Multiple algos (subtypes) per type
        SW_O365_EXCHANGE_HIGH_RISK_OPS: DetectionType.SW_O365_RISKY_EXCHANGE,
        SW_O365_EXCHANGE_MAILBOX_ADMIN: DetectionType.SW_O365_RISKY_EXCHANGE,
        SW_O365_EXCHANGE_POLICY_TAMPERING: DetectionType.SW_O365_RISKY_EXCHANGE,
        SW_O365_MAILBOX_NON_OWNER: DetectionType.SW_O365_RISKY_EXCHANGE,
        SW_O365_PAA_ADMIN_ANOMALY: DetectionType.SW_O365_PAA_ANOMALY,
        SW_O365_PAA_USER_ANOMALY: DetectionType.SW_O365_PAA_ANOMALY,
    }

    @classmethod
    def _sanitize_user_type(cls, user_type):
        """
        Transforms the user type field into the correct format, if available.
        Returns the input if it cannot
        """
        USER_TYPE_MAP = {
            0: "Regular",
            1: "Reserved",
            2: "Admin",
            3: "DcAdmin",
            4: "System",
            5: "Application",
            6: "ServicePrincipal",
            7: "CustomPolicy",
            8: "SystemPolicy",
        }
        return USER_TYPE_MAP.get(user_type, user_type)

    @classmethod
    def _sanitize_parameters(cls, parameters):
        """
        Transforms the parameters into the correct format, if available.
        Returns none if it cannot.
        Parameters should follow the pattern of:
        [
            { 'name': 'Identity', 'value': 'Cognito' },
            { 'name': 'Company', 'value': 'Vectra' }
        ]
        """
        NAMES_FIELDS = ['name', 'Name']
        VALUES_FIELDS = ['value', 'Value']

        if not isinstance(parameters, list):
            return None

        for parameter in parameters:
            if not isinstance(parameter, dict):
                return None

            for name_field in NAMES_FIELDS:
                if parameter.get(name_field) is not None:
                    parameter['name'] = parameter.pop(name_field)
                    break

            for value_field in VALUES_FIELDS:
                if parameter.get(value_field) is not None:
                    parameter['value'] = parameter.pop(value_field)
                    break

            if not 'name' in parameter:
                return None

            if not 'value' in parameter:
                return None

        return parameters

    @classmethod
    def _sanitize_subject(cls, subject):
        """
        Transforms the subject field into `(no subject)` if it does not currently have a subject.
        Returns input if not null
        """
        if not subject:
            return '(no subject)'
        return subject

    @classmethod
    def _sanitize_cert(cls, cert):
        """
        Atempt to load a cert into a json object instead of a string
        """
        try:
            return json.loads(cert)
        except json.JSONDecodeError:
            return cert

    @classmethod
    def sanitize_detail_fields(cls, detection_type, detail_fields):
        if 'user_type' in detail_fields:
            try:
                detail_fields['user_type'] = cls._sanitize_user_type(detail_fields['user_type'])
            except Exception:
                LOG.exception('Issue sanitising user_type field %s', detail_fields['user_type'])

        if 'parameters' in detail_fields:
            parameters = detail_fields.pop('parameters')
            try:
                sanitized_parameters = cls._sanitize_parameters(parameters)

                if sanitized_parameters:
                    detail_fields['parameters'] = sanitized_parameters
                else:
                    # If the parameters are not in the correct format, place in command_arguments
                    detail_fields['command_arguments'] = parameters
            except Exception:
                detail_fields['command_arguments'] = parameters
                LOG.exception('Issue sanitising parameters field %s', parameters)

        if detection_type == DetectionType.SW_O365_SPEARPHISHING:
            detail_fields['subject'] = cls._sanitize_subject(detail_fields['subject'])

        if detection_type == DetectionType.O365_NEW_CERT_AUTHORITY:
            detail_fields['cert_authority'] = cls._sanitize_cert(detail_fields['cert_authority'])

        return detail_fields

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in SWDetections.SW_ALGO_MAPPING

    @classmethod
    def load_and_transform_doc(cls, couch_doc, algo_name):
        O365_PREFIX = 'O365:{}'
        try:
            required = couch_doc['required']
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)
            detection_type = algo_name
            # Force certain fields into the correct format
            detail_fields = cls.sanitize_detail_fields(detection_type, required['details'])
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp'], excluded_fields=['src_ip']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'type': detection_type,
                'subtype': couch_doc['algorithm'],
                'src_ip': cls.sanitize_ip_address(required.get('ip')),
                'count': required.get('volume', 1),
                'flex_json': detail_fields,
            }
            fields['detection_guid'] = required.get('detection_guid', None)

            # Add in threat/certainty to flex_json
            fields['flex_json']['threat_score'] = required['threat_score']
            fields['flex_json']['certainty_score'] = required['certainty_score']
            return fields
        except KeyError as e:
            LOG.exception('sw_o365 detection note has missing required keys: {} doc: {}'.format(str(e), couch_doc))

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a saas-watch detection detail from the corresponding couch detection note.
        """
        fields = cls.load_and_transform_doc(couch_doc, cls.SW_ALGO_MAPPING[couch_doc['algorithm']])
        detail = detection_detail(**fields)

        try:
            sanity_check_dynamic_detail(detail)
        except Exception:
            LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
            _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
            raise

        detail.save()
        LOG.info('Create detection details for {} detection {}'.format(couch_doc['algorithm'], couch_doc['_id']))


class O365SuspectEDiscoveryUsage(BaseDetectionHandler):
    """
    Process detection notes of type 'o365_suspect_e_discovery_usage' to create a detection detail for each object in `operations_commands`.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `operations_commands`
        """
        O365_PREFIX = 'O365:{}'
        try:
            required = copy.deepcopy(couch_doc['required'])
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'count': required.get('volume'),
                'flex_json': {
                    'search_id': required['details']['search_id'],
                    'threat_score': required['threat_score'],
                    'certainty_score': required['certainty_score'],
                },
            }

            for operation_command in required['details']['operations_commands']:
                fields = copy.deepcopy(base_fields)
                last_timestamp = parser.parse(operation_command.pop('timestamp'))
                fields['last_timestamp'] = last_timestamp
                fields['flex_json'].update(operation_command)

                detail = detection_detail(**fields)

                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return

                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])
        except KeyError:
            LOG.exception('o365 detection note has missing required keys: {}'.format(couch_doc))


class O365SuspiciousFactorRegistration(BaseDetectionHandler):
    """
    Process detection notes of type 'o365_suspiciousFactorRegistration' to create a detection detail for each object in `registrations`.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.O365_SUSPICIOUS_FACTOR_REGISTRATION

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `registrations`
        """
        O365_PREFIX = 'O365:{}'
        try:
            required = copy.deepcopy(couch_doc['required'])
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'count': required.get('volume'),
                'flex_json': {
                    'threat_score': required['threat_score'],
                    'certainty_score': required['certainty_score'],
                },
            }

            for registration in required['registrations']:
                fields = copy.deepcopy(base_fields)
                last_timestamp = parser.parse(registration.pop('timestamp'))
                src_ip = registration.pop('ip')

                fields['src_ip'] = src_ip
                fields['last_timestamp'] = last_timestamp
                fields['flex_json'].update(registration)

                detail = detection_detail(**fields)

                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return

                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])
        except KeyError:
            LOG.exception('o365 detection note has missing required keys: {}'.format(couch_doc))


class O365SuspiciousCloudAccess(BaseDetectionHandler):
    """
    Process detection notes of type 'o365_suspicious_cloud_access' to create a detection detail for each object in `details`
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.O365_SUSPICIOUS_CLOUD_ACCESS

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `details`
        """
        O365_PREFIX = 'O365:{}'
        try:
            required = copy.deepcopy(couch_doc['required'])
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'first_timestamp': start_time,
                'flex_json': {
                    'threat_score': required['threat_score'],
                    'certainty_score': required['certainty_score'],
                },
            }

            for detail in required['details']:
                fields = copy.deepcopy(base_fields)
                last_timestamp = parser.parse(detail.pop('timestamp'))
                cloud_activity_table = []
                cloud_activity_table.append(
                    {
                        'login': last_timestamp,
                        'user_agent': detail['user_agent'],
                        'os': detail['os'],
                        'browser': detail['browser'],
                        'device_id': detail['device_id'],
                        'device_name': detail['display_name'],
                        'is_compliant': detail['is_compliant'],
                        'is_managed': detail['is_managed'],
                    }
                )

                fields['last_timestamp'] = last_timestamp
                fields['flex_json'].update(detail)
                fields['flex_json']['cloud_activity_table'] = cloud_activity_table

                detail = detection_detail(**fields)

                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return

                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])

        except KeyError:
            LOG.exception('o365 detection note has missing required keys: {}'.format(couch_doc))


class SWO365InfoAccountBrute(BaseDetectionHandler):
    """
    Process detection notes of type 'sw_o365_infoAccountBruteForce' to create a detection detail for each object in `ips`.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SW_O365_INFO_ACCOUNT_BRUTE_FORCE

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `ips`, only if V2
        Keeping original for backwards compatibility, and it can be cleaned up after DS flips algo
        """

        try:
            base_fields = SWDetections.load_and_transform_doc(couch_doc, DetectionType.SW_O365_INFO_ACCOUNT_BRUTE_FORCE)
            # Limit attackedUsers to first 100
            base_fields['flex_json']['attackedUsers'] = (base_fields['flex_json'].get('attackedUsers', []) or [])[:100]

            ips = base_fields.get('flex_json').pop('ips', None)

            if float(couch_doc.get('required').get('version', 0)) >= 3:
                if not ips:
                    LOG.exception('sw_o365_infoAccountBruteForce detection note is missing ips: {}'.format(couch_doc))
                    return

                # Grab first 100 IPs sorted by descending volume; there is no value in creating >100 detection details
                for ip in sorted(ips, key=lambda ip_entry: ip_entry.get('volume', 0) or 0, reverse=True)[:100]:
                    fields = copy.deepcopy(base_fields)
                    fields.update(
                        {
                            'src_ip': SWDetections.sanitize_ip_address(ip.get('ip')),
                            'first_timestamp': parser.parse(ip.get('start_time')),
                            'last_timestamp': parser.parse(ip.get('end_time')),
                            'count': ip.get('volume'),
                        }
                    )
                    fields['flex_json'].update({'useragent': ip.get('useragent')})
                    detail = detection_detail(**fields)
                    try:
                        sanity_check_dynamic_detail(detail)
                    except Exception:
                        LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                        _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                        return
                    detail.save()
                    LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])
            else:
                detail = detection_detail(**base_fields)
                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return
                detail.save()

            LOG.info('Create detection details for {} detection {}'.format(couch_doc['algorithm'], couch_doc['_id']))
        except KeyError:
            LOG.exception('sw_o365 detection note has missing required keys: {}'.format(couch_doc))


class SWO365AccountBruteForceFailedLogin(BaseDetectionHandler):
    """
    Process detection notes of type 'sw_o365_accountBruteForceFailedLogin' to create a detection detail for each object in `ips`.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SW_O365_ACCOUNT_BRUTE_FORCE_FAILED_LOGIN

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `ips`
        """

        try:
            required = copy.deepcopy(couch_doc['required'])
            base_fields = SWDetections.load_and_transform_doc(couch_doc, DetectionType.SW_O365_ACCOUNT_BRUTE_FORCE_FAILED_LOGIN)

            detail_fields = SWDetections.sanitize_detail_fields(DetectionType.SW_O365_ACCOUNT_BRUTE_FORCE_FAILED_LOGIN, required['details'])
            base_fields['flex_json'].update(detail_fields)
            # Limit attackedUsers to first 100
            base_fields['flex_json']['attackedUsers'] = (base_fields['flex_json'].get('attackedUsers', []))[:100]

            ips = base_fields.get('flex_json').pop('ips', None)

            if not ips:
                LOG.exception(f'sw_o365_accountBruteForceFailedLogin detection note is missing ips: {couch_doc}')
                return

            # Grab first 100 IPs sorted by descending volume; there is no value in creating >100 detection details
            for ip in sorted(ips, key=lambda ip_entry: ip_entry.get('volume', 0), reverse=True)[:100]:
                fields = copy.deepcopy(base_fields)
                src_ip = SWDetections.sanitize_ip_address(ip.get('ip'))
                fields.update(
                    {
                        'src_ip': src_ip,
                        'first_timestamp': parser.parse(ip.get('start_time')),
                        'last_timestamp': parser.parse(ip.get('end_time')),
                        'count': ip.get('volume'),
                    }
                )
                fields['flex_json'].update({'user_agent': ip.get('useragent')})

                detail = detection_detail(**fields)
                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return
                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])

        except KeyError:
            LOG.exception(f'sw_o365 detection note has missing required keys: {couch_doc}')


class O365SuspectPowerAutomate(BaseDetectionHandler):
    """
    Process detection notes of type 'o365_suspicious_power_automate' to create a detection detail
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.O365_SUSPICIOUS_POWER_AUTOMATE

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for the detection detail
        """
        O365_PREFIX = 'O365:{}'
        try:
            required = copy.deepcopy(couch_doc['required'])
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            detail_fields = required['details']
            # Remove new connectors from flow_connector_names list
            new_connectors = detail_fields.get('new_connectors', [])
            all_connectors = detail_fields.get('flow_connector_names', [])
            all_connectors = list(filter(lambda con: con not in new_connectors, all_connectors))
            # None can sometimes make it into the list, we don't want it there
            baseline_connectors = detail_fields.get('baseline', [])
            baseline_connectors = list(filter(None, baseline_connectors))

            detail_fields['flow_connector_names'] = all_connectors
            detail_fields['baseline'] = baseline_connectors
            detail_fields['threat_score'] = required['threat_score']
            detail_fields['certainty_score'] = required['certainty_score']

            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'flex_json': detail_fields,
            }

            detail = detection_detail(**fields)

            try:
                sanity_check_dynamic_detail(detail)
            except Exception:
                LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                return

            detail.save()
        except KeyError:
            LOG.exception('o365 detection note has missing required keys: {}'.format(couch_doc))


class O365SuspiciousDeviceRegistration(BaseDetectionHandler):
    """
    Process detection notes of type 'o365_suspiciousDeviceRegistration' to create a detection detail.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.O365_SUSPICIOUS_DEVICE_REGISTRATION

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `registrations`
        """
        O365_PREFIX = 'O365:{}'
        try:
            required = copy.deepcopy(couch_doc['required'])
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'count': required.get('volume'),
                'flex_json': {
                    'threat_score': required['threat_score'],
                    'certainty_score': required['certainty_score'],
                },
            }

            for registration in required['registrations']:
                fields = copy.deepcopy(base_fields)
                last_timestamp = parser.parse(registration.pop('timestamp'))
                src_ip = registration.pop('ip')

                fields['src_ip'] = src_ip
                fields['last_timestamp'] = last_timestamp
                fields['flex_json'].update(registration)
                detail = detection_detail(**fields)

                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return

                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])
        except KeyError:
            LOG.exception(f'o365 detection note has missing required keys: {couch_doc}')


class O365SuspiciousCopilotAccess(BaseDetectionHandler):
    """
    Process detection notes of type 'o365_suspicious_copilot_access' to create a detection detail for each object in `details`
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.O365_SUSPICIOUS_COPILOT_ACCESS

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create sql rows for every object in `details`
        """
        O365_PREFIX = 'O365:{}'
        try:
            required = copy.deepcopy(couch_doc['required'])
            account_uid = O365_PREFIX.format(required['src_account']['uid'].lower())
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            accounts_to_create = [
                {'uid': account_uid, 'account_type': account_type.AccountType.O365, 'first_seen': start_time, 'last_seen': end_time}
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create)

            base_fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['last_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
                'count': required.get('volume'),
                'flex_json': {
                    'threat_score': required['threat_score'],
                    'certainty_score': required['certainty_score'],
                },
            }

            for copilot_access in required['copilot_access']:
                fields = copy.deepcopy(base_fields)
                fields['src_ip'] = copilot_access.pop('ip')
                fields['reason'] = copilot_access.pop('reason')
                fields['last_timestamp'] = parser.parse(copilot_access['timestamp'])
                default_source = copilot_access.pop('default_source', None)
                fields['flex_json'].update(copilot_access)

                # BRIDGE-1799: Guard against approximate_ip not being defined in older notes
                approximate_ip = fields['flex_json'].get('approximate_ip')
                if isinstance(approximate_ip, dict):
                    source = approximate_ip.pop('source', default_source) or default_source
                    fields['flex_json']['approximate_ip']['source'] = source

                fields['flex_json']['geolocation_associations'] = [
                    {
                        'name': 'Account CIDR Geolocations',
                        "value": '; '.join(
                            [f'{assoc["state"]}, {assoc["country"]}' for assoc in copilot_access.pop('cidr_geolocation_associations')]
                        ),
                    },
                    {
                        'name': 'User Geolocations',
                        "value": "; ".join(
                            [f'{assoc["state"]}, {assoc["country"]}' for assoc in copilot_access.pop('user_geolocation_associations')]
                        ),
                    },
                ]

                detail = detection_detail(**fields)

                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return

                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])

        except KeyError:
            LOG.exception('o365 detection note has missing required keys: {}'.format(couch_doc))


class SecurityInsightNewHost(BaseDetectionHandler):
    """
    Process detection notes of type 'si_new_host' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SI_NEW_HOST

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create security insight detection details from detection note.
        """

        try:
            required = couch_doc['required']
            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['src_ip']),
            }
            detection_detail.objects.bulk_create(
                [
                    detection_detail(
                        last_timestamp=artifact_info['timestamp'],
                        flex_json={'artifact': artifact_info['artifact'], 'via': artifact_info['via']},
                        **base_fields,
                    )
                    for artifact_info in required['artifact_info']
                ]
            )

            LOG.info('Create detection details for security insight new host detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('si_new_host detection note has missing required keys: {}'.format(couch_doc))


class SecurityInsightNovelMACVendor(BaseDetectionHandler):
    """
    Process detection notes of type 'si_novel_mac_vendor' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SI_NOVEL_MAC_VENDOR

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create single security insight novel_mac_vendor detection detail from detection note.
        """

        try:
            required = couch_doc['required']
            fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['last_timestamp']),
                'last_timestamp': required['timestamp'],
                'flex_json': {
                    'vendor': required['vendor'],
                    'dhcp_name': required['dhcp_name'],
                    'mac_address': required['mac_address'],
                    'high_mac_randomization': required['high_mac_randomization'],
                },
            }
            detection_detail.objects.create(**fields)

            LOG.info('Create detection details for Security Insight Novel MAC Vendor detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('si_novel_mac_vendor detection note has missing required keys: {}'.format(couch_doc))


class SecurityInsightNewHostRole(BaseDetectionHandler):
    """
    Process detection notes of type 'si_new_host_role' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SI_NEW_HOST_ROLE

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create security insight detection details from detection note.
        """

        try:
            required = couch_doc['required']
            fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['last_timestamp']),
                'subtype': required['role_name'],
                'last_timestamp': required['timestamp'],
                'flex_json': {'role_display_name': required['role_display_name']},
            }
            detection_detail.objects.create(**fields)

            LOG.info('Create detection details for security insight new host role detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('si_new_host_role detection note has missing required keys: %s', couch_doc)


class SecurityInsightNovelAdmnProtocolUsage(BaseDetectionHandler):
    """
    Process detection notes of type 'si_novel_admin_protocol_usage' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SI_NOVEL_ADMIN_PROTOCOL_USAGE

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create single security insight si_novel_admin_protocol_usage detection detail from detection note.
        """

        try:
            required = couch_doc['required']
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_port'], excluded_fields=['last_timestamp']),
                'dst_session_luid': required.get('dest_host_session_luid'),
                'proto': required['protocol'],
                'last_timestamp': required['timestamp'],
                'flex_json': {'src_port': required['src_port'], 'period_identified': required['period_identified']},
            }
            detection_detail.objects.create(**fields)

            LOG.info('Create detection details for Security Insight Novel Admin Protocol Usage detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('si_novel_admin_protocol_usage detection note has missing required keys: {}'.format(couch_doc))


class SecurityInsightNovelPorts(BaseDetectionHandler):
    """
    Process detection notes of type 'si_novel_ports' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SI_NOVEL_PORTS

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create security insight detection details from detection note.
        """

        try:
            required = couch_doc['required']
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip', 'dst_port', 'first_timestamp']),
                'subtype': required['long_session_guid'],
                'proto': required['protocol'],
                'dst_dns': required['dest_domain_name'],
                'total_bytes_sent': required['send_bytes'],
                'total_bytes_rcvd': required['recv_bytes'],
                'last_timestamp': required['last_time'],
                'flex_json': {'src_port': required['src_port']},
            }
            detection_detail.objects.create(**fields)

            LOG.info('Create detection details for security insight new host role detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('si_new_host_role detection note has missing required keys: %s', couch_doc)


class SecurityInsightNovelAccess(BaseDetectionHandler):
    """
    Process detection notes of type 'si_acaas' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SI_ACAAS

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create security insight detection details from detection note.
        """

        try:
            required = couch_doc['required']
            fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['dst_ip'], excluded_fields=['last_timestamp']),
                'dst_session_luid': required.get('dest_host_session_luid'),
                'count': 1,
                'last_timestamp': required['timestamp'],
                'flex_json': {
                    'path': required['path'],
                    'action': required['action'],
                    'user_name': required['user_name'],
                    'admin_share': required['admin_share'],
                },
            }
            detection_detail.objects.create(**fields)

            LOG.info('Create detection details for si_acaas %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('si_acaas detection note has missing required keys: %s', couch_doc)


class VectraIndicatorMatch(BaseDetectionHandler):
    """
    Process detection notes of type 'vectra_indicator_match' to create single detection detail record.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.RANSOMWARE

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create detection detail events for vectra_indicator_match
        """
        try:
            required = couch_doc['required']
            base_fields = {
                **cls.extract_common_fields(couch_doc, excluded_fields=['first_timestamp', 'last_timestamp']),
            }
            for match in required['matches']:
                fields = {
                    **base_fields,
                    **{
                        'dst_session_luid': match['dest_luid'],
                        'dst_ip': match['dest_ip'],
                        'dst_dns': match['domain_name'],
                        'first_timestamp': parser.parse(match['start_time']),
                        'last_timestamp': parser.parse(match['end_time']),
                        'count': match['match_count'],
                        'flex_json': {
                            'match_type': [r['type'] for r in match['rule']],
                            'match_value': [r['value'] for r in match['rule']],
                            'match_context': match['attacker_detail'],
                            "session_context": [{'source': k, 'context': v if v else None} for k, v in match['source_context'].items()],
                        },
                    },
                }
                detection_detail.objects.create(**fields)

            LOG.info('Create detection details for vectra_indicator_match %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('vectra_indicator_match detection note has missing required keys: %s', couch_doc)


class DataStaging(BaseDetectionHandler):
    """
    Process detection bundeled notes of type 'data_staging' to create multiple detection detail records.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.DATA_STAGING

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create multiple data_staging detection details from the corresponding couch detection note.
        """

        try:
            required = couch_doc['required']

            base_fields = {
                **cls.extract_common_fields(
                    couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp', 'src_ip'], excluded_fields=['dst_ip']
                ),
                'flex_json': {'normal_bytes_received': required['data_to_host_avg_long']},
            }
            for port_event in required['dest_host_details']:
                fields = {
                    **base_fields,
                    **{
                        'dst_ip': port_event['dest_ip'],
                        'dst_dns': port_event['dest_name'] if port_event['dest_name'] else None,
                        'dst_session_luid': port_event['dest_host_session_luid'],
                        'total_bytes_sent': port_event['bytes_sent'],
                        'total_bytes_rcvd': port_event['bytes_gathered'],
                        'dst_port': port_event['server_port'],
                        'proto': port_event['protocol'],
                    },
                }
                detection_detail.objects.create(**fields)

            LOG.info('Create detection details for data_staging detection %s', couch_doc['_id'])
        except KeyError:
            LOG.exception('data_staging detection note has missing required keys: {}'.format(couch_doc))


class Kerberoasting(BaseDetectionHandler):
    """
    Process detection notes of algorithm types that fall under the `kerberoasting` collection of detections
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in [
            DetectionType.KERBEROASTING_CIPHER_DOWNGRADE,
            DetectionType.KERBEROASTING_SINGLE_TICKET_CIPHER_DOWNGRADE,
            DetectionType.KERBEROASTING_SINGLE_TICKET_CIPHER_DOWNGRADE_INFO,
            DetectionType.KERBEROASTING_SPN_SWEEP,
        ]

    @staticmethod
    def _count_privilege_occurances(services: List[dict], category: str) -> int:
        """
        Returns the number of occurances of a specific privilege category in a list of services

        :param services: list of servies that contain privilege informatin
        :type services: List[dict]

        :returns: Count of occurances of requested privilege category
        :type: int
        """
        return len(
            [service['privilege_level'] for service in services if get_privilege_level_to_category(service['privilege_level']) == category]
        )

    @classmethod
    def create_sql_row(cls, couch_doc: dict):
        try:
            required = couch_doc['required']

            fields = {
                **cls.extract_common_fields(
                    couch_doc, mandatory_fields=['src_ip', 'dst_port', 'dst_ip', 'first_timestamp', 'last_timestamp']
                ),
                'dst_session_luid': required['dest_luid'],
                'flex_json': {
                    'kerberos_account_uid': required['client'],
                    'src_port': required['src_port'],
                    'ciphers_requested': required['reqCiphers'],
                    'cipher_response': required['repCipher'],
                    'services_requested': required['spns_requested'],
                    'num_services_low_privilege': cls._count_privilege_occurances(services=required['spns_requested'], category='Low'),
                    'num_services_medium_privilege': cls._count_privilege_occurances(
                        services=required['spns_requested'], category='Medium'
                    ),
                    'num_services_high_privilege': cls._count_privilege_occurances(services=required['spns_requested'], category='High'),
                    'services_requested_list': [service['spn'] for service in required['spns_requested']],
                },
            }

            if couch_doc['algorithm'] in [
                DetectionType.KERBEROASTING_SINGLE_TICKET_CIPHER_DOWNGRADE,
                DetectionType.KERBEROASTING_SINGLE_TICKET_CIPHER_DOWNGRADE_INFO,
            ]:
                fields['flex_json'].update(
                    {
                        'triggering_spn': required['triggering_spn'],
                        'triggering_service_privilege': required['triggering_service_privilege'],
                        'num_weak_tickets_for_service_host_session_pair': required['num_weak_tickets_for_service_host_session_pair'],
                        'num_tickets_in_history': required['num_tickets_in_history'],
                        'earliest_ticket_in_history': required['earliest_ticket_in_history'],
                        'prob_weak_cipher_response_in_history': required['prob_weak_cipher_response_in_history'],
                    }
                )

            detection_detail.objects.create(**fields)
            LOG.info(f'Created detection detail', extra={'type': couch_doc["algorithm"], 'note_id': couch_doc["_id"]})
        except KeyError:
            LOG.exception('Detection note has missing required keys', extra={'type': couch_doc["algorithm"], 'note': couch_doc})


class SuspiciousAD(BaseDetectionHandler):
    """
    Process detection notes for the `suspicious_ad` algorithm
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') == DetectionType.SUSPICIOUS_AD

    @staticmethod
    def _is_dc_shadow_event(operation: str):
        dc_shadow_events = ['DRSReplicaAdd', 'DRSReplicaDel']
        return operation in dc_shadow_events

    @classmethod
    def create_sql_row(cls, couch_doc: dict):
        try:
            required = couch_doc['required']

            operation = required['drsuapi_operation']
            fields = {
                **cls.extract_common_fields(
                    couch_doc, mandatory_fields=['src_ip', 'dst_ip', 'dst_port', 'first_timestamp', 'last_timestamp']
                ),
                'dst_ip': required['dest_ip'],
                'dst_session_luid': required['dest_luid'],
                'dst_port': required['dest_port'],
                'count': 1,
                'flex_json': {
                    'src_port': required['src_port'],
                    'operation': operation,
                    'is_dc_shadow_event': cls._is_dc_shadow_event(operation),
                    'threat_score': couch_doc['scoring']['threat'],
                    'certainty_score': couch_doc['scoring']['certainty'],
                },
            }

            detection_detail.objects.create(**fields)
            LOG.info(f'Created detection detail', extra={'type': couch_doc["algorithm"], 'note_id': couch_doc["_id"]})
        except KeyError:
            LOG.exception('Detection note has missing required keys', extra={'type': couch_doc["algorithm"], 'note': couch_doc})


class AWSDetections(BaseDetectionHandler):
    # Mapping of algo to detection type
    AWS_ALGO = {
        # Algo is type
        # TODO - need to check if there are more new aws types
        DetectionType.AWS_CONTAINER_HIJACKING,
        DetectionType.AWS_CREDENTIAL_ACCESS_CONTAINER,
        DetectionType.AWS_CREDENTIAL_ACCESS_EC2,
        DetectionType.AWS_CREDENTIAL_ACCESS_PARAM_STORE,
        DetectionType.AWS_CRYPTOMINING,
        DetectionType.AWS_EC2_BROWSING,
        DetectionType.AWS_EC2_DISCOVERY,
        DetectionType.AWS_EXTERNAL_ACCESS_GRANTED,
        DetectionType.AWS_LOGIN_PROFILE_MANIPULATION,
        DetectionType.AWS_ORGANIZATION_DISCOVERY,
        DetectionType.AWS_ROOT_ACCESS,
        DetectionType.AWS_S3_DISCOVERY,
        DetectionType.AWS_SUSPECT_CONSOLE_ESCALATION,
        DetectionType.AWS_SUSPECT_PUBLIC_EBS_ACCESS,
        DetectionType.AWS_SUSPECT_PUBLIC_S3_ACCESS,
        DetectionType.AWS_TOR_ACTIVITY,
        DetectionType.AWS_USER_PERMISSION_DISCOVERY,
        DetectionType.AWS_LAMBDA_HIJACKING,
        DetectionType.AWS_RANSOMWARE_S3_ACTIVITY,
        DetectionType.AWS_USER_HIJACKING,
        DetectionType.AWS_SUSPECT_PRIVILEGE_ESCALATION,
        DetectionType.AWS_SUSPICIOUS_CREDENTIAL_USAGE,
        DetectionType.AWS_CHANGE_TO_EC2_ACCESS,
        DetectionType.AWS_ADMIN_PRIVILEGE_GRANTED,
        DetectionType.AWS_PRIVILEGE_ESCALATION_DISCOVERY,
        DetectionType.AWS_GUARDDUTY_DISABLED,
        DetectionType.AWS_EXTERNAL_NETWORK_DISCOVERY,
        DetectionType.AWS_NETWORK_CONFIGURATION_DISCOVERY,
        DetectionType.AWS_LOGGING_MODIFIED,
        DetectionType.AWS_SUSPECT_REGION_ACTIVITY,
        DetectionType.AWS_MFA_DISABLED,
        DetectionType.AWS_LOGGING_DISABLED,
        DetectionType.AWS_ATTACK_TOOLS,
        DetectionType.AWS_RECON_FROM_EC2,
        DetectionType.AWS_SUSPECT_ORGANIZATION_EXIT,
        DetectionType.AWS_SUSPECT_PUBLIC_RDS_CHANGE,
        DetectionType.AWS_TRAFFIC_MIRROR_CREATED,
        DetectionType.AWS_SUSPECT_AMI_EXFILTRATION,
    }

    IDENTITY_TYPE_MAP = {
        'IAMUser': aws_account_attrs.ID_TYPE_IAM_USER,
        'SAMLUser': aws_account_attrs.ID_TYPE_FED_ACC,
        'AWSAccount': aws_account_attrs.ID_TYPE_AWS_EX_ACC,
        'Root': aws_account_attrs.ID_TYPE_ROOT_USER,
        'AWSService': aws_account_attrs.ID_TYPE_AWS_SERVICE,
    }

    @classmethod
    def format_account_type(cls, src_identity):
        base_uid = f"AWS:{src_identity['account_id']}"
        identity_type = cls.IDENTITY_TYPE_MAP.get(src_identity['type'])

        if identity_type == aws_account_attrs.ID_TYPE_IAM_USER:
            if not src_identity.get('user_name'):
                raise KeyError(
                    f'couch_doc did not have required field user_name for AWS Detection type {aws_account_attrs.ID_TYPE_IAM_USER}'
                )
            return f"{base_uid}/{src_identity['user_name'].lower()}", identity_type
        elif identity_type == aws_account_attrs.ID_TYPE_FED_ACC:
            if not src_identity.get('user_name'):
                raise KeyError(
                    f'couch_doc did not have required field user_name for AWS Detection type {aws_account_attrs.ID_TYPE_FED_ACC}'
                )
            return f"SAML:{src_identity['user_name'].lower()}", identity_type
        elif identity_type == aws_account_attrs.ID_TYPE_AWS_EX_ACC:
            return base_uid, identity_type
        elif identity_type == aws_account_attrs.ID_TYPE_ROOT_USER:
            return f"{base_uid}/root", identity_type
        elif identity_type == aws_account_attrs.ID_TYPE_AWS_SERVICE:
            # ec2.amazonaws.com
            invoked_by = src_identity['invoked_by'].split('.')[0]
            return (f"{base_uid}/{src_identity['aws_region']}/{invoked_by}:{src_identity['role_session_name']}", identity_type)
        else:
            return None, None

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Checks detection couch doc note to verify is of correct type.
        """
        return couch_doc.get('algorithm') in AWSDetections.AWS_ALGO

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create an AWS detection event from the corresponding couch detection note.
        """
        try:
            required = couch_doc['required']
            account_uid, identity_type = cls.format_account_type(required['resolved_src_identity'])
            invoked_by = required['resolved_src_identity']['invoked_by']
            principal_id = required['resolved_src_identity']['principal_id']
            session_name = required['resolved_src_identity']['role_session_name']
            if not identity_type:
                LOG.exception('Dropping note %s due to invalid src_identity type. Note: %s', couch_doc.get('_id'), couch_doc)
                _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                return
            user_name = required['resolved_src_identity']['user_name']
            start_time = parser.parse(required['start_time'])
            end_time = parser.parse(required['end_time'])
            service_name = required['resolved_src_identity']['invoked_by']
            region = required['resolved_src_identity']['aws_region']
            accounts_to_create = [
                {
                    'uid': account_uid,
                    'account_type': account_type.AccountType.AWS,
                    'invoked_by': invoked_by,
                    'principal_id': principal_id,
                    'session_name': session_name,
                    'identity_type': identity_type,
                    'user_name': user_name,
                    'first_seen': start_time,
                    'last_seen': end_time,
                    'region': region,
                    'service_id_attr': required['role_chain'][-1]['principal_id'].split(":")[-1] if required['role_chain'] else None,
                    'service_name': service_name,
                    'role_name': required['role_chain'][0]['role_name'] if required['role_chain'] else None,
                }
            ]
            account_map = lib_account.create_or_update_accounts(accounts_to_create, flag_enabled(Flags.aws_reconciliation))

            base_fields = {
                **cls.extract_common_fields(couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp']),
                'is_account_detail': True,
                'is_host_detail': False,
                'account_uid': account_uid,
                'account_id': account_map.get(account_uid),
            }

            for detail in required['event_details']:
                fields = dict({'flex_json': detail}, **base_fields)
                fields['src_ip'] = cls.sanitize_ip_address(fields['flex_json'].get('source_ip_address'))
                fields['flex_json']['src_domain'] = fields['flex_json']['source_domain'] if 'source_domain' in fields['flex_json'] else None
                fields['flex_json']['threat_score'] = required['threat_score']
                fields['flex_json']['certainty_score'] = required['certainty_score']
                fields['flex_json']['role_chain'] = [{'role_name': account_uid}] + required['role_chain']
                fields['flex_json']['assumed_role'] = required['role_chain'][-1]['role_name'] if required['role_chain'] else None
                fields['flex_json']['account_id'] = required['resolved_src_identity']['account_id']
                if couch_doc['algorithm'] == DetectionType.AWS_ATTACK_TOOLS:
                    fields['flex_json']['attack_tool'] = required['attack_tool']
                    fields['flex_json']['attack_tool_count'] = required['attack_tool_count']
                # Overwrite given identity type with sanitized one
                fields['flex_json']['user_identity']['type'] = identity_type
                detail = detection_detail(**fields)

                try:
                    sanity_check_dynamic_detail(detail)
                except Exception:
                    LOG.exception('Dropping note %s due to exception during validation. Note: %s', couch_doc.get('_id'), couch_doc)
                    _cloud_log_dropped_note(couch_doc, type='DROPPED_DYNAMIC_NOTE')
                    return

                detail.save()
                LOG.info('Create detection details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])
        except KeyError:
            LOG.exception('aws detection note has missing required keys: {}'.format(couch_doc))


class PublishDetectionHandlerRouter(BaseDetectionHandler):
    """
    Subclass for handling dispatch from BaseDetectionHandler.dispatch_detection() for detections
    that used to be handled by the old Publish service.
    """

    @staticmethod
    def _get_handler(couch_doc):
        return PUBLISH_ALGO_HANDLER_MAP[couch_doc.get('algorithm')]

    @classmethod
    def get_detail_objects(cls, couch_doc):
        return cls._get_handler(couch_doc).get_detail_objects(couch_doc)

    @staticmethod
    def can_handle_detection(couch_doc):
        return couch_doc.get('algorithm') in PUBLISH_ALGO_HANDLER_MAP

    @classmethod
    def create_sql_row(cls, couch_doc):
        cls._get_handler(couch_doc).create_sql_row(couch_doc)


PUBLISH_ALGO_HANDLER_MAP = {
    DetectionType.BINARYLOADER: publish_handlers.BinaryLoaderDetectionHandler,
    DetectionType.BITCOIN: publish_handlers.BitcoinDetectionHandler,
    DetectionType.BRUTE_FORCE_I2I: publish_handlers.BruteForceDetectionHandler,
    DetectionType.BRUTE_FORCE_I2O: publish_handlers.BruteForceDetectionHandler,
    # These are type=cnc_dga algorithms.
    'cnc_comm': publish_handlers.CncCommDetectionHandler,
    'nxdomain': publish_handlers.NxDomainDetectionHandler,
    'suspect_domain': publish_handlers.SuspectDomainDetectionHandler,
    DetectionType.DARKNET: publish_handlers.DarknetDetectionHandler,
    DetectionType.KERBEROS_CLIENT: publish_handlers.KerberosClientDetectionHandler,
    DetectionType.KERBEROS_BRUTE_FORCE: publish_handlers.KerberosBruteForceDetectionHandler,
    DetectionType.KERBEROS_SERVER: publish_handlers.KerberosServerDetectionHandler,
    DetectionType.OUT_DOS: publish_handlers.OutDosDetectionHandler,
    DetectionType.HIDDEN_HTTP_TUNNEL_EXFIL: publish_handlers.HiddenHttpTunnelDetectionHandler,
    DetectionType.HIDDEN_HTTP_TUNNEL_CNC: publish_handlers.HiddenHttpTunnelDetectionHandler,
    DetectionType.HIDDEN_DNS_TUNNEL_EXFIL: publish_handlers.HiddenDnsTunnelDetectionHandler,
    DetectionType.HIDDEN_DNS_TUNNEL_CNC: publish_handlers.HiddenDnsTunnelDetectionHandler,
    DetectionType.INTERNAL_SPREADING: publish_handlers.InternalSpreadingDetectionHandler,
    DetectionType.STEALTH_POST: publish_handlers.StealthPostDetectionHandler,
    DetectionType.PORT_SWEEP: publish_handlers.PortSweepDetectionHandler,
    DetectionType.OUT_PORT_SWEEP: publish_handlers.OutPortSweepDetectionHandler,
    DetectionType.P2P_CNC: publish_handlers.P2PCncDetectionHandler,
    DetectionType.REVERSE_RAT: publish_handlers.ReverseRatDetectionHandler,
    DetectionType.TOR: publish_handlers.TorDetectionHandler,
}


class SuspectProtocolActivityHandler(BaseDetectionHandler):
    """
    Process detection notes of type 'spa_*_*' to create a detection detail for its couch note.
    """

    @staticmethod
    def can_handle_detection(couch_doc):
        """
        Check detection couch doc note to verify it is of the correct type.
        """
        valid_algorithms = [
            DetectionType.SPA_HTTP_INFO,
            DetectionType.SPA_HTTPS_INFO,
            DetectionType.SPA_DNS_INFO,
            DetectionType.SPA_TCP_INFO,
            DetectionType.SPA_HTTP_CNC,
            DetectionType.SPA_HTTPS_CNC,
            DetectionType.SPA_DNS_CNC,
            DetectionType.SPA_TCP_CNC,
        ]
        return couch_doc.get('algorithm') in valid_algorithms

    @staticmethod
    def is_info_detection(couch_doc):
        """
        Check if the detection is of type 'info'.
        """
        info_spa_algorithms = [
            DetectionType.SPA_HTTP_INFO,
            DetectionType.SPA_HTTPS_INFO,
            DetectionType.SPA_DNS_INFO,
            DetectionType.SPA_TCP_INFO,
        ]
        return couch_doc.get('algorithm') in info_spa_algorithms

    @staticmethod
    def _normalize_details(details):
        """
        Normlize the details entry into the expected form with default values.
        :param details: The incomming details to be normalized.

        Returns: The normalized data in a form matching the protocol from the defaults template.
        """
        if not details:
            return {}

        def nested_merge(default, custom):
            for key, value in default.items():
                if key in custom:
                    if isinstance(value, dict) and isinstance(custom[key], dict):
                        custom[key] = nested_merge(value, custom[key])
                    elif isinstance(value, list) and isinstance(custom[key], list):
                        for i in range(len(custom[key])):
                            if isinstance(custom[key][i], dict) and i < len(value):
                                custom[key][i] = nested_merge(value[i], custom[key][i])
            default.update(custom)
            return default

        defaults = {
            'dns': {'query': [{'type': None, 'id': None, 'rrname': None, 'rrtype': None}]},
            'http': {
                'hostname': None,
                'url': None,
                'http_user_agent': None,
                'http_content_type': None,
                'http_refer': None,
                'http_method': None,
                'protocol': None,
                'status': None,
            },
            'tls': {
                'subject': None,
                'issuerdn': None,
                'serial': None,
                'fingerprint': None,
                'sni': None,
                'version': None,
                'notbefore': None,
                'notafter': None,
                'ja3': {'hash': None, 'string': None},
                'ja3s': {'hash': None, 'string': None},
            },
            'tcp': {},
        }

        for protocol in defaults.keys():
            if protocol in details:
                return {protocol: nested_merge(defaults[protocol], details[protocol])}

        return {}

    @staticmethod
    def _normalize_mitre(mitre):
        """
        Normlize the mitra data into the expected form with default values.
        :param mitre: The incomming mitre data to be normalized.

        Returns: A list containsing object in the form  [{'mitre_id': '', 'name': '', 'framework': ''}]
        """
        if mitre is None:
            return []
        elif isinstance(mitre, dict):
            mitre = [mitre]

        required_keys = {'mitre_id', 'name', 'framework'}
        normalized_mitre = []
        for entry in mitre:
            normalized_entry = {key: entry.get(key, '') for key in required_keys}
            normalized_mitre.append(normalized_entry)

        return normalized_mitre

    @classmethod
    def create_sql_row(cls, couch_doc):
        """
        Create a spa_*_* detection_detail for the given couch note.
        """
        default_bucket = "cncHC"
        details = cls._normalize_details(couch_doc['required']['details'])
        archetype_bucket = couch_doc['required']['metadata']['archetype_bucket']
        try:
            mitre = cls._normalize_mitre(couch_doc['required']['metadata'].get('mitre'))
        except Exception:
            if cls.is_info_detection(couch_doc):
                LOG.warning(
                    'Failed to normalize mitre data for detection %s. Mitre data will be empty since it is info detection.',
                    couch_doc['_id'],
                )
                mitre = []
            else:
                LOG.exception(
                    'Failed to normalize mitre data for detection %s. Detection will not be processed since its non-info detection',
                    couch_doc['_id'],
                )
                raise

        if archetype_bucket is None and couch_doc.get('algorithm') in [
            DetectionType.SPA_HTTP_CNC,
            DetectionType.SPA_HTTPS_CNC,
            DetectionType.SPA_DNS_CNC,
            DetectionType.SPA_TCP_CNC,
        ]:
            archetype_bucket = default_bucket

        dd_fields = {
            **cls.extract_common_fields(
                couch_doc, mandatory_fields=['first_timestamp', 'last_timestamp', 'src_ip', 'dst_port', 'dst_dns', 'dst_ip']
            ),
            'proto': couch_doc['required']['transport'],
            'subtype': couch_doc['required']['alert_type'],
            'flex_json': {
                'threat_score': couch_doc['required']['metadata']['threat_score'],
                'certainty_score': couch_doc['required']['metadata']['certainty_score'],
                'details': details,
                'alert_type': couch_doc['required']['alert_type'],
                'app_protocol': couch_doc['required'].get('app_protocol'),
                'metadata': {
                    'name': couch_doc['required']['metadata']['vectra_name'],
                    'description': couch_doc['required']['metadata']['vectra_description'],
                    'mitre': mitre,
                    'archetype_bucket': archetype_bucket,
                },
                'unique_packets_sent': couch_doc['required'].get('unique_packets_sent', 0),
                'unique_packets_recvd': couch_doc['required'].get('unique_packets_recvd', 0),
            },
            'total_bytes_rcvd': couch_doc['required'].get('bytes_recvd', 0),
            'total_bytes_sent': couch_doc['required'].get('bytes_sent', 0),
        }

        detection_detail.objects.create(**dd_fields)
        LOG.info('Created all detection_detail details for %s detection %s', couch_doc['algorithm'], couch_doc['_id'])
