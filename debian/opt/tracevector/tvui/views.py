# Copyright (c) 2015-2022 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
"""View Endpoints for VUI"""
import base64
import hashlib
import json
import os
import pytz
import re
import requests
from abc import ABC
from datetime import datetime, timedelta
from django.contrib.auth.decorators import login_required
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.db import transaction
from django.db.models import Q
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import redirect, render
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import ugettext as _
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import View
from jose import jwt
from multiprocessing.pool import ThreadPool
from base_tvui.providers import lib_sensors
from rest_framework.viewsets import ViewSet
from time import time

from base_tvui import (
    audit_factory,
    couch_utils,
    custom_errors,
    feature_flipper,
    lib_pairing,
    lib_tagging,
    lib_tv,
    settings as base_tvui_settings,
)
from base_tvui.auth_backends import CloudbridgeJWTAuthBackend
from base_tvui.decorators import login_required_ajax, permissions_required
from base_tvui.feature_flipper import Flags, conditions, flag_enabled
from base_tvui.feature_flipper.flags import get_cbi_flags
from base_tvui.lib_cloud_metrics import MetricName
from base_tvui.lib_cloud_metrics_labels import publish_timing_metric
from base_tvui.lib_vsupport import post_vsupport_api, put_vsupport_api
from base_tvui.lib_tv import (
    get_audit_user_data,
    get_couch_traffic,
    handle_login_failed_event,
    handle_logout_event,
    sanitize_filename,
    is_aws_device,
)
from base_tvui.platform_api import PlatformClient
from base_tvui.settings import CLOUD_ENV
from base_tvui.updater_cloud_utils import Cloud
from lockdown import lockdown_utils
from tvui import helpers
from tvui.account_views import AllAccountsEndpoint
from tvui.custom_responses import JsonMessageResponse
from tvui.custom_view_classes import PermissionView, VadminView
from tvui.detections.detection_utils import serve_pcap_file
from tvui.general_settings.inactive_user_logout_views import InactiveUserLogoutViews
from tvui.host_views import HostPermissionView
from tvui.models import (
    SensorType,
    DataSourceType,
    LimitedTimeLink,
    NetworkSensor,
    UserSaasSAMLProfile,
    detection,
    detection_detail,
    setting,
    user_usage,
)
from tvui.reports import DashboardReport
from tvui.settings.cognito_views import CognitoConfigView
from tvui.traffic_stats.network_traffic import get_sensor_traffic, get_brain_traffic, reformat_health_traffic
from tvui.azure_ad_lockdown.models.settings import AzureADLockdownSettingsFlag
from tvui.user_views import UserBaseView
from tvui.investigations.advanced.view_utils import searchable_days_allowed


from pure_utils import log_utils

LOG = log_utils.get_vectra_logger(__name__)
AUDIT = audit_factory.get_audit_object()


def ping(request):
    """Healthcheck endpoint for SaaSUI.
    Used by the AWS application loadbalancer and EKS liveness and readiness probes.
    """
    return HttpResponse("ok")


def handle_login_event(sender, **kwargs):
    user = kwargs['user']
    try:
        if user.username not in ['vadmin']:
            request = kwargs['request']
            user_usage_obj, created = user_usage.objects.get_or_create(type="user-agent", data=request.META['HTTP_USER_AGENT'])
        LOG.info("User [%s] logged in" % user.username)
    except Exception:
        LOG.info("Unknown user requested /login")


user_logged_in.connect(handle_login_event)
user_logged_out.connect(handle_logout_event)
user_login_failed.connect(handle_login_failed_event)


class AjaxDashboard(PermissionView):
    """
    Vectra Dashboard Ajax Data
    """

    permission = 'dashboard'

    def get(self, request):
        now = timezone.localtime()
        days_offset = int(request.GET.get('days', 1))

        try:
            dashboard_data = DashboardReport(from_date=now - timedelta(days=days_offset), to_date=now).dashboard_report_ajax()

            # We need to make sure that this user has permissions to view hosts before we show them the worst offenders
            has_host_perms = HostPermissionView().is_authorized(request)
            if not has_host_perms:
                dashboard_data['worstOffendersHost'] = None
                dashboard_data['keyAssets'] = None

            # We need to make sure that this user has permissions to view accounts before we show them the worst offenders
            has_account_perms = AllAccountsEndpoint().is_authorized(request)
            if not has_account_perms:
                dashboard_data['worstOffendersAccount'] = None

        except Exception as e:
            LOG.exception(e)
            return JsonResponse({}, status=500)

        return JsonResponse(dashboard_data)


class TaggingAction(PermissionView):
    """
    View for adding/deleting tags
    """

    permission = 'tag'

    def limitedtimelink_perm(self, model_type=None, model_type_id=None, *args, **kwargs):
        """Check if limited time link guest user has access to tag

        If we are tagging a detection event row (detection_detail, dns)
        then we need to check the detection for that row allows the ltl user to accces.

        Returns: True if LTL user has permissions to action
        """
        try:
            tag_id = int(kwargs['tag_id'])
            table_type = kwargs['table']

            if table_type == 'detection_detail':
                # Retrieve detection id for detection event row and set table_type to detection
                detection_event = lib_tagging.TAG_OBJECTS[table_type].objects.get(id=tag_id)
                tag_id = detection_event.detection_id
                table_type = 'detection'

            if model_type == 'host' and table_type == 'detection':
                # look up host of this detection and see if the ltl user has access
                tag_host = lib_tagging.TAG_OBJECTS[table_type].objects.get(id=tag_id).host
                tag_id = tag_host.id if tag_host else None
                table_type = 'host'

            if model_type == 'linkedaccount' and table_type == 'account':
                table_type = 'linkedaccount'
            return (model_type == table_type) and (model_type_id == tag_id)
        except KeyError:
            LOG.error("Unable to retrieve tag_id from view to check against Limited Time Link")
        except (detection_detail.DoesNotExist, detection.DoesNotExist, detection.DoesNotExist):
            LOG.error('Unable to find (tag) objects relating to table[%s] tag_id[%s]', table_type, tag_id)
        return False

    def _fix_table(self, table):
        return 'linked_account' if table == 'account' else table

    def delete(self, request, table, tag_id):
        """Delete tag from object

        Args:
            request (HttpRequest): Request Object
            table (str): ORM table type to delete tag from
            tag_id (int): id of object to remove tag from

        Returns: HttpResponse (object)
        """

        tag_id = int(tag_id)

        tag_name = json.loads(request.body.decode('utf-8'))['tag']
        try:
            lib_tagging.remove_tag(
                table=self._fix_table(table),
                object_ids=[tag_id],
                tag_name=tag_name,
                audit_user_data=get_audit_user_data(request),
                request=request,
            )
        except custom_errors.InvalidTableError:
            return JsonResponse({'errors': ['Invalid object type.']}, status=422)

        return HttpResponse(status=204)

    def post(self, request, table, tag_id):
        """Add tag to object

        Args:
            request (HttpRequest): Request Object
            table (str): ORM table type to add tag to
            tag_id (int): id of object to add tag to

        Returns: HttpResponse (object)
        """

        tag_id = int(tag_id)

        tag_name = json.loads(request.body.decode('utf-8'))['tag']
        try:
            lib_tagging.add_tag(
                table=self._fix_table(table),
                object_ids=[tag_id],
                tag_name=tag_name,
                audit_user_data=get_audit_user_data(request),
                request=request,
            )
        except custom_errors.InvalidTagError:
            return JsonResponse({'errors': ['Invalid tag.']}, status=422)
        except custom_errors.InvalidTableError:
            return JsonResponse({'errors': ['Invalid object type.']}, status=422)

        return HttpResponse(status=204)


class BulkTaggingAction(PermissionView):
    permission = 'tag'

    def limitedtimelink_perm(self, model_type=None, model_type_id=None, *args, **kwargs):
        return False

    def _fix_table(self, table):
        return 'linked_account' if table == 'account' else table

    def delete(self, request, table):
        """
        Delete tag from objects

        Args:
            request (HttpRequest): Request Object
            table (str): ORM table type to delete tag from

        """
        try:
            request_data = json.loads(request.body.decode('utf-8'))
            lib_tagging.remove_tag(
                table=self._fix_table(table),
                object_ids=request_data['objectIds'],
                tag_name=request_data['tag'],
                audit_user_data=get_audit_user_data(request),
                request=request,
            )
        except custom_errors.InvalidTagError:
            return JsonResponse({'errors': ['Invalid tag.']}, status=422)
        except custom_errors.InvalidTableError:
            return JsonResponse({'errors': ['Invalid object type.']}, status=422)
        except Exception:
            error_msg = 'Failed to remove tag from objects.'
            LOG.exception(error_msg)
            return JsonResponse({'errors': [error_msg]}, status=500)

        return HttpResponse(status=204)

    def post(self, request, table):
        """
        Add tag to objects

        Args:
            request (HttpRequest): Request Object
            table (str): ORM table type to add tag to
        """
        try:
            request_data = json.loads(request.body.decode('utf-8'))
            lib_tagging.add_tag(
                table=self._fix_table(table),
                object_ids=request_data['objectIds'],
                tag_name=request_data['tag'],
                audit_user_data=get_audit_user_data(request),
                request=request,
            )
        except custom_errors.InvalidTagError:
            return JsonResponse({'errors': ['Invalid tag.']}, status=422)
        except custom_errors.InvalidTableError:
            return JsonResponse({'errors': ['Invalid object type.']}, status=422)
        except Exception:
            error_msg = 'Failed to add tag to objects.'
            LOG.exception(error_msg)
            return JsonResponse({'errors': [error_msg]}, status=500)

        return HttpResponse(status=204)


class GrafanaView(VadminView):
    @csrf_exempt
    def dispatch(self, request, *args, **kwargs):
        if not flag_enabled(Flags.platform_grafana_enabled):
            return lib_tv.handler404(request)
        return super(GrafanaView, self).dispatch(request, *args, **kwargs)

    def http_method_not_allowed(self, request, *args, **kwargs):
        # Forward every type of HTTP request to Grafana
        response = HttpResponse()
        response["X-Accel-Redirect"] = "@grafana-proxy"
        return response


# AMC: possibly find a way to combine these next three views?
# TODO make this into a class view and use inheritance?
@permissions_required('view_pcap')
def serve_file(request, filename):
    """
    Serve Static Files w/ Auth
    """
    if not os.path.isfile(os.path.join(base_tvui_settings.PCAP_FILE_PATH, filename)):
        return lib_tv.handler404(request)

    return serve_pcap_file(request, filename)


@permissions_required('view_pcap')
def diagnostics_serve_file(request, filename):
    """
    Serve Static Files w/ Auth
    """
    if not os.path.isfile(os.path.join('/opt/protected/diagnostics/', filename)):
        return lib_tv.handler404(request)

    filename = sanitize_filename(filename)
    response = HttpResponse(content_type='application/force-download')
    response['Content-Disposition'] = 'attachement;filename="%s"' % filename
    response['X-Accel-Redirect'] = '/diagnostics/' + filename

    return response


@permissions_required('view_pcap')
def status_report_serve_file(request, filename):
    """
    Serve Static Files w/ Auth
    """
    # nginx will handle 404 if file doesn't exist
    filename = sanitize_filename(filename)
    response = HttpResponse(content_type='application/force-download')
    response['Content-Disposition'] = 'attachement;filename="%s"' % filename
    response['X-Accel-Redirect'] = '/status_reports/' + filename

    return response


@permissions_required('view_pcap')
def status_report_clear_file(request, filename):
    """
    Clear Static Files w/ Auth
    """
    resp = put_vsupport_api('/status-report/clear', json={'filename': filename}, timeout=30)

    if not resp.ok:
        return lib_tv.handler404(request)

    return HttpResponseRedirect('/tools/status_report')


@login_required
def resources_serve_file(request, filename):
    """
    Serve Static Resources Files w/ Auth
    """
    filename = sanitize_filename(filename)
    response = HttpResponse(content_type='application/force-download')
    response['Content-Disposition'] = 'attachement;filename="%s"' % filename
    response['X-Accel-Redirect'] = '/download/' + filename
    return response


class VSIDownload(ViewSet, PermissionView, ABC):

    HASH_SEED = '4-WJoq12%Sf'

    def get_expiry(self):
        try:
            expiry_hours = float(setting.objects.get(group='vsi', key='vsi_link_expiration_hrs').value)
        except setting.DoesNotExist:
            expiry_hours = 2
        epoch = int((timezone.now() + timedelta(hours=expiry_hours)).timestamp())
        return epoch

    def hash_value(self, expiry, fname, internal_path):
        uri = os.path.join(internal_path, fname)
        hash_params = f"{self.HASH_SEED}{uri}{expiry}".encode('utf-8')
        hashi = hashlib.md5(hash_params).digest()
        b64_hash = base64.urlsafe_b64encode(hashi)
        str_hash = b64_hash.decode('utf-8').rstrip('=')
        return str_hash

    def _get_download_symlinked_image_response(self, request, image_symlink_path, internal_path):
        """
        Get response that should trigger an image to be downloaded.
        :param request: django request
        :param image_symlink_path: path to image file symlink
        :param internal_path: nginx path that is mapped to folder where file is located
        :return: HttpResponse that triggers image download
        """
        if conditions.is_cloud():
            internal_path += '/secured'
            fname = os.path.basename(os.path.normpath(image_symlink_path))
            device = lib_tv.get_device_address()
            if not device:
                LOG.info('Unable to fetch brain IP')
                return HttpResponse(status=404)
            expiry = self.get_expiry()
            hash_params = self.hash_value(expiry, fname, internal_path)
            return HttpResponseRedirect(f"https://{device}{internal_path}/{fname}?expiry={expiry}&hash={hash_params}")
        else:
            try:
                file_name = os.path.basename(os.readlink(image_symlink_path))
            except OSError:
                LOG.exception('Attempting to download image, read link %s failed.', image_symlink_path)
                return lib_tv.r2r('404.html', locals(), request)
            response = HttpResponse(content_type='application/force-download')
            response['Content-Disposition'] = 'attachment; filename={}'.format(file_name)
            response['X-Accel-Redirect'] = os.path.join(internal_path, file_name)
            return response


class VSIDownloadVsensor(VSIDownload):

    permission = 'sensors'

    def download_vsensor_image(self, request):
        return self._get_download_symlinked_image_response(
            request=request,
            image_symlink_path=base_tvui_settings.VSENSOR_LATEST_IMAGE_SYMLINK_PATH,
            internal_path=base_tvui_settings.VSENSOR_INTERNAL_IMAGES_PATH,
        )

    def download_vsensor_qcow2(self, request):
        return self._get_download_symlinked_image_response(
            request=request,
            image_symlink_path=base_tvui_settings.VSENSOR_LATEST_QCOW2_SYMLINK_PATH,
            internal_path=base_tvui_settings.VSENSOR_INTERNAL_IMAGES_PATH,
        )

    def download_vsensor_vhd(self, request):
        return self._get_download_symlinked_image_response(
            request=request,
            image_symlink_path=base_tvui_settings.VSENSOR_LATEST_VHD_SYMLINK_PATH,
            internal_path=base_tvui_settings.VSENSOR_INTERNAL_IMAGES_PATH,
        )


class VSIDownloadStream(VSIDownload):

    permission = "service_cognito_stream"

    def download_stream_image(self, request):
        """
        Returns latest (customized) Stream OVA.
        Sets redirect in response and lets NGINX handle serving the file.
        """
        return self._get_download_symlinked_image_response(
            request=request,
            image_symlink_path=base_tvui_settings.STREAM_LATEST_IMAGE_SYMLINK_PATH,
            internal_path=base_tvui_settings.STREAM_INTERNAL_IMAGES_PATH,
        )

    def download_stream_vhd(self, request):
        """
        Returns latest (customized) Stream VHD.
        Sets redirect in response and lets NGINX handle serving the file.
        """
        return self._get_download_symlinked_image_response(
            request=request,
            image_symlink_path=base_tvui_settings.STREAM_LATEST_VHD_SYMLINK_PATH,
            internal_path=base_tvui_settings.STREAM_INTERNAL_IMAGES_PATH,
        )

    def download_stream_qcow2(self, request):
        """
        Returns latest (customized) Stream QCOW2.
        Sets redirect in response and lets NGINX handle serving the file.
        """
        return self._get_download_symlinked_image_response(
            request=request,
            image_symlink_path=base_tvui_settings.STREAM_LATEST_QCOW2_SYMLINK_PATH,
            internal_path=base_tvui_settings.STREAM_INTERNAL_IMAGES_PATH,
        )


def _load_image_info(info_file_path):
    """
    Get image info from JSON metadata file.
    :param info_file_path: path to info file
    :return: Image info dict
    """
    data = {'version': None, 'date': None, 'ip_address': None}
    try:
        with open(info_file_path, 'r') as info_file:
            info = json.load(info_file)
        data['version'] = info.get('version')
        data['date'] = info.get('date')
        data['ip_address'] = info.get('ip_address')
    except OSError:
        LOG.warning('Attempting to retrieve image, opening %s failed.', info_file_path)
    except IOError:
        LOG.warning('Could not open file: %s', info_file_path)
    except ValueError:
        LOG.exception('Unable to load json information from %s', info_file_path)

    return data


@permissions_required('view_sensors')
def vsensor_image_info(request):
    """
    Returns JSON response about latest (customized) virtual sensor image information.

    {'version': version_string, 'ip_address': ip_address_string, 'date': timstamp}
    version: version of vsensor image.
    ip address: (headend) ip address or hostname tied to vsensor image.
    date: date when customized vsensor image was generated.
    """
    image_info = _load_image_info(info_file_path=base_tvui_settings.VSENSOR_IMAGE_INFO_PATH)
    return JsonResponse(image_info)


@permissions_required('view_sensors')
def vsensor_vhd_info(request):
    """
    Returns JSON response about latest (customized) virtual sensor image information.

    {'version': version_string, 'ip_address': ip_address_string, 'date': timstamp}
    version: version of vsensor image.
    ip address: (headend) ip address or hostname tied to vsensor image.
    date: date when customized vsensor image was generated.
    """
    image_info = _load_image_info(info_file_path=base_tvui_settings.VSENSOR_IMAGE_INFO_PATH)
    return JsonResponse(image_info)


@permissions_required('view_sensors')
def vsensor_qcow2_info(request):
    """
    Returns JSON response about latest (customized) virtual sensor image information.

    {'version': version_string, 'ip_address': ip_address_string, 'date': timstamp}
    version: version of vsensor image.
    ip address: (headend) ip address or hostname tied to vsensor image.
    date: date when customized vsensor image was generated.
    """
    image_info = _load_image_info(info_file_path=base_tvui_settings.VSENSOR_IMAGE_INFO_PATH)
    return JsonResponse(image_info)


@permissions_required('view_service_cognito_stream')
def stream_image_info(request):
    """
    Returns JSON response about latest (customized) Stream image information.

    {'version': version_string, 'ip_address': ip_address_string, 'date': timstamp}
    version: version of Stream image.
    ip address: (headend) ip address or hostname tied to Stream image.
    date: date when customized Stream image was generated.
    """
    image_info = _load_image_info(info_file_path=base_tvui_settings.STREAM_IMAGE_INFO_PATH)
    return JsonResponse(image_info)


@permissions_required('view_service_cognito_stream')
def stream_vhd_info(request):
    """
    Returns JSON response about latest (customized) Stream image information.

    {'version': version_string, 'ip_address': ip_address_string, 'date': timstamp}
    version: version of Stream image.
    ip address: (headend) ip address or hostname tied to Stream image.
    date: date when customized Stream image was generated.
    """
    image_info = _load_image_info(info_file_path=base_tvui_settings.STREAM_IMAGE_INFO_PATH)
    return JsonResponse(image_info)


@permissions_required('view_service_cognito_stream')
def stream_qcow2_info(request):
    """
    Returns JSON response about latest (customized) Stream image information.

    {'version': version_string, 'ip_address': ip_address_string, 'date': timstamp}
    version: version of Stream image.
    ip address: (headend) ip address or hostname tied to Stream image.
    date: date when customized Stream image was generated.
    """
    image_info = _load_image_info(info_file_path=base_tvui_settings.STREAM_IMAGE_INFO_PATH)
    return JsonResponse(image_info)


@permissions_required('view_traffic', licit_redirect=True)
def health_subnets(request):
    """
    Redirect to new devices page
    """
    return redirect('network_stats_devices')


@permissions_required('view_traffic')
def ajax_health_traffic(request):
    """
    Endpoint /ajax_health_traffic/ for retrieving traffic health monitoring data from couch.
    This endpoint is the same as ajax_health but does not include subnet data.
    """
    date_now = timezone.now()
    date_now_ts = datetime.strftime(timezone.localtime(date_now), "%m/%d/%Y, %H:%M")
    time_stride = timezone.now() - timedelta(hours=8)
    time_stride_ts = datetime.strftime(timezone.localtime(time_stride), "%m/%d/%Y, %H:%M")

    # Fetch traffic data from couch for overall traffic and overall traffic breakdown graphs
    try:
        if conditions.is_cloud():
            all_health_data = []  # TODO: re-enable this when this page moves to ember
        else:
            all_health_data = get_couch_traffic("all", include_traffic=True, include_subnets=False)
    except Exception:
        LOG.exception('Could not load data from couchdb for overall traffic data')
        all_health_data = []

    filter_query = Q(status=NetworkSensor.STATUS_PAIRED) | Q(status=NetworkSensor.STATUS_AVAILABLE)
    search_query = request.GET.get('search', '')
    if search_query != '':
        filter_query &= Q(alias__icontains=search_query)

    sensor_luids = list(NetworkSensor.objects.filter(filter_query).values_list('luid', flat=True))

    sensor_health_data = []
    # Add Headend Sensor Traffic Data if in mix mode
    try:
        if brain_traffic := get_brain_traffic():
            if conditions.is_cloud():
                reformat_health_traffic(brain_traffic)
            sensor_health_data.append(brain_traffic)
    except Exception:
        LOG.warning("Unable to pull brain sensor data")

    return JsonResponse(
        {
            "all_health_data": all_health_data,
            "per_sensor_health": sensor_health_data,
            "sensor_luids": sensor_luids,
            "time_stride": time_stride_ts,
            "date_now": date_now_ts,
        }
    )


@permissions_required('view_traffic')
def ajax_sensor_health_traffic(request):
    sensor_luid = request.GET.get('luid')
    if not sensor_luid:
        return JsonResponse({})

    try:
        sensor = NetworkSensor.objects.get(luid=sensor_luid)
        sensor_health_data = get_sensor_traffic(sensor)
        if conditions.is_cloud():
            reformat_health_traffic(sensor_health_data)
    except NetworkSensor.DoesNotExist:
        LOG.exception("Sensor does not exist")
        return JsonResponse({"per_sensor_health": []})
    except Exception:
        LOG.exception("Could not load data from couchdb for headend")
        return JsonResponse({"per_sensor_health": []})

    return JsonResponse({"per_sensor_health": sensor_health_data})


@login_required
def cloud_check(request):
    """
    Basic Ajax cloud connectivity check
    """
    try:
        cloud_status = Cloud().health_check()

    except NotImplementedError:
        LOG.exception('cloud_check not implemented exception, return status of 410')
        cloud_status = 410

    except Exception:
        LOG.exception('cloud_check exception, return status of 500')
        cloud_status = 500

    LOG.info("Cloud status [%s] from ajax cloud_call", cloud_status)

    return HttpResponse(cloud_status, content_type="application/json")


@permissions_required('view_physical_host')
def conn_check_vcenter(request):
    plat_client = PlatformClient()
    statuses = plat_client.test_vsphere_connection()
    if statuses:
        # Service is enabled and got response from platform client about connectivity of each vCenter
        # Map platform error messages to localized UI messages
        for conn_info in statuses['vcenters'].values():
            err_msg = conn_info.get('message')
            if err_msg:
                # Assume generic connection error
                error_type = 'connection'
                message = _(helpers.vcenter_error_msg['connection_error'])

                # Check for more specific errors
                if 'credential' in err_msg:
                    error_type = 'credential'
                    message = _(helpers.vcenter_error_msg['credential_error'])
                elif 'permission' in err_msg:
                    error_type = 'permission'
                    message = _(helpers.vcenter_error_msg['permission_error'])

                conn_info['error_type'] = error_type
                conn_info['message'] = message
        return JsonResponse(statuses)
    elif statuses is None:
        # vCenter is not enabled, return No Content
        return HttpResponse(204)
    else:
        # Service is enabled, but failed to reach platform client
        return HttpResponse(500)


@permissions_required('view_setting_aws')
def conn_check_aws(request):
    plat_client = PlatformClient()

    aws_status = plat_client.test_aws_connection()

    if aws_status:
        return JsonResponse(aws_status, safe=False)
    elif aws_status is None:
        return HttpResponse(204)
    else:
        return HttpResponse(500)


def validate_aws_credential(cred_to_check):
    """
    validate cred_to_check
    since it may be an existing aws cred or a brand new
    cred that is not saved in couch
    Returns the cred or None if not validated
    """
    access_key = cred_to_check.get('access_key', '')
    secret_key = cred_to_check.get('secret_key', '')
    if len(access_key) < 16 | len(access_key) > 128:
        return None
    if not secret_key:
        return None
    return cred_to_check


@permissions_required('view_setting_aws')
def cred_check_aws(request):
    """
    Check a single AWS account credential that may or may not be saved in couch
    """
    plat_client = PlatformClient()

    credential = json.loads(request.body.decode('utf-8')).get('credential')
    cred_to_check = validate_aws_credential(credential)

    if cred_to_check:
        aws_cred_status = plat_client.test_aws_credential(cred_to_check)
        cred_to_check.pop('secret_key')
        if aws_cred_status.get('status_code') in [200, 401]:
            cred_status = aws_cred_status.get('aliases', {}).get('tested account', {})
            cred_to_check['status'] = {'credValid': cred_status.get('success')}
            if cred_status.get('success'):
                child_account_statuses = aws_cred_status.get('aliases', {}).get('tested account', {}).get('child_accounts', {})
                for child_account in cred_to_check.get('child_accounts'):
                    account = child_account.get('account')
                    child_account['isValid'] = child_account_statuses.get(account).get('success', False)
                    child_account['error'] = child_account_statuses.get(account).get('error', '')
        else:
            cred_to_check['status'] = {'credValid': False}

    return JsonResponse(cred_to_check, safe=False)


@permissions_required('view_setting_aws')
def conn_check_aws_cloudwatch(request):
    plat_client = PlatformClient()

    aws_cloudwatch_status = plat_client.test_aws_cloudwatch_connection()

    return JsonResponse(
        {'status_code': aws_cloudwatch_status.get('status_code'), 'error_message': aws_cloudwatch_status.get('error_message')}, safe=False
    )


@permissions_required('view_setting_aws')
def conn_check_aws_securityhub(request):
    plat_client = PlatformClient()

    aws_security_hub_status = plat_client.test_aws_security_hub_connection()

    return JsonResponse(
        {'status_code': aws_security_hub_status.get('status_code'), 'error_message': aws_security_hub_status.get('error_message')},
        safe=False,
    )


@permissions_required('view_setting_aws')
def aws_coverage_report(request):
    plat_client = PlatformClient()
    status = plat_client.aws_coverage_csv_report_status()

    response = HttpResponse(status=204)
    if status.get('ready'):
        coverage_csv = plat_client.get_aws_coverage_csv_report()
        response = HttpResponse(coverage_csv.get('csv'), content_type='text/csv', status=200)
        response['Content-Disposition'] = 'attachment; filename="aws_coverage_report.csv"'

    return response


@permissions_required('view_setting_aws')
def aws_coverage_report_status(request):
    plat_client = PlatformClient()
    return JsonResponse(plat_client.aws_coverage_csv_report_status(), content_type='application/json')


@login_required_ajax  # Do not use permissions required
def ich_template(request, template, render=False):
    """
    Returns ICanHaz JSON template.
    """
    template_path = os.path.join(base_tvui_settings.SRC_ROOT, 'tracevector/tvui/tv_templates/flow/%s.html' % template)
    if not os.path.exists(template_path):
        return lib_tv.handler404(request)
    template_doc = {}
    if render:
        template_doc['template'] = render_to_string('flow/%s.html' % template, context={}, request=request)
    else:
        with open(template_path, 'r') as f:
            template_doc['template'] = f.read()
    return JsonResponse(template_doc)


@permissions_required('view_backups')
def backup(request, backup_name):
    """
    Allow users to download a backup file. For other backup / restore logic, see colossus's vectra.platform.backup_restore package.
    """
    response = HttpResponse()

    if request.method == "GET":
        if re.match(r"migration.*\.tar\.(gz|zst)\.gpg", backup_name):
            response["Content-Disposition"] = "attachment; filename={0}".format(backup_name)
            response['X-Accel-Redirect'] = "/backups/{0}".format(backup_name)

            audit_msg = "Downloaded backup: {}".format(backup_name)
            AUDIT.audit(get_audit_user_data(request), True, audit_msg)
        else:
            response = lib_tv.handler404(request)
    else:
        response.status_code = 405

    return response


def backup_cloudbridge(request, backup_name):
    """
    Allow users to download a backup file from an appliance in cloudbridge mode using
    JWT authentication.
    """
    response = HttpResponse()
    error_messages = {
        400: 'Authentication token is missing. Please include token in the request and try again.',
        401: 'Authentication token is not valid. Please use a download link with a valid token.',
        403: 'Authorization failed for requested file.',
        404: 'The requested backup file cannot be found.',
        410: 'Authentication token has expired. Please generate a new download link and try again.',
    }

    if request.method != "GET":
        response.status_code = 405
        return response

    status_code, filename = CloudbridgeJWTAuthBackend().authenticate(request, 'ntc_backup_download_secret')
    if status_code != 200:
        response = render(request, 'CloudBridgeDetail.html', {'message': error_messages.get(status_code, f'{status_code}: Unknown error')})
        response.status_code = status_code
        return response

    # Compare the filename extracted from JWT claims to the one from the URL parameter
    if filename != backup_name:
        response = render(request, 'CloudBridgeDetail.html', {'message': error_messages.get(403)})
        response.status_code = status_code
        return response

    if re.match(r"^migration.[-\w._:]+\.tar\.(gz|zst)\.gpg$", backup_name):
        response.status_code = 200
        response["Content-Disposition"] = f"attachment; filename={backup_name}"
        response['X-Accel-Redirect'] = f"/backups/{backup_name}"
    else:
        response = render(request, 'CloudBridgeDetail.html', {'message': error_messages.get(404)})
        response.status_code = 404

    return response


@permissions_required('view_detection')
def ajax_virustotal(request):
    if request.method == "GET":
        try:
            vt_domain = request.GET.get('domain', "")
            if vt_domain:
                vt_domain = re.sub("[^0-9a-zA-Z\.]", "", vt_domain)

            vt_ips = re.sub("[^0-9\.\,]", "", request.GET.get('ips')).split(',')

            plapi = PlatformClient()
            vt_resp = plapi.get_virus_group_total_data(domain=vt_domain, ips=vt_ips, needs_update=True)
            return JsonResponse(vt_resp)

        except ValueError as ve:
            LOG.error("Unable to get value | %s", str(ve))
            return JsonMessageResponse('error', 'No host')
        except Exception as e:
            LOG.error("Unable to query virustotal | %s", str(e))
            return JsonMessageResponse('error', 'No host')
    else:
        response = HttpResponse()
        response.status_code = 405
        return response


class VUIStatusAPI(View):
    """External status endpoint, requires NO AUTH"""

    def get(self, request):
        return JsonResponse({'status': 'on'}, status=200)


def _get_product_enablement():
    """
    Retrieve information about which products are enabled
    """
    if not flag_enabled(Flags.recall):
        return []

    plapi = PlatformClient()
    config = plapi.get_total_recall_config()
    product_enablement = {'recall': config.get('licensed', False)}
    return [p for p, enabled in product_enablement.items() if enabled]


@login_required
def user_defined_settings(request):
    """
    /api/app/userSettings that returns a list of information about user-defined settings
    """
    inactivity = InactiveUserLogoutViews()
    if request.method == "GET":
        user_settings = []
        try:
            auto_refresh = setting.objects.get_or_create(group='autorefresh', key='dashboard', defaults={'value': 'off'})[0].value == 'on'
            user_settings.append({'id': 'auto-refresh', 'value': auto_refresh})
            if conditions.is_cloud():
                cloud_timezone_value = lib_tv.get_ui_timezone()
                user_settings.append({'id': 'timezone', 'value': cloud_timezone_value})
                user_settings.append({'id': 'gitVersion', 'value': lib_tv.get_version_number()})
            else:
                user_settings.append({'id': 'timezone', 'value': base_tvui_settings.TIME_ZONE})
            username = request.user.username
            user_settings.append({'id': 'username', 'value': username})
            user_settings.append({'id': 'instanceName', 'value': lib_tv.get_instance_name()})
            if LimitedTimeLink.objects.filter(user_id=request.user.id).exists():
                user_type = 'guest'
            else:
                user_type = request.user.account_type

                if flag_enabled(Flags.saas_saml_profiles):
                    role = request.user.groups.all().first().group_extend
                    user_settings.append(
                        {
                            'id': 'currentUser',
                            'value': UserBaseView._retrieve_user_info(request.user, role, request, with_api_token=False),
                        }
                    )
            user_settings.append({'id': 'userType', 'value': user_type})
            features = list(setting.objects.filter(group='feature', value='on').values_list('key', flat=True).order_by('key'))
            user_settings.append({'id': 'features', 'value': features})
            user_settings.append({'id': 'passwordExpirationWarning', 'value': lib_tv.get_password_expiration(request)})
            user_settings.append(
                {'id': 'accountLockdownEnabled', 'value': lockdown_utils.get_account_lockdown_settings()['lockdown_enabled']}
            )
            user_settings.append({'id': 'hostLockdownEnabled', 'value': lockdown_utils.get_host_lockdown_settings()['lockdown_enabled']})
            user_settings.append({'id': 'enabledProducts', 'value': _get_product_enablement()})
            user_settings.append(
                {'id': 'featureFlags', 'value': feature_flipper.get_flag_states_from_request(request, conditions.is_cloudbridge())}
            )
            user_settings.append({'id': 'azureADManualLockdownEnabled', 'value': AzureADLockdownSettingsFlag.is_manual_lockdown_enabled()})

            if conditions.is_cloud() or conditions.is_cloudbridge():
                user_settings.append({'id': 'investigateSearchableDays', 'value': searchable_days_allowed()})

            if conditions.is_cloud():
                data_source_onboarding = lib_sensors.get_data_source_onboarding_stats()
                user_settings.append({'id': 'dataSourceOnboarding', 'value': data_source_onboarding})

            notifications = {}
            platform_type = lib_tv.get_device_type()
            if platform_type == "X24":
                notifications['x24'] = {'eolDate': 'September 30th, 2021'}

            user_settings.append({'id': 'notifications', 'value': notifications})

            role = ''
            try:
                role = request.user.groups.all().first().group_extend.vname
            except Exception:  # user is not assigned to a group
                pass

            user_settings.append({'id': 'role', 'value': role})

            in_app_support_flag = setting.objects.get(group='feature', key='pendo_on_prem').value == 'on'
            if in_app_support_flag:
                in_app_support_config = CognitoConfigView.retrieve_in_app_support_config()
                user_settings.append({'id': 'inAppSupport', 'value': in_app_support_config['values']})

            if flag_enabled(Flags.settings_inactive_user_logout):
                user_settings.append({'id': 'inactivity', 'value': inactivity.get_inactivity()})

            # if this is from aws and doesn't have a token we are from the marketplace, and they already had to accept the EULA from there.
            if is_aws_device():
                try:
                    # try except, otherwise if platform rest is down, VUI does not let anyone log in
                    if not PlatformClient.get_aws_provision_token():
                        user_settings.append({'id': 'isAwsDevice', 'value': True})
                except Exception as e:
                    pass

            user_settings.append(
                {'id': 'eulaConfirmed', 'value': setting.objects.filter(group='eula', key__in=["username", "email"]).exists()}
            )

            showMetadataSharingModal = setting.objects.get(group='datasharing', key='alert').value == 'True'
            user_settings.append({'id': 'showMetadataSharingModal', 'value': showMetadataSharingModal})

            default_password_change_required, _ = setting.objects.get_or_create(
                group="alert", key="default_password_change_required", defaults={'value': 'True'}
            )
            user_settings.append({'id': 'defaultPasswordChangeRequired', 'value': default_password_change_required.value == 'True'})
            user_settings.append({'id': 'externalVui', 'value': os.environ.get('EXTERNAL_VUI', 'UNKNOWN EXTERNAL VUI')})
            user_settings.append({'id': 'internalBrain', 'value': os.environ.get('INTERNAL_BRAIN', 'UNKNOWN BRAIN')})
            if flag_enabled(Flags.global_view_enabled):
                global_view_instance_role = lib_tv.get_global_view_instance_role()
                global_view_anchor_url = lib_tv.get_global_view_anchor_url()

                user_settings.append(
                    {
                        'id': 'globalView',
                        'value': {"globalViewInstanceRole": global_view_instance_role, "globalViewAnchorUrl": global_view_anchor_url},
                    }
                )
            return JsonResponse({'userSettings': user_settings}, status=200)
        except Exception:
            LOG.exception('Unable to retrieve settings information')
            return JsonResponse({}, status=500)


@login_required
@require_http_methods(["GET"])
def cbi_feature_flags(request):

    if not conditions.is_cloudbridge():
        return JsonMessageResponse('error', "This mode is not supported.", status_code=400)

    try:
        cbi_flags = get_cbi_flags()
        return JsonResponse({'cbiFeatureFlags': cbi_flags}, status=200)
    except Exception:
        LOG.exception("Error when retrieve CBI feature flags")
        return JsonMessageResponse('error', "Unknown error retrieving CBI feature flags", status_code=500)


@login_required
@require_http_methods(["GET"])
def sensors_filter_options(request):
    """
    Function returns a dictionary with sensors and respective metadata (SaaS and Hardware) based on filters.
    * Filtering mechanism *:
    - First all sensors are retrieved
    - Then based on filter specifics a subset of sensors objects are returned in dictionary.
    """
    request_start_time = time()
    tracking_context = request.GET.get('trackingContext', None)

    valid_sensor_type_options = [
        *DataSourceType.SAAS_SOURCES,
        *SensorType.ALL_SENSOR_TYPES,
    ]
    try:
        sensor_type_filters = []
        sensor_type_filters_params = request.GET.get('sensorType')
        if sensor_type_filters_params:
            sensor_type_filters = sensor_type_filters_params.split(',')
        if sensor_type_filters:
            if 'allNetworkSensors' in sensor_type_filters:
                sensor_type_filters.remove('allNetworkSensors')
                sensor_type_filters.extend(SensorType.ALL_SENSOR_TYPES)
            for sensor_type_filter in sensor_type_filters:
                if sensor_type_filter and sensor_type_filter not in valid_sensor_type_options:
                    LOG.error(f"Invalid sensor_type: {sensor_type_filters_params} provided in request.")
                    if conditions.is_cloud():
                        labels = {'trackingContext': tracking_context, 'status': '400'}
                        elapsed = time() - request_start_time
                        publish_timing_metric(metric_name=MetricName.SENSOR_FILTER_OPTIONS_API_RTT_S, labels=labels, elapsed_time_s=elapsed)
                    return JsonResponse({"errors": [{"title": "invalid sensor_type request."}]}, status=400)
        headend = {}
        if not sensor_type_filters or 'brain' in sensor_type_filters or 'mixed' in sensor_type_filters:
            include_brain = True
            pc = PlatformClient()
            mode = pc.platform_get_mode()
            if mode == 'mixed' and 'mixed' in sensor_type_filters:
                include_brain = True
            if mode == 'mixed' and 'brain' in sensor_type_filters:
                include_brain = True
            if mode == 'brain' and 'mixed' in sensor_type_filters:
                include_brain = False
            if mode == 'brain' and 'brain' in sensor_type_filters:
                include_brain = True
            detector_config = pc.get_sensors()
            device_servername = lib_tv.get_device_name()
            if detector_config:
                for luid, v in detector_config.get('sources', {}).items():
                    if v.get('local', False):
                        headend = {'id': 0, 'luid': luid, 'type': 'brain', 'alias': device_servername}

            if not headend and conditions.is_appliance():
                return JsonResponse({"errors": [{"title": "could not retrieve brain info."}]}, status=500)
        return_dict = {"meta": {"count": None}, "sensorFilterOptions": [headend] if headend and include_brain else []}
        sensors = lib_pairing.get_sensors(include_cloud=False)
        for sensor in sensors:
            sensor_type = sensor.get('type') if sensor.get('is_saas') else sensor.get('product_name')
            if not sensor_type_filters or sensor_type in sensor_type_filters:
                sensor_id = sensor.get('sensor_name') if sensor.get('is_saas') else sensor.get('id')
                sensor_data = {
                    'type': sensor_type if sensor_type in DataSourceType.SAAS_SOURCES else 'sensor',
                    'id': sensor_id,
                    'alias': sensor['alias'],
                    'luid': sensor['luid'],
                    'dataSource': sensor_type if sensor_type in DataSourceType.SAAS_SOURCES else None,
                }
                return_dict['sensorFilterOptions'].append(sensor_data)
        return_dict['meta']['count'] = len(return_dict['sensorFilterOptions'])
        if conditions.is_cloud():
            labels = {'trackingContext': tracking_context, 'status': '200'}
            elapsed = time() - request_start_time
            publish_timing_metric(metric_name=MetricName.SENSOR_FILTER_OPTIONS_API_RTT_S, labels=labels, elapsed_time_s=elapsed)
        return JsonResponse(return_dict, status=200)
    except Exception as e:
        LOG.exception(e)
        if conditions.is_cloud():
            labels = {'trackingContext': tracking_context, 'status': '500'}
            elapsed = time() - request_start_time
            publish_timing_metric(metric_name=MetricName.SENSOR_FILTER_OPTIONS_API_RTT_S, labels=labels, elapsed_time_s=elapsed)

    return JsonResponse({"errors": [{"title": "could not retrieve sensor info."}]}, status=500)


@require_http_methods(["POST"])
@permissions_required('edit_brain')
def shutdown(request):
    """
    Restart machine if no critical processes are running
    """
    status = 500
    msg = {'status': 'failure', 'message': 'Error shutting machine down'}
    audit_msg = 'Error shutting down'
    try:
        request_body = json.loads(request.body.decode('utf-8'))
        reboot = request_body.get('reboot', False)

        is_reboot = reboot is True or str(reboot).lower() == 'true'
        endpoint = '/reboot' if is_reboot else '/shutdown'
        resp = post_vsupport_api(endpoint)

        if resp.status_code == 200:
            msg = {'status': 'success', 'message': 'Shutting machine down'}
            status = 200
            audit_msg = 'Shutting down machine'
        elif resp.status_code == 400:
            status = 503
            proc = resp.json()['message']
            msg = {'status': 'failure', 'message': proc}
            audit_msg = 'Could not shutdown: {} is running'.format(proc)
    except Exception:
        LOG.exception('Error rebooting machine.')
    AUDIT.audit(get_audit_user_data(request), status == 200, audit_msg)
    return JsonResponse(msg, status=status)


class PCAPDeletion(PermissionView):
    """
    Delete existing PCAPs
    """

    permission = 'system_pcap_generation'

    @staticmethod
    @transaction.atomic
    def _clear_pcap_references():
        """
        Remove all references to PCAPS from the detection_detail and dns tables
        """
        detection_detail.objects.filter(pcap_file__isnull=False).update(pcap_file=None)
        LOG.info("Remove PCAP references from detection_details table")

    def delete(self, request):
        status = 500
        msg = {}
        audit_msg = ''
        try:
            now = timezone.now()
            resp = post_vsupport_api('/wipe-pcaps')
            status = resp.status_code
            if status == 200:
                self._clear_pcap_references()
                setting.objects.update_or_create(group='pcap', key='last_deleted', defaults={'value': now.isoformat()})
                local_time_deleted = now.astimezone(pytz.timezone(base_tvui_settings.TIME_ZONE))
                msg = {'status': 'success', 'timestamp': local_time_deleted.strftime('%B %d, %Y, %H:%M %p')}
                audit_msg = 'Deleting PCAPs'
            else:
                LOG.warning(resp.json())
                error_msg = 'Cognito is busy, please try to delete PCAPs later.'
                msg = {'status': 'failure', 'message': error_msg}
                audit_msg = 'Failed to wipe pcaps: {}'.format(error_msg)
        except Exception:
            LOG.exception('Error deleting PCAPs')
            msg = {'status': 'failure', 'message': 'Error deleting PCAPs'}
            audit_msg = 'Error deleting PCAPs'

        AUDIT.audit(get_audit_user_data(request), status == 200, audit_msg)
        return JsonResponse(msg, status=status)
