from celery.app.task import Task
from celery import shared_task
from celery.signals import worker_ready
from celery.exceptions import Ignore

from django_celery_results.models import TaskResult
from django.http import JsonResponse
from django.db import connection
from cachetools.func import ttl_cache
from contextlib import closing
import json
import time
import sentry_sdk
import os
import re
from datetime import datetime, timezone, timedelta
from base_tvui import lib_account
from base_tvui import lib_entra_context
from base_tvui.feature_flipper import conditions, flag_enabled, Flags
from base_tvui.lib_cloud_metrics import MetricName, Metrics
from base_tvui.account_reconciliation import periodic_account_reconciliation
from base_tvui.bin_utils import should_run_job
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils import get_external_connector_fields, get_edr_fields
from bin import (
    reevaluate_all_smart_rules,
    sql_reaper,
    run_bulk_account_reconciliation,
    cleanup_data_sources,
    cloud_failed_login_collection,
    insight_metrics_collection,
    host_and_host_sessions_sync,
    ad_metric_bulk,
    host_scoring_run_once,
    sql_stats,
    fill_pure_uid,
)
from host_scoring_v2 import host_scoring
from tvui.custom_view_classes import PermissionView
from tvui.models import detection, setting, EntityContextTypes
from pure_utils.log_utils import get_vectra_logger
from bin.cloud.azure_active_directory_auto_lockdown import AzureADAutoLockdown
from bin.account_service_scripts import update_account_t_c_scores, prune_kerberos_events


LOG = get_vectra_logger(__name__)
METRIC_COLLECTION_TASK_NAME = 'tvui.async_tasks.celery_tasks.collect_celery_metrics'


def run_task(task: Task, *args, **kwargs):
    """
    Helper method for executing backgroundable tasks in VUI.
    This will automatically determine whether or not to perform the
    task asynchronously or synchronously given the feature flag output.

    ---

    Implement a new task like the following example:

    from celery import shared_task
    @shared_task
    def new_task(self, arg1)
        perform_task(arg1)

    ---

    Now call your new task like the following:

    tvui.async_tasks.celery_tasks.run_task(new_task, arg1, ...)
    """
    async_enabled = flag_enabled(Flags.enable_async_tasks)
    priority = None
    try:
        countdown = int(kwargs.pop('countdown', 0))
        if 'priority' in kwargs.keys():
            priority = kwargs.pop('priority')
        if async_enabled:
            LOG.info(f'Scheduling task {task.name} to run asynchronously')
            return task.apply_async(args=args, kwargs=kwargs, countdown=countdown, priority=priority)
        else:
            return task(*args, **kwargs)
    except Exception as e:
        LOG.error(f'Failed to execute task {task.name} due to error: {e}')
        Metrics.increment(MetricName.CELERY_TASK_FAILURE, labels={'task_name': task.name})
        raise e


class VuiAsyncTask(Task):
    vui_log_redact_args: bool = False  # If True, prevent show "secret" value info in log.

    @ttl_cache(ttl=60)
    def _load_skip_tasks_config(self) -> list:
        """
        Loads the skip_task_names configuration from tvui_setting, parsing it into a list.
        skip_task_names should be a comma-separated string of task names to skip.

        Compares the skip_task_names list against currently registered tasks, logging those that are not found and removing them from the configuration list.

        Returns:
            list: List of valid task names to skip
        """
        from tvui.async_tasks.celery_app import app

        try:
            raw_db_value = setting.objects.get(group='async_tasks', key='skip_task_names').value
            # This regex matches any word character, including underscore, dots, commas and spaces, inside square brackets
            skip_tasks_match = re.match(r"\[([\w., ]*)\]", raw_db_value)
            if skip_tasks_match:
                skip_task_names = skip_tasks_match.group(1).split(',')
                # Remove lead and back spaces, as they may be introduced as a way of make it easier
                skip_task_names = [task.rstrip(' ').lstrip(' ') for task in skip_task_names]
            else:
                LOG.error(f'skip_task_names has no valid format, it should be "[task1,task2]", but is instead "{raw_db_value}"')
                skip_task_names = []

        except setting.DoesNotExist as e:
            LOG.error(f'Failed to get skip_task_names setting, does not exist in DB: {e}')
            skip_task_names = []
        except Exception as e:
            LOG.error(f'Failed to get skip_task_names setting due to unexpected error: {e}')
            skip_task_names = []

        return_skip_task_names = []

        # Handle case where .split(',') returns [''] for an empty list
        if skip_task_names == ['']:
            return return_skip_task_names

        # Return only the ones that are valid task names
        # We should not change in place the skip_task_names in a loop, so we generate a copy
        # This could be a list comprehension, but we want to raise a log for elements not matched
        for task_name in skip_task_names:
            if task_name in app.tasks.keys():
                return_skip_task_names.append(task_name)
            else:
                LOG.error(f'Skip task name: {task_name} not found in celery app tasks')

        return return_skip_task_names

    def before_start(self, task_id, args, kwargs):

        sentry_sdk.set_tag("external_vui", os.environ.get('EXTERNAL_VUI', 'UNKNOWN EXTERNAL VUI'))
        sentry_sdk.set_tag("internal_brain", os.environ.get('INTERNAL_BRAIN', 'UNKNOWN BRAIN'))
        sentry_sdk.set_tag("region", os.environ.get('AWS_REGION', 'UNKNOWN REGION'))

        skip_task_names = self._load_skip_tasks_config()
        if self.name in skip_task_names:
            LOG.info(f'Skipping task {self.name} due to skip_task_names setting')
            raise Ignore()
        super().before_start(task_id, args, kwargs)
        self.start_time = datetime.now()

    def on_retry(self, exc, task_id, args, kwargs, einfo):
        LOG.exception(f'Retrying task {self.name} due to error: {exc}')
        super().on_retry(exc, task_id, args, kwargs, einfo)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        if isinstance(exc, Ignore):
            # don't record any metrics for ignored tasks
            LOG.debug('Ignoring task exception')
        else:
            if self.vui_log_redact_args:
                # prevent show secret in celery_tasks log
                args = (
                    [re.sub(r'"secret":\s*".*?"', '"secret": "XXXXXXXXXXXX"', v) if v else v for i, v in enumerate(args)] if args else args
                )

            LOG.exception(f'Failed to run task {self.name}(args: {args}, kwargs: {kwargs}) due to error: {exc}')
            Metrics.increment(MetricName.CELERY_TASK_FAILURE, labels={'task_name': self.name})

        super().on_failure(exc, task_id, args, kwargs, einfo)

    def on_success(self, retval, task_id, args, kwargs):
        latency = (datetime.now() - self.start_time).total_seconds()
        Metrics.timing(MetricName.CELERY_TASK_SUCCESS, latency, labels={'task_name': self.name})
        super().on_success(retval, task_id, args, kwargs)


class AppCeleryTaskResultEndpoint(PermissionView):
    """
    Class for session based JSON endpoints /api/app/task-result/{id}
    """

    # Correct permissions should be required to queue task, generic permission for now
    permission = 'resources'

    _MAX_WAIT_TIME_S = 10

    def get(self, request, task_id):
        try:
            waited = 0
            while (result := TaskResult.objects.filter(task_id=task_id).first()) is None:
                time.sleep(2)
                waited += 1
                if waited >= self._MAX_WAIT_TIME_S:
                    break

            if result is None:
                return JsonResponse({'errors': 'Unknown task id requested.'}, status=404)

            return JsonResponse({'status': result.status, 'result': json.loads(result.result)})
        except Exception as e:
            LOG.exception(f'Unknown exception while retrieving task {task_id} result: {e}')
            return JsonResponse({'errors': 'Unknown exception occurred.'}, status=500)


class AppCeleryPollingTimeoutEndpoint(PermissionView):
    """
    Class for session based JSON endpoints /api/app/task-result/pollingTimeout
    """

    permission = 'resources'

    def get(self, request):
        try:
            timeout_seconds, _ = setting.objects.get_or_create(
                group='async_tasks', key='ui_task_result_polling_timeout_seconds', defaults={'value': 120}
            )
            timeout_ms = int(timeout_seconds.value) * 1000
        except setting.DoesNotExist:
            timeout_ms = 120 * 1000  # default 2 minutes, in milliseconds
        return JsonResponse({'pollingTimeoutMS': timeout_ms})


@shared_task
@worker_ready.connect
def refresh_external_connector_fields(**kwargs):
    """Task to be backgrounded in celery"""
    if conditions.is_cloud():
        return get_external_connector_fields()


@shared_task
@worker_ready.connect
def refresh_edr_health_fields(**kwargs):
    """Task to refresh EDR connection info"""
    if conditions.is_cloud():
        return get_edr_fields()


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def vui_metrics_collection_task():
    with closing(connection.cursor()) as cursor:
        cursor.execute("SELECT TIME_MS FROM INFORMATION_SCHEMA.PROCESSLIST;")
        rows = cursor.fetchall()

        connection_count = len(rows)
        max_duration_seconds = max([float(row[0]) for row in rows]) / 1000

        Metrics.count(MetricName.VUI_DB_CONNECTION_COUNT, connection_count)
        Metrics.timing(MetricName.VUI_DB_CONNECTION_MAX_AGE, max_duration_seconds)

        LOG.info(f'Using {connection_count} db connections. Max duration: {max_duration_seconds}s')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def reevaluate_smart_rules():
    reevaluate_all_smart_rules.main()


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def sql_reaper_task():
    if should_run_job('sql_reaper'):
        sql_reaper.main()
    else:
        LOG.info('Skipping sql_reaper celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 10})
def send_notification(notification_id):
    from base_tvui.notification_processor import ExternalNotificationProcessor
    from tvui.models import ExternalNotification

    notification_list = [ExternalNotification.objects.get(id=notification_id)]

    result = ExternalNotificationProcessor(notification_list).execute()
    if not result:
        raise Exception('Failed to send alert notifications.')
    return result


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def auto_triage_orchestrator_task():
    if should_run_job('auto_triage_orchestrator'):
        from kube_bin import auto_triage_orchestrator

        auto_triage_orchestrator.main()
    else:
        LOG.info('Skipping auto_triage_orchestrator celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def send_report_task():
    if should_run_job('send_report'):
        from bin import send_report

        send_report.main()
    else:
        LOG.info('Celery skipping send_report task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def failed_login_collection():
    if should_run_job('cloud_failed_login_collection'):
        cloud_failed_login_collection.main()
    else:
        LOG.info('Skipping cloud_failed_login_collection celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def azure_ad_auto_lockdown_task():
    if should_run_job('azure_ad_auto_lockdown'):
        LOG.info('Celery starting to run azure_ad_auto_lockdown task')
        AzureADAutoLockdown().main()
    else:
        LOG.info('Celery skipping azure_ad_auto_lockdown task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def prune_kerberos_events_task():
    if should_run_job('prune_kerberos_events'):
        LOG.info('Celery starting to run prune_kerberos_events task')
        prune_kerberos_events.main()
    else:
        LOG.info('Celery skipping prune_kerberos_events task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def score_entities(entities, type, reason=None):
    if type == detection.EntityType.host:
        LOG.info(f'Beginning rescore of {len(entities)} hosts.')
        for host in entities:
            host_scoring.score_host(host, reason)
        LOG.info(f'Successfully rescored {len(entities)} hosts.')
    elif type == detection.EntityType.account:
        LOG.info(f'Beginning rescore of {len(entities)} accounts.')
        lib_account.rescore_accounts(entities)
        LOG.info(f'Successfully rescored {len(entities)} accounts.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30}, ignore_result=True)
def insight_metrics_collection_task():
    if should_run_job('insight_metrics_collection'):
        LOG.info('Celery starting to run insight_metrics_collection task')
        insight_metrics_collection.main()
    else:
        LOG.info('Celery skipping insight_metrics_collection task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def cleanup_data_sources_task():
    if should_run_job('cleanup_data_sources'):
        LOG.info('Celery starting to run cleanup_data_sources task')
        cleanup_data_sources.main()
    else:
        LOG.info('Celery skipping cleanup_data_sources task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def aws_account_reconciliation_task():
    LOG.info('Celery starting to run aws_account_reconciliation task')
    periodic_account_reconciliation.periodic_aws_reconciliation()


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def o365_account_reconciliation_task():
    if should_run_job('account_reconciliation'):
        LOG.info('Celery starting to run account_reconciliation task')
        run_bulk_account_reconciliation.bulk_account_reconciliation()
    else:
        LOG.info("Celery skipping account_reconciliation task")


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def federated_entra_principal_account_reconciliation_task():
    if flag_enabled(Flags.entra_context_processing__periodic_federated_reconciliation):
        LOG.info('Celery starting to run federated_entra_principal_account_reconciliation task')
        lib_entra_context.periodic_reconcile_federated_entra_principal_accounts()
    else:
        LOG.info("Celery skipping federated_entra_prinipal_account_reconciliation task")


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def update_account_t_c_scores_task():
    if should_run_job('update_account_t_c_scores'):
        LOG.info('Celery starting to run update_account_t_c_scores task')
        update_account_t_c_scores.main()
    else:
        LOG.info('Celery skipping update_account_t_c_scores task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def host_and_host_sessions_sync_task():
    if should_run_job('host_and_host_sessions_sync'):
        LOG.info('Celery starting to run host_and_host_sessions_sync task')
        host_and_host_sessions_sync.main()
    else:
        LOG.info('Celery skipping host_and_host_sessions_sync task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def ad_metric_bulk_task():
    if should_run_job('ad_metric_bulk'):
        ad_metric_bulk.main()
    else:
        LOG.info('Celery skipping ad_metric_bulk task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def host_scoring_run_once_task():
    if should_run_job('host_scoring_run_once'):
        host_scoring_run_once.main()
    else:
        LOG.info('Celery skipping host_scoring_run_once task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30}, ignore_result=True)
def unbucketed_detection_detail_metrics_task():
    from kube_bin import unbucketed_detection_detail_metrics

    unbucketed_detection_detail_metrics.main()


@shared_task(ignore_result=True)
def entra_context_processing_task():
    if flag_enabled(Flags.entra_context_processing):
        from entity_receiver import entity_persister

        LOG.info('Running Entra Context Processing')
        context_types = [EntityContextTypes.ENTRA_GRAPH_V1_USER, EntityContextTypes.ENTRA_GRAPH_V1_SERVICE_PRINCIPAL]
        entity_persister.persist_entity_types(context_types)
    else:
        LOG.info("Celery skipping running Entra Context Processing, feature flag not enabled")


@shared_task(ignore_result=True)
def ldap_context_processing_task():
    if flag_enabled(Flags.ad_context_receiver):
        from entity_receiver import entity_persister

        LOG.info('Running LDAP Context Processing')
        entity_persister.persist_entity_types_v2(['ad_context'])
    else:
        LOG.info("Celery skipping running LDAP Context Processing, feature flag not enabled")


@shared_task(ignore_result=True)
def vui_entity_summary_metric_collection_task():
    from kube_bin import vui_entity_summary_metric_collection

    vui_entity_summary_metric_collection.main()


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30})
def cbi_heartbeat_task():
    if should_run_job('cbi_heartbeat'):
        from kube_bin.cbi_heartbeat_task import cbi_heartbeat

        cbi_heartbeat()
    else:
        LOG.info('Skipping cbi_heartbeat celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30})
def lockdown_monitor_task():
    if should_run_job('lockdown_monitor'):
        from kube_bin.lockdown_monitor_task import lockdown_monitor

        lockdown_monitor()
    else:
        LOG.info('Skipping lockdown_monitor celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30})
def sync_data_sources_task():
    if should_run_job('sync_data_sources'):
        from kube_bin.sync_data_sources_task import sync_data_sources

        sync_data_sources()
    else:
        LOG.info('Skipping sync_data_sources celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30})
def retry_customer_files_task():
    if should_run_job('retry_customer_files'):
        from kube_bin.retry_customer_files_task import retry_customer_files_run

        retry_customer_files_run()
    else:
        LOG.info('Skipping retry_customer_files celery task.')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30})
def entra_principal_federated_reconciliation_stats_task():
    from kube_bin import entra_principal_federated_reconciliation_stats

    entra_principal_federated_reconciliation_stats.main()


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30})
def cognito_users_sync():
    from bin.cognito_users_sync import sync_enabled_users, reactivate_cloud_users_sync, get_cognito_usernames

    LOG.info('Starting cognito users sync')
    cognito_usernames = get_cognito_usernames()
    reactivate_cloud_users_sync(cognito_usernames)
    sync_enabled_users(cognito_usernames)
    LOG.info('Finished cognito users sync')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def linked_account_triage_metrics_task():
    # A local import as django app is not defined yet when file is loaded by celery
    from kube_bin.linked_account_triage_metrics_task import count_account_references_in_smart_rules
    from kube_bin.linked_account_triage_metrics_task import count_account_references_in_account_groups

    LOG.info('Celery starting to run linked_account_triage_metrics_task task')
    try:
        count_account_references_in_smart_rules()
        count_account_references_in_account_groups()
    except Exception as err:
        LOG.exception(f'An Exception occured while executing linked_account_metrics_task')
        raise err
    LOG.info('Celery finished running linked_account_triage_metrics_task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30}, ignore_result=True)
def ldap_context_sync_task():
    from kube_bin.ldap_context_sync import sync_ldap_context

    sync_ldap_context()


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def edr_context_sync_task():
    from kube_bin import edr_context_sync

    LOG.info('Celery starting to run edr_context_sync task')
    edr_context_sync.main()
    LOG.info('Celery finished running edr_context_sync task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def sql_stats_task():
    LOG.info('Celery starting to run sql_stats task')
    sql_stats.generate_sql_table_metrics()
    LOG.info('Celery finished running sql_stats task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def fill_pure_uid_task():
    LOG.info('Celery starting to run fill_pure_uid task')
    fill_pure_uid.fill_linked_account_pure_uid()
    fill_pure_uid.fill_subaccount_pure_uid()
    LOG.info('Celery finished running fill_pure_uid task')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def host_audit_snapshot():
    from bin import host_audit_snapshot

    if flag_enabled(Flags.host_audit_snapshot):
        LOG.info('Celery starting for task host audit snapshot')
        host_audit_snapshot.create_host_audit_snapshot_with_backfill()
        host_audit_snapshot.cleanup_host_audit_snapshots()
        LOG.info('Celery finished for task host audit snapshot')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30}, ignore_result=True)
def daily_user_metrics():
    """
    Collect cloud user signin and count stats.
    """
    from bin import cloud_daily_user_metrics

    LOG.info('Starting cloud user metrics collection')
    try:
        cloud_daily_user_metrics.create_user_count_per_account_type_metrics()
    except Exception as err:
        LOG.exception(f'Failed to collect/send cloud user metrics due to error: {err}')
        raise err
    LOG.info('Ending cloud user metrics collection')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30}, ignore_result=True)
def account_group_to_linked_account_group_migration_task():
    """
    Migrate account groups to linked account groups
    """
    from bin import account_group_to_linked_account_group_migration

    LOG.info('Starting account group to linked account group migration')
    try:
        account_group_to_linked_account_group_migration.main()
    except Exception as err:
        LOG.exception(f'Failed to migrate account groups to linked account groups due to error')
        raise err
    LOG.info('Ending account group to linked account group migration')


@shared_task(autoretry_for=(Exception,), retry_kwargs={'max_retries': 1, 'countdown': 30}, ignore_result=True)
def update_host_roles_task():
    """
    Poll HAPI/CBI to update host roles
    """
    from bin import update_host_roles

    LOG.info('Starting host role update task')
    try:
        update_host_roles.main()
    except Exception as err:
        LOG.exception(f'Failed to update host roles due to error - {err}')
        raise err
    LOG.info('Ending host role update task')
