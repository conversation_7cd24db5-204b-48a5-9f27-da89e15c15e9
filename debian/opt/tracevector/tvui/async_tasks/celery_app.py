import os
import time

from celery import Celery, signals
from celery.schedules import crontab
from django.conf import settings
from kombu.serialization import register
from datetime import timedelta

from base_tvui.settings import DEPLOYMENT, CLOUD_ENV
from base_tvui.lib_cloud_metrics import <PERSON>ric<PERSON>ame, Metrics
from tvui.async_tasks.celery_serializers import celery_data_encryption, celery_data_decryption
from tvui.async_tasks.celery_logging import setup_celery_logging

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'base_tvui.settings')


class Envs:
    cloud = 'cloud'
    appliance = 'appliance'


ENV = Envs.appliance if DEPLOYMENT == 'onprem' else Envs.cloud
ELASTICACHE_URL = os.environ.get("CELERY_REDIS_HOST", None)
ON_PREM_REDIS = 'redis://localhost:6379/8'

CLOUD_REDIS = f'rediss://{ELASTICACHE_URL}/0'

app = Celery('tasks')


signals.setup_logging.connect(setup_celery_logging)
app.log.setup()

app.task_cls = 'tvui.async_tasks.celery_tasks.VuiAsyncTask'
app.config_from_object('django.conf:settings', namespace='CELERY')
app.conf.broker_url = CLOUD_REDIS if ENV == 'cloud' else ON_PREM_REDIS
# Priority is a number between 0 and 9. The default and the highest priority is 0.
# We do not want every task to be priority 0, so we set it to 3.
app.conf.task_default_priority = 3
app.conf.broker_pool_limit = int(os.environ.get('CELERY_BROKER_POOL_LIMIT', '10'))
app.conf.broker_transport_options = {
    'sep': ':',
    'queue_order_strategy': 'priority',
    'fanout_patterns': True,
    'max_connections': 10,
}
app.conf.database_engine_options = {
    'pool_size': 2,
    'max_overflow': 5,
}
if ENV == 'cloud':
    INTERNAL_BRAIN = os.environ.get('INTERNAL_BRAIN', None)
    if INTERNAL_BRAIN is None:
        raise Exception("INTERNAL_BRAIN not found.")
    app.conf.broker_transport_options = {'global_keyprefix': INTERNAL_BRAIN}
app.autodiscover_tasks()

_cloudbridge_tasks = {
    # Add cloudbridge specific schedules here
    'prune_kerberos_events': {
        'task': 'tvui.async_tasks.celery_tasks.prune_kerberos_events_task',
        'schedule': timedelta(days=1),
    },
    'host_and_host_sessions_sync': {
        'task': 'tvui.async_tasks.celery_tasks.host_and_host_sessions_sync_task',
        'schedule': timedelta(days=1),
    },
    'ad_metric_bulk': {'task': 'tvui.async_tasks.celery_tasks.ad_metric_bulk_task', 'schedule': timedelta(days=1)},
    'host_scoring_run_once': {
        'task': 'tvui.async_tasks.celery_tasks.host_scoring_run_once_task',
        'schedule': timedelta(hours=3),
    },
    'cbi_heartbeat': {
        'task': 'tvui.async_tasks.celery_tasks.cbi_heartbeat_task',
        'schedule': timedelta(minutes=1),
    },
    'lockdown_monitor_task': {
        'task': 'tvui.async_tasks.celery_tasks.lockdown_monitor_task',
        'schedule': timedelta(minutes=1),
    },
    'sync_data_sources_task': {
        'task': 'tvui.async_tasks.celery_tasks.sync_data_sources_task',
        'schedule': timedelta(minutes=5),
    },
    'retry_customer_files_task': {
        'task': 'tvui.async_tasks.celery_tasks.retry_customer_files_task',
        'schedule': timedelta(minutes=5),
    },
    'update_host_roles_task': {
        'task': 'tvui.async_tasks.celery_tasks.update_host_roles_task',
        'schedule': timedelta(days=1),
    },
    'evaluate_dynamic_host_groups_batch': {
        'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_dynamic_host_groups_batch',
        'schedule': timedelta(minutes=5),
    },
    'evaluate_all_dynamic_host_groups': {
        'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_all_dynamic_host_groups',
        'schedule': timedelta(hours=1),
    },
    'cognito_users_sync': {
        'task': 'tvui.async_tasks.celery_tasks.cognito_users_sync',
        'schedule': timedelta(hours=1),
    },
    'ldap_context_sync': {'task': 'tvui.async_tasks.celery_tasks.ldap_context_sync_task', 'schedule': timedelta(hours=6)},
    'edr_context_sync': {
        'task': 'tvui.async_tasks.celery_tasks.edr_context_sync_task',
        'schedule': timedelta(hours=6),
    },
    'host_audit_snapshot': {
        'task': 'tvui.async_tasks.celery_tasks.host_audit_snapshot',
        'schedule': timedelta(days=1),
    },
    'daily_user_metrics': {
        'task': 'tvui.async_tasks.celery_tasks.daily_user_metrics',
        'schedule': timedelta(days=1),
    },
    'sync_ad_accounts': {
        'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.sync_ad_accounts',
        'schedule': timedelta(hours=6),
    },
    'sync_ad_groups': {
        'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.sync_ad_groups',
        'schedule': timedelta(hours=6),
    },
    'sync_ad_hosts': {
        'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.sync_ad_hosts',
        'schedule': timedelta(hours=6),
    },
    'evaluate_ad_account_groups': {
        'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.evaluate_ad_account_groups_all',
        'schedule': timedelta(hours=12),
    },
    'evaluate_ad_host_groups': {
        'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.evaluate_ad_host_groups_all',
        'schedule': timedelta(hours=12),
    },
}

_global_view_tasks = {
    'global_view_refresh_client_tokens': {
        'task': 'tvui.async_tasks.global_view_tasks.global_view_refresh_client_tokens_task',
        'schedule': timedelta(minutes=5),
    },
    'global_view_fetch_all_secrets': {
        'task': 'tvui.async_tasks.global_view_tasks.global_view_fetch_all_secrets_task',
    },
    'global_view_patch_secret': {
        'task': 'tvui.async_tasks.global_view_tasks.global_view_patch_secret_task',
    },
    'global_view_delete_secret': {
        'task': 'tvui.async_tasks.global_view_tasks.global_view_delete_secret_task',
    },
}

beat_schedule_env_map = {
    Envs.appliance: {
        'reevaluate_smart_rules': {
            'task': 'tvui.async_tasks.celery_tasks.reevaluate_smart_rules',
            'schedule': crontab(minute=0, hour=3),
        },
        'sql_reaper': {'task': 'tvui.async_tasks.celery_tasks.sql_reaper_task', 'schedule': timedelta(hours=12)},
        'cleanup_data_sources': {'task': 'tvui.async_tasks.celery_tasks.cleanup_data_sources_task', 'schedule': timedelta(days=1)},
        'aws_account_reconciliation': {
            'task': 'tvui.async_tasks.celery_tasks.aws_account_reconciliation_task',
            'schedule': timedelta(hours=1),
        },
        'o365_account_reconciliation': {
            'task': 'tvui.async_tasks.celery_tasks.o365_account_reconciliation_task',
            'schedule': timedelta(hours=1),
        },
        'update_account_t_c_scores': {
            'task': 'tvui.async_tasks.celery_tasks.update_account_t_c_scores_task',
            'schedule': timedelta(minutes=30),
        },
        'send_report': {'task': 'tvui.async_tasks.celery_tasks.send_report_task', 'schedule': crontab(minute=0, hour=11)},
        'prune_kerberos_events': {
            'task': 'tvui.async_tasks.celery_tasks.prune_kerberos_events_task',
            'schedule': timedelta(days=1),
        },
        'host_and_host_sessions_sync': {
            'task': 'tvui.async_tasks.celery_tasks.host_and_host_sessions_sync_task',
            'schedule': timedelta(days=1),
        },
        'update_host_roles_task': {
            'task': 'tvui.async_tasks.celery_tasks.update_host_roles_task',
            'schedule': timedelta(days=1),
        },
        'ad_metric_bulk': {'task': 'tvui.async_tasks.celery_tasks.ad_metric_bulk_task', 'schedule': timedelta(days=1)},
        'evaluate_dynamic_host_groups_batch': {
            'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_dynamic_host_groups_batch',
            'schedule': timedelta(minutes=5),
        },
        'evaluate_all_dynamic_host_groups': {
            'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_all_dynamic_host_groups',
            'schedule': timedelta(hours=1),
        },
        'evaluate_dynamic_account_groups_batch': {
            'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_dynamic_account_groups_batch',
            'schedule': timedelta(minutes=5),
        },
        'reevaluate_dynamic_account_groups': {
            'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_dynamic_account_groups',
            'schedule': timedelta(hours=1),
        },
        'fill_pure_uid_task': {
            'task': 'tvui.async_tasks.celery_tasks.fill_pure_uid_task',
            'schedule': timedelta(hours=6),
        },
        'sync_ad_accounts': {
            'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.sync_ad_accounts',
            'schedule': timedelta(hours=6),
        },
        'sync_ad_groups': {
            'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.sync_ad_groups',
            'schedule': timedelta(hours=6),
        },
        'sync_ad_hosts': {
            'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.sync_ad_hosts',
            'schedule': timedelta(hours=6),
        },
        'evaluate_ad_account_groups': {
            'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.evaluate_ad_account_groups_all',
            'schedule': timedelta(hours=12),
        },
        'evaluate_ad_host_groups': {
            'task': 'tvui.group_collections.ad_groups.ad_groups_tasks.evaluate_ad_host_groups_all',
            'schedule': timedelta(hours=12),
        },
    },
    Envs.cloud: {
        **{
            'reevaluate_smart_rules': {
                'task': 'tvui.async_tasks.celery_tasks.reevaluate_smart_rules',
                'schedule': timedelta(days=1),
            },
            'refresh_external_connector_fields': {
                'task': 'tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
                'schedule': timedelta(minutes=10),
            },
            'refresh_edr_health_fields': {
                'task': 'tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
                'schedule': timedelta(minutes=10),
            },
            'failed_login_collection': {'task': 'tvui.async_tasks.celery_tasks.failed_login_collection', 'schedule': timedelta(hours=1)},
            'auto_triage_orchestrator': {
                'task': 'tvui.async_tasks.celery_tasks.auto_triage_orchestrator_task',
                'schedule': timedelta(days=1),
            },
            'insight_metrics_collection': {
                'task': 'tvui.async_tasks.celery_tasks.insight_metrics_collection_task',
                'schedule': timedelta(hours=6),
            },
            'sql_reaper': {'task': 'tvui.async_tasks.celery_tasks.sql_reaper_task', 'schedule': timedelta(hours=12)},
            'cleanup_data_sources': {'task': 'tvui.async_tasks.celery_tasks.cleanup_data_sources_task', 'schedule': timedelta(days=1)},
            'aws_account_reconciliation': {
                'task': 'tvui.async_tasks.celery_tasks.aws_account_reconciliation_task',
                'schedule': timedelta(hours=1),
            },
            'o365_account_reconciliation': {
                'task': 'tvui.async_tasks.celery_tasks.o365_account_reconciliation_task',
                'schedule': timedelta(hours=1),
            },
            'federated_entra_principal_account_reconciliation': {
                'task': 'tvui.async_tasks.celery_tasks.federated_entra_principal_account_reconciliation_task',
                'schedule': timedelta(hours=6),
            },
            'update_account_t_c_scores': {
                'task': 'tvui.async_tasks.celery_tasks.update_account_t_c_scores_task',
                'schedule': timedelta(minutes=30),
            },
            'azure_ad_auto_lockdown': {
                'task': 'tvui.async_tasks.celery_tasks.azure_ad_auto_lockdown_task',
                'schedule': timedelta(minutes=5),
            },
            'send_report': {'task': 'tvui.async_tasks.celery_tasks.send_report_task', 'schedule': crontab(minute=0, hour=11)},
            'vui_metrics_collection': {
                'task': 'tvui.async_tasks.celery_tasks.vui_metrics_collection_task',
                'schedule': timedelta(minutes=5),
            },
            'unbucketed_detection_detail_metrics': {
                'task': 'tvui.async_tasks.celery_tasks.unbucketed_detection_detail_metrics_task',
                'schedule': timedelta(hours=1),
            },
            'ldap_context_processing': {
                'task': 'tvui.async_tasks.celery_tasks.ldap_context_processing_task',
                'schedule': timedelta(minutes=5),
            },
            'entra_context_processing': {
                'task': 'tvui.async_tasks.celery_tasks.entra_context_processing_task',
                'schedule': timedelta(minutes=5),
            },
            'vui_entity_summary_metric_collection': {
                'task': 'tvui.async_tasks.celery_tasks.vui_entity_summary_metric_collection_task',
                'schedule': timedelta(hours=1),
            },
            'evaluate_dynamic_account_groups_batch': {
                'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_dynamic_account_groups_batch',
                'schedule': timedelta(minutes=5),
            },
            'reevaluate_dynamic_account_groups': {
                'task': 'tvui.group_collections.dynamic_groups.dynamic_group_tasks.evaluate_dynamic_account_groups',
                'schedule': timedelta(hours=1),
            },
            'entra_principal_federated_reconciliation_stats': {
                'task': 'tvui.async_tasks.celery_tasks.entra_principal_federated_reconciliation_stats_task',
                'schedule': timedelta(hours=3),
            },
            'exec_cloud_stats_push_task': {
                'task': 'tvui.async_tasks.cloud_stats_push_tasks.exec_cloud_stats_push_task',
                'schedule': timedelta(minutes=10),
            },
            # --------
            # This task is meant to temporarily get some metrics to help inform this decision. It should be removed soon after
            'linked_account_triage_metrics_task': {
                'task': 'tvui.async_tasks.celery_tasks.linked_account_triage_metrics_task',
                'schedule': timedelta(hours=12),
            },
            # --------
            'sql_stats_task': {
                'task': 'tvui.async_tasks.celery_tasks.sql_stats_task',
                'schedule': timedelta(hours=6),
            },
            'fill_pure_uid_task': {
                'task': 'tvui.async_tasks.celery_tasks.fill_pure_uid_task',
                'schedule': timedelta(hours=6),
            },
            # --------
            'account_group_to_linked_account_group_migration_task': {
                'task': 'tvui.async_tasks.celery_tasks.account_group_to_linked_account_group_migration_task',
                'schedule': timedelta(hours=3),
            },
            'nes_event_router_task': {
                'task': 'tvui.events.tasks.event_router',  # TODO: EC-2356 VLCN-859 also add to appliance section
                'schedule': timedelta(minutes=5),
            },
        },
        **(_cloudbridge_tasks if CLOUD_ENV and CLOUD_ENV.is_cloudbridge_enabled() else {}),
        **(_global_view_tasks if os.environ.get("GLOBAL_VIEW_INSTANCE_ROLE", None) == 'anchor' else {}),
    },
}

app.conf.beat_schedule = beat_schedule_env_map[ENV]

# Setup encryption/decryption for the cloud environment
if ENV == Envs.cloud:
    register(
        "celery_encryption",
        celery_data_encryption,
        celery_data_decryption,
        content_type="application/x-binary",
        content_encoding="utf-8",
    )
    app.conf.update(
        task_serializer="celery_encryption",
        accept_content=['application/x-binary'],
        result_accept_content=['application/json'],
    )


@signals.before_task_publish.connect
def before_task_publish(*_, headers: dict, **__):
    """Add publish time (executed in task producer/caller)"""
    headers['_vui_publish_time_ms'] = round(time.time() * 1000)


@signals.task_prerun.connect
def task_prerun(*_, task: Celery.Task, **__):
    """Record task queue delay (executed in worker)"""
    if queued_time_ms := getattr(task.request, '_vui_publish_time_ms', None):
        start_time_ms = round(time.time() * 1000)
        delay_ms = start_time_ms - queued_time_ms
        Metrics.gauge(
            MetricName.CELERY_TASK_QUEUE_WAIT_TIME_MS,
            value=delay_ms,
            labels={'task_name': task.name},
        )
