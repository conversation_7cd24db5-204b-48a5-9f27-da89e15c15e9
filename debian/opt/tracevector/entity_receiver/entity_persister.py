# Copyright (c) 2023 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import time
from collections import defaultdict
from contextlib import contextmanager
from datetime import timedelta
from django.db.models import F, Q
from django.utils import timezone
from enum import Enum
from typing import List, Tuple, Optional
from typing_extensions import TypeAlias

from base_tvui.lib_cloud_metrics import Metrics, MetricName
from base_tvui.feature_flipper import flag_enabled, Flags
from entity_receiver.entity_dispatch_timer import record_latencies
from entity_receiver.entity_helpers import EntityIdentifier, EntityPersister, EntityTypes, LONG_S3_PATH_ENTITY_TYPES, LongS3EntityIdentifier
from entity_receiver.entity_versions import ENTITY_PERSISTER_MAPPING
from tvui.models import EntityWorkQueue

from pure_utils.log_utils import get_vectra_logger

LOG = get_vectra_logger(__name__)

RETRY_DELAY = 30
MAX_RETRIES = 5

##Lists are tentative


class PersistenceSchedules(Enum):
    CONTINUOUS = 'CONTINUOUS'
    HOURLY = 'HOURLY'
    SIX_HOURLY = 'SIX_HOURLY'
    NEVER = 'NEVER'


CONTINUOUS_PERSIST_ENTITY_TYPES = [
    EntityTypes.HOST,
    EntityTypes.HOST_SESSIONS,
    EntityTypes.NOTIFICATION,
]

HOURLY_PERSIST_ENTITY_TYPES = [
    EntityTypes.KERBEROS_ACCOUNT,
    EntityTypes.KERBEROS_SERVICE,
    EntityTypes.KERBEROS_EVENT,
    EntityTypes.APPLIANCE_STATS,
]

SIX_HOURLY_PERSIST_ENTITY_TYPES = [
    EntityTypes.ACCOUNT_PRIVILEGE,
    EntityTypes.HOST_PRIVILEGE,
    EntityTypes.PROBABLE_OWNERS,
    EntityTypes.SERVICE_PRIVILEGE,
]

# Don't expect these entity types, or they are processed by a different pipeline
NEVER_PERSIST_ENTITY_TYPES = [
    EntityTypes.DETECTIONS,
    EntityTypes.ENTRA_GRAPH_V1_USER,  # Celery Task
    EntityTypes.ENTRA_GRAPH_V1_SERVICE_PRINCIPAL,  # Celery Task
    EntityTypes.AD_CONTEXT,
]

ENTITY_TO_FREQUENCY_MAP = {
    **{entity: PersistenceSchedules.CONTINUOUS.value for entity in CONTINUOUS_PERSIST_ENTITY_TYPES},
    **{entity: PersistenceSchedules.HOURLY.value for entity in HOURLY_PERSIST_ENTITY_TYPES},
    **{entity: PersistenceSchedules.SIX_HOURLY.value for entity in SIX_HOURLY_PERSIST_ENTITY_TYPES},
    **{entity: PersistenceSchedules.NEVER.value for entity in NEVER_PERSIST_ENTITY_TYPES},
}

EntityType: TypeAlias = str
EntityBatchUid: TypeAlias = str
EntityBatchIndex: TypeAlias = int
EntityId: TypeAlias = 'tuple[EntityBatchUid, EntityBatchIndex]'
SeenEntityCache: TypeAlias = "defaultdict[EntityType, set[EntityId]]"


def _filter_entities(
    persister: EntityPersister,
    entities: List[EntityWorkQueue],
    entity_type: EntityTypes,
    seen_entity_cache: SeenEntityCache,
) -> List[EntityWorkQueue]:
    """
    Excludes entities from work queue batch that have either:
        1) Already been persisted into their respective tables
        2) Are older than the current entities in their respective tables
        3) Have not waited long enough for their retry count

    :param persister: EntityPersister
        NamedTuple of the assigned table and publisher for the entity type

    :param entities: List[EntityWorkQueue]
        List of entities to process

    :param entity_type: EntityTypes
        What entity the entities from the work queue. This should be removed in the long-term and is only temporary, for entra entity types
        having different treatment of batch_uid

    :returns: List[EntityWorkQueue]
        List of the entities that have not been filtered out due to one of the reasons above
    """
    # Filter for entities that were last updated 30 seconds ago for each retry attempt they have made
    # So immediately for their first attempt, 30 seconds later for their second, and so on.
    now = timezone.now()
    already_processed_list = []
    filtered_entities = []
    excluded_list = []
    use_long_s3_path = entity_type in LONG_S3_PATH_ENTITY_TYPES

    eid_cache = seen_entity_cache[entity_type]
    table_check_list = []

    for entity in entities:
        entity_s3_path = entity.s3_full_path if use_long_s3_path else entity.batch_uid
        eid = (entity_s3_path, entity.batch_index)
        if eid in eid_cache:
            already_processed_list.append(entity)
        else:
            table_check_list.append(entity)

    LOG.info(f'Memory eid cache filtered out {len(already_processed_list)} of {entity_type}, {len(table_check_list)} remaining')

    t_table_search_start = time.monotonic()

    if use_long_s3_path:
        existing_entities = set(
            persister.table.objects.filter(
                Q(
                    entity_batch_uid_md5__in=[entity.batch_uid_md5 for entity in table_check_list],
                    entity_batch_index__in=[entity.batch_index for entity in table_check_list],
                )
            ).values_list('entity_batch_uid', 'entity_batch_index')
        )
    else:
        existing_entities = set(
            persister.table.objects.filter(
                Q(
                    entity_batch_uid__in=[entity.batch_uid for entity in table_check_list],
                    entity_batch_index__in=[entity.batch_index for entity in table_check_list],
                )
            ).values_list('entity_batch_uid', 'entity_batch_index')
        )

    t_table_search_dur = time.monotonic() - t_table_search_start
    LOG.info(f'Table search for already processed {entity_type} returned {len(existing_entities)} in {t_table_search_dur:.3f}s')

    table_search_positives = 0

    for entity in entities:
        entity_s3_path = entity.s3_full_path if use_long_s3_path else entity.batch_uid
        eid = (entity_s3_path, entity.batch_index)
        if eid in existing_entities:
            already_processed_list.append(entity)
            eid_cache.add(eid)
            table_search_positives += 1
            continue

        if entity.updated_date + timedelta(seconds=RETRY_DELAY * entity.retry_count) < now:
            filtered_entities.append(entity)
            eid_cache.add(eid)  # note: it's not already processed yet, but we don't want to attempt it again during this persister run
        else:
            excluded_list.append(eid)

    overselection = len(existing_entities) - table_search_positives
    LOG.info(f'Table search had true hits {table_search_positives} out of {len(existing_entities)} (over-selected by {overselection})')

    if excluded_list:
        LOG.info(f'Excluding entity_ids: {excluded_list} from batch due to retry delay')

    if already_processed_list:
        with log_duration(f'Closing out {len(already_processed_list)} entities due to already being processed'):
            EntityWorkQueue.objects.filter(id__in=[entity.id for entity in already_processed_list]).update(is_processed=True)

    return filtered_entities


def _split_entity_data(entities: List[EntityWorkQueue], entity_type: EntityTypes) -> Tuple[List[dict], List[EntityIdentifier]]:
    """
    Splits the entity queryset into two lists.
    One of the data for each entity and one of identifier fields.

    :param entities: List[EntityWorkQueue]
        List of entities to process

    :returns: Tuple[List[dict], List[EntityIdentifier]
        Tuple of the entity dataset and indentifiers for the list
    """
    entity_dataset = []
    entity_identifiers = []
    use_long_s3_path = entity_type in LONG_S3_PATH_ENTITY_TYPES

    for entry in entities:
        entity_dataset.append(entry.data)
        if use_long_s3_path:
            entity_identifiers.append(
                LongS3EntityIdentifier(
                    batch_uid=entry.batch_uid,
                    batch_index=entry.batch_index,
                    forwarder_date=entry.forwarder_date,
                    batch_uid_md5=entry.batch_uid_md5,
                    s3_full_path=entry.s3_full_path,
                )
            )
        else:
            entity_identifiers.append(
                EntityIdentifier(batch_uid=entry.batch_uid, batch_index=entry.batch_index, forwarder_date=entry.forwarder_date)
            )
    return entity_dataset, entity_identifiers


def persist_entity_types(entity_types: List[str] = None, max_batch_size: int = 5000, time_limit: Optional[float] = None):
    """
    Publish queued up entities of the given types

    :param entity_types: List[str]
        List of entity types to process

    :param max_batch_size: int
        Max number of entities to process in a batch

    :param time_limit: float | None
        Amount of time (in seconds) after which remaining batches will not be processed.
        Only used in persister v2.
    """

    if flag_enabled(Flags.entity_persister_v2):
        return persist_entity_types_v2(entity_types, max_batch_size, time_limit)

    LOG.info(f'PersisterV1: persisting {entity_types=}')

    if not entity_types:
        LOG.info('No entity types provided')
        return

    if not isinstance(entity_types, list):
        LOG.error(f'Invalid list of entities types `{entity_types}`')
        return

    if not isinstance(max_batch_size, int):
        LOG.error(f'Invalid max_batch_size `{max_batch_size}`')
        return

    # won't actually make any difference here, but this lets us keep the _filter_entities API simpler
    seen_entity_cache: SeenEntityCache = defaultdict(set)

    for entity_type in entity_types:
        t_chunk_start = time.monotonic()

        if entity_type not in ENTITY_PERSISTER_MAPPING:
            LOG.error(f'Invalid entity type `{entity_type}`')
            continue

        persister = ENTITY_PERSISTER_MAPPING[entity_type]
        # Grab ids of the desired work queue items then filter for the actual objects once ids are sliced by max amount
        t_q_start = time.monotonic()
        entities = list(
            EntityWorkQueue.objects.filter(entity_type=entity_type, is_processed=False, is_errored=False).order_by('updated_date')[
                :max_batch_size
            ]
        )
        t_q_dur = time.monotonic() - t_q_start
        LOG.info(f'Retrieved batch of {entity_type} ({len(entities)} / {max_batch_size}) in {t_q_dur:.3f}s')

        filtered_entities = _filter_entities(persister, entities, entity_type, seen_entity_cache)
        entity_dataset, entity_identifiers = _split_entity_data(filtered_entities, entity_type)
        LOG.info(f'Persisting {len(entity_dataset)} entities of type {entity_type}')
        publisher_results = persister.publisher(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)

        # if we are in "choked" scenario, e.g. we started with 5000 but filtered down to very few or 0 here
        # re-iterate the loop to try and pick up more this round?
        if len(publisher_results) != len(entity_identifiers):
            supplied_identifiers = {(identifier.batch_uid, identifier.batch_index) for identifier in entity_identifiers}
            published_identifiers = {(result.batch_uid, result.batch_index) for result in publisher_results}
            excluded_entities = supplied_identifiers - published_identifiers
            LOG.error(
                f'Results from publishing `{entity_type}` do not match initial query length. Missing results for (`batch_uid`, `batch_index`) {excluded_entities}'
            )

        ids_is_processed = []
        ids_increment_retry = []
        ids_is_errored = []
        for result, entry in zip(publisher_results, filtered_entities):
            if (result.batch_uid, result.batch_index) != (entry.batch_uid, entry.batch_index):
                LOG.error(
                    f'Ordering mismatch when processing PublisherResult: {(result.batch_uid, result.batch_index)} and EntityWorkQueue: {(entry.batch_uid, entry.batch_index)}'
                )
            else:
                # Successful processing
                if result.success:
                    Metrics.increment(name=MetricName.ENTITY_PERSISTER_SUCCESS, labels={'entity_type': entity_type})
                    ids_is_processed.append(entry.id)
                # Unsuccessful processing but will attempt again
                elif result.retry:
                    Metrics.increment(name=MetricName.ENTITY_PERSISTER_RETRY, labels={'entity_type': entity_type})
                    ids_increment_retry.append(entry.id)
                    # If we will hit the max number of retries with this entry, set is_error to True
                    if entry.retry_count == MAX_RETRIES - 1:
                        ids_is_errored.append(entry.id)
                # Unsuccessful processing and will not attempt again
                else:
                    Metrics.increment(name=MetricName.ENTITY_PERSISTER_FAILURE, labels={'entity_type': entity_type})
                    ids_is_errored.append(entry.id)
        EntityWorkQueue.objects.filter(id__in=ids_is_processed).update(is_processed=True)
        EntityWorkQueue.objects.filter(id__in=ids_increment_retry).update(retry_count=F('retry_count') + 1)
        EntityWorkQueue.objects.filter(id__in=ids_is_errored).update(is_errored=True)

        processed_entities = EntityWorkQueue.objects.filter(id__in=ids_is_processed)
        for entity in processed_entities:
            try:
                record_latencies(entity, ENTITY_TO_FREQUENCY_MAP.get(entity_type))
            except Exception as e:
                LOG.error(f'Failed to record latencies inside entity_persister. Entity: {str(entity)}. Exception: {str(e)}')

        t_chunk_dur = time.monotonic() - t_chunk_start
        Metrics.timing(MetricName.ENTITY_PERSISTER_CHUNKTIME, t_chunk_dur, {'entity_type': entity_type})


def persist_entity_types_v2(entity_types: List[EntityType], batch_size: int = 5000, time_limit: Optional[float] = None):
    """
    Publish queued up entities of the given types.
    If given a `time_limit`, will process in batches of `batch_size`, in a round-robin order by entity_type,
    until finished or the time limit is exceeded.

    :param entity_types: List[str]
        List of entity types to process

    :param batch_size: int
        Max number of entities to process per batch

    :param time_limit: float | None
        Amount of time (in seconds) after which remaining batches will not be processed.
    """

    entity_types = [et for et in entity_types if et in ENTITY_PERSISTER_MAPPING]
    if not entity_types:
        LOG.warning('No valid entity_types provided.')
        return

    work_remaining_by_type = {entity_type: True for entity_type in entity_types}  # True, so we check all entity_types at least once
    last_id_processed_by_type = {entity_type: 0 for entity_type in entity_types}

    LOG.info(f'PersisterV2: persisting {entity_types=}')

    t_start = time.monotonic()

    seen_entity_cache: SeenEntityCache = defaultdict(set)

    while any(work_remaining_by_type.values()):

        LOG.info('Starting a turn of the persister loop')
        LOG.info(f'{work_remaining_by_type=}')
        LOG.info(f'{last_id_processed_by_type=}')

        for entity_type in entity_types:
            t_chunk_start = time.monotonic()

            if not work_remaining_by_type[entity_type]:
                LOG.info(f'No remaining work for entity_type {entity_type}')
                continue

            LOG.info(f'Fetching batch for entity_type {entity_type}')

            persister = ENTITY_PERSISTER_MAPPING[entity_type]

            t_q_start = time.monotonic()

            entities = list(
                EntityWorkQueue.objects.filter(
                    entity_type=entity_type,
                    is_processed=False,
                    is_errored=False,
                    id__gt=last_id_processed_by_type[entity_type],
                ).order_by('id')[:batch_size]
                # order by id ~= order by created_date, but more performant
            )

            t_q_dur = time.monotonic() - t_q_start
            LOG.info(f'Retrieved batch of {entity_type} ({len(entities)} / {batch_size}) in {t_q_dur:.3f}s')

            if len(entities) == 0:
                LOG.info(f'Found no entries to process for entity_type {entity_type}')
                work_remaining_by_type[entity_type] = False
                continue

            # if we were returned the full batch_size, we'll need to check for more on the next turn
            work_remaining_by_type[entity_type] = len(entities) == batch_size

            last_id_processed_by_type[entity_type] = entities[-1].id

            with log_duration(f'Filtering entities {entity_type}'):
                filtered_entities = _filter_entities(persister, entities, entity_type, seen_entity_cache)

            entity_dataset, entity_identifiers = _split_entity_data(filtered_entities, entity_type)

            with log_duration(f'Persisting {len(entity_dataset)} entities of type {entity_type}'):
                publisher_results = persister.publisher(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)

            if len(publisher_results) != len(entity_identifiers):
                supplied_identifiers = {(identifier.batch_uid, identifier.batch_index) for identifier in entity_identifiers}
                published_identifiers = {(result.batch_uid, result.batch_index) for result in publisher_results}
                excluded_entities = supplied_identifiers - published_identifiers
                LOG.error(
                    f'Results from publishing `{entity_type}` do not match initial query length. Missing results for (`batch_uid`, `batch_index`) {excluded_entities}'
                )

            ids_is_processed = []
            ids_increment_retry = []
            ids_is_errored = []

            for result, entry in zip(publisher_results, filtered_entities):
                if (result.batch_uid, result.batch_index) != (entry.batch_uid, entry.batch_index):
                    LOG.error(
                        f'Ordering mismatch when processing PublisherResult: {(result.batch_uid, result.batch_index)} and EntityWorkQueue: {(entry.batch_uid, entry.batch_index)}'
                    )
                    continue

                # Successful processing
                if result.success:
                    Metrics.increment(name=MetricName.ENTITY_PERSISTER_SUCCESS, labels={'entity_type': entity_type})
                    ids_is_processed.append(entry.id)
                # Unsuccessful processing but will attempt again
                elif result.retry:
                    Metrics.increment(name=MetricName.ENTITY_PERSISTER_RETRY, labels={'entity_type': entity_type})
                    ids_increment_retry.append(entry.id)
                    # If we will hit the max number of retries with this entry, set is_error to True
                    if entry.retry_count == MAX_RETRIES - 1:
                        ids_is_errored.append(entry.id)
                # Unsuccessful processing and will not attempt again
                else:
                    Metrics.increment(name=MetricName.ENTITY_PERSISTER_FAILURE, labels={'entity_type': entity_type})
                    ids_is_errored.append(entry.id)

            with log_duration(f'Marking {len(ids_is_processed)} {entity_type} as processed'):
                EntityWorkQueue.objects.filter(id__in=ids_is_processed).update(is_processed=True)

            with log_duration(f'Marking {len(ids_increment_retry)} {entity_type} as retry'):
                EntityWorkQueue.objects.filter(id__in=ids_increment_retry).update(retry_count=F('retry_count') + 1)

            with log_duration(f'Marking {len(ids_is_errored)} {entity_type} as errored'):
                EntityWorkQueue.objects.filter(id__in=ids_is_errored).update(is_errored=True)

            with log_duration(f'Reporting latencies of {len(ids_is_processed)} entities'):
                processed_entities = EntityWorkQueue.objects.filter(id__in=ids_is_processed)
                for entity in processed_entities:
                    try:
                        record_latencies(entity, ENTITY_TO_FREQUENCY_MAP.get(entity_type))
                    except Exception:
                        LOG.exception(f'Failed to record latencies inside entity_persister. Entity: {str(entity)}.')

            t_chunk_dur = time.monotonic() - t_chunk_start
            Metrics.timing(MetricName.ENTITY_PERSISTER_CHUNKTIME, t_chunk_dur, {'entity_type': entity_type})

        time_elapsed = time.monotonic() - t_start
        if time_limit is None or time_elapsed >= time_limit:
            if time_limit:
                LOG.info(f"Timed out processing entities: {time_elapsed:.3f}s > {time_limit}s")
            break
    else:
        LOG.info('Finished processing all entities without timeout.')


@contextmanager
def log_duration(message: str):
    LOG.info(f'START: {message}')
    t_start = time.monotonic()
    try:
        yield
    finally:
        duration = time.monotonic() - t_start
        LOG.info(f'END: {message} took {duration:.3f}s')
