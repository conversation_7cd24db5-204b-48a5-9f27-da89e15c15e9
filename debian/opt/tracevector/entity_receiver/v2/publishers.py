# Copyright (c) 2023 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
import json
from django.db import DatabaseError
from typing import List

import couch_doc_handlers
from base_tvui import lib_account, lib_kerberos_event, lib_privilege
from base_tvui.lib_entra_context import create_entity_context_user_principal, create_entity_context_service_principal
from base_tvui.lib_notifications import LibExternalNotification
from base_tvui.lib_service import create_or_update_services
from base_tvui.lib_probable_owner import update_probable_owners
from base_tvui.notification_render import SystemNotification
from base_tvui.cb_appliance_stats import handle_appliance_stats
from base_tvui.lib_ldap_context import process_ldap_context
from entity_receiver.entity_helpers import EntityIdentifier, PublisherResult
from tvui import models
from tvui.internal_views import queue_notification_check
from tvui.events import xevent
from tvui.events.event_type_handlers import system_health


from pure_utils import log_utils

LOG = log_utils.get_vectra_logger(__name__)


def _inject_identifiers(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]):
    """
    Inject the entity identifiers into the actual entity data to make consumption by the handler methods easier

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.
    """
    for entity, identifier in zip(entity_dataset, entity_identifiers):
        entity['entity_batch_uid'] = identifier.batch_uid
        entity['entity_batch_index'] = identifier.batch_index


def account_privilege_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for account privilege scores

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    success = False
    accounts_to_update, privilege_history_updates, invalid_entities = lib_privilege.get_account_privilege_updates(
        entity_dataset=entity_dataset, entity_identifiers=entity_identifiers
    )

    try:
        models.Account.objects.bulk_update(accounts_to_update, fields=['priv_level', 'priv_level_date', 'priv_score', 'priv_score_date'])
        lib_privilege.insert_priv_history_table(privilege_history_updates, models.PrivHistory.ACCOUNT)
        success = True
    except Exception:
        LOG.exception('Encountered error updating Account Privilege')

    # Parse results into list that follows the same ordering as input
    return [
        PublisherResult(
            success=not (identifier in invalid_entities or not success),
            retry=False,
            batch_uid=identifier.batch_uid,
            batch_index=identifier.batch_index,
        )
        for identifier in entity_identifiers
    ]


def appliance_stats_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for appliance stats

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        stats_handle_results = handle_appliance_stats(stats=entity_dataset, entity_identifiers=entity_identifiers)
        return [
            PublisherResult(
                success=result.get('success', False), retry=False, batch_uid=identifier.batch_uid, batch_index=identifier.batch_index
            )
            for result, identifier in zip(stats_handle_results, entity_identifiers)
        ]
    except Exception:
        LOG.exception('Unable to handle appliance stats')
        return [
            PublisherResult(success=False, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]


def host_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for hosts

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    failed_entities = set()
    entities_to_retry = set()
    _inject_identifiers(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)

    handler = couch_doc_handlers.SQLHostHandler()

    for entity, identifier in zip(entity_dataset, entity_identifiers):
        try:
            success = handler.handle(entity)
            if not success:
                entities_to_retry.add(identifier)
        except DatabaseError:
            LOG.exception(f"Could not write couch host doc due to database error {entity}")
            entities_to_retry.add(identifier)
        except Exception:
            LOG.exception(f"Could not write couch host doc due to unknown error {entity}")
            failed_entities.add(identifier)

    return [
        PublisherResult(
            success=not (identifier in failed_entities or identifier in entities_to_retry),
            retry=identifier in entities_to_retry,
            batch_uid=identifier.batch_uid,
            batch_index=identifier.batch_index,
        )
        for identifier in entity_identifiers
    ]


def host_privilege_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for host privilege scores

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    success = False
    hosts_to_update, privilege_history_updates, invalid_entities = lib_privilege.get_host_privilege_updates(
        entity_dataset=entity_dataset, entity_identifiers=entity_identifiers
    )

    try:
        # TODO Since there are only 10 possible values here for level and the date is always the same, could we do this as filters to improve speed?
        models.host.objects.bulk_update(hosts_to_update, fields=['priv_level', 'priv_level_date'])
        lib_privilege.insert_priv_history_table(privilege_history_updates, models.PrivHistory.HOST)
        success = True
    except Exception:
        LOG.exception('Encountered error updating Host Privilege')

    # Parse results into list that follows the same ordering as input
    return [
        PublisherResult(
            success=not (identifier in invalid_entities or not success),
            retry=False,
            batch_uid=identifier.batch_uid,
            batch_index=identifier.batch_index,
        )
        for identifier in entity_identifiers
    ]


def host_session_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for host sessions

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """

    _inject_identifiers(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)
    hs_handler = couch_doc_handlers.SQLHostSessionHandler()
    try:
        valid_entities, invalid_idx = hs_handler.get_valid_docs(entity_dataset)
        results = hs_handler.handle_batch_host_sessions(valid_entities)

        for err_idx in invalid_idx:
            results.insert(err_idx, True)

        return [
            PublisherResult(
                success=bool(result), retry=not bool(result), batch_uid=identifier.batch_uid, batch_index=identifier.batch_index
            )
            for result, identifier in zip(results, entity_identifiers)
        ]
    except DatabaseError:
        LOG.exception(f'Could not write host session couchdoc due to database error')
        return [
            PublisherResult(success=False, retry=True, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]
    except Exception:
        LOG.exception(f'Could not write host session couch doc due to unknown error')
        return [
            PublisherResult(success=False, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]


def kerberos_account_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for kerberos accounts

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        _inject_identifiers(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)
        results = lib_account.create_or_update_accounts(entity_dataset)
        return [
            PublisherResult(
                success=entity['uid'] in results, retry=False, batch_uid=identifier.batch_uid, batch_index=identifier.batch_index
            )
            for identifier, entity in zip(entity_identifiers, entity_dataset)
        ]
    except DatabaseError:
        LOG.exception(f'Could not write kerberos accounts due to database error {entity_dataset}')
        return [
            PublisherResult(success=False, retry=True, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]
    except Exception:
        LOG.exception(f'Could not write kerberos accounts due to unknown error {entity_dataset}')
        return [
            PublisherResult(success=False, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]


def kerberos_event_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for kerberos events

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        _inject_identifiers(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)
        invalid_idx, retryable_idx = lib_kerberos_event.create_or_update_events(entity_dataset)
        return [
            PublisherResult(
                success=not (idx in invalid_idx or idx in retryable_idx),
                retry=idx in retryable_idx,
                batch_uid=identifier.batch_uid,
                batch_index=identifier.batch_index,
            )
            for idx, identifier in enumerate(entity_identifiers)
        ]
    except DatabaseError:
        LOG.exception(f'Could not write kerberos events due to database error {entity_dataset}')
        return [
            PublisherResult(success=False, retry=True, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]
    except Exception:
        LOG.exception(f'Could not write kerberos events due to unknown error {entity_dataset}')
        return [
            PublisherResult(success=False, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]


def kerberos_service_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for kerberos services

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        _inject_identifiers(entity_dataset=entity_dataset, entity_identifiers=entity_identifiers)
        results = create_or_update_services(entity_dataset)
        return [
            PublisherResult(
                success=entity['uid'] in results, retry=False, batch_uid=identifier.batch_uid, batch_index=identifier.batch_index
            )
            for identifier, entity in zip(entity_identifiers, entity_dataset)
        ]
    except DatabaseError:
        LOG.exception(f'Could not write kerberos services due to database error {entity_dataset}')
        return [
            PublisherResult(success=False, retry=True, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]
    except Exception:
        LOG.exception(f'Could not write kerberos services due to unknown error {entity_dataset}')
        return [
            PublisherResult(success=False, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]


def notification_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for notifications

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    failed_entities = set()
    entities_to_retry = set()

    valid_message_keys = SystemNotification().system_messages.keys()
    sensor_mapping = {sensor.serial_number: sensor for sensor in models.NetworkSensor.objects.all()}
    for identifier, entity in zip(entity_identifiers, entity_dataset):
        message_key = entity.get("message_key")
        message_params = entity.get("message_params")
        serial_number = entity.get("serial_number")

        notification_type = 'system'
        if message_key == 'system_health':
            notification_type = 'system_health'
        elif message_key not in valid_message_keys:
            LOG.error('Unable to create SystemNotification | invalid message_key')
            failed_entities.add(identifier)
            continue

        if serial_number:
            # Check to see if the sensor is associated with this headend before raising a notification
            if serial_number in sensor_mapping:
                sensor = sensor_mapping[serial_number]
                LOG.debug('Sensor %s (%s) found, attempting to send notification', sensor.alias, serial_number)
            else:
                # Assume that this sensor is associated with a different headend and don't send a notification
                LOG.warning('Sensor %s not found, no notification is being sent', serial_number)
                failed_entities.add(identifier)
                continue

        try:
            message_params_json = json.loads(message_params)
            if notification_type == system_health.EVENT_TYPE:
                output_data = message_params_json.get('check', {}).get('output', {})
                xevent.emit(
                    system_health.SystemHealthEvent(
                        data={
                            'title': str(output_data.get('title', '')),
                            'message': str(output_data.get('message', '')),
                            'error': str(output_data.get('error', '')),
                            'retcode': int(output_data.get('retcode', '0')),
                        }
                    )
                )
            if not queue_notification_check(message_params_json):
                LOG.exception(
                    'Could not queue notification %s %s due to notification check failed - %s',
                    notification_type,
                    message_key,
                    message_params,
                )
                failed_entities.add(identifier)
                continue
            else:
                response = LibExternalNotification(
                    notification_type, {"message_key": message_key, "message_parameters": message_params_json}, identifier=identifier
                ).create()

                LOG.info('Notification %s has been queued - %s', notification_type, message_key)
                if not bool(response):
                    failed_entities.add(identifier)
        except DatabaseError:
            LOG.exception('Could not queue notification due to database error')
            entities_to_retry.add(identifier)
        except Exception:
            LOG.exception(f'Could not queue notification due to unexpected error {entity}')
            failed_entities.add(identifier)

    # Parse results into list that follows the same ordering as input
    return [
        PublisherResult(
            success=not (identifier in failed_entities or identifier in entities_to_retry),
            retry=identifier in entities_to_retry,
            batch_uid=identifier.batch_uid,
            batch_index=identifier.batch_index,
        )
        for identifier in entity_identifiers
    ]


def probable_owners_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for probably owners

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        # Probable owners sends a large mapping instead of smaller sets of entities but we still need to treat as a batch, even if it will be rare we have more than 1.
        # This should be reworked at some point to fit better as batches of single entities instead of large dumps of data in a single entity.
        probable_owners_mapping = {}
        probable_owners_identifier_mapping = {}
        for entry, identifier in zip(entity_dataset, entity_identifiers):
            for host_luid, account_uids in entry.items():
                # Convert uids to a set
                account_uid_set = set(account_uids)
                if host_luid not in probable_owners_mapping:
                    probable_owners_mapping[host_luid] = account_uid_set
                    probable_owners_identifier_mapping[host_luid] = identifier
                else:
                    probable_owners_mapping[host_luid].update(account_uid_set)
                    # Only update the identifier if it is newer than the current one
                    if probable_owners_identifier_mapping[host_luid].batch_uid < identifier.batch_uid:
                        probable_owners_identifier_mapping[host_luid] = identifier
        update_probable_owners(probable_owners_mapping, probable_owners_identifier_mapping)

        return [
            PublisherResult(success=True, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]
    except Exception:
        LOG.exception(f'Failed to publisher probable owners {entity_dataset}')
        return [
            PublisherResult(success=False, retry=False, batch_uid=batch_uid, batch_index=batch_index)
            for batch_uid, batch_index, _ in entity_identifiers
        ]


def service_privilege_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for service privilege scores

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    success = False
    services_to_update, privilege_history_updates, invalid_entities = lib_privilege.get_service_privilege_updates(
        entity_dataset=entity_dataset, entity_identifiers=entity_identifiers
    )

    try:
        # TODO Since there are only 10 possible values here for level and the date is always the same, could we do this as filters to improve speed?
        models.Service.objects.bulk_update(services_to_update, fields=['priv_level', 'priv_level_date'])
        lib_privilege.insert_priv_history_table(privilege_history_updates, models.PrivHistory.SERVICE)
        success = True
    except Exception:
        LOG.exception('Encountered error updating Service Privilege')

    # Parse results into list that follows the same ordering as input
    return [
        PublisherResult(
            success=not (identifier in invalid_entities or not success),
            retry=False,
            batch_uid=identifier.batch_uid,
            batch_index=identifier.batch_index,
        )
        for identifier in entity_identifiers
    ]


def entra_graph_v1_users_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for Entra graph v1 user principal context

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """

    try:
        publisher_results = create_entity_context_user_principal(entity_dataset, entity_identifiers)
        return [PublisherResult(**pr) for pr in publisher_results]
    except Exception:
        LOG.exception(f'Failed to publish user entity context')
        return [PublisherResult(success=False, retry=True, batch_uid=ei.batch_uid, batch_index=ei.batch_index) for ei in entity_identifiers]


def entra_graph_v1_service_principals_publisher(
    entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]
) -> List[PublisherResult]:
    """
    Publisher for Entra graph v1 service principal context

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        publisher_results = create_entity_context_service_principal(entity_dataset, entity_identifiers)
        return [PublisherResult(**pr) for pr in publisher_results]
    except Exception:
        LOG.exception(f'Failed to publish service principal entity context')
        return [PublisherResult(success=False, retry=True, batch_uid=ei.batch_uid, batch_index=ei.batch_index) for ei in entity_identifiers]


def ldap_context_publisher(entity_dataset: List[dict], entity_identifiers: List[EntityIdentifier]) -> List[PublisherResult]:
    """
    Publisher for LDAP Context service principal context

    :param entity_dataset: List[dict]
        List of entity data in the batch. Follows same ordering as the entity_identifier list.
    :param entity_identifiers: List[EntityIdentifier]
        List of identification data in the batch. Follows same ordering as the entity_dataset list.

    :returns: List[PublisherResult]
        List of results from publishing the entities. Follows same ordering as entity_identifier and entity_dataset lists.
    """
    try:
        publisher_results = process_ldap_context(entity_dataset, entity_identifiers)
        return [PublisherResult(**pr) for pr in publisher_results]
    except Exception:
        LOG.exception(f'Failed to publish ldap context')
        return [PublisherResult(success=False, retry=True, batch_uid=ei.batch_uid, batch_index=ei.batch_index) for ei in entity_identifiers]
