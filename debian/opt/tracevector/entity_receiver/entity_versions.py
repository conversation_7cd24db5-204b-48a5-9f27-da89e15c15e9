# Copyright (c) 2023 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
"""
Entity versions to map entities to publisher
"""
import time

from base_tvui.feature_flipper import conditions
from entity_receiver.entity_helpers import EntityPersister, EntityTypes, ErrorMsgs, HandleDocResult
from entity_receiver.v1 import publishers as publishers_v1
from entity_receiver.v1_1 import publishers as publishers_v1_1
from entity_receiver.v2 import publishers as publishers_v2
from tvui import models

from pure_utils.log_utils import get_vectra_logger

LOG = get_vectra_logger(__name__)
CB_TIMEOUT_SECONDS = 25
APPLIANCE_TIMEOUT_SECONDS = 300

# Entities Version 1
ENTITY_HANDLERS_V1 = {
    EntityTypes.HOST: publishers_v1.host_publisher,
    EntityTypes.HOST_SESSIONS: publishers_v1.host_session_publisher,
    EntityTypes.KERBEROS_ACCOUNT: publishers_v1.kerberos_account_publisher,
    EntityTypes.KERBEROS_EVENT: publishers_v1.kerberos_event_publisher,
    EntityTypes.KERBEROS_SERVICE: publishers_v1.kerberos_service_publisher,
    EntityTypes.DETECTIONS: publishers_v1.detection_publisher,
    EntityTypes.ACCOUNT_PRIVILEGE: publishers_v1.account_privilege_publisher,
    EntityTypes.HOST_PRIVILEGE: publishers_v1.host_privilege_publisher,
    EntityTypes.SERVICE_PRIVILEGE: publishers_v1.service_privilege_publisher,
    EntityTypes.NOTIFICATION: publishers_v1.notification_publisher,
}


def create_v1_1_to_v1_shim(v1_handler):
    def v1_1_to_v1_shim(entity_type: str, forwarder_id: int, entities: list, send_metrics=lambda x: None) -> list:
        """
        Shim to convert v1.1 calling format to v1 handler format.
        A list of entities is passed in, and the shim will call the v1 handler for each entity, aggregating the results.
        """
        processed_entries = 0
        results = []
        start_time = time.time()
        timeout = CB_TIMEOUT_SECONDS if conditions.is_cloud() else APPLIANCE_TIMEOUT_SECONDS

        for entity in entities:
            results.append(v1_handler(forwarder_id, entity, send_metrics))
            processed_entries += 1
            elapsed_time = time.time() - start_time

            if elapsed_time >= timeout and processed_entries < len(entities):
                LOG.warning(f'V1 shim timed out after processing {processed_entries} out of {len(entities)} entities of type {entity_type}')
                results.extend(
                    HandleDocResult(False, retry=True, message=ErrorMsgs.TIMEOUT) for _ in range(len(entities) - processed_entries)
                )
                break

        return results

    return v1_1_to_v1_shim


# Entities Version 1.1
ENTITY_HANDLERS_V1_1 = {k: create_v1_1_to_v1_shim(v1_handler) for k, v1_handler in ENTITY_HANDLERS_V1.items()}
ENTITY_HANDLERS_V1_1[EntityTypes.HOST_SESSIONS] = publishers_v1_1.host_session_batch_publisher
ENTITY_HANDLERS_V1_1[EntityTypes.PROBABLE_OWNERS] = publishers_v1_1.probable_owners_publisher
ENTITY_HANDLERS_V1_1[EntityTypes.APPLIANCE_STATS] = publishers_v1_1.cb_appliance_stats_publisher

# Entities Version 2.0
# Not backwards compatible with v1.1 or v.1
ENTITY_PERSISTER_MAPPING = {
    EntityTypes.ACCOUNT_PRIVILEGE: EntityPersister(table=models.PrivHistory, publisher=publishers_v2.account_privilege_publisher),
    EntityTypes.APPLIANCE_STATS: EntityPersister(table=models.Statistic, publisher=publishers_v2.appliance_stats_publisher),
    EntityTypes.HOST: EntityPersister(table=models.host, publisher=publishers_v2.host_publisher),
    EntityTypes.HOST_PRIVILEGE: EntityPersister(table=models.PrivHistory, publisher=publishers_v2.host_privilege_publisher),
    EntityTypes.HOST_SESSIONS: EntityPersister(table=models.host_session, publisher=publishers_v2.host_session_publisher),
    EntityTypes.KERBEROS_ACCOUNT: EntityPersister(table=models.Account, publisher=publishers_v2.kerberos_account_publisher),
    EntityTypes.KERBEROS_EVENT: EntityPersister(table=models.KerberosEvent, publisher=publishers_v2.kerberos_event_publisher),
    EntityTypes.KERBEROS_SERVICE: EntityPersister(table=models.Service, publisher=publishers_v2.kerberos_service_publisher),
    EntityTypes.NOTIFICATION: EntityPersister(table=models.ExternalNotification, publisher=publishers_v2.notification_publisher),
    EntityTypes.PROBABLE_OWNERS: EntityPersister(table=models.HostProbableOwner, publisher=publishers_v2.probable_owners_publisher),
    EntityTypes.SERVICE_PRIVILEGE: EntityPersister(table=models.PrivHistory, publisher=publishers_v2.service_privilege_publisher),
    EntityTypes.ENTRA_GRAPH_V1_USER: EntityPersister(table=models.EntityContext, publisher=publishers_v2.entra_graph_v1_users_publisher),
    EntityTypes.ENTRA_GRAPH_V1_SERVICE_PRINCIPAL: EntityPersister(
        table=models.EntityContext, publisher=publishers_v2.entra_graph_v1_service_principals_publisher
    ),
    EntityTypes.AD_CONTEXT: EntityPersister(table=models.LdapContext, publisher=publishers_v2.ldap_context_publisher),
}
