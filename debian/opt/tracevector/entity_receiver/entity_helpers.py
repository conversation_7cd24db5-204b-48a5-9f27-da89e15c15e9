from dataclasses import dataclass
from datetime import datetime
import hashlib
from django.db.models import Model
from typing import Callable, NamedTuple

from base_tvui.lib_cloud_metrics import MetricName


class EntityTypes:
    # From dl-network-entities
    ACCOUNT_PRIVILEGE = 'account_privilege'
    APPLIANCE_STATS = 'appliance_stats'
    DETECTIONS = 'detections'
    HOST = 'host'
    HOST_PRIVILEGE = 'host_privilege'
    HOST_SESSIONS = 'host_sessions'
    KERBEROS_ACCOUNT = 'kerberos_account'
    KERBEROS_EVENT = 'kerberos_event'
    KERBEROS_SERVICE = 'kerberos_service'
    NOTIFICATION = 'notification'
    PROBABLE_OWNERS = 'probable_owners'
    SERVICE_PRIVILEGE = 'service_privilege'

    # From dl-azure-logflow
    ENTRA_GRAPH_V1_USER = 'entra_graph_v1_user'
    ENTRA_GRAPH_V1_SERVICE_PRINCIPAL = 'entra_graph_v1_service_principal'

    AD_CONTEXT = 'ad_context'

    @classmethod
    def get_all_entity_types(cls):
        entity_types = [
            attr_value for attr_name, attr_value in vars(cls).items() if isinstance(attr_value, str) and not attr_name.startswith("__")
        ]
        return entity_types


# Summary: batch_uid is an indexed column capped at 128 characters and is the S3 path associated with entity. These entity types can have
# S3 paths well exceeding 128 and this is a stop gap as we transition to treat batch uid md5 as the indexed column
LONG_S3_PATH_ENTITY_TYPES = [EntityTypes.ENTRA_GRAPH_V1_USER, EntityTypes.ENTRA_GRAPH_V1_SERVICE_PRINCIPAL, EntityTypes.AD_CONTEXT]


@dataclass
class HandleDocResult:
    success: bool
    retry: bool = False
    message: str = ''


class EntityIdentifier(NamedTuple):
    batch_uid: str
    batch_index: int
    forwarder_date: datetime


class LongS3EntityIdentifier(NamedTuple):
    batch_uid: str
    batch_index: int
    forwarder_date: datetime
    batch_uid_md5: str
    s3_full_path: str


class EntityPersister(NamedTuple):
    table: Model
    publisher: Callable


class PublisherResult(NamedTuple):
    success: bool
    retry: bool
    batch_uid: str
    batch_index: int


class ErrorMsgs:
    DOCUMENT = 'Invalid document'
    DB = 'Database Error'
    JSON = 'Invalid json payload'
    ENTITY = 'Unsupported entity type'
    RELATION = 'Missing entity dependencies'
    FORMAT = "Invalid entity format"
    UNSUPPORTED_DOC = 'Unsupported document'
    TIMEOUT = 'Processing Timeout'
    FAILED_LZ4_DECOMPRESSION = 'Failed lz4 decompression'

    @staticmethod
    def unknown(err):
        return f'Unknown Error: {err}'


metric_names = {
    EntityTypes.HOST: {
        'success': MetricName.ENTITY_RECEIVER_HOST_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_HOST_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_HOST_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_HOST_INVALID,
    },
    EntityTypes.HOST_SESSIONS: {
        'success': MetricName.ENTITY_RECEIVER_HOST_SESSION_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_HOST_SESSION_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_HOST_SESSION_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_HOST_SESSION_INVALID,
    },
    EntityTypes.KERBEROS_ACCOUNT: {
        'success': MetricName.ENTITY_RECEIVER_KERBEROS_ACCOUNT_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_KERBEROS_ACCOUNT_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_KERBEROS_ACCOUNT_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_KERBEROS_ACCOUNT_INVALID,
    },
    EntityTypes.KERBEROS_EVENT: {
        'success': MetricName.ENTITY_RECEIVER_KERBEROS_EVENT_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_KERBEROS_EVENT_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_KERBEROS_EVENT_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_KERBEROS_EVENT_INVALID,
    },
    EntityTypes.KERBEROS_SERVICE: {
        'success': MetricName.ENTITY_RECEIVER_KERBEROS_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_KERBEROS_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_KERBEROS_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_KERBEROS_SERVICE_INVALID,
    },
    EntityTypes.DETECTIONS: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
    EntityTypes.ACCOUNT_PRIVILEGE: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
    EntityTypes.HOST_PRIVILEGE: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
    EntityTypes.SERVICE_PRIVILEGE: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
    EntityTypes.NOTIFICATION: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
    EntityTypes.PROBABLE_OWNERS: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
    EntityTypes.APPLIANCE_STATS: {
        'success': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_SUCCESS,
        'failure': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_FAILURE,
        'retry': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_RETRY,
        'invalid': MetricName.ENTITY_RECEIVER_DETECTION_SERVICE_INVALID,
    },
}
