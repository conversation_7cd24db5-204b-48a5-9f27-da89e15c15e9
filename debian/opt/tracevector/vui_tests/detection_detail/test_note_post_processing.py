import json
import os

from base_tvui import settings
from django.forms import model_to_dict
from django.utils import timezone
from tvui.detection_detail.handlers import BaseDetectionHandler
from tvui.detections.dynamic_detections import dynamic_utils
from tvui.models import InternalDetectionTarget, detection_detail, DetectionSchema
from vui_tests.vui_testcase import VuiTestCase

# BaseDetectionHandler.dispatch_detection()

DETECTION_SCHEMAS_PATH = os.path.join(settings.SRC_ROOT, 'tracevector/bin/dynamic_detections/detection_schemas/{}.json')


class TestInternalDetectionPostProcess(VuiTestCase):

    def test_target_host(self):
        couch_doc = {
            "_id": "ds1",
            "_rev": "1-008d7a1d739f3527a357e4b176543179",
            "published": {"location": "abcdefg", "pcap_filename": None},
            "algorithm": "data_staging",
            "required": {
                "start_time": "2017-01-23T19:16:19.848455Z",
                "end_time": "2017-01-23T19:11:17.974496Z",
                "infected_host_session_luid": "src_luid_1",
                "infected_host_session_ip": "*************",
                "data_to_host_avg_long": 2370,
                "dest_host_details": [
                    {
                        "bytes_gathered": 80613,
                        "bytes_sent": 50,
                        "protocol": "tcp",
                        "server_port": 445,
                        "dest_ip": "*************",
                        "dest_name": "foo.bar",
                        "dest_host_session_luid": "dest_luid1",
                    },
                    {
                        "bytes_gathered": 73441,
                        "bytes_sent": 50,
                        "protocol": "udp",
                        "server_port": 345,
                        "dest_ip": "*************",
                        "dest_name": "",
                        "dest_host_session_luid": "dest_luid2",
                    },
                ],
            },
        }

        det_dict1 = {
            'src_session_luid': 'sluid1',
            'type': 'data_staging',
            'couch_note_id': 'ds1',
            'dst_session_luid': 'dluid1',
            'last_timestamp': timezone.now(),
        }

        det_dict2 = {
            'src_session_luid': 'sluid1',
            'type': 'data_staging',
            'couch_note_id': 'ds1',
            'dst_session_luid': 'dluid2',
            'last_timestamp': timezone.now(),
        }

        d1 = detection_detail.objects.create(**det_dict1)
        d2 = detection_detail.objects.create(**det_dict2)

        # load dynamic schema
        with open(DETECTION_SCHEMAS_PATH.format('data_staging'), 'rb') as detection_schema:
            schema = json.loads(detection_schema.read().decode('utf-8'))
            dynamic_utils.generate_detection_schema(schema, 'data_staging')

        BaseDetectionHandler.post_process_note(couch_doc)

        expected_targets = ['dluid1', 'dluid2']
        target_entries = InternalDetectionTarget.objects.all().order_by('target_host_session_luid')
        self.assertEqual(target_entries.count(), 2)
        for i, entry in enumerate(target_entries):
            self.assertEqual(entry.source_host_session_luid, 'sluid1')
            self.assertEqual(entry.target_host_session_luid, expected_targets[i])
            self.assertIsNone(entry.source_account_uid)
            self.assertIsNone(entry.target_account_uid)

    def test_target_account(self):
        couch_doc = {
            "_id": "kb1",
            "_rev": "2-008d7a1d739f3527a357e4b156997179",
            "algorithm": "kerberos_password_spray",
            "required": {
                "end_time": "2019-12-04T20:24:31.376109Z",
                "infected_host_session_ip": "*********",
                "infected_host_session_luid": "src_luid_1",
                "start_time": "2019-12-04T20:20:43.991411Z",
                "accounts": [
                    {
                        "account": "acc1",
                        "count": 13,
                        "end_time": "2019-12-01T20:24:31.376109Z",
                        "start_time": "2019-12-02T18:13:43.991411Z",
                    },
                    {
                        "account": "acc2",
                        "count": 7,
                        "end_time": "2019-12-03T22:22:31.376109Z",
                        "start_time": "2019-12-04T14:11:43.991411Z",
                    },
                    {
                        "account": "acc3",
                        "count": 53,
                        "end_time": "2019-12-04T20:24:31.376109Z",
                        "start_time": "2019-12-04T20:23:43.991411Z",
                    },
                ],
            },
            "optional": {},
            "pcap": {},
            "published": {"start": "2023-08-23T12:00:00", "location": "!4s3ns0r", "pcap_filename": None},
            "commit": {"gmt": "2023-08-23T12:34:56"},
            "$s3EventTime": "2023-08-23T12:45:00",
        }

        det_dict1 = {
            'src_session_luid': 'sluid1',
            'type': 'kerberos_password_spray',
            'couch_note_id': 'kb1',
            'account_uid': 'acc1',
            'last_timestamp': timezone.now(),
        }

        det_dict2 = {
            'src_session_luid': 'sluid1',
            'type': 'kerberos_password_spray',
            'couch_note_id': 'kb1',
            'account_uid': 'acc2',
            'last_timestamp': timezone.now(),
        }

        det_dict3 = {
            'src_session_luid': 'sluid1',
            'type': 'kerberos_password_spray',
            'couch_note_id': 'kb1',
            'account_uid': 'acc3',
            'last_timestamp': timezone.now(),
        }

        d1 = detection_detail.objects.create(**det_dict1)
        d2 = detection_detail.objects.create(**det_dict2)
        d3 = detection_detail.objects.create(**det_dict3)

        # load dynamic schema
        with open(DETECTION_SCHEMAS_PATH.format('kerberos_password_spray'), 'rb') as detection_schema:
            schema = json.loads(detection_schema.read().decode('utf-8'))
            dynamic_utils.generate_detection_schema(schema, 'kerberos_password_spray')

        BaseDetectionHandler.post_process_note(couch_doc)
        expected_targets = ['acc1', 'acc2', 'acc3']
        target_entries = InternalDetectionTarget.objects.all().order_by('target_account_uid')
        self.assertEqual(target_entries.count(), 3)

        for i, entry in enumerate(target_entries):
            self.assertEqual(entry.source_host_session_luid, 'sluid1')
            self.assertIsNone(entry.target_host_session_luid)
            self.assertIsNone(entry.source_account_uid)
            self.assertEqual(entry.target_account_uid, expected_targets[i])

    def test_target_host_csv(self):
        couch_doc = {
            "_id": "rpc1",
            "_rev": "2-008d7a1d739f3527a357e4b156997179",
            "algorithm": "rpc_recon",
            "required": {
                "dest_luid": "6wRU5Eyr",
                "infected_host_luid": "src_luid_1",
                "start_time": "2020-03-02T13:10:53.877002Z",
                "first_seen": "2020-03-02T13:10:53.877002Z",
                "last_seen": "2020-03-02T13:12:53.877002Z",
                "dest_host_session_luid": "dest1,dest2,dest3",
                "infected_host_session_ip": "*******",
                "end_time": "2020-03-02T13:12:53.877002Z",
                "infected_host_session_luid": "src_luid_1",
                "src_port": 82,
                "dest_ip": "*******",
                "dest_port": 82,
                "account": "<EMAIL>",
                "anomalous_function_call": {"service": "samr", "function_call": "SamrQueryInformationDomain"},
                "anomalous_function_call_count": 1,
                "source_profile": [{"count": 377, "service": "samr", "function_call": "SamrQueryInformationDomain2"}],
                "dest_profile": [
                    {"count": 71, "service": "samr", "function_call": "SamrQueryInformationDomain2"},
                    {"count": 32, "service": "samr2", "function_call": "SamrQueryInformationDomain3"},
                ],
            },
            "published": {"location": None, "pcap_filename": None},
            "commit": {
                "image_version": "3.11.0-2-11",
                "mode": "mixed",
                "os_release": "16.04",
                "model": "X80",
                "local": "Mon Mar 02 08:08:43 2020",
                "colossus_version": "5.5.0-19-33",
                "gmt": "2020-03-02T13:12:53.877002Z",
            },
            "optional": {},
            "pcap": {},
        }

        det_dict1 = {
            'src_session_luid': 'sluid1',
            'type': 'rpc_recon',
            'couch_note_id': 'rpc1',
            'flex2': 'dest1,dest2,dest3',
            'last_timestamp': timezone.now(),
        }

        d1 = detection_detail.objects.create(**det_dict1)

        with open(DETECTION_SCHEMAS_PATH.format('rpc_recon'), 'rb') as detection_schema:
            schema = json.loads(detection_schema.read().decode('utf-8'))
            dynamic_utils.generate_detection_schema(schema, 'rpc_recon')

        BaseDetectionHandler.post_process_note(couch_doc)
        expected_targets = ['dest1', 'dest2', 'dest3']
        target_entries = InternalDetectionTarget.objects.all().order_by('target_host_session_luid')
        self.assertEqual(target_entries.count(), 3)

        for i, entry in enumerate(target_entries):
            self.assertEqual(entry.source_host_session_luid, 'sluid1')
            self.assertEqual(entry.target_host_session_luid, expected_targets[i])
            self.assertIsNone(entry.source_account_uid)
            self.assertIsNone(entry.target_account_uid)

    def test_src_account(self):
        couch_doc = {
            "$sequence": "23",
            "published": {"location": "test_location"},
            "algorithm": "sw_o365_accountManipulation",
            "required": {
                "src_account": {"uid": "src1"},
                "ip": "127.0.0.1",
                "volume": 1,
                "threat_score": 80,
                "certainty_score": 70,
                "details": {"operation": "Add-MailboxPermission", "account": "dest1", "mailboxes": ["CEO Email Group"]},
            },
            "optional": {},
            "scoring": {},
        }

        det_dict1 = {
            'account_uid': 'src_acc',
            'type': 'sw_o365_accountManipulation',
            'sequence_id': '23',
            'is_host_detail': False,
            'flex_json': {"account": 'acc1'},
            'last_timestamp': timezone.now(),
        }

        with open(DETECTION_SCHEMAS_PATH.format('sw_o365_accountManipulation'), 'rb') as detection_schema:
            schema = json.loads(detection_schema.read().decode('utf-8'))
            dynamic_utils.generate_detection_schema(schema, 'sw_o365_accountManipulation')

        d1 = detection_detail.objects.create(**det_dict1)

        BaseDetectionHandler.post_process_note(couch_doc)
        print(InternalDetectionTarget.objects.count())
        entry = InternalDetectionTarget.objects.get()

        self.assertIsNone(entry.source_host_session_luid)
        self.assertIsNone(entry.target_host_session_luid)
        self.assertEqual(entry.source_account_uid, 'src_acc')
        self.assertEqual(entry.target_account_uid, 'acc1')
