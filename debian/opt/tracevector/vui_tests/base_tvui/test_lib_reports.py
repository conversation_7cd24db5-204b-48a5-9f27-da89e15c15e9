# Copyright (c) 2016-2019 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import pytz
from datetime import timedelta, datetime
from dateutil.relativedelta import relativedelta
from decimal import Decimal
from unittest import mock
from collections import defaultdict
from freezegun import freeze_time
from django.utils import timezone

from base_tvui import lib_account
from tvui.detections.detection_types import DetectionType
from tvui.helpers import DETECTION_METADATA as detection_metadata
from tvui.reports import DashboardReport, AssetInventoryReport, HostSeverityReport
from tvui.reports.CISOReport import CISOReport, upload_ciso_workflow_telemetry
from tvui.reports.report_sending import send_scheduled_reports
from tvui.models import (
    Account,
    AssetInventoryReportDef,
    Assignment,
    AssignmentOutcome,
    detection,
    custom_model_definition,
    Account,
    host,
    host_session,
    notes,
    report_def,
    AssetInventoryReportDef,
    CISOReportDef,
    host_artifact,
    User,
    PrivHistory,
    score,
    Service,
    smart_rule,
    PrioritizationTimings,
    LinkedAccount,
    LinkedAccountScoreHistory,
    notes,
    NetworkSensor,
    setting,
    HostRole,
)
from vui_tests.vui_testcase import VuiTestCase


class TestReport(VuiTestCase):
    """
    Tests for lib_reports.Report class
    """

    def setUp(self):
        _custom_model_definition = {
            'custom_model_id': 'ced6c6f3c2ad492da5a39383d63b78c5',
            'full_custom_model_id': 'search:ced6c6-f3c2-ad492da5a-39383d63b78c5',
            'name': 'HTTP lateral movement',
            'description': 'Checks for lateral movement of HTTP requests',
            'lucene': "test:true",
            'elastic_filters': '[]',
            'index': 'httpsessioninfo',
            'category': 'lateral',
            'threat_score': 1,
            'certainty_score': 2,
            'custom_model_search_link': 'https://recall.vectra.ai/discover/ced6c6f3c2ad492da5a39383d63b78c5',
            'deprecated': True,
        }

        _custom_model_definition_2 = {
            'custom_model_id': 'a2af31321a104f99b7e7cd56bc4f9f55',
            'full_custom_model_id': 'search:a2af3132-1a10-4f99-b7e7-cd56bc4f9f55',
            'previous_custom_model_id': 'ced6c6f3c2ad492da5a39383d63b78c5',
            'deprecated': True,
            'name': 'HTTP lateral movement',
            'description': 'Checks for lateral movement of HTTP requests',
            'lucene': "test:true",
            'elastic_filters': '[]',
            'index': 'httpsessioninfo',
            'category': 'lateral',
            'threat_score': 1,
            'certainty_score': 2,
            'custom_model_search_link': 'https://recall.vectra.ai/discover/ced6c6f3c2ad492da5a39383d63b78c5',
        }

        _custom_model_definition_3 = {
            'custom_model_id': '05879df40a0e47829ec4f6ab6f6c1bfe',
            'full_custom_model_id': 'search:05879df40-a0e4-7829-ec4f6ab6f6c1bfe',
            'previous_custom_model_id': 'a2af31321a104f99b7e7cd56bc4f9f55',
            'deprecated': False,
            'name': 'HTTP lateral movement',
            'description': 'Checks for lateral movement of HTTP requests',
            'lucene': "test:true",
            'elastic_filters': '[]',
            'index': 'httpsessioninfo',
            'category': 'lateral',
            'threat_score': 1,
            'certainty_score': 2,
            'custom_model_search_link': 'https://recall.vectra.ai/discover/ced6c6f3c2ad492da5a39383d63b78c5',
        }

        _custom_model_definition_4 = {
            'custom_model_id': '82d560335c1b4b03b9674d2329fad8e8',
            'full_custom_model_id': 'search:82d56033-5c1b-4b03-b967-4d2329fad8e8',
            'previous_custom_model_id': '05879df40a0e47829ec4f6ab6f6c1bfe',
            'deprecated': False,
            'name': 'HTTP lateral movement',
            'description': 'Checks for lateral movement of HTTP requests',
            'lucene': "test:true",
            'elastic_filters': '[]',
            'index': 'httpsessioninfo',
            'category': 'lateral',
            'threat_score': 1,
            'certainty_score': 2,
            'custom_model_search_link': 'https://recall.vectra.ai/discover/ced6c6f3c2ad492da5a39383d63b78c5',
        }

        _custom_model_definition_5 = {
            'custom_model_id': 'f5b3829e93d84d4abc27777fbdd84a95',
            'full_custom_model_id': 'search:f5b3829e-93d8-4d4a-bc27-777fbdd84a95',
            'deprecated': False,
            'name': 'HTTP lateral movement',
            'description': 'Checks for lateral movement of HTTP requests',
            'lucene': "test:true",
            'elastic_filters': '[]',
            'index': 'httpsessioninfo',
            'category': 'lateral',
            'threat_score': 1,
            'certainty_score': 2,
            'custom_model_search_link': 'https://recall.vectra.ai/discover/ced6c6f3c2ad492da5a39383d63b78c5',
        }

        self.custom_model_definition = custom_model_definition.objects.create(**_custom_model_definition)
        self.custom_model_definition_2 = custom_model_definition.objects.create(**_custom_model_definition_2)
        self.custom_model_definition_3 = custom_model_definition.objects.create(**_custom_model_definition_3)
        self.custom_model_definition_4 = custom_model_definition.objects.create(**_custom_model_definition_4)
        self.custom_model_definition_5 = custom_model_definition.objects.create(**_custom_model_definition_5)

        self.now = timezone.now()
        self.last_30_days = self.now - timedelta(days=30)
        self.yesterday = self.now - timedelta(days=1)

        self.from_date = self.last_30_days
        self.to_date = self.now

        self.create_dets = {
            'COMMAND & CONTROL': 10,
            'RECONNAISSANCE': 20,
            'LATERAL MOVEMENT': 30,
            'BOTNET ACTIVITY': 40,
            'EXFILTRATION': 50,
        }
        self.custom_model_ids = ['f5b3829e93d84d4abc27777fbdd84a95', '05e35f8207e04d3a867d8b6f53145efa']
        self.tags = ['test', 'testing']

        self.detection_types = {
            'BOTNET ACTIVITY': DetectionType.BITCOIN,
            'RECONNAISSANCE': DetectionType.DARKNET,
            'COMMAND & CONTROL': DetectionType.REVERSE_RAT,
            'EXFILTRATION': DetectionType.SMASH_N_GRAB,
            'LATERAL MOVEMENT': DetectionType.SQL_INJECT,
        }

        # detection.objects.all().delete()
        for cd_k, cd_v in self.create_dets.items():
            for det_count in range(cd_v):
                det_created = detection.objects.create(
                    category=cd_k, src_ip='***********', last_timestamp=self.yesterday, c_score=70, t_score=70, state='active'
                )

                # Add detection_tags
                if det_count <= 1:
                    det_created.tags.add(self.tags[0])
                if det_count <= 2:
                    det_created.tags.add(self.tags[1])

                detection_type = self.detection_types[cd_k]
                det_created.type = detection_type
                det_created.type_vname = detection_metadata[detection_type].get('vname')

                # Add custom_models
                if det_count < 2 and cd_k == 'LATERAL MOVEMENT':
                    det_created.description = self.custom_model_ids[det_count]
                    det_created.type = 'cm_httpsessioninfo_lateral'
                    det_created.type_vname = self.custom_model_ids[det_count]

                det_created.save()
        self.account = Account.objects.create(uid='<EMAIL>', account_type=Account.TYPE_KERBEROS)
        self.account_detection = detection.objects.create(
            account=self.account,
            type=DetectionType.PAPI_ROGUE_ADMIN,
            type_vname=detection_metadata[DetectionType.PAPI_ROGUE_ADMIN]['vname'],
            last_timestamp=self.yesterday,
            category='LATERAL MOVEMENT',
            c_score=70,
            t_score=70,
            state='active',
        )

    def report_context(self, **kwargs):
        if 'detection_types' not in kwargs:
            kwargs['detection_types'] = ['frontwatch']
        if 'from_date' not in kwargs:
            kwargs['from_date'] = self.from_date
        if 'to_date' not in kwargs:
            kwargs['to_date'] = self.to_date
        report = HostSeverityReport(report_def(report_type='host_severity', frequency='on_demand', **kwargs))
        return report.get_context()

    def test_detection_type_breakdown_include_account_detections(self):
        parameters = report_def(
            detection_types=[DetectionType.PAPI_ROGUE_ADMIN], to_date=self.to_date, from_date=self.from_date, frequency='on_demand'
        )
        report = HostSeverityReport(parameters)
        page_report = report.get_context()
        detection_type_breakdown = page_report['detection_type']['top']
        types_found = [r.type_vname for r in detection_type_breakdown]
        self.assertNotIn(detection_metadata[DetectionType.PAPI_ROGUE_ADMIN]['vname'], types_found)
        self.assertEqual(len(detection_type_breakdown), 0)

        report = DashboardReport(self.from_date, self.to_date)
        report.detection_types = [DetectionType.PAPI_ROGUE_ADMIN]  # artificially limit so it will show up in top 5
        dashboard_ajax_page_report = report.dashboard_report_ajax()
        dashboard_ajax_detection_type_breakdown = dashboard_ajax_page_report['detectionsByType']
        dashboard_ajax_types_found = [r['name'] for r in dashboard_ajax_detection_type_breakdown]
        self.assertIn(DetectionType.PAPI_ROGUE_ADMIN, dashboard_ajax_types_found)
        self.assertEqual(len(dashboard_ajax_detection_type_breakdown), 1)

    @mock.patch('tvui.reports.HostSeverity.execute_sql')
    def test_get_account_severity_dashboard_math(self, mock_execute_sql):
        now = timezone.now()
        from_date = now - timedelta(hours=24)
        report = DashboardReport(from_date, now)

        start_result = mock.MagicMock()
        start_result._asdict.return_value = {
            'criticalStart': Decimal('1'),
            'highStart': Decimal('2'),
            'mediumStart': Decimal('3'),
            'lowStart': Decimal('4'),
        }

        end_result = mock.MagicMock()
        end_result._asdict.return_value = {
            'criticalEnd': Decimal('9'),
            'highEnd': Decimal('1'),
            'mediumEnd': Decimal('0'),
            'lowEnd': Decimal('8'),
        }

        mock_execute_sql.side_effect = ([start_result], [end_result])

        self.assertEqual(
            report._get_account_severity_dashboard(),
            {
                'criticalEnd': 9,
                'highEnd': 1,
                'mediumEnd': 0,
                'lowEnd': 8,
                'criticalChange': 8,
                'highChange': -1,
                'mediumChange': -3,
                'lowChange': 4,
            },
        )

    def test_ai_detection_type(self):
        """
        Test detection type breakdown for AI detections, no custom models
        """
        parameters = report_def(
            detection_types=['bitcoin', 'smash_n_grab'], to_date=self.to_date, from_date=self.from_date, frequency='on_demand'
        )
        report = HostSeverityReport(parameters)

        page_report = report.get_context()

        detection_type_breakdown = page_report['detection_type']['top']
        types_found = [r['type_vname'] for r in detection_type_breakdown]
        self.assertTrue(detection_metadata[DetectionType.BITCOIN]['vname'] in types_found)
        self.assertTrue(detection_metadata[DetectionType.SMASH_N_GRAB]['vname'] in types_found)

        self.assertEqual(len(detection_type_breakdown), 2)

    def test_custom_model_detection_type(self):
        """
        custom models, no AI detections
        """
        parameters = report_def(
            custom_model_ids=[self.custom_model_ids[0]], to_date=self.to_date, from_date=self.from_date, frequency='on_demand'
        )
        report = HostSeverityReport(parameters)
        page_report = report.get_context()
        detection_type_breakdown = page_report['detection_type']['top']
        types_found = [r.type_vname for r in detection_type_breakdown]
        self.assertTrue(self.custom_model_ids[0] in types_found)

    def test_both_detection_type(self):
        """
        custom model and AI detections
        """
        parameters = report_def(
            custom_model_ids=[self.custom_model_ids[0]],
            detection_types=['bitcoin'],
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
        )

        report = HostSeverityReport(parameters)
        page_report = report.get_context()
        detection_type_breakdown = page_report['detection_type']['top']
        types_found = [r['type_vname'] for r in detection_type_breakdown]
        self.assertTrue(self.custom_model_ids[0] in types_found)
        self.assertTrue(detection_metadata[DetectionType.BITCOIN]['vname'] in types_found)

    def test_get_detection_breakdown_with_custom_models(self):
        """
        custom model
        """
        parameters = report_def(
            custom_model_ids=[self.custom_model_ids[0]], to_date=self.to_date, from_date=self.from_date, frequency='on_demand'
        )

        report = HostSeverityReport(parameters)
        page_report = report.get_context()
        detection_breakdown = page_report['detection_breakdown']
        threat_number = detection_breakdown[0]
        self.assertEqual(threat_number, 1)

    def test_get_detection_breakdown_with_info_detections(self):
        """
        Make sure info detections don't get counted as threat detections
        """
        parameters = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'], to_date=self.to_date, from_date=self.from_date, frequency='on_demand'
        )
        self._set_up_si_dets()

        report = HostSeverityReport(parameters)
        page_report = report.get_context()
        detection_breakdown = page_report['detection_breakdown']

        self.assertEqual(detection_breakdown.threat, 0)
        self.assertEqual(detection_breakdown.custom, 0)
        # Assert that info detections are not included at all
        with self.assertRaises(AttributeError):
            info_number = detection_breakdown.info

    def test_get_detection_breakdown_with_triaged_info_detections(self):
        """
        Make sure info detections don't get counted as threat detections
        """
        parameters = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'], to_date=self.to_date, from_date=self.from_date, frequency='on_demand'
        )
        self._set_up_si_dets()
        test_smart_rule = smart_rule.objects.create(
            type='test', priority=10, smart_category='test', is_whitelist=False, conditions={}, family=smart_rule.FAMILY_CUSTOMER
        )
        detection.objects.filter(category='INFO').update(smart_rule=test_smart_rule)
        report = HostSeverityReport(parameters)
        page_report = report.get_context()
        detection_breakdown = page_report['detection_breakdown']

        self.assertEqual(detection_breakdown.threat, 0)
        self.assertEqual(detection_breakdown.custom, 0)
        # Assert that info detections are not included at all
        with self.assertRaises(AttributeError):
            info_number = detection_breakdown.info

    def _set_up_si_dets(self):
        """
        Set up Security Insight detections for testing
        """
        test_hst = host.objects.create(name='test_host', state='active', last_source='**********', t_score=10, c_score=10)

        self.test_hs = host_session.objects.create(session_luid='TestHS00', ip_address='**********', host=test_hst, start=self.yesterday)

        self.d1 = detection.objects.create(
            type=DetectionType.SI_NEW_HOST,
            category='INFO',
            t_score=0,
            c_score=0,
            state='active',
            src_ip='**********',
            last_timestamp=self.yesterday,
            host_session=self.test_hs,
            type_vname=detection_metadata[DetectionType.SI_NEW_HOST]['vname'],
        )

        detection.objects.create(
            type=DetectionType.SI_NOVEL_MAC_VENDOR,
            category='INFO',
            t_score=0,
            c_score=0,
            state='active',
            src_ip='**********',
            last_timestamp=self.yesterday,
            host_session=self.test_hs,
            type_vname=detection_metadata[DetectionType.SI_NOVEL_MAC_VENDOR]['vname'],
        )

    def test_try_to_get_detections_with_info_detections(self):
        """
        Try and Get Info Detections, which should not be included
        """
        self._set_up_si_dets()

        params = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'],
            detection_t=10,
            detection_c=10,
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
        )

        report = HostSeverityReport(params)
        dets_reported = report.get_context()
        det_types_reported = set(det.type for det in dets_reported['detections']['all'])
        self.assertNotIn('si_new_host', det_types_reported)
        self.assertNotIn('si_novel_mac_vendor', det_types_reported)

    def test_get_detections_check_non_info(self):
        """
        Make sure scoring detections with 0,0 score should not bypass scoring check
        """
        self._set_up_si_dets()

        detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            category='COMMAND & CONTROL',
            t_score=0,
            c_score=0,
            state='active',
            src_ip='**********',
            last_timestamp=self.yesterday,
            host_session=self.test_hs,
            type_vname=detection_metadata[DetectionType.HIDDEN_DNS_TUNNEL_CNC]['vname'],
        )

        params = report_def(
            detection_types=['si_new_host'],
            detection_t=10,
            detection_c=10,
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
        )

        report = HostSeverityReport(params)
        dets_reported = report.get_context()
        det_types_reported = set(det.type for det in dets_reported['detections']['all'])
        self.assertNotIn('si_new_host', det_types_reported)
        self.assertNotIn('si_novel_mac_vendor', det_types_reported)
        self.assertNotIn('hidden_dns_tunnel_cnc', det_types_reported)

    def test_multiple_notes_for_host(self):
        self._set_up_si_dets()

        params = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'],
            detection_t=10,
            detection_c=10,
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
        )

        # create notes
        now = timezone.now()
        _notes = [
            notes.objects.create(
                type='host', type_id=self.test_hs.host.id, note='a test note {}'.format(i), date_created=now + timedelta(hours=i)
            )
            for i in range(3)
        ]

        _notes.append(
            notes.objects.create(
                type='host',
                type_id=self.test_hs.host.id,
                note='a test note x',
                date_created=now + timedelta(hours=-10),
                date_modified=now + timedelta(hours=-10),
            )
        )

        report = HostSeverityReport(params)
        dets_reported = report.get_context()
        self.assertEqual(dets_reported['hosts']['all'][0].notes[0].note, 'a test note 2')
        self.assertEqual(dets_reported['hosts']['all'][0].notes[1].note, 'a test note 1')

    def test_multiple_notes_for_detection(self):
        self._set_up_si_dets()

        params = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'],
            detection_t=10,
            detection_c=10,
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
        )

        # create notes
        now = timezone.now()
        _notes = [
            notes.objects.create(
                type='detection', type_id=self.d1.id, note='a test note {}'.format(i), date_created=now + timedelta(hours=i)
            )
            for i in range(3)
        ]

        _notes.append(
            notes.objects.create(
                type='detection',
                type_id=self.d1.id,
                note='a test note x',
                date_created=now + timedelta(hours=-10),
                date_modified=now + timedelta(hours=-10),
            )
        )

        report = HostSeverityReport(params)
        dets_reported = report.get_context()

        dets = dets_reported['detections']['all']

        for det in dets:
            if det.id == self.d1.id:
                self.assertEqual(det.notes[0].note, 'a test note 2')
                self.assertEqual(det.notes[1].note, 'a test note 1')

    def test_worst_offenders_specific_sensor(self):
        params = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'],
            detection_t=10,
            detection_c=10,
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
            source='sensor1',
        )

        # create hosts with varying sensors
        hosts_in_report = [host.objects.create(name='test_host{}'.format(i), sensor_luid='sensor1') for i in range(10)]
        hosts_not_in_report = [host.objects.create(name='not_in_report_host{}'.format(i), sensor_luid='sensor2') for i in range(10)]

        # create score entries
        scores = [
            score.objects.create(host=_host, timestamp=self.yesterday, threat_score=100, confidence_score=100) for _host in hosts_in_report
        ]
        scores2 = [
            score.objects.create(host=_host, timestamp=self.yesterday, threat_score=100, confidence_score=100)
            for _host in hosts_not_in_report
        ]
        score.objects.all().update(end_timestamp=self.yesterday)

        # create PrivHistory entries
        priv_histories = [
            PrivHistory.objects.create(host=_host, priv_level=10, created_date=self.yesterday, type=PrivHistory.HOST)
            for _host in hosts_in_report
        ]
        priv_histories2 = [
            PrivHistory.objects.create(host=_host, priv_level=10, created_date=self.yesterday, type=PrivHistory.HOST)
            for _host in hosts_not_in_report
        ]

        report = HostSeverityReport(params)
        report_context = report.get_context()

        # make sure sensor separation is respected
        worst_offenders = report_context['worst_offender_host']
        _hosts_in_included_sensor = set([_host.id for _host in hosts_in_report])

        for _host in worst_offenders:
            self.assertIn(_host.id, _hosts_in_included_sensor)

    def test_worst_offenders_all_sensors(self):
        params = report_def(
            detection_types=['si_new_host', 'si_novel_mac_vendor'],
            detection_t=10,
            detection_c=10,
            to_date=self.to_date,
            from_date=self.from_date,
            frequency='on_demand',
            source='all',
        )

        # create hosts with varying sensors
        hosts_in_report = [host.objects.create(name='test_host{}'.format(i), sensor_luid='sensor{}'.format(i)) for i in range(5)]

        # create score entries
        scores = [
            score.objects.create(host=_host, timestamp=self.yesterday, threat_score=100, confidence_score=100) for _host in hosts_in_report
        ]
        score.objects.all().update(end_timestamp=self.yesterday)

        # create PrivHistory entries
        priv_histories = [
            PrivHistory.objects.create(host=_host, priv_level=10, created_date=self.yesterday, type=PrivHistory.HOST)
            for _host in hosts_in_report
        ]

        report = HostSeverityReport(params)
        report_context = report.get_context()

        # make sure hosts from both sensors are included
        worst_offenders = report_context['worst_offender_host']
        host_ids = set([_host.id for _host in worst_offenders])

        for _host in hosts_in_report:
            self.assertIn(_host.id, host_ids)

    def _setup_sensors(self):
        self.test_sensor = NetworkSensor.objects.create(
            alias='damsensor',
            serial_number='S211222333444',
            status='paired',
            luid='1xx35',
            mode='sensor',
            ip_address='*******',
            product_name='S2',
            last_seen=self.now,
            details={'update_count': 1},
        )

    def test_general_data_sensor(self):
        self._setup_sensors()
        self.assertEqual(self.report_context(source=self.test_sensor.luid)['general_data']['pretty_source'], self.test_sensor.alias)

    def test_general_data_sensor_missing(self):
        self._setup_sensors()
        self.assertEqual(self.report_context(source='fakeluid')['general_data']['pretty_source'], 'All')


class TestScheduledReports(VuiTestCase):
    def setUp(self):
        self.user = User.objects.create_user('cognito', email='<EMAIL>', password='cognito')

        self.patch_get_attributes = mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes', return_value=[])
        self.patch_get_attributes.start()
        self.addCleanup(self.patch_get_attributes.stop)

        patch_get_mac_vendor = mock.patch('pure_utils.lookup_mac_vendor.get_mac_vendor', return_value=None)
        self.get_mac_vendor = patch_get_mac_vendor.start()
        self.addCleanup(patch_get_mac_vendor.stop)

    @mock.patch('base_tvui.email_utils.send_email_immediately')
    @mock.patch('tvui.reports.lib_reports.PDFReport.render_pdf')
    @mock.patch('tvui.reports.report_sending.render_to_string')
    def test_daily_scheduled_asset_inventory_reports(self, mock_render_to_string, mock_render_pdf, mock_send_email):
        mock_render_to_string.return_value = ''

        # Test daily asset inventory report
        daily_report_obj = AssetInventoryReportDef.objects.create(
            name='a report', email_addresses=['<EMAIL>'], frequency='daily', created_by=self.user
        )

        date = datetime.strptime("2020-09-25 00:00:00", '%Y-%m-%d %H:%M:%S')

        with freeze_time(date):
            send_scheduled_reports()

            daily_report_obj.refresh_from_db()
            self.assertEqual(daily_report_obj.lastsent_timestamp, timezone.now())

        # make sure call to send email was made
        mock_send_email.assert_called_once_with(
            daily_report_obj.email_addresses[0],
            'Vectra Daily Asset Inventory Report - {}'.format(daily_report_obj.name),
            {'html': ''},
            raw_files=[{'bytes': mock_render_pdf(), 'filename': 'Vectra Daily Asset Inventory Report 2020-09-25.pdf'}],
        )

    @mock.patch('base_tvui.email_utils.send_email_immediately')
    @mock.patch('tvui.reports.lib_reports.PDFReport.render_pdf')
    @mock.patch('tvui.reports.report_sending.render_to_string')
    def test_weekly_scheduled_asset_inventory_reports(self, mock_render_to_string, mock_render_pdf, mock_send_email):
        mock_render_to_string.return_value = ''

        # Test weekly asset inventory report
        weekly_report_obj = AssetInventoryReportDef.objects.create(
            name='a weekly report', email_addresses=['<EMAIL>'], frequency='weekly', created_by=self.user
        )

        start_of_week = "2020-09-21 12:00:00"  # Monday
        not_start_of_week = "2020-09-22 12:00:00"  # Tuesday

        # no weekly email should be sent if it's not Monday
        with freeze_time(not_start_of_week):
            mock_send_email.assert_not_called()

        with freeze_time(start_of_week):
            send_scheduled_reports()

            weekly_report_obj.refresh_from_db()
            self.assertEqual(weekly_report_obj.lastsent_timestamp, timezone.now())

        mock_send_email.assert_called_once_with(
            weekly_report_obj.email_addresses[0],
            'Vectra Weekly Asset Inventory Report - {}'.format(weekly_report_obj.name),
            {'html': ''},
            raw_files=[{'bytes': mock_render_pdf(), 'filename': 'Vectra Weekly Asset Inventory Report 2020-09-21.pdf'}],
        )

    @mock.patch('base_tvui.email_utils.send_email_immediately')
    @mock.patch('tvui.reports.lib_reports.PDFReport.render_pdf')
    @mock.patch('tvui.reports.report_sending.render_to_string')
    def test_monthly_scheduled_asset_inventory_reports(self, mock_render_to_string, mock_render_pdf, mock_send_email):
        mock_render_to_string.return_value = ''

        # Test monthly asset inventory report
        monthly_report_obj = AssetInventoryReportDef.objects.create(
            name='a monthly report', email_addresses=['<EMAIL>'], frequency='monthly', created_by=self.user
        )

        start_of_month = "2020-09-01 12:00:00"
        not_start_of_month = "2020-09-02 12:00:00"

        # no weekly email should be sent if it's not the 1st of the month
        with freeze_time(not_start_of_month):
            mock_send_email.assert_not_called()

        with freeze_time(start_of_month):
            send_scheduled_reports()

            monthly_report_obj.refresh_from_db()
            self.assertEqual(monthly_report_obj.lastsent_timestamp, timezone.now())

        mock_send_email.assert_called_once_with(
            monthly_report_obj.email_addresses[0],
            'Vectra Monthly Asset Inventory Report - {}'.format(monthly_report_obj.name),
            {'html': ''},
            raw_files=[{'bytes': mock_render_pdf(), 'filename': 'Vectra Monthly Asset Inventory Report 2020-09-01.pdf'}],
        )

    @mock.patch('base_tvui.email_utils.send_email_immediately')
    @mock.patch('tvui.reports.lib_reports.PDFReport.render_pdf')
    @mock.patch('tvui.reports.report_sending.render_to_string')
    def test_daily_scheduled_host_reports(self, mock_render_to_string, mock_render_pdf, mock_send_email):
        mock_render_to_string.return_value = ''

        # Test daily host severity report
        daily_report_obj = report_def.objects.create(
            name='a report', report_type='host_severity', email_addresses=['<EMAIL>'], frequency='daily'
        )

        date = datetime.strptime("2020-09-25 00:00:00", '%Y-%m-%d %H:%M:%S')

        with freeze_time(date):
            send_scheduled_reports()

            daily_report_obj.refresh_from_db()
            self.assertEqual(daily_report_obj.lastsent_timestamp, timezone.now())

        # make sure call to send email was made
        mock_send_email.assert_called_once_with(
            daily_report_obj.email_addresses[0],
            'Vectra Daily Host Severity Report - {}'.format(daily_report_obj.name),
            {'html': ''},
            raw_files=[{'bytes': mock_render_pdf(), 'filename': 'Vectra Daily Host Severity Report 2020-09-25.pdf'}],
        )

    @mock.patch('base_tvui.email_utils.send_email_immediately')
    @mock.patch('tvui.reports.lib_reports.PDFReport.render_pdf')
    @mock.patch('tvui.reports.report_sending.render_to_string')
    def test_weekly_scheduled_host_reports(self, mock_render_to_string, mock_render_pdf, mock_send_email):
        mock_render_to_string.return_value = ''

        # Test weekly host severity report
        weekly_report_obj = report_def.objects.create(
            name='a weekly report', report_type='host_severity', email_addresses=['<EMAIL>'], frequency='weekly'
        )

        start_of_week = "2020-09-21 12:00:00"  # Monday
        not_start_of_week = "2020-09-22 12:00:00"  # Tuesday

        # no weekly email should be sent if it's not Monday
        with freeze_time(not_start_of_week):
            mock_send_email.assert_not_called()

        with freeze_time(start_of_week):
            send_scheduled_reports()

            weekly_report_obj.refresh_from_db()
            self.assertEqual(weekly_report_obj.lastsent_timestamp, timezone.now())

        mock_send_email.assert_called_once_with(
            weekly_report_obj.email_addresses[0],
            'Vectra Weekly Host Severity Report - {}'.format(weekly_report_obj.name),
            {'html': ''},
            raw_files=[{'bytes': mock_render_pdf(), 'filename': 'Vectra Weekly Host Severity Report 2020-09-21.pdf'}],
        )

    @mock.patch('base_tvui.email_utils.send_email_immediately')
    @mock.patch('tvui.reports.lib_reports.PDFReport.render_pdf')
    @mock.patch('tvui.reports.report_sending.render_to_string')
    def test_monthly_scheduled_host_reports(self, mock_render_to_string, mock_render_pdf, mock_send_email):
        mock_render_to_string.return_value = ''

        # Test monthly host severity report
        monthly_report_obj = report_def.objects.create(
            name='a monthly report', report_type='host_severity', email_addresses=['<EMAIL>'], frequency='monthly'
        )

        start_of_month = "2020-09-01 12:00:00"
        not_start_of_month = "2020-09-02 12:00:00"

        # no weekly email should be sent if it's not the 1st of the month
        with freeze_time(not_start_of_month):
            mock_send_email.assert_not_called()

        with freeze_time(start_of_month):
            send_scheduled_reports()

            monthly_report_obj.refresh_from_db()
            self.assertEqual(monthly_report_obj.lastsent_timestamp, timezone.now())

        mock_send_email.assert_called_once_with(
            monthly_report_obj.email_addresses[0],
            'Vectra Monthly Host Severity Report - {}'.format(monthly_report_obj.name),
            {'html': ''},
            raw_files=[{'bytes': mock_render_pdf(), 'filename': 'Vectra Monthly Host Severity Report 2020-09-01.pdf'}],
        )

    def test_manual_scheduled_report_generation(self):
        """A scheduled report should be able to render out-of-band of the email-sender job"""

        with freeze_time('2020-01-02 08:00:00'):
            report = HostSeverityReport(report_def(frequency='daily'))
            context = report.get_context()
            self.assertEqual(str(context['general_data']['from_datetime']), '2020-01-01 00:00:00+00:00')
            self.assertEqual(str(context['general_data']['to_datetime']), '2020-01-02 00:00:00+00:00')

            exec_report = AssetInventoryReport(AssetInventoryReportDef(frequency='daily'))
            context = exec_report.get_context()
            self.assertEqual(str(context['general_data']['from_datetime']), '2020-01-01 00:00:00+00:00')
            self.assertEqual(str(context['general_data']['to_datetime']), '2020-01-02 00:00:00+00:00')


class TestAssetInventoryReport(VuiTestCase):
    """
    Tests for lib_reports.Report class
    """

    def setUp(self):
        self.user = User.objects.create_user(username='vadmin', email='<EMAIL>', password='plaintext_pass')
        self.now = timezone.now().replace(second=0, microsecond=0)
        self.last_30_days = self.now - timedelta(days=30)
        self.yesterday = self.now - timedelta(days=1)

        self.from_date = self.last_30_days
        self.to_date = self.now
        self.old_date = self.now - timedelta(days=100)
        self.future_date = self.now + timedelta(days=100)

        # create asset inventory report def
        self.asset_inventory_report = AssetInventoryReport(
            AssetInventoryReportDef(
                from_date=self.from_date, to_date=self.to_date, name='Test Report', created_by=self.user, frequency='daily'
            )
        )

        # create hosts
        self._hosts = [
            host.objects.create(
                name='testhost{}'.format(i), last_source='1.2.{}.5'.format(i), first_timestamp=self.yesterday, host_luid='luid{}'.format(i)
            )
            for i in range(20)
        ]

        # create hosts that shouldn't be a part of the report
        self._excluded_hosts = [
            host.objects.create(name='excludedhost{}'.format(i), last_source='1.2.{}.5'.format(i), first_timestamp=self.old_date)
            for i in range(20)
        ]
        self._excluded_hosts = [
            host.objects.create(name='excludedhost{}'.format(i), last_source='1.2.{}.5'.format(i), first_timestamp=self.future_date)
            for i in range(20)
        ]
        host.objects.create(name='testhostnoip', last_source=None, first_timestamp=self.yesterday)

        # create MAC artifacts
        # Apple
        host_artifact.objects.create(host=self._hosts[5], type='mac', value='90:60:F1:2E:F2:8E')
        host_artifact.objects.create(host=self._hosts[6], type='mac', value='90:60:F1:2E:F2:8E')
        host_artifact.objects.create(host=self._hosts[7], type='mac', value='90:60:F1:2E:F2:8E')

        # Microsoft
        host_artifact.objects.create(host=self._hosts[8], type='mac', value='F0:1D:BC:FE:2E:62')
        host_artifact.objects.create(host=self._hosts[9], type='mac', value='F0:1D:BC:FE:2E:62')

        # Unknown
        self._unknown_vendors = [
            host_artifact.objects.create(host=self._hosts[i], type='mac', value='unknown-vendor') for i in range(10, 15)
        ]

        # create non MAC artifacts for first 5 hosts
        self._host_artifacts = [
            host_artifact.objects.create(host=self._hosts[i], type='dhcp', value='vectra-vsensor'.format(i)) for i in range(5)
        ]
        self._host_artifacts = [
            host_artifact.objects.create(host=self._hosts[i], type='kerberos', value='abcd'.format(i)) for i in range(5)
        ]

        self.get_attributes_patch = mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
        self.get_attributes_patch.start()
        self.addCleanup(self.get_attributes_patch.stop)

        patch_get_mac_vendor = mock.patch('tvui.reports.AssetInventory.get_mac_vendor', return_value=None)

        self.get_mac_vendor = patch_get_mac_vendor.start()
        self.addCleanup(patch_get_mac_vendor.stop)

    def test_get_general_data(self):
        with freeze_time(timezone.now().replace(second=0, microsecond=0)):
            asset_inventory_report = AssetInventoryReportDef(
                from_date=self.from_date, to_date=self.to_date, name='Test Report', created_by=self.user, frequency='on_demand'
            )
            report = AssetInventoryReport(asset_inventory_report)
            page_report = report.get_context()

            devices_observed = page_report['general_data']
            exp_result = {
                'created_by': self.user.username,
                'created_datetime': timezone.now(),
                'from_datetime': self.from_date,
                'to_datetime': self.to_date,
                'report_name': 'Test Report',
            }

            self.assertEqual(devices_observed, exp_result)

    def test_get_devices_observed(self):
        self.get_mac_vendor.return_value = 'Xerox Corp'
        page_report = self.asset_inventory_report.get_context()
        devices_observed = page_report['devices_observed']
        exp_result = [
            {
                "title": "Xerox Corp",
                "subnets": [
                    "1.2.5.0/24",
                    "1.2.6.0/24",
                    "1.2.7.0/24",
                    "1.2.8.0/24",
                    "1.2.9.0/24",
                    "1.2.10.0/24",
                    "1.2.11.0/24",
                    "1.2.12.0/24",
                    "1.2.13.0/24",
                    "1.2.14.0/24",
                ],
                "count": 10,
            },
            {
                "title": "No MAC artifacts",
                "subnets": [
                    "1.2.0.0/24",
                    "1.2.1.0/24",
                    "1.2.2.0/24",
                    "1.2.3.0/24",
                    "1.2.4.0/24",
                    "1.2.15.0/24",
                    "1.2.16.0/24",
                    "1.2.17.0/24",
                    "1.2.18.0/24",
                    "1.2.19.0/24",
                ],
                "count": 10,
            },
        ]

        self.assertEqual(devices_observed, exp_result)

    def test_get_privileged_accounts(self):
        with freeze_time(self.yesterday):
            now = timezone.now().replace(second=0, microsecond=0)

            _accounts = [
                Account.objects.create(
                    uid='a{}'.format(i), account_type=Account.TYPE_KERBEROS, priv_level=i, priv_level_date=now + timedelta(hours=i)
                )
                for i in range(1, 10)
            ]

            _accounts2 = [
                Account.objects.create(
                    uid='b{}'.format(i), account_type=Account.TYPE_KERBEROS, priv_level=i, priv_level_date=now + timedelta(hours=10 + i)
                )
                for i in range(1, 10)
            ]

            # create old priv history to confirm newer score is used
            PrivHistory.objects.create(
                account=_accounts[0], priv_level=5, created_date=timezone.now() + timedelta(hours=-10), type=PrivHistory.ACCOUNT
            )

            history = [
                PrivHistory.objects.create(account=_accounts[i], priv_level=i + 1, created_date=timezone.now(), type=PrivHistory.ACCOUNT)
                for i in range(9)
            ]

            history = [
                PrivHistory.objects.create(account=_accounts2[i], priv_level=i + 1, created_date=timezone.now(), type=PrivHistory.ACCOUNT)
                for i in range(9)
            ]

            # create score history out of bounds of to date in report to make sure only scores within range are used
            PrivHistory.objects.create(
                account=_accounts[1], priv_level=5, created_date=timezone.now() + timedelta(days=10), type=PrivHistory.ACCOUNT
            )

            # make sure 0 score account doesn't show up
            PrivHistory.objects.create(
                account=_accounts[1], priv_level=5, created_date=timezone.now() + timedelta(days=10), type=PrivHistory.ACCOUNT
            )

        # these accounts should not show up in the report
        with freeze_time(self.future_date):
            _accounts3 = [
                Account.objects.create(
                    uid='c{}'.format(i), account_type=Account.TYPE_KERBEROS, priv_level=i, priv_level_date=now + timedelta(hours=10 + i)
                )
                for i in range(1, 10)
            ]

            not_included_history = [
                PrivHistory.objects.create(account=_accounts3[i], priv_level=i, created_date=timezone.now(), type=PrivHistory.ACCOUNT)
                for i in range(9)
            ]

        page_report = self.asset_inventory_report.get_context()
        privileged_accounts = page_report['privileged_accounts']
        exp_result = [
            {'uid': 'a9', 'priv_level': 9, 'priv_level_date': self.yesterday},
            {'uid': 'b9', 'priv_level': 9, 'priv_level_date': self.yesterday},
            {'uid': 'a8', 'priv_level': 8, 'priv_level_date': self.yesterday},
            {'uid': 'b8', 'priv_level': 8, 'priv_level_date': self.yesterday},
            {'uid': 'a7', 'priv_level': 7, 'priv_level_date': self.yesterday},
            {'uid': 'b7', 'priv_level': 7, 'priv_level_date': self.yesterday},
            {'uid': 'a6', 'priv_level': 6, 'priv_level_date': self.yesterday},
            {'uid': 'b6', 'priv_level': 6, 'priv_level_date': self.yesterday},
            {'uid': 'a5', 'priv_level': 5, 'priv_level_date': self.yesterday},
            {'uid': 'b5', 'priv_level': 5, 'priv_level_date': self.yesterday},
            {'uid': 'a4', 'priv_level': 4, 'priv_level_date': self.yesterday},
            {'uid': 'b4', 'priv_level': 4, 'priv_level_date': self.yesterday},
            {'uid': 'a3', 'priv_level': 3, 'priv_level_date': self.yesterday},
            {'uid': 'b3', 'priv_level': 3, 'priv_level_date': self.yesterday},
            {'uid': 'a2', 'priv_level': 2, 'priv_level_date': self.yesterday},
            {'uid': 'b2', 'priv_level': 2, 'priv_level_date': self.yesterday},
            {'uid': 'a1', 'priv_level': 1, 'priv_level_date': self.yesterday},
            {'uid': 'b1', 'priv_level': 1, 'priv_level_date': self.yesterday},
        ]

        self.assertEqual(privileged_accounts, exp_result)

    def test_get_privileged_accounts_max_limit(self):
        with freeze_time(self.yesterday):
            now = timezone.now().replace(second=0, microsecond=0)

            _accounts = [
                Account.objects.create(uid='a{}'.format(i), account_type=Account.TYPE_KERBEROS, priv_level=5, priv_level_date=now)
                for i in range(50)
            ]

            history = [
                PrivHistory.objects.create(
                    account=_accounts[i], priv_level=5 if i < 25 else 1, created_date=timezone.now(), type=PrivHistory.ACCOUNT
                )
                for i in range(50)
            ]

        page_report = self.asset_inventory_report.get_context()
        privileged_accounts = page_report['privileged_accounts']

        # make sure it picked the top priv accounts; lower half have priv level of 1; top half have priv level 5
        for acc in privileged_accounts:
            self.assertEqual(acc['priv_level'], 5)

        self.assertEqual(len(privileged_accounts), 25)

    def test_get_privileged_accounts_exclude_zero_score(self):
        with freeze_time(self.yesterday):
            now = timezone.now().replace(second=0, microsecond=0)

            _accounts = [
                Account.objects.create(uid='a{}'.format(i), account_type=Account.TYPE_KERBEROS, priv_level=5, priv_level_date=now)
                for i in range(10)
            ]

            history = [
                PrivHistory.objects.create(
                    account=_accounts[i],
                    priv_level=5 if i < 25 else 1,
                    created_date=timezone.now() + timedelta(hours=-1),
                    type=PrivHistory.ACCOUNT,
                )
                for i in range(10)
            ]

            # first account has 0 priv level now; not privileged
            PrivHistory.objects.create(account=_accounts[0], priv_level=0, created_date=timezone.now(), type=PrivHistory.ACCOUNT)

        page_report = self.asset_inventory_report.get_context()
        privileged_accounts = page_report['privileged_accounts']

        # make sure it picked the top priv accounts; lower half have priv level of 1; top half have priv level 5
        for acc in privileged_accounts:
            self.assertEqual(acc['priv_level'], 5)

        self.assertEqual(len(privileged_accounts), 9)

    def test_get_privileged_accounts_no_accounts(self):
        page_report = self.asset_inventory_report.get_context()
        privileged_accounts = page_report['privileged_accounts']

        self.assertEqual(len(privileged_accounts), 0)

    def test_get_privileged_hosts(self):
        with freeze_time(self.yesterday):
            _hosts = [host.objects.create(name='a{}'.format(i)) for i in range(1, 10)]

            _hosts2 = [host.objects.create(name='b{}'.format(i)) for i in range(1, 10)]

            # create old priv history to confirm newer score is used
            PrivHistory.objects.create(
                host=_hosts[0], priv_level=5, created_date=timezone.now() + timedelta(hours=-10), type=PrivHistory.HOST
            )

            history = [
                PrivHistory.objects.create(host=_hosts[i], priv_level=i + 1, created_date=timezone.now(), type=PrivHistory.HOST)
                for i in range(9)
            ]

            history = [
                PrivHistory.objects.create(host=_hosts2[i], priv_level=i + 1, created_date=timezone.now(), type=PrivHistory.HOST)
                for i in range(9)
            ]

            # create score history out of bounds of to date in report to make sure only scores within range are used
            PrivHistory.objects.create(
                host=_hosts[1], priv_level=5, created_date=timezone.now() + timedelta(days=10), type=PrivHistory.HOST
            )

            # make sure 0 score host doesn't show up
            PrivHistory.objects.create(
                host=_hosts[1], priv_level=5, created_date=timezone.now() + timedelta(days=10), type=PrivHistory.HOST
            )

        # these hosts should not show up in the report
        with freeze_time(self.future_date):
            _hosts3 = [host.objects.create(name='c{}'.format(i)) for i in range(1, 10)]

            not_included_history = [
                PrivHistory.objects.create(host=_hosts3[i], priv_level=i, created_date=timezone.now(), type=PrivHistory.HOST)
                for i in range(9)
            ]

        page_report = self.asset_inventory_report.get_context()
        privileged_hosts = page_report['privileged_hosts']
        exp_result = [
            {'name': 'a9', 'priv_level': 9, 'priv_level_date': self.yesterday},
            {'name': 'b9', 'priv_level': 9, 'priv_level_date': self.yesterday},
            {'name': 'a8', 'priv_level': 8, 'priv_level_date': self.yesterday},
            {'name': 'b8', 'priv_level': 8, 'priv_level_date': self.yesterday},
            {'name': 'a7', 'priv_level': 7, 'priv_level_date': self.yesterday},
            {'name': 'b7', 'priv_level': 7, 'priv_level_date': self.yesterday},
            {'name': 'a6', 'priv_level': 6, 'priv_level_date': self.yesterday},
            {'name': 'b6', 'priv_level': 6, 'priv_level_date': self.yesterday},
            {'name': 'a5', 'priv_level': 5, 'priv_level_date': self.yesterday},
            {'name': 'b5', 'priv_level': 5, 'priv_level_date': self.yesterday},
            {'name': 'a4', 'priv_level': 4, 'priv_level_date': self.yesterday},
            {'name': 'b4', 'priv_level': 4, 'priv_level_date': self.yesterday},
            {'name': 'a3', 'priv_level': 3, 'priv_level_date': self.yesterday},
            {'name': 'b3', 'priv_level': 3, 'priv_level_date': self.yesterday},
            {'name': 'a2', 'priv_level': 2, 'priv_level_date': self.yesterday},
            {'name': 'b2', 'priv_level': 2, 'priv_level_date': self.yesterday},
            {'name': 'a1', 'priv_level': 1, 'priv_level_date': self.yesterday},
            {'name': 'b1', 'priv_level': 1, 'priv_level_date': self.yesterday},
        ]

        self.assertEqual(privileged_hosts, exp_result)

    def test_get_privileged_hosts_max_limit(self):
        with freeze_time(self.yesterday):
            _hosts = [host.objects.create(name='a{}'.format(i)) for i in range(50)]

            history = [
                PrivHistory.objects.create(
                    host=_hosts[i], priv_level=5 if i < 25 else 1, created_date=timezone.now(), type=PrivHistory.HOST
                )
                for i in range(50)
            ]

        page_report = self.asset_inventory_report.get_context()
        privileged_hosts = page_report['privileged_hosts']

        # make sure it picked the top priv hosts; lower half have priv level of 1; top half have priv level 5
        for _host in privileged_hosts:
            self.assertEqual(_host['priv_level'], 5)

        self.assertEqual(len(privileged_hosts), 25)

    def test_get_privileged_hosts_exclude_zero_score(self):
        with freeze_time(self.yesterday):
            _hosts = [host.objects.create(name='a{}'.format(i)) for i in range(10)]

            history = [
                PrivHistory.objects.create(
                    host=_hosts[i], priv_level=5 if i < 25 else 1, created_date=timezone.now() + timedelta(hours=-1), type=PrivHistory.HOST
                )
                for i in range(10)
            ]

            # first host has 0 priv level now; not privileged
            PrivHistory.objects.create(host=_hosts[0], priv_level=0, created_date=timezone.now(), type=PrivHistory.HOST)

        page_report = self.asset_inventory_report.get_context()
        privileged_hosts = page_report['privileged_hosts']

        # make sure it picked the top priv hosts; lower half have priv level of 1; top half have priv level 5
        for _host in privileged_hosts:
            self.assertEqual(_host['priv_level'], 5)

        self.assertEqual(len(privileged_hosts), 9)

    def test_get_privileged_hosts_no_hosts(self):
        page_report = self.asset_inventory_report.get_context()
        privileged_hosts = page_report['privileged_hosts']

        self.assertEqual(len(privileged_hosts), 0)

    def test_get_privileged_services(self):
        with freeze_time(self.yesterday):
            _services = [Service.objects.create(uid='a{}'.format(i)) for i in range(1, 10)]

            _services2 = [Service.objects.create(uid='b{}'.format(i)) for i in range(1, 10)]

            # create old priv history to confirm newer score is used
            PrivHistory.objects.create(
                service=_services[0], priv_level=5, created_date=timezone.now() + timedelta(hours=-10), type=PrivHistory.SERVICE
            )

            history = [
                PrivHistory.objects.create(service=_services[i], priv_level=i + 1, created_date=timezone.now(), type=PrivHistory.SERVICE)
                for i in range(9)
            ]

            history = [
                PrivHistory.objects.create(service=_services2[i], priv_level=i + 1, created_date=timezone.now(), type=PrivHistory.SERVICE)
                for i in range(9)
            ]

            # create score history out of bounds of to date in report to make sure only scores within range are used
            PrivHistory.objects.create(
                service=_services[1], priv_level=5, created_date=timezone.now() + timedelta(days=10), type=PrivHistory.SERVICE
            )

            # make sure 0 score service doesn't show up
            PrivHistory.objects.create(
                service=_services[1], priv_level=5, created_date=timezone.now() + timedelta(days=10), type=PrivHistory.SERVICE
            )

        # these services should not show up in the report
        with freeze_time(self.future_date):
            _services3 = [Service.objects.create(uid='c{}'.format(i)) for i in range(1, 10)]

            not_included_history = [
                PrivHistory.objects.create(service=_services3[i], priv_level=i, created_date=timezone.now(), type=PrivHistory.SERVICE)
                for i in range(9)
            ]

        page_report = self.asset_inventory_report.get_context()
        privileged_services = page_report['privileged_services']
        exp_result = [
            {'uid': 'a9', 'priv_level': 9, 'priv_level_date': self.yesterday},
            {'uid': 'b9', 'priv_level': 9, 'priv_level_date': self.yesterday},
            {'uid': 'a8', 'priv_level': 8, 'priv_level_date': self.yesterday},
            {'uid': 'b8', 'priv_level': 8, 'priv_level_date': self.yesterday},
            {'uid': 'a7', 'priv_level': 7, 'priv_level_date': self.yesterday},
            {'uid': 'b7', 'priv_level': 7, 'priv_level_date': self.yesterday},
            {'uid': 'a6', 'priv_level': 6, 'priv_level_date': self.yesterday},
            {'uid': 'b6', 'priv_level': 6, 'priv_level_date': self.yesterday},
            {'uid': 'a5', 'priv_level': 5, 'priv_level_date': self.yesterday},
            {'uid': 'b5', 'priv_level': 5, 'priv_level_date': self.yesterday},
            {'uid': 'a4', 'priv_level': 4, 'priv_level_date': self.yesterday},
            {'uid': 'b4', 'priv_level': 4, 'priv_level_date': self.yesterday},
            {'uid': 'a3', 'priv_level': 3, 'priv_level_date': self.yesterday},
            {'uid': 'b3', 'priv_level': 3, 'priv_level_date': self.yesterday},
            {'uid': 'a2', 'priv_level': 2, 'priv_level_date': self.yesterday},
            {'uid': 'b2', 'priv_level': 2, 'priv_level_date': self.yesterday},
            {'uid': 'a1', 'priv_level': 1, 'priv_level_date': self.yesterday},
            {'uid': 'b1', 'priv_level': 1, 'priv_level_date': self.yesterday},
        ]

        self.assertEqual(privileged_services, exp_result)

    def test_get_privileged_services_max_limit(self):
        with freeze_time(self.yesterday):
            _services = [Service.objects.create(uid='a{}'.format(i)) for i in range(50)]

            history = [
                PrivHistory.objects.create(
                    service=_services[i], priv_level=5 if i < 25 else 1, created_date=timezone.now(), type=PrivHistory.SERVICE
                )
                for i in range(50)
            ]

        page_report = self.asset_inventory_report.get_context()
        privileged_services = page_report['privileged_services']

        # make sure it picked the top priv hosts; lower half have priv level of 1; top half have priv level 5
        for _host in privileged_services:
            self.assertEqual(_host['priv_level'], 5)

        self.assertEqual(len(privileged_services), 25)

    def test_get_privileged_services_exclude_zero_score(self):
        with freeze_time(self.yesterday):
            _services = [Service.objects.create(uid='a{}'.format(i)) for i in range(10)]

            history = [
                PrivHistory.objects.create(
                    service=_services[i],
                    priv_level=5 if i < 25 else 1,
                    created_date=timezone.now() + timedelta(hours=-1),
                    type=PrivHistory.SERVICE,
                )
                for i in range(10)
            ]

            # first service has 0 priv level now; not privileged
            PrivHistory.objects.create(service=_services[0], priv_level=0, created_date=timezone.now(), type=PrivHistory.SERVICE)

        page_report = self.asset_inventory_report.get_context()
        privileged_services = page_report['privileged_services']

        # make sure it picked the top priv hosts; lower half have priv level of 1; top half have priv level 5
        for _host in privileged_services:
            self.assertEqual(_host['priv_level'], 5)

        self.assertEqual(len(privileged_services), 9)

    def test_get_privileged_services_no_hosts(self):
        page_report = self.asset_inventory_report.get_context()
        privileged_services = page_report['privileged_services']

        self.assertEqual(len(privileged_services), 0)

    def test_no_created_by(self):
        asset_inventory_report = AssetInventoryReport(
            AssetInventoryReportDef(from_date=self.from_date, to_date=self.to_date, name='Test Report', frequency='on_demand')
        )

        # this should not raise an error
        page_report = asset_inventory_report.get_context()

        self.assertEqual(page_report['general_data']['created_by'], 'Unknown user')

    @mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
    def test_get_endpoint_coverage(self, mock_get_attributes):
        mock_get_attributes.return_value = []
        host_session.objects.all().delete()
        # add host_sessions, scores, and priv history for all current hosts
        for i, _host in enumerate(self._hosts[:-2]):
            host_session.objects.create(
                host=_host, start=self.yesterday, session_luid='{}ab'.format(_host.id), ip_address='1.2.{}.5'.format(_host.id)
            )

            PrivHistory.objects.create(host=_host, created_date=self.yesterday, priv_level=i % 10)

            score.objects.create(host=_host, timestamp=self.yesterday, threat_score=(i * 10) % 100, confidence_score=(i * 11) % 100)

        # host_sessions that overlap the report range entirely;
        # this one starts before and ends after the report range
        long_host = self._hosts[-1]
        PrivHistory.objects.create(host=long_host, created_date=self.yesterday, priv_level=10)  # to show up in top list
        host_session.objects.create(
            host=long_host,
            start=self.old_date,
            end=self.future_date,
            session_luid=f'asd{long_host.id}',
            ip_address=f'1.2.{long_host.id}.5',
        )

        # this one starts before the report range and has not yet ended
        long_ongoing_host = self._hosts[-2]
        PrivHistory.objects.create(host=long_ongoing_host, created_date=self.yesterday, priv_level=10)  # to show up in top list
        host_session.objects.create(
            host=long_ongoing_host,
            start=self.old_date,
            end=None,
            session_luid=f'asdf{long_ongoing_host.id}',
            ip_address=f'1.2.{long_ongoing_host.id}.5',
        )

        # create EDR artifacts for some hosts
        host_artifact.objects.create(host=self._hosts[0], type='crowdstrike', value='asdf')
        host_artifact.objects.create(host=self._hosts[1], type='crowdstrike', value='asdf')
        host_artifact.objects.create(host=self._hosts[2], type='crowdstrike', value='asdf')
        host_artifact.objects.create(host=self._hosts[3], type='crowdstrike', value='asdf')

        host_artifact.objects.create(host=self._hosts[3], type='carbon_black', value='asdf')
        host_artifact.objects.create(host=self._hosts[4], type='carbon_black', value='asdf')

        host_artifact.objects.create(host=self._hosts[5], type='windows_defender', value='asdf')

        context = self.asset_inventory_report._get_endpoint_coverage()

        expected_context = [
            {
                'title': 'No EDR Coverage',
                'count': 14,
                'top_hosts': [
                    {'priv_level': 10, 'dfz_score': 0, 'name': 'testhost19', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                    {'priv_level': 10, 'dfz_score': 0, 'name': 'testhost18', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                    {'priv_level': 9, 'dfz_score': 134, 'name': 'testhost9', 't_score': 90, 'c_score': 99, 'urgency_score': 0},
                    {'priv_level': 8, 'dfz_score': 119, 'name': 'testhost8', 't_score': 80, 'c_score': 88, 'urgency_score': 0},
                    {'priv_level': 7, 'dfz_score': 112, 'name': 'testhost17', 't_score': 70, 'c_score': 87, 'urgency_score': 0},
                    {'priv_level': 7, 'dfz_score': 104, 'name': 'testhost7', 't_score': 70, 'c_score': 77, 'urgency_score': 0},
                    {'priv_level': 6, 'dfz_score': 97, 'name': 'testhost16', 't_score': 60, 'c_score': 76, 'urgency_score': 0},
                    {'priv_level': 6, 'dfz_score': 89, 'name': 'testhost6', 't_score': 60, 'c_score': 66, 'urgency_score': 0},
                    {'priv_level': 5, 'dfz_score': 82, 'name': 'testhost15', 't_score': 50, 'c_score': 65, 'urgency_score': 0},
                    {'priv_level': 4, 'dfz_score': 67, 'name': 'testhost14', 't_score': 40, 'c_score': 54, 'urgency_score': 0},
                ],
                'percent': 70.0,
            },
            {
                'title': 'Carbon Black',
                'count': 2,
                'top_hosts': [
                    {'priv_level': 4, 'dfz_score': 59, 'name': 'testhost4', 't_score': 40, 'c_score': 44, 'urgency_score': 0},
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                ],
                'percent': 10.0,
            },
            {
                'title': 'CrowdStrike',
                'count': 4,
                'top_hosts': [
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 2, 'dfz_score': 30, 'name': 'testhost2', 't_score': 20, 'c_score': 22, 'urgency_score': 0},
                    {'priv_level': 1, 'dfz_score': 15, 'name': 'testhost1', 't_score': 10, 'c_score': 11, 'urgency_score': 0},
                    {'priv_level': 0, 'dfz_score': 0, 'name': 'testhost0', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                ],
                'percent': 20.0,
            },
            {
                'title': 'Microsoft Defender ATP',
                'count': 1,
                'top_hosts': [{'priv_level': 5, 'dfz_score': 74, 'name': 'testhost5', 't_score': 50, 'c_score': 55, 'urgency_score': 0}],
                'percent': 5.0,
            },
        ]

        self.assertEqual(context, expected_context)

        # test that the endpoints don't get entries if there is no coverage for them
        host_artifact.objects.filter(type='windows_defender').delete()

        context = self.asset_inventory_report._get_endpoint_coverage()
        self.assertTrue(not any(entry['title'] == 'Microsoft Defender ATP' for entry in context))

    @mock.patch('tvui.reports.AssetInventoryReport._get_roles_observed')
    @mock.patch('tvui.reports.AssetInventoryReport._get_privileged_entities')
    @mock.patch('tvui.reports.AssetInventoryReport._get_devices_observed')
    @mock.patch('tvui.reports.AssetInventoryReport._get_endpoint_coverage')
    @mock.patch('tvui.reports.AssetInventoryReport._get_general_data')
    def test_exceptions(self, mock_get_general_data, mock_edr_coverage, mock_devices_observed, mock_priv_entities, mock_roles_observed):
        mock_get_general_data.side_effect = Exception
        mock_edr_coverage.side_effect = Exception
        mock_devices_observed.side_effect = Exception
        mock_priv_entities.side_effect = Exception
        mock_roles_observed.side_effect = Exception
        context = self.asset_inventory_report.get_context()
        exp_results = {
            'unified_prioritization': False,
            'roles_observed': [],
            'endpoints': [],
            'devices_observed': [],
            'privileged_accounts': [],
            'privileged_hosts': [],
            'privileged_services': [],
            'general_data': {},
        }

        self.assertEqual(context, exp_results)

    @mock.patch('tvui.reports.AssetInventoryReport._get_edr_ids')
    @mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
    def test_observed_roles_using_HAPI(self, mock_get_attributes, mock_get_edr_ids):
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "off"})
        mock_get_edr_ids.return_value = defaultdict(list), set()
        mock_get_attributes.return_value = [
            {"value": "DHCP Server", "host_luid": "luid0", "role_display_name": "DHCP Server"},
            {"value": "Proxy Server", "host_luid": "luid1", "role_display_name": "Proxy Server"},
            {"value": "Proxy Server", "host_luid": "luid2", "role_display_name": "Proxy Server"},
            {"value": "Proxy Server", "host_luid": "luid3", "role_display_name": "Proxy Server"},
            {"value": "Proxy Server", "host_luid": "luid4", "role_display_name": "Proxy Server"},
            {"value": "Proxy Server", "host_luid": "luid5", "role_display_name": "Proxy Server"},
            {"value": "Domain Controller Server", "host_luid": "luid6", "role_display_name": "Domain Controller Server"},
            {"value": "Domain Controller Server", "host_luid": "luid7", "role_display_name": "Domain Controller Server"},
            {"value": "Domain Controller Server", "host_luid": "luid8", "role_display_name": "Domain Controller Server"},
            {"value": "Domain Controller Server", "host_luid": "luid9", "role_display_name": "Domain Controller Server"},
            {"value": "DHCP Server", "host_luid": "luid9", "role_display_name": "DHCP Server"},
        ]
        host.objects.all().delete()
        _hosts = [host.objects.create(name='host {}'.format(i), host_luid='luid{}'.format(i), t_score=i, c_score=i) for i in range(10)]

        roles = self.asset_inventory_report.get_context()['roles_observed']
        exp_results = [
            {
                'hosts': [
                    {'name': 'host 5', 't_score': 5, 'c_score': 5, 'urgency_score': 0},
                    {'name': 'host 4', 't_score': 4, 'c_score': 4, 'urgency_score': 0},
                    {'name': 'host 3', 't_score': 3, 'c_score': 3, 'urgency_score': 0},
                    {'name': 'host 2', 't_score': 2, 'c_score': 2, 'urgency_score': 0},
                    {'name': 'host 1', 't_score': 1, 'c_score': 1, 'urgency_score': 0},
                ],
                'name': 'Proxy Server',
                'count': 5,
            },
            {
                'hosts': [
                    {'name': 'host 9', 't_score': 9, 'c_score': 9, 'urgency_score': 0},
                    {'name': 'host 8', 't_score': 8, 'c_score': 8, 'urgency_score': 0},
                    {'name': 'host 7', 't_score': 7, 'c_score': 7, 'urgency_score': 0},
                    {'name': 'host 6', 't_score': 6, 'c_score': 6, 'urgency_score': 0},
                ],
                'name': 'Domain Controller Server',
                'count': 4,
            },
            {
                'hosts': [
                    {'name': 'host 9', 't_score': 9, 'c_score': 9, 'urgency_score': 0},
                    {'name': 'host 0', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                ],
                'name': 'DHCP Server',
                'count': 2,
            },
        ]

        self.assertEqual(roles, exp_results)

    @mock.patch('tvui.reports.AssetInventoryReport._get_edr_ids')
    @mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
    def test_observed_no_roles_using_HAPI(self, mock_get_attributes, mock_get_edr_ids):
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "off"})
        mock_get_edr_ids.return_value = defaultdict(list), set()
        mock_get_attributes.return_value = []
        host.objects.all().delete()
        _hosts = [host.objects.create(name='host {}'.format(i), host_luid='luid{}'.format(i), t_score=i, c_score=i) for i in range(10)]

        roles = self.asset_inventory_report.get_context()['roles_observed']
        exp_results = []

        self.assertEqual(roles, exp_results)

    @mock.patch('tvui.reports.AssetInventoryReport._get_edr_ids')
    def test_observed_roles_using_host_role_table(self, mock_get_edr_ids):
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "on"})
        mock_get_edr_ids.return_value = defaultdict(list), set()
        host.objects.all().delete()
        _hosts = [host.objects.create(name='host {}'.format(i), host_luid='luid{}'.format(i), t_score=i, c_score=i) for i in range(3)]

        HostRole.objects.create(role_name='Web Server', source='detection_event', host_id=_hosts[0].id, date_last_seen=self.old_date)
        HostRole.objects.create(role_name='DNS Server', source='detection_event', host_id=_hosts[1].id, date_last_seen=self.from_date)
        HostRole.objects.create(
            role_name='DHCP Server', source='detection_event', host_id=_hosts[2].id, date_last_seen=self.from_date + timedelta(days=10)
        )
        exp_results = [
            {
                'hosts': [
                    {'name': 'host 1', 't_score': 1, 'c_score': 1, 'urgency_score': 0},
                ],
                'name': 'DNS Server',
                'count': 1,
            },
            {
                'hosts': [
                    {'name': 'host 2', 't_score': 2, 'c_score': 2, 'urgency_score': 0},
                ],
                'name': 'DHCP Server',
                'count': 1,
            },
        ]
        asset_inventory_report_test = AssetInventoryReport(
            AssetInventoryReportDef(
                from_date=self.from_date, to_date=self.to_date, name='Test Report', created_by=self.user, frequency='on_demand'
            )
        )
        roles = asset_inventory_report_test.get_context()['roles_observed']
        self.assertEqual(roles, exp_results)

    @mock.patch('tvui.reports.AssetInventoryReport._get_edr_ids')
    def test_observed_no_roles_using_host_role_table(self, mock_get_edr_ids):
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "on"})
        mock_get_edr_ids.return_value = defaultdict(list), set()
        host.objects.all().delete()
        _hosts = [host.objects.create(name='host {}'.format(i), host_luid='luid{}'.format(i), t_score=i, c_score=i) for i in range(3)]

        exp_results = []
        asset_inventory_report_test = AssetInventoryReport(
            AssetInventoryReportDef(
                from_date=self.from_date, to_date=self.to_date, name='Test Report', created_by=self.user, frequency='on_demand'
            )
        )
        roles = asset_inventory_report_test.get_context()['roles_observed']
        self.assertEqual(roles, exp_results)

    @mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
    def test_edr_id_pagination(self, mock_get_attributes):
        mock_get_attributes.side_effect = [
            [
                {'id': 1, 'host_luid': 'luid1', 'value': 'abc', 'edr_display_name': "BitDefender"},
                {'id': 2, 'host_luid': 'luid2', 'value': 'abc2', 'edr_display_name': "Fireeye"},
            ],
            [{'id': 3, 'host_luid': 'luid3', 'value': 'abc3', 'edr_display_name': "Carbon Black"}],
            [
                {'id': 4, 'host_luid': 'luid4', 'value': 'abc4', 'edr_display_name': "BitDefender"},
                {'id': 5, 'host_luid': 'luid1', 'value': 'abc4', 'edr_display_name': "Fireeye"},
            ],
            [],
        ]
        edr_ids, edr_id_types = self.asset_inventory_report._get_edr_ids()

        # confirm calls have correct args
        mock_get_attributes.assert_has_calls(
            [
                mock.call('host_edr', limit=1000, id_gt=0),
                mock.call('host_edr', limit=1000, id_gt=2),
                mock.call('host_edr', limit=1000, id_gt=3),
                mock.call('host_edr', limit=1000, id_gt=5),
            ]
        )

        # confirm result is correct
        edr_ids = dict(edr_ids)
        exp_edr_ids = {'luid4': ['BitDefender'], 'luid2': ['Fireeye'], 'luid3': ['Carbon Black'], 'luid1': ['BitDefender', 'Fireeye']}
        self.assertEqual(edr_ids, exp_edr_ids)

        exp_edr_id_types = {'Carbon Black', 'Fireeye', 'BitDefender'}
        self.assertEqual(edr_id_types, exp_edr_id_types)

    @mock.patch('tvui.reports.AssetInventoryReport._get_edr_ids')
    @mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
    def test_endpoint_coverage_with_edr_id(self, mock_get_attributes, mock_get_edr_ids):
        mocked_edrs = defaultdict(list)
        mocked_edrs.update(
            {
                self._hosts[0].host_luid: ['BitDefender', 'CrowdStrike'],
                self._hosts[1].host_luid: ['Fireeye'],
                self._hosts[2].host_luid: ['Carbon Black'],
                self._hosts[3].host_luid: ['BitDefender', 'Fireeye'],
            }
        )
        mocked_edr_id_types = {'Carbon Black', 'Fireeye', 'BitDefender'}
        mock_get_attributes.return_value = []
        mock_get_edr_ids.return_value = mocked_edrs, mocked_edr_id_types

        # add host_sessions, scores, and priv history for all current hosts
        for i, _host in enumerate(self._hosts):
            host_session.objects.create(
                host=_host, start=self.yesterday, session_luid='as{}'.format(_host.id), ip_address='1.2.{}.5'.format(_host.id)
            )

            PrivHistory.objects.create(host=_host, created_date=self.yesterday, priv_level=i % 10)

            score.objects.create(host=_host, timestamp=self.yesterday, threat_score=(i * 10) % 100, confidence_score=(i * 11) % 100)

        # create EDR artifacts for some hosts
        host_artifact.objects.create(host=self._hosts[0], type='crowdstrike', value='asdf')
        host_artifact.objects.create(host=self._hosts[1], type='crowdstrike', value='asdf')
        host_artifact.objects.create(host=self._hosts[2], type='crowdstrike', value='asdf')
        host_artifact.objects.create(host=self._hosts[3], type='crowdstrike', value='asdf')

        host_artifact.objects.create(host=self._hosts[3], type='carbon_black', value='asdf')
        host_artifact.objects.create(host=self._hosts[4], type='carbon_black', value='asdf')

        host_artifact.objects.create(host=self._hosts[5], type='windows_defender', value='asdf')

        context = self.asset_inventory_report._get_endpoint_coverage()

        expected_context = [
            {
                'title': 'No EDR Coverage',
                'count': 14,
                'top_hosts': [
                    {'priv_level': 9, 'dfz_score': 134, 'name': 'testhost9', 't_score': 90, 'c_score': 99, 'urgency_score': 0},
                    {'priv_level': 9, 'dfz_score': 90, 'name': 'testhost19', 't_score': 90, 'c_score': 9, 'urgency_score': 0},
                    {'priv_level': 8, 'dfz_score': 127, 'name': 'testhost18', 't_score': 80, 'c_score': 98, 'urgency_score': 0},
                    {'priv_level': 8, 'dfz_score': 119, 'name': 'testhost8', 't_score': 80, 'c_score': 88, 'urgency_score': 0},
                    {'priv_level': 7, 'dfz_score': 112, 'name': 'testhost17', 't_score': 70, 'c_score': 87, 'urgency_score': 0},
                    {'priv_level': 7, 'dfz_score': 104, 'name': 'testhost7', 't_score': 70, 'c_score': 77, 'urgency_score': 0},
                    {'priv_level': 6, 'dfz_score': 97, 'name': 'testhost16', 't_score': 60, 'c_score': 76, 'urgency_score': 0},
                    {'priv_level': 6, 'dfz_score': 89, 'name': 'testhost6', 't_score': 60, 'c_score': 66, 'urgency_score': 0},
                    {'priv_level': 5, 'dfz_score': 82, 'name': 'testhost15', 't_score': 50, 'c_score': 65, 'urgency_score': 0},
                    {'priv_level': 4, 'dfz_score': 67, 'name': 'testhost14', 't_score': 40, 'c_score': 54, 'urgency_score': 0},
                ],
                'percent': 70.0,
            },
            {
                'title': 'BitDefender',
                'count': 2,
                'top_hosts': [
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 0, 'dfz_score': 0, 'name': 'testhost0', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                ],
                'percent': 10.0,
            },
            {
                'title': 'Carbon Black',
                'count': 3,
                'top_hosts': [
                    {'priv_level': 4, 'dfz_score': 59, 'name': 'testhost4', 't_score': 40, 'c_score': 44, 'urgency_score': 0},
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 2, 'dfz_score': 30, 'name': 'testhost2', 't_score': 20, 'c_score': 22, 'urgency_score': 0},
                ],
                'percent': 15.0,
            },
            {
                'title': 'CrowdStrike',
                'count': 4,
                'top_hosts': [
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 2, 'dfz_score': 30, 'name': 'testhost2', 't_score': 20, 'c_score': 22, 'urgency_score': 0},
                    {'priv_level': 1, 'dfz_score': 15, 'name': 'testhost1', 't_score': 10, 'c_score': 11, 'urgency_score': 0},
                    {'priv_level': 0, 'dfz_score': 0, 'name': 'testhost0', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                ],
                'percent': 20.0,
            },
            {
                'title': 'Fireeye',
                'count': 2,
                'top_hosts': [
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 1, 'dfz_score': 15, 'name': 'testhost1', 't_score': 10, 'c_score': 11, 'urgency_score': 0},
                ],
                'percent': 10.0,
            },
            {
                'title': 'Microsoft Defender ATP',
                'count': 1,
                'top_hosts': [{'priv_level': 5, 'dfz_score': 74, 'name': 'testhost5', 't_score': 50, 'c_score': 55, 'urgency_score': 0}],
                'percent': 5.0,
            },
        ]

        self.assertEqual(context, expected_context)

    @mock.patch('tvui.reports.AssetInventoryReport._get_edr_ids')
    @mock.patch('tvui.reports.AssetInventory.HostAttributeAPI.get_attributes')
    def test_endpoint_coverage_no_artifacts(self, mock_get_attributes, mock_get_edr_ids):
        mocked_edrs = defaultdict(list)
        mocked_edrs.update(
            {
                self._hosts[0].host_luid: ['BitDefender'],
                self._hosts[1].host_luid: ['Fireeye'],
                self._hosts[2].host_luid: ['Carbon Black'],
                self._hosts[3].host_luid: ['BitDefender', 'Fireeye'],
            }
        )
        mocked_edr_id_types = {'Carbon Black', 'Fireeye', 'BitDefender'}
        mock_get_attributes.return_value = []
        mock_get_edr_ids.return_value = mocked_edrs, mocked_edr_id_types

        # add host_sessions, scores, and priv history for all current hosts
        for i, _host in enumerate(self._hosts):
            host_session.objects.create(
                host=_host, start=self.yesterday, session_luid='as{}'.format(_host.id), ip_address='1.2.{}.5'.format(_host.id)
            )

            PrivHistory.objects.create(host=_host, created_date=self.yesterday, priv_level=i % 10)

            score.objects.create(host=_host, timestamp=self.yesterday, threat_score=(i * 10) % 100, confidence_score=(i * 11) % 100)

        context = self.asset_inventory_report._get_endpoint_coverage()

        expected_context = [
            {
                'title': 'No EDR Coverage',
                'count': 16,
                'top_hosts': [
                    {'priv_level': 9, 'dfz_score': 134, 'name': 'testhost9', 't_score': 90, 'c_score': 99, 'urgency_score': 0},
                    {'priv_level': 9, 'dfz_score': 90, 'name': 'testhost19', 't_score': 90, 'c_score': 9, 'urgency_score': 0},
                    {'priv_level': 8, 'dfz_score': 127, 'name': 'testhost18', 't_score': 80, 'c_score': 98, 'urgency_score': 0},
                    {'priv_level': 8, 'dfz_score': 119, 'name': 'testhost8', 't_score': 80, 'c_score': 88, 'urgency_score': 0},
                    {'priv_level': 7, 'dfz_score': 112, 'name': 'testhost17', 't_score': 70, 'c_score': 87, 'urgency_score': 0},
                    {'priv_level': 7, 'dfz_score': 104, 'name': 'testhost7', 't_score': 70, 'c_score': 77, 'urgency_score': 0},
                    {'priv_level': 6, 'dfz_score': 97, 'name': 'testhost16', 't_score': 60, 'c_score': 76, 'urgency_score': 0},
                    {'priv_level': 6, 'dfz_score': 89, 'name': 'testhost6', 't_score': 60, 'c_score': 66, 'urgency_score': 0},
                    {'priv_level': 5, 'dfz_score': 82, 'name': 'testhost15', 't_score': 50, 'c_score': 65, 'urgency_score': 0},
                    {'priv_level': 5, 'dfz_score': 74, 'name': 'testhost5', 't_score': 50, 'c_score': 55, 'urgency_score': 0},
                ],
                'percent': 80.0,
            },
            {
                'title': 'BitDefender',
                'count': 2,
                'top_hosts': [
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 0, 'dfz_score': 0, 'name': 'testhost0', 't_score': 0, 'c_score': 0, 'urgency_score': 0},
                ],
                'percent': 10.0,
            },
            {
                'title': 'Carbon Black',
                'count': 1,
                'top_hosts': [{'priv_level': 2, 'dfz_score': 30, 'name': 'testhost2', 't_score': 20, 'c_score': 22, 'urgency_score': 0}],
                'percent': 5.0,
            },
            {
                'title': 'Fireeye',
                'count': 2,
                'top_hosts': [
                    {'priv_level': 3, 'dfz_score': 45, 'name': 'testhost3', 't_score': 30, 'c_score': 33, 'urgency_score': 0},
                    {'priv_level': 1, 'dfz_score': 15, 'name': 'testhost1', 't_score': 10, 'c_score': 11, 'urgency_score': 0},
                ],
                'percent': 10.0,
            },
        ]

        self.assertEqual(context, expected_context)


class TestCISOReport(VuiTestCase):
    """
    Tests for CISO reports
    """

    def setUp(self):
        self.maxDiff = None
        self.user = User.objects.create_user(username='vadmin', email='<EMAIL>', password='plaintext_pass')
        self.now = datetime(2021, 6, 25, 0, 0, 0, 0, pytz.utc)
        self.one_month_ago = self.now - timedelta(days=40)
        self.last_30_days = self.now - timedelta(days=30)
        self.yesterday = self.now - timedelta(days=1)
        self.in_range_date = self.now - timedelta(days=15)
        self.out_of_bound_date_past = self.now.replace(day=1) - relativedelta(months=6)
        self.out_of_bound_date_future = self.now.replace(day=1) + relativedelta(months=6)

        self.from_date = self.last_30_days
        self.to_date = self.now
        self.old_date = self.now - timedelta(days=100)
        self.future_date = self.now + timedelta(days=100)

        # create hosts
        self._hosts = [
            host.objects.create(
                name='testhost{}'.format(i),
                last_source='1.2.{}.5'.format(i),
                first_timestamp=self.yesterday,
                host_luid='luid{}'.format(i),
                priv_level=(i + 1) % 10,
            )
            for i in range(10)
        ]

        # create accounts
        self.linked_accounts = [LinkedAccount.objects.create(display_uid=f'abc{i}@abc.com') for i in range(10)]

        accounts = [
            {
                'uid': f'abc{i}@abc.com',
                'account_type': Account.TYPE_KERBEROS,
                'first_seen': self.now,
                'last_seen': self.now,
            }
            for i in range(10)
        ]

        lib_account.create_or_update_accounts(accounts)
        self.linked_accounts = list(LinkedAccount.objects.all())

        self.btp_outcome = AssignmentOutcome.objects.create(title='Benign True Positive', builtin=True, category='benign_true_positive')
        self.fp_outcome = AssignmentOutcome.objects.create(title='False Positive', builtin=True, category='false_positive')
        self.mtp_outcome = AssignmentOutcome.objects.create(
            title='Malicious True Positive', builtin=True, category='malicious_true_positive'
        )
        self.host_role_patcher = mock.patch('host_scoring_v2.host_scoring.HostAttributeAPI.get_attributes')
        self.host_role_patcher.start()
        self.addCleanup(self.host_role_patcher.stop)

    def _create_prioritizations_and_assignments(self, now, prioritizations, assignments, entity_type, entity_id):
        """
        Duration of prioritizations = 1 hour
        :param prioritizations: list of delta hours to create prioritizations at
        :param assignments: list of delta hours to create assignments at
        :param entity_type:
        :param entity_id:
        :return:
        """

        p_to_create = [
            PrioritizationTimings(
                obj_type=entity_type,
                entity_id=entity_id,
                duration_seconds=60 * 60,
                time_first_detected=now + timedelta(hours=p - 1),
                time_prioritized=now + timedelta(hours=p),
            )
            for p in prioritizations
        ]
        a_to_create = [
            Assignment(
                obj_type=entity_type, type_id=entity_id, user=self.user, assigned_by=self.user, date_assigned=now + timedelta(hours=a)
            )
            for a in assignments
        ]

        PrioritizationTimings.objects.bulk_create(p_to_create)
        Assignment.objects.bulk_create(a_to_create)

    def test_report_no_acc_no_host(self):
        ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
        assert ciso_report._get_obs_privilege(None, None) == "No privilege found"

    def test_get_totals(self):
        # p's outside of date (before and after)
        times_before_report_range = [self.out_of_bound_date_past + timedelta(days=i) for i in range(10)]
        times_after_report_range = [self.out_of_bound_date_future + timedelta(days=i) for i in range(10)]

        # create prioritizations out of date range
        PrioritizationTimings.objects.bulk_create(
            PrioritizationTimings(
                obj_type='host',
                entity_id=self._hosts[0].id,
                duration_seconds=60 * 60,
                time_first_detected=time,
                time_prioritized=time + timedelta(hours=1),
            )
            for time in times_before_report_range + times_after_report_range
        )

        # create assignments out of date range
        all_p_runs = PrioritizationTimings.objects.all()

        Assignment.objects.bulk_create(
            Assignment(
                obj_type=p.obj_type,
                type_id=p.entity_id,
                user=self.user,
                date_assigned=p.time_prioritized + timedelta(days=1),
                date_resolved=p.time_prioritized + timedelta(days=1),
            )
            for i, p in enumerate(all_p_runs)
        )

        # create prioritizations in report date range
        with freeze_time(self.in_range_date):
            for i, _host in enumerate(self._hosts):
                now = self.in_range_date + timedelta(days=i)
                PrioritizationTimings.objects.create(
                    obj_type='host',
                    entity_id=self._hosts[i].id,
                    duration_seconds=60 * 60,
                    time_first_detected=now,
                    time_prioritized=now + timedelta(hours=1),
                )

            # create assignments within date range
            Assignment.objects.bulk_create(
                [
                    Assignment(
                        obj_type='host',
                        type_id=self._hosts[0].id,
                        user=self.user,
                        date_assigned=now + timedelta(hours=2),
                        date_resolved=now + timedelta(hours=3),
                        outcome=self.btp_outcome,
                    ),
                    Assignment(
                        obj_type='host',
                        type_id=self._hosts[3].id,
                        user=self.user,
                        date_assigned=now + timedelta(days=3, hours=2),
                        date_resolved=now + timedelta(days=3, hours=3),
                        outcome=self.mtp_outcome,
                    ),
                    # not resolved
                    Assignment(
                        obj_type='host',
                        type_id=self._hosts[4].id,
                        user=self.user,
                        date_assigned=now + timedelta(days=4, hours=2),
                    ),
                    Assignment(
                        obj_type='host',
                        type_id=self._hosts[5].id,
                        user=self.user,
                        date_assigned=now + timedelta(days=5, hours=2),
                        date_resolved=now + timedelta(days=5, hours=3),
                        outcome=self.fp_outcome,
                    ),
                ]
            )

        with freeze_time(self.in_range_date):
            for i, _host in enumerate(self.linked_accounts):
                now = self.in_range_date + timedelta(days=i)
                PrioritizationTimings.objects.create(
                    obj_type='linked_account',
                    entity_id=self.linked_accounts[i].id,
                    duration_seconds=60 * 60,
                    time_first_detected=now,
                    time_prioritized=now + timedelta(hours=1),
                )

            # create assignments within date range
            Assignment.objects.bulk_create(
                [
                    Assignment(
                        obj_type='linked_account',
                        type_id=self.linked_accounts[0].id,
                        user=self.user,
                        date_assigned=now + timedelta(hours=2),
                        date_resolved=now + timedelta(hours=3),
                        outcome=self.btp_outcome,
                    ),
                    Assignment(
                        obj_type='linked_account',
                        type_id=self.linked_accounts[3].id,
                        user=self.user,
                        date_assigned=now + timedelta(days=3, hours=2),
                        date_resolved=now + timedelta(days=3, hours=3),
                        outcome=self.mtp_outcome,
                    ),
                    # not resolved
                    Assignment(
                        obj_type='linked_account',
                        type_id=self.linked_accounts[4].id,
                        user=self.user,
                        date_assigned=now + timedelta(days=4, hours=2),
                    ),
                    Assignment(
                        obj_type='linked_account',
                        type_id=self.linked_accounts[5].id,
                        user=self.user,
                        date_assigned=now + timedelta(days=5, hours=2),
                        date_resolved=now + timedelta(days=5, hours=3),
                        outcome=self.fp_outcome,
                    ),
                ]
            )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        self.assertEqual(context['total_prioritizations'], 20)
        self.assertEqual(context['total_acknowledgments'], 8)
        self.assertEqual(context['total_resolutions'], 6)
        self.assertEqual(context['total_ack_resolutions'], 6)
        self.assertEqual(context['total_resolutions_mtp'], 2)
        self.assertEqual(context['total_resolutions_btp'], 2)
        self.assertEqual(context['total_resolutions_fp'], 2)
        self.assertEqual(context['percent_acknowledged'], 40)
        self.assertEqual(context['percent_responded'], 75)

    def test_ttp_stats(self):
        # not included in calculation
        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i % 10].id,
                    duration_seconds=60 * 60,
                    time_first_detected=self.out_of_bound_date_past + timedelta(hours=i),
                    time_prioritized=self.out_of_bound_date_past + timedelta(hours=i + 1),
                )
                for i in range(100)
            ]
        )

        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i % 10].id,
                    duration_seconds=60 * 60,
                    time_first_detected=self.out_of_bound_date_future + timedelta(hours=i),
                    time_prioritized=self.out_of_bound_date_future + timedelta(hours=i + 1),
                )
                for i in range(100)
            ]
        )

        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i].id,
                    duration_seconds=i * 60 * 60,
                    time_first_detected=self.in_range_date,
                    time_prioritized=self.in_range_date + timedelta(hours=i),
                )
                for i in range(10)
            ]
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        self.assertEqual(context['mean_time_to_prioritize'], 16200)
        self.assertEqual(context['mean_ttp_month_over_month'], None)
        self.assertEqual(set(context['times_to_prioritize']), set([0, 3600, 7200, 10800, 14400, 18000, 21600, 25200, 28800, 32400]))

        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i].id,
                    duration_seconds=i / 2 * 60 * 60,
                    time_first_detected=self.one_month_ago,
                    time_prioritized=self.one_month_ago + timedelta(hours=i / 2),
                )
                for i in range(10)
            ]
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        self.assertEqual(context['mean_ttp_month_over_month'], 100)

    def test_ttp_stats_empty(self):
        ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
        context = ciso_report.get_context()

        self.assertEqual(context['mean_time_to_prioritize'], None)
        self.assertEqual(context['mean_ttp_month_over_month'], None)

    def test_tta_stats(self):
        outcomes = [self.btp_outcome, self.mtp_outcome, self.fp_outcome]
        # not included in calculation
        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i % 10].id,
                    duration_seconds=60 * 60,
                    time_first_detected=self.out_of_bound_date_past + timedelta(hours=i),
                    time_prioritized=self.out_of_bound_date_past + timedelta(hours=i + 1),
                )
                for i in range(100)
            ]
        )

        Assignment.objects.bulk_create(
            Assignment(
                obj_type='host',
                type_id=self._hosts[i % 10].id,
                user=self.user,
                assigned_by=self.user,
                date_assigned=self.out_of_bound_date_past + timedelta(hours=i + 2),
            )
            for i in range(100)
        )

        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i % 10].id,
                    duration_seconds=60 * 60,
                    time_first_detected=self.out_of_bound_date_future + timedelta(hours=i),
                    time_prioritized=self.out_of_bound_date_future + timedelta(hours=i + 1),
                )
                for i in range(100)
            ]
        )

        Assignment.objects.bulk_create(
            Assignment(
                obj_type='host',
                type_id=self._hosts[i % 10].id,
                user=self.user,
                assigned_by=self.user,
                date_assigned=self.out_of_bound_date_future + timedelta(hours=i + 2),
            )
            for i in range(100)
        )

        # included in calculation
        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i].id,
                    duration_seconds=i * 60 * 60,
                    time_first_detected=self.in_range_date,
                    time_prioritized=self.in_range_date + timedelta(hours=i),
                )
                for i in range(10)
            ]
        )

        Assignment.objects.bulk_create(
            Assignment(
                obj_type='host',
                type_id=self._hosts[i].id,
                user=self.user,
                assigned_by=self.user,
                date_assigned=self.in_range_date + timedelta(hours=i * 2),
                date_resolved=self.in_range_date + timedelta(hours=i * 2 + 1),
                outcome=outcomes[i % 3],
            )
            for i in range(9)
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        exp_mean_time_to_acknowledge = (0 + 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8) * 60 * 60 / 9
        self.assertEqual(context['mean_time_to_acknowledge'], exp_mean_time_to_acknowledge)
        self.assertEqual(context['mean_tta_month_over_month'], None)
        self.assertEqual(set(context['times_to_acknowledge']), set([0, 3600, 7200, 10800, 14400, 18000, 21600, 25200, 28800]))

        expected_event_breakdown = {'btp': 3, 'fp': 3, 'mtp': 3, 'total': 9}

        expected_incident_timings = {
            'btp': {
                'name': 'Benign True Positive',
                'abbreviation': 'BTP',
                'mtta': 10800,
                'mttp': 10800,
                'mttr': 3600,
                'mtt_total': 25200,
            },
            'fp': {
                'name': 'False Positive',
                'abbreviation': 'FP',
                'mtta': 18000,
                'mttp': 18000,
                'mttr': 3600,
                'mtt_total': 39600,
            },
            'mtp': {
                'name': 'Malicious True Positive',
                'abbreviation': 'MTP',
                'mtta': 14400,
                'mttp': 14400,
                'mttr': 3600,
                'mtt_total': 32400,
            },
        }

        self.assertEqual(context['event_breakdown'], expected_event_breakdown)
        self.assertEqual(context['incident_timings'], expected_incident_timings)

        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='host',
                    entity_id=self._hosts[i].id,
                    duration_seconds=i / 2 * 60 * 60,
                    time_first_detected=self.one_month_ago,
                    time_prioritized=self.one_month_ago + timedelta(hours=i * 2),
                )
                for i in range(10)
            ]
        )

        Assignment.objects.bulk_create(
            Assignment(
                obj_type='host',
                type_id=self._hosts[i].id,
                user=self.user,
                assigned_by=self.user,
                date_assigned=self.one_month_ago + timedelta(hours=i * 4),
            )
            for i in range(9)
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        self.assertEqual(context['mean_tta_month_over_month'], -50)

    def test_tta_stats_single_entity(self):
        prioritizations = [0, 10, 20, 30, 40, 50]
        assignments = [1, 12, 24, 38, 45, 51]
        self._create_prioritizations_and_assignments(self.in_range_date, prioritizations, assignments, 'host', self._hosts[0].id)

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        exp_mean_time_to_acknowledge = (1 + 2 + 4 + 8 + 5 + 1) * 60 * 60 / 6
        self.assertEqual(context['mean_time_to_acknowledge'], exp_mean_time_to_acknowledge)

    def test_tta_stats_empty(self):
        ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
        context = ciso_report.get_context()

        self.assertEqual(context['mean_time_to_acknowledge'], None)
        self.assertEqual(context['mean_tta_month_over_month'], None)

    def test_tta_stats_old_prioritization(self):
        prioritizations = [-4 * 30 * 24]  # ~4 months ago last prioritization
        assignments = [0]
        self._create_prioritizations_and_assignments(self.in_range_date, prioritizations, assignments, 'host', self._hosts[0].id)

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        exp_mean_time_to_acknowledge = (4 * 30 * 24) * 60 * 60
        self.assertEqual(context['mean_time_to_acknowledge'], exp_mean_time_to_acknowledge)

    def test_ttr_stats(self):
        # not in report date range
        Assignment.objects.bulk_create(
            [
                Assignment(
                    obj_type='linked_account',
                    type_id=self.linked_accounts[i % 10].id,
                    user=self.user,
                    assigned_by=self.user,
                    date_assigned=self.out_of_bound_date_past + timedelta(hours=i),
                    date_resolved=self.out_of_bound_date_past + timedelta(hours=i + 1),
                )
                for i in range(100)
            ]
        )

        Assignment.objects.bulk_create(
            [
                Assignment(
                    obj_type='linked_account',
                    type_id=self.linked_accounts[i % 10].id,
                    user=self.user,
                    assigned_by=self.user,
                    date_assigned=self.out_of_bound_date_future + timedelta(hours=i),
                    date_resolved=self.out_of_bound_date_future + timedelta(hours=i + 1),
                )
                for i in range(100)
            ]
        )

        # included in report date range
        PrioritizationTimings.objects.bulk_create(
            [
                PrioritizationTimings(
                    obj_type='linked_account',
                    entity_id=self.linked_accounts[i].id,
                    duration_seconds=i * 60 * 60,
                    time_first_detected=self.in_range_date,
                    time_prioritized=self.in_range_date + timedelta(hours=i),
                )
                for i in range(10)
            ]
        )
        Assignment.objects.bulk_create(
            [
                Assignment(
                    obj_type='linked_account',
                    type_id=self.linked_accounts[i].id,
                    user=self.user,
                    assigned_by=self.user,
                    date_assigned=self.in_range_date + timedelta(hours=i),
                    date_resolved=self.in_range_date + timedelta(hours=i * 2),
                    outcome=self.btp_outcome,
                )
                for i in range(10)
            ]
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        self.assertEqual(context['mean_time_to_respond'], (0 + 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9) * 60 * 60 / 10)
        self.assertEqual(context['mean_ttr_month_over_month'], None)
        self.assertEqual(set(context['times_to_respond']), set([32400, 28800, 25200, 21600, 18000, 14400, 10800, 7200, 3600, 0]))

        Assignment.objects.bulk_create(
            [
                Assignment(
                    obj_type='linked_account',
                    type_id=self.linked_accounts[i % 10].id,
                    user=self.user,
                    assigned_by=self.user,
                    date_assigned=self.one_month_ago + timedelta(hours=i),
                    date_resolved=self.one_month_ago + timedelta(hours=i * 2),
                    outcome=self.btp_outcome,
                )
                for i in range(10)
            ]
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        self.assertEqual(context['mean_ttr_month_over_month'], 0)

    def test_ttr_stats_empty(self):
        ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
        context = ciso_report.get_context()

        self.assertEqual(context['mean_time_to_respond'], None)
        self.assertEqual(context['mean_ttr_month_over_month'], None)

    @mock.patch('tvui.models.host.get_host_archetype_details')
    @mock.patch("host_scoring_v2.host_scoring.HostAttributeAPI.get_attributes")
    def test_events_breakdown_simple(self, mock_get_attributes, mock_host_archetype):
        # create data for host
        mock_get_attributes.return_value = []
        mock_host_archetype.return_value = None
        _hs = host_session.objects.create(host=self._hosts[0], ip_address=f'*******', start=self.now, session_luid=f'abc123')
        PrioritizationTimings.objects.create(
            obj_type='host',
            entity_id=self._hosts[0].id,
            duration_seconds=60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=1),
        )  # TTP = 1 hour
        assignment = Assignment.objects.create(
            obj_type='host',
            type_id=self._hosts[0].id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 9 hours
            date_resolved=self.in_range_date + timedelta(hours=12),  # TTR = 2 hours
            outcome=self.btp_outcome,
        )
        _note = notes.objects.create(
            note="another note",
            type="assignment",
            type_id=assignment.id,
            date_created=self.now,
            date_modified=self.now,
            created_by=self.user,
            modified_by=self.user,
        )

        # create data for account
        sub_account = Account.objects.create(
            uid='<EMAIL>',
            account_type=Account.TYPE_KERBEROS,
            first_seen=self.now,
            last_seen=self.now,
            t_score=5,
            c_score=5,
            linked_account=self.linked_accounts[0],
            priv_level=5,
        )
        linked_account = sub_account.linked_account
        linked_account.t_score = 40
        linked_account.c_score = 60
        linked_account.save()
        PrioritizationTimings.objects.create(
            obj_type='linked_account',
            entity_id=linked_account.id,
            duration_seconds=2 * 60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=2),
        )  # TTP = 2 hours
        assignment2 = Assignment.objects.create(
            obj_type='linked_account',
            type_id=linked_account.id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 8 hours
            date_resolved=self.in_range_date + timedelta(hours=13),  # TTR = 3 hours
            outcome=self.mtp_outcome,
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        exp_mtp_events = [
            {
                'entity_type': 'linked_account',
                'entity_name': '<EMAIL>',
                'resolution_assignee': 'vadmin',
                'time_to_prioritize': 7200,
                'timestamp_prioritized': self.in_range_date + timedelta(hours=2),
                'time_to_acknowledge': 28800,
                'timestamp_acknowledged': self.in_range_date + timedelta(hours=10),
                'time_to_respond': 10800,
                'timestamp_responded': self.in_range_date + timedelta(hours=13),
                'note': None,
                'observed_privilege': 5,
                'detections': [],
                'tags': [],
                'last_seen_timestamp': self.now,
                'cloud_account': '<EMAIL>',
                'max_threat': None,
                'max_certainty': None,
                'max_urgency_score': None,
            }
        ]
        exp_btp_events = [
            {
                'entity_type': 'host',
                'entity_name': 'testhost0',
                'resolution_assignee': 'vadmin',
                'time_to_prioritize': 3600,
                'timestamp_prioritized': self.in_range_date + timedelta(hours=1),
                'time_to_acknowledge': 32400,
                'timestamp_acknowledged': self.in_range_date + timedelta(hours=10),
                'time_to_respond': 7200,
                'timestamp_responded': self.in_range_date + timedelta(hours=12),
                'note': {'author': 'vadmin', 'timestamp': self.now, 'note': 'another note'},
                'observed_privilege': 1,
                'detections': [],
                'tags': [],
                'roles': [],
                'detection_profile': None,
                'last_seen_timestamp': self.now,
                'last_seen_ip': '*******',
                'probable_account': None,
                'max_threat': None,
                'max_certainty': None,
                'max_urgency_score': None,
                'host_groups': [],
            }
        ]

        self.assertEqual(context['impactful_events'], exp_mtp_events + exp_btp_events)

    @mock.patch('tvui.models.host.get_host_archetype_details')
    @mock.patch("host_scoring_v2.host_scoring.HostAttributeAPI.get_attributes")
    def test_events_breakdown_single_using_HAPI(self, mock_get_attributes, mock_host_archetype):
        # create data for host
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "off"})
        acct = Account.objects.create(
            uid='<EMAIL>', account_type=Account.TYPE_KERBEROS, first_seen=self.now, last_seen=self.now, t_score=5, c_score=5
        )
        self._hosts[0].probable_owners.add(acct)
        mock_get_attributes.return_value = [
            {
                "id": 1,
                "first_seen": "2020-10-22T01:22:10Z",
                "value": "DHCP Server",
                "host_luid": "Q8iTspm2",
                'role_display_name': 'DHCP Server',
                "last_seen": "2020-10-22T01:22:10Z",
            },
            {
                "id": 2,
                "first_seen": "2020-10-22T01:22:10Z",
                "value": "Proxy Server",
                "host_luid": "Q8iTspm2",
                'role_display_name': 'Proxy Server',
                "last_seen": "2020-10-22T01:22:10Z",
            },
            {
                "id": 3,
                "first_seen": "2020-10-22T01:22:10Z",
                "value": "Proxy Server",
                "host_luid": "Q8iTspm2",
                'role_display_name': 'Proxy Server',
                "last_seen": "2020-10-22T01:22:10Z",
            },
        ]
        mock_host_archetype.return_value = {'name': 'abc', 'vname': 'Ransomware', 'scoring_detections': []}

        _hs = host_session.objects.create(host=self._hosts[0], ip_address=f'*******', start=self.now, session_luid=f'abc123')
        PrioritizationTimings.objects.create(
            obj_type='host',
            entity_id=self._hosts[0].id,
            duration_seconds=60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=1),
        )  # TTP = 1 hour
        assignment = Assignment.objects.create(
            obj_type='host',
            type_id=self._hosts[0].id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 9 hours
            date_resolved=self.in_range_date + timedelta(hours=12),  # TTR = 2 hours
            outcome=self.btp_outcome,
        )
        _note = notes.objects.create(
            note="note",
            type="assignment",
            type_id=assignment.id,
            date_created=self.now,
            date_modified=self.now,
            created_by=self.user,
            modified_by=self.user,
        )
        _note.save()
        detection.objects.create(
            type='ransomware',
            type_vname='Ransomware',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now,
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        detection.objects.create(
            type='ransomware',
            type_vname='Another detection',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now + timedelta(hours=1),
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        self._hosts[0].tags.add('a tag')

        # create scores
        score.objects.bulk_create(
            score(
                host=self._hosts[0],
                threat_score=i,
                confidence_score=i,
                timestamp=self.in_range_date + timedelta(hours=i),
                end_timestamp=self.in_range_date + timedelta(hours=i + 1),
            )
            for i in range(10)
        )

        # create data for account
        sub_account = Account.objects.create(
            uid='<EMAIL>',
            account_type=Account.TYPE_KERBEROS,
            first_seen=self.now,
            last_seen=self.now,
            t_score=5,
            c_score=5,
            linked_account=self.linked_accounts[0],
            priv_level=5,
        )
        linked_account = sub_account.linked_account
        linked_account.t_score = 40
        linked_account.c_score = 60
        linked_account.tags.add('some tag')
        linked_account.save()
        PrioritizationTimings.objects.create(
            obj_type='linked_account',
            entity_id=linked_account.id,
            duration_seconds=2 * 60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=2),
        )  # TTP = 2 hours
        assignment2 = Assignment.objects.create(
            obj_type='linked_account',
            type_id=linked_account.id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 8 hours
            date_resolved=self.in_range_date + timedelta(hours=13),  # TTR = 3 hours
            outcome=self.mtp_outcome,
        )
        _note = notes.objects.create(
            note="another note",
            type="assignment",
            type_id=assignment2.id,
            date_created=self.now,
            date_modified=self.now,
            created_by=self.user,
            modified_by=self.user,
        )
        _note.save()
        detection.objects.create(
            type='ransomware',
            type_vname='Bitcoin Mining',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now,
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            account=sub_account,
        )
        detection.objects.create(
            type='ransomware',
            type_vname='Another detection',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now + timedelta(hours=1),
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            account=sub_account,
        )

        # create scores
        LinkedAccountScoreHistory.objects.bulk_create(
            LinkedAccountScoreHistory(
                account=linked_account,
                t_score=i,
                c_score=i,
                score_date=self.in_range_date + timedelta(hours=i),
            )
            for i in range(10)
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        exp_mtp_events = [
            {
                'entity_type': 'linked_account',
                'entity_name': '<EMAIL>',
                'resolution_assignee': 'vadmin',
                'time_to_prioritize': 7200,
                'timestamp_prioritized': self.in_range_date + timedelta(hours=2),
                'time_to_acknowledge': 28800,
                'timestamp_acknowledged': self.in_range_date + timedelta(hours=10),
                'time_to_respond': 10800,
                'timestamp_responded': self.in_range_date + timedelta(hours=13),
                'note': {'author': 'vadmin', 'timestamp': self.now, 'note': 'another note'},
                'observed_privilege': 5,
                'detections': [
                    {
                        'name': 'Another detection',
                        'timestamp': self.now + timedelta(hours=1),
                    },
                    {'name': 'Bitcoin Mining', 'timestamp': self.now},
                ],
                'tags': ['some tag'],
                'last_seen_timestamp': self.now,
                'cloud_account': '<EMAIL>',
                'max_threat': 9,
                'max_certainty': 9,
                'max_urgency_score': 0,
            }
        ]
        exp_btp_events = [
            {
                'entity_type': 'host',
                'entity_name': 'testhost0',
                'resolution_assignee': 'vadmin',
                'time_to_prioritize': 3600,
                'timestamp_prioritized': self.in_range_date + timedelta(hours=1),
                'time_to_acknowledge': 32400,
                'timestamp_acknowledged': self.in_range_date + timedelta(hours=10),
                'time_to_respond': 7200,
                'timestamp_responded': self.in_range_date + timedelta(hours=12),
                'note': {'author': 'vadmin', 'timestamp': self.now, 'note': 'note'},
                'observed_privilege': 1,
                'detections': [
                    {'name': 'Another detection', 'timestamp': self.now + timedelta(hours=1)},
                    {'name': 'Ransomware', 'timestamp': self.now},
                ],
                'tags': ['a tag'],
                'roles': ['DHCP Server', 'Proxy Server'],
                'detection_profile': 'Ransomware',
                'last_seen_timestamp': self.now,
                'last_seen_ip': '*******',
                'probable_account': '<EMAIL>',
                'max_threat': 9,
                'max_certainty': 9,
                'max_urgency_score': 0,
                'host_groups': [],
            }
        ]
        self.assertEqual(context['impactful_events'], exp_mtp_events + exp_btp_events)

    @mock.patch('tvui.models.host.get_host_archetype_details')
    @mock.patch("host_scoring_v2.host_scoring.HostAttributeAPI.get_attributes")
    def test_events_breakdown_single_using_HAPI_no_roles(self, mock_get_attributes, mock_host_archetype):
        # create data for host
        acct = Account.objects.create(
            uid='<EMAIL>', account_type=Account.TYPE_KERBEROS, first_seen=self.now, last_seen=self.now, t_score=5, c_score=5
        )
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "off"})
        self._hosts[0].probable_owners.add(acct)
        mock_get_attributes.return_value = []
        mock_host_archetype.return_value = {'name': 'abc', 'vname': 'Ransomware', 'scoring_detections': []}

        _hs = host_session.objects.create(host=self._hosts[0], ip_address=f'*******', start=self.now, session_luid=f'abc123')
        PrioritizationTimings.objects.create(
            obj_type='host',
            entity_id=self._hosts[0].id,
            duration_seconds=60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=1),
        )  # TTP = 1 hour
        assignment = Assignment.objects.create(
            obj_type='host',
            type_id=self._hosts[0].id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 9 hours
            date_resolved=self.in_range_date + timedelta(hours=12),  # TTR = 2 hours
            outcome=self.btp_outcome,
        )
        _note = notes.objects.create(
            note="note",
            type="assignment",
            type_id=assignment.id,
            date_created=self.now,
            date_modified=self.now,
            created_by=self.user,
            modified_by=self.user,
        )
        _note.save()
        detection.objects.create(
            type='ransomware',
            type_vname='Ransomware',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now,
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        detection.objects.create(
            type='ransomware',
            type_vname='Another detection',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now + timedelta(hours=1),
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        self._hosts[0].tags.add('a tag')

        # create scores
        score.objects.bulk_create(
            score(
                host=self._hosts[0],
                threat_score=i,
                confidence_score=i,
                timestamp=self.in_range_date + timedelta(hours=i),
                end_timestamp=self.in_range_date + timedelta(hours=i + 1),
            )
            for i in range(10)
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()
        self.assertEqual(context['impactful_events'][0]['roles'], [])

    @mock.patch('tvui.models.host.get_host_archetype_details')
    def test_events_breakdown_single_using_host_role_table(self, mock_host_archetype):
        # create data for host
        acct = Account.objects.create(
            uid='<EMAIL>', account_type=Account.TYPE_KERBEROS, first_seen=self.now, last_seen=self.now, t_score=5, c_score=5
        )
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "on"})
        self._hosts[0].probable_owners.add(acct)
        mock_host_archetype.return_value = {'name': 'abc', 'vname': 'Ransomware', 'scoring_detections': []}

        _hs = host_session.objects.create(host=self._hosts[0], ip_address=f'*******', start=self.now, session_luid=f'abc123')
        PrioritizationTimings.objects.create(
            obj_type='host',
            entity_id=self._hosts[0].id,
            duration_seconds=60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=1),
        )  # TTP = 1 hour
        assignment = Assignment.objects.create(
            obj_type='host',
            type_id=self._hosts[0].id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 9 hours
            date_resolved=self.in_range_date + timedelta(hours=12),  # TTR = 2 hours
            outcome=self.btp_outcome,
        )
        _note = notes.objects.create(
            note="note",
            type="assignment",
            type_id=assignment.id,
            date_created=self.now,
            date_modified=self.now,
            created_by=self.user,
            modified_by=self.user,
        )
        _note.save()
        detection.objects.create(
            type='ransomware',
            type_vname='Ransomware',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now,
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        detection.objects.create(
            type='ransomware',
            type_vname='Another detection',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now + timedelta(hours=1),
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        self._hosts[0].tags.add('a tag')

        # create scores
        score.objects.bulk_create(
            score(
                host=self._hosts[0],
                threat_score=i,
                confidence_score=i,
                timestamp=self.in_range_date + timedelta(hours=i),
                end_timestamp=self.in_range_date + timedelta(hours=i + 1),
            )
            for i in range(10)
        )
        HostRole.objects.update_or_create(host_id=self._hosts[0].id, role_name='Web Server', source='HAPI')
        HostRole.objects.update_or_create(host_id=self._hosts[0].id, role_name='DNS Server', source='HAPI')

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()
        self.assertEqual(context['impactful_events'][0]['roles'], ['DNS Server', 'Web Server'])

    @mock.patch('tvui.models.host.get_host_archetype_details')
    def test_events_breakdown_single_using_host_role_table_no_roles(self, mock_host_archetype):
        # create data for host
        acct = Account.objects.create(
            uid='<EMAIL>', account_type=Account.TYPE_KERBEROS, first_seen=self.now, last_seen=self.now, t_score=5, c_score=5
        )
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "on"})
        self._hosts[0].probable_owners.add(acct)
        mock_host_archetype.return_value = {'name': 'abc', 'vname': 'Ransomware', 'scoring_detections': []}

        _hs = host_session.objects.create(host=self._hosts[0], ip_address=f'*******', start=self.now, session_luid=f'abc123')
        PrioritizationTimings.objects.create(
            obj_type='host',
            entity_id=self._hosts[0].id,
            duration_seconds=60 * 60,
            time_first_detected=self.in_range_date,
            time_prioritized=self.in_range_date + timedelta(hours=1),
        )  # TTP = 1 hour
        assignment = Assignment.objects.create(
            obj_type='host',
            type_id=self._hosts[0].id,
            user=self.user,
            date_assigned=self.in_range_date + timedelta(hours=10),  # TTA = 9 hours
            date_resolved=self.in_range_date + timedelta(hours=12),  # TTR = 2 hours
            outcome=self.btp_outcome,
        )
        _note = notes.objects.create(
            note="note",
            type="assignment",
            type_id=assignment.id,
            date_created=self.now,
            date_modified=self.now,
            created_by=self.user,
            modified_by=self.user,
        )
        _note.save()
        detection.objects.create(
            type='ransomware',
            type_vname='Ransomware',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now,
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        detection.objects.create(
            type='ransomware',
            type_vname='Another detection',
            category='EXFILTRATION',
            src_ip='*******',
            first_timestamp=self.now,
            last_timestamp=self.now + timedelta(hours=1),
            state='active',
            t_score=10,
            c_score=10,
            dfz_score=13,
            sensor_luid='********',
            host_session=_hs,
        )
        self._hosts[0].tags.add('a tag')

        # create scores
        score.objects.bulk_create(
            score(
                host=self._hosts[0],
                threat_score=i,
                confidence_score=i,
                timestamp=self.in_range_date + timedelta(hours=i),
                end_timestamp=self.in_range_date + timedelta(hours=i + 1),
            )
            for i in range(10)
        )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()
        self.assertEqual(context['impactful_events'][0]['roles'], [])

    @mock.patch('tvui.models.host.get_host_archetype_details')
    @mock.patch("host_scoring_v2.host_scoring.HostAttributeAPI.get_attributes")
    def test_events_breakdown_mutliple(self, mock_get_attributes, mock_host_archetype):
        mock_get_attributes.return_value = []
        mock_host_archetype.return_value = {}

        for i in range(4):
            offset_date = self.in_range_date + timedelta(hours=i)
            _hs = host_session.objects.create(
                host=self._hosts[i], ip_address='1.2.3.{}'.format(i), start=self.now, session_luid='abc12{}'.format(i)
            )
            PrioritizationTimings.objects.create(
                obj_type='host',
                entity_id=self._hosts[i].id,
                duration_seconds=60 * 60,
                time_first_detected=offset_date,
                time_prioritized=offset_date + timedelta(hours=1),
            )  # TTP = 1 hour
            assignment = Assignment.objects.create(
                obj_type='host',
                type_id=self._hosts[i].id,
                user=self.user,
                date_assigned=offset_date + timedelta(hours=10),  # TTA = 9 hours
                date_resolved=offset_date + timedelta(hours=12),  # TTR = 2 hours
                outcome=self.mtp_outcome,
            )
            _note = notes.objects.create(
                note="note {}".format(i),
                type="assignment",
                type_id=assignment.id,
                date_created=self.now,
                date_modified=self.now,
                created_by=self.user,
                modified_by=self.user,
            )
            _note.save()
            detection.objects.create(
                type='ransomware',
                type_vname='Ransomware',
                category='EXFILTRATION',
                src_ip='*******',
                first_timestamp=self.now,
                last_timestamp=self.now + timedelta(hours=i),
                state='active',
                t_score=10,
                c_score=10,
                dfz_score=13,
                sensor_luid='********',
                host_session=_hs,
            )
            score.objects.create(
                host=self._hosts[i],
                threat_score=(10 - i) * 2,
                confidence_score=(10 - i) * 3,
                timestamp=self.in_range_date + timedelta(hours=i),
                end_timestamp=self.in_range_date + timedelta(hours=i + 1),
            )

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()

        exp_mtp_events = [
            {
                'entity_type': 'host',
                'entity_name': 'testhost{}'.format(i),
                'resolution_assignee': 'vadmin',
                'time_to_prioritize': 3600,
                'timestamp_prioritized': self.in_range_date + timedelta(hours=1 + i),
                'time_to_acknowledge': 32400,
                'timestamp_acknowledged': self.in_range_date + timedelta(hours=10 + i),
                'time_to_respond': 7200,
                'timestamp_responded': self.in_range_date + timedelta(hours=12 + i),
                'note': {'author': 'vadmin', 'note': 'note {}'.format(i), 'timestamp': self.now},
                'observed_privilege': i + 1,
                'detections': [
                    {'name': 'Ransomware', 'timestamp': self.now + timedelta(hours=i)},
                ],
                'tags': [],
                'roles': [],
                'detection_profile': None,
                'last_seen_timestamp': self.now,
                'last_seen_ip': '1.2.{}.5'.format(i),
                'probable_account': None,
                'max_threat': (10 - i) * 2,
                'max_certainty': (10 - i) * 3,
                'max_urgency_score': 0,
                'host_groups': [],
            }
            for i in range(4)
        ]

        self.assertEqual(context['impactful_events'], exp_mtp_events)

    @mock.patch('pure_utils.event_utils.bundle_cloud_log')
    def test_impactful_events_unprioritized(self, bundle_cloud_log):
        """List of impactful events should include (all MTP) and (BTP with notes) even if not prioritized"""

        def make_assignment_for(host_, outcome, note=False):
            resolve_date = self.now - timedelta(hours=6)

            assignment = Assignment.objects.create(
                obj_type='host',
                type_id=host_.id,
                user=self.user,
                date_assigned=self.now - timedelta(hours=12),
                date_resolved=resolve_date,
                outcome=outcome,
            )

            if note:
                notes.objects.create(
                    note='somenote',
                    type="assignment",
                    type_id=assignment.id,
                    date_created=resolve_date,
                    date_modified=resolve_date,
                    created_by=self.user,
                    modified_by=self.user,
                )

            assignment.refresh_from_db()
            return assignment

        #                                                                                                 expected to appear?
        mtp_assn_no_note = make_assignment_for(self._hosts[0], outcome=self.mtp_outcome, note=False)  # yes
        mtp_assn_with_note = make_assignment_for(self._hosts[1], outcome=self.mtp_outcome, note=True)  # yes

        btp_assn_with_note = make_assignment_for(self._hosts[2], outcome=self.btp_outcome, note=True)  # yes
        btp_assn_with_note = make_assignment_for(self._hosts[3], outcome=self.btp_outcome, note=False)  # no

        with freeze_time(self.now):
            ciso_report = CISOReport(CISOReportDef(name='Test Report', created_by=self.user, frequency='on_demand'))
            context = ciso_report.get_context()
            events = context['impactful_events']

        self.assertEqual(context['total_resolutions'], 4)
        self.assertEqual(context['total_ack_resolutions'], 0)

        self.assertEqual(len(events), 3)

        expected_event_0 = {
            'detection_profile': None,
            'detections': [],
            'entity_name': 'testhost0',
            'entity_type': 'host',
            'host_groups': [],
            'last_seen_ip': '*******',
            'last_seen_timestamp': None,
            'max_certainty': None,
            'max_threat': None,
            'max_urgency_score': None,
            'note': None,
            'observed_privilege': 1,
            'probable_account': None,
            'resolution_assignee': 'vadmin',
            'roles': [],
            'tags': [],
            'time_to_acknowledge': None,
            'time_to_prioritize': None,
            'time_to_respond': 21600,
            'timestamp_acknowledged': None,
            'timestamp_prioritized': None,
            'timestamp_responded': self.now - timedelta(hours=6),
        }
        self.assertDictEqual(events[0], expected_event_0)

        expected_event_1 = {
            'detection_profile': None,
            'detections': [],
            'entity_name': 'testhost1',
            'entity_type': 'host',
            'host_groups': [],
            'last_seen_ip': '*******',
            'last_seen_timestamp': None,
            'max_certainty': None,
            'max_threat': None,
            'max_urgency_score': None,
            'note': {'author': 'vadmin', 'note': 'somenote', 'timestamp': self.now - timedelta(hours=6)},
            'observed_privilege': 2,
            'probable_account': None,
            'resolution_assignee': 'vadmin',
            'roles': [],
            'tags': [],
            'time_to_acknowledge': None,
            'time_to_prioritize': None,
            'time_to_respond': 21600,
            'timestamp_acknowledged': None,
            'timestamp_prioritized': None,
            'timestamp_responded': self.now - timedelta(hours=6),
        }
        self.assertDictEqual(events[1], expected_event_1)

        expected_event_2 = {
            'detection_profile': None,
            'detections': [],
            'entity_name': 'testhost2',
            'entity_type': 'host',
            'host_groups': [],
            'last_seen_ip': '*******',
            'last_seen_timestamp': None,
            'max_certainty': None,
            'max_threat': None,
            'max_urgency_score': None,
            'note': {'author': 'vadmin', 'note': 'somenote', 'timestamp': self.now - timedelta(hours=6)},
            'observed_privilege': 3,
            'probable_account': None,
            'resolution_assignee': 'vadmin',
            'roles': [],
            'tags': [],
            'time_to_acknowledge': None,
            'time_to_prioritize': None,
            'time_to_respond': 21600,
            'timestamp_acknowledged': None,
            'timestamp_prioritized': None,
            'timestamp_responded': self.now - timedelta(hours=6),
        }
        self.assertDictEqual(events[2], expected_event_2)

    @mock.patch('pure_utils.event_utils.bundle_cloud_log')
    def test_upload_ciso_workflow_telemetry(self, bundle_cloud_log):

        expected_index_key = 'CISO_WORKFLOW_METRICS'
        expected_telemetry = {
            'total_resolutions': 0,
            'total_resolutions_mtp': 0,
            'total_resolutions_btp': 0,
            'total_resolutions_fp': 0,
            'total_prioritizations': 0,
            'total_acknowledgments': 0,
            'percent_acknowledged': None,
            'percent_responded': None,
            'times_to_prioritize': [],
            'times_to_acknowledge': [],
            'times_to_respond': [],
            'mean_time_to_respond': None,
            'mean_time_to_prioritize': None,
            'mean_time_to_acknowledge': None,
            'mean_ttp_month_over_month': None,
            'mean_tta_month_over_month': None,
            'mean_ttr_month_over_month': None,
            'mtp_mean_ttp': 0,
            'mtp_mean_tta': 0,
            'mtp_mean_ttr': 0,
            'btp_mean_ttp': 0,
            'btp_mean_tta': 0,
            'btp_mean_ttr': 0,
            'fp_mean_ttp': 0,
            'fp_mean_tta': 0,
            'fp_mean_ttr': 0,
        }

        upload_ciso_workflow_telemetry()

        bundle_cloud_log.assert_called_once_with(type=expected_index_key, doc=expected_telemetry)
