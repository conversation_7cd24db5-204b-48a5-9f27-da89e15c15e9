# -*- coding: utf-8 -*-
# Copyright (c) 2018 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import json
import urllib
from unittest import mock
from datetime import datetime, timedelta
from django.urls import reverse
from django.utils import timezone
from rest_framework.authtoken.models import Token
from rest_framework.test import APIClient
from unittest.mock import patch
from django.test.utils import override_settings
from base_tvui.feature_flipper.base import SimpleFlag

from tvui.detections.detection_types import DetectionType
from tvui.models import (
    Account,
    AccountGroup,
    CloseHistory,
    detection,
    ExternalDomainGroup,
    HealthSubject,
    HealthSubjectHistory,
    HostGroup,
    host,
    host_session,
    IPGroup,
    GroupCollection,
    LinkedAccount,
    HostGroup,
    setting,
    smart_rule,
    StateReasons,
    User,
    DataSourceType,
    detection_detail,
)
from base_tvui import account_type
from base_tvui.lib_ldap import LdapClient
from vui_tests.vui_testcase import VuiTestCase
from vui_tests.base_tvui.smart_rules import testing_utils

from freezegun import freeze_time


class BaseTests(VuiTestCase):
    """
    Basic tests for API V2.5
    """

    def setUp(self):
        self.now = timezone.now()


class BasicAPIV2_5Tests(BaseTests):
    """
    API Client authentication.
    """

    def setUp(self):
        super(BasicAPIV2_5Tests, self).setUp()
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')

        # ignore permissions
        self.mock_perm_check = self.add_patch(
            'has_permission', mock.patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True
        self.mock_perm_check2 = self.add_patch('backend_has_perm', mock.patch('base_tvui.auth_backends.VectraModelBackend.has_perm'))
        self.mock_perm_check2.return_value = True

        # client
        self.client = APIClient(REMOTE_ADDR='************')
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)
        self.client.force_authenticate(user=self.user, token=self.token)


@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'customer_api_v2_5': SimpleFlag(lambda: True)})
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups': SimpleFlag(lambda: True)})
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'ad_groups': SimpleFlag(lambda: True)})
class APIV2_5MultiGroupsTest(BasicAPIV2_5Tests):
    """
    Test Groups Endpoint in API v2.5
    """

    def setUp(self):
        super(APIV2_5MultiGroupsTest, self).setUp()

        # feature flags
        setting.objects.create(group='feature', key='host_groups', value='on')
        setting.objects.create(group='feature', key='account_groups', value='on')

        # create accounts
        self.account_0 = Account.objects.create(uid='test_account_0', account_type=Account.TYPE_KERBEROS)
        self.account_1 = Account.objects.create(uid='test_account_1', account_type=Account.TYPE_KERBEROS)
        self.account_2 = Account.objects.create(uid='test_account_2', account_type=Account.TYPE_KERBEROS)

        # create smart rules
        self.sr = smart_rule.objects.create(
            type='typeA',
            priority=40,
            conditions=testing_utils.get_smart_rule_conditions(source_conditions=None, additional_conditions=None),
        )

        # create group
        self.accountgroup_description = None
        self.accountgroup_last_modified = self.now
        self.accountgroup_modified_by_username = 'vadmin'
        self.accountgroup_members = [self.account_0, self.account_2]
        self.accountgroup_rules = []
        self.accountgroup_name = 'Test Name'
        self.accountgroup_type = 'account'
        self.accountgroup_last_modified_by = self.user
        self.accountgroup_family = GroupCollection.FAMILY_CUSTOMER
        self.accountgroup_sr = [{'description': self.sr.description, 'id': self.sr.id, 'triage_category': self.sr.category}]
        self.acct_rule = {"rule_conditions": {"OR": [{"AND": ["test_account_[\d]"]}]}}
        self.dynamic_acct_group = AccountGroup.objects.create(
            name='testAccountGroup', last_modified_by=self.user, rule=self.acct_rule, member_type='dynamic'
        )
        self.dynamic_acct_group1 = AccountGroup.objects.create(
            name='testAccountGroup1',
            last_modified_by=self.user,
            rule=self.acct_rule,
            member_type='dynamic',
            member_eval_pending='1111-2222-333-444',
        )
        self.host_rule = {"rule_conditions": {"OR": [{"AND": ["VHE[\d]*"]}]}}
        self.dynamic_host_group1 = HostGroup.objects.create(
            name='testHostGroup1',
            last_modified_by=self.user,
            rule=self.host_rule,
            member_type='dynamic',
            member_eval_pending=None,
        )

        self.test_ip_groups = IPGroup.objects.create(
            name='ip_grp1', description='Test 1 Internal IP Group', last_modified_by=self.user, ips='*************'
        )

        # create AD groups
        self.ad_acct_group1 = AccountGroup.objects.create(
            name='testAdGroup1',
            last_modified_by=self.user,
            importance='low',
            member_type='active_directory',
            ad_group_dn='CN=TestGroup,OU=Groups,DC=example,DC=com',
        )

        self.ad_host_group1 = HostGroup.objects.create(
            name='testAdGroup2',
            last_modified_by=self.user,
            importance='high',
            member_type='active_directory',
            ad_group_dn='CN=Testing,OU=Groups,DC=example,DC=com',
        )

        # create account group collection
        self.accountgroup = AccountGroup.objects.create(
            name=self.accountgroup_name,
            type=self.accountgroup_type,
            last_modified_timestamp=self.accountgroup_last_modified,
            last_modified_by=self.accountgroup_last_modified_by,
            family=self.accountgroup_family,
            description=self.accountgroup_description,
        )
        self.accountgroup.accountgroup.accounts.set(self.accountgroup_members)
        self.accountgroup.accountgroup.smart_rule_set.set([self.sr])

        self.test_host = host.objects.create(name='host', state='active', last_source='*************')
        self.test_host_group1 = HostGroup.objects.create(name='test1', type='host', last_modified_by=self.user)
        self.test_host_group2 = HostGroup.objects.create(
            name='test2', type='host', description='Test 2 Host Group', last_modified_by=self.user
        )

        self.test_host_group1.hosts.add(self.test_host)
        self.test_host_group2.hosts.add(self.test_host)

        self.url = reverse('api-v2.5:api-groups-v2.5')
        self.regex = '(\w\d)*@vectra.ai'
        self.invalid_regex = '(\w\d)*@(vectra.ai'

    def _validate_account_group_creation(self, response_group_id, post_payload):
        """
        uses the id from the response to locate the created account group,
        then compares the created account group object with data from the post_payload
        """
        account_group = AccountGroup.objects.get(id=response_group_id)
        self.assertEqual(account_group.name, post_payload['name'])
        self.assertEqual(account_group.description, post_payload['description'])
        self.assertEqual(account_group.type, post_payload['type'])
        self.assertEqual(account_group.last_modified_by.display_username, self.user.display_username)

    def _validate_host_group_creation(self, response_group_id, post_payload):
        """
        uses the id from the response to locate the created host group,
        then compares the created host group object with data from the post_payload
        """
        host_group = HostGroup.objects.get(id=response_group_id)
        self.assertEqual(host_group.name, post_payload['name'])
        self.assertEqual(host_group.description, post_payload['description'])
        self.assertEqual(host_group.type, post_payload['type'])
        self.assertEqual(host_group.last_modified_by.display_username, self.user.display_username)

    def tearDown(self):
        Account.objects.all().delete()
        AccountGroup.objects.all().delete()
        host.objects.all().delete()
        HostGroup.objects.all().delete()
        IPGroup.objects.all().delete()
        User.objects.all().delete()
        GroupCollection.objects.all().delete()
        smart_rule.objects.all().delete()

    def test_api_v2_5_groups_failed_query_params(self):
        """Test V2.5 /groups endpoint GET failed query params"""

        # test invalid query param account_ids
        url = (
            reverse('api-v2.5:api-groups-v2.5')
            + '?type=account&account_ids='
            + ','.join(list(str(acct.id) for acct in self.accountgroup_members))
        )
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 400)

    def test_api_v2_5_groups_pagination(self):
        """Test V2.5 /groups endpoint GET pagination"""

        # test pagination query params
        url = reverse('api-v2.5:api-groups-v2.5') + '?page_size=1&page=1'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)

    def test_api_v2_5_groups_unauthorized_fail(self):
        """Test V2.5 /groups endpoint GET unauthorize fail"""
        # unauthorize group
        self.mock_perm_check.return_value = False
        url = reverse('api-v2.5:api-groups-v2.5')
        resp = self.client.get(url)
        # verify response unauthorized
        self.assertEqual(resp.status_code, 403)
        # reset permissions
        self.mock_perm_check.return_value = True

    def test_api_v2_5_groups_include_members_param(self):
        """Test V2.5 Groups with include_members param"""
        url = reverse('api-v2.5:api-groups-v2.5')
        url_params = {'include_members': 'false'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        resp = self.client.get(full_url)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['results'][0]['members'], [])

    def test_api_v2_5_groups_is_regex_param(self):
        """Test V2.5 Groups with is_regex param"""
        url = reverse('api-v2.5:api-groups-v2.5')
        url_params = {'is_regex': 'True'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        resp = self.client.get(full_url)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 3)

    def test_api_v2_5_groups_is_membership_evaluation_ongoing_param(self):
        """Test V2.5 Groups with is_membership_evaluation_ongoing param"""
        url = reverse('api-v2.5:api-groups-v2.5')
        url_params = {'is_membership_evaluation_ongoing': 'True'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        resp = self.client.get(full_url)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

        resp_data = json.loads(resp.content)
        self.assertEqual(resp_data['count'], 1)

    def test_api_v2_5_group(self):
        """Test V2.5 Single Group"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.test_host_group1.id})
        resp = self.client.get(url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.test_host_group1.name)
        self.assertEqual(resp_data["regex"], None)
        self.assertEqual(resp_data["member_count"], 1)
        self.assertEqual(resp_data["membership_evaluation_ongoing"], False)
        self.assertEqual(resp_data["built_using"], 'static_members')

    def test_api_v2_5_dynamic_group(self):
        """Test V2.5 Single Dynamic Group"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_acct_group.id})
        resp = self.client.get(url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.dynamic_acct_group.name)
        self.assertIsNotNone(resp_data["regex"])
        self.assertEqual(resp_data["built_using"], 'regex')

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_dynamic_account_group(self):
        """Test V2.5 PATCH Account Single Dynamic Group"""

        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_acct_group.id})
        data = {
            "name": self.dynamic_acct_group.name,
            "type": "account",
            "members": [],
            "description": "Different Description",
            "regex": "test_account_[\d]",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.dynamic_acct_group.name)
        self.assertEqual(resp_data["regex"], "test_account_[\d]")
        self.assertEqual(resp_data['description'], "Different Description")

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_dynamic_account_group_invalid_regex(self):
        """Test V2.5 PATCH Account Single Dynamic Group with invalid regex"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_acct_group.id})
        data = {
            "name": self.dynamic_acct_group.name,
            "type": "account",
            "members": [],
            "description": "Different Description",
            "regex": "(test_account_[\d]",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        # verify endpoint
        self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_dynamic_host_group(self):
        """Test V2.5 PATCH Host Single Dynamic Group"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_host_group1.id})
        data = {
            "name": "new hg name",
            "type": "host",
            "members": [],
            "description": "Different Description",
            "regex": "host_[\d]?",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], "new hg name")
        self.assertEqual(resp_data["regex"], "host_[\d]?")
        self.assertEqual(resp_data['description'], "Different Description")

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_dynamic_host_group_invalid_regex(self):
        """Test V2.5 PATCH Host Single Dynamic Group errors with invalid regex"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_host_group1.id})
        data = {
            "name": "new hg name",
            "type": "host",
            "members": [],
            "description": "Different Description",
            "regex": "host_[\d]?)",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        # verify endpoint
        self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_dynamic_host_group_with_members_errors(self):
        """Test V2.5 PATCH Host Single Dynamic Group errors if members included"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_host_group1.id})
        data = {
            "name": "new hg name",
            "type": "host",
            "members": [self.test_host.id],
            "description": "Different Description",
            "regex": "host_[\d]?",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        # verify endpoint
        self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_dynamic_host_group_no_regex_errors(self):
        """Test V2.5 PATCH Host Single Dynamic Group errors with no regex"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.dynamic_host_group1.id})
        data = {
            "name": "new hg name",
            "type": "host",
            "description": "Different Description",
            "regex": None,
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        # verify endpoint
        self.assertEqual(resp.status_code, 400)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_IP_group_error(self):
        """Test V2.5 PATCH IP Single Dynamic Group"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.test_ip_groups.id})
        data = {
            "name": "ip group",
            "type": "IP",
            "members": [],
            "description": "Different Description",
            "regex": "host_[\d]?",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        # verify endpoint
        self.assertEqual(resp.status_code, 400)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_patch_static_group_regex_errors(self):
        """Test V2.5 PATCH Account Single Dynamic Group with invalid regex"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.accountgroup.id})
        data = {
            "name": self.accountgroup.name,
            "type": "account",
            "members": [],
            "regex": "(test_account_[\d])",
        }
        resp = self.client.patch(url, json.dumps(data), content_type='application/json')
        # verify endpoint
        self.assertEqual(resp.status_code, 400)

    def test_api_v2_5_groups_create_account(self):
        """Test V2.5 POST Account Group success"""
        data = {
            "name": "test_group_account_success",
            "type": "account",
            "members": [str(self.account_0.uid)],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        resp_data = json.loads(resp.content)
        self._validate_account_group_creation(resp_data['group']['id'], data)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_dynamic_groups_create_account(self):
        """Test V2.5 POST Account Dynamic Group success"""
        data = {"name": "test_dynamic_group_account_success", "type": "account", "regex": self.regex, "description": "Test Description"}
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        resp_data = json.loads(resp.content)
        self.assertEqual(resp.status_code, 201)
        self._validate_account_group_creation(resp_data['group']['id'], data)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_dynamic_groups_create_account_invalid(self):
        """Test V2.5 POST Account Dynamic Group Invalid Regex"""
        data = {
            "name": "test_group_account_invalid_regex",
            "type": "account",
            "regex": self.invalid_regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_dynamic_groups_create_host(self):
        """Test V2.5 POST Host Dynamic Group success"""
        data = {
            "name": "test_dynamic_group_host_success",
            "type": "host",
            "regex": self.regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        resp_data = json.loads(resp.content)
        self._validate_host_group_creation(resp_data['group']['id'], data)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v2_5_dynamic_groups_create_host_with_members_errors(self):
        """Test V2.5 POST Host Dynamic Group success"""
        data = {
            "name": "test_dynamic_group_host_success",
            "type": "host",
            "members": [self.test_host.id],
            "regex": self.regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)

    def test_api_v2_5_groups_create_host(self):
        """Test V2.5 POST Host Group success"""
        data = {
            "name": "test_group_host_success",
            "type": "host",
            "members": [self.test_host.id],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        resp_data = json.loads(resp.content)
        self.assertEqual(resp.status_code, 201)
        self._validate_host_group_creation(resp_data['group']['id'], data)

    def test_api_v2_5_groups_create_host_invalid(self):
        """Test V2.5 POST Host Groups cannot have members and regex"""
        data = {
            "name": "test_group_host_members_and_regex",
            "type": "host",
            "members": [self.test_host.id],
            "regex": self.regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)

    def test_api_v2_5_dynamic_groups_create_host_invalid(self):
        """Test V2.5 POST Dynamic Group Host Invalid Regex"""
        data = {
            "name": "test_group_host_invalid_regex",
            "type": "host",
            "regex": self.invalid_regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)

    def test_api_v2_5_dynamic_groups_create_host_invalid_IP_type(self):
        """Test V2.5 POST Dynamic Group Host Invalid IP type"""
        data = {
            "name": "test_group_host_invalid_ip_type",
            "type": "IP",
            "regex": self.regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)

    def test_api_v2_5_groups_create_ip(self):
        """Test V2.5 POST IP Group success"""
        data = {
            "name": "test_group_ip",
            "type": "ip",
            "members": [],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)

    def test_api_v2_5_groups_create_domain(self):
        """Test V2.5 POST Domain Group success"""
        data = {
            "name": "test_group_domain",
            "type": "domain",
            "members": [],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)

    def test_api_v2_5_dynamic_groups_create_domain_invalid(self):
        """Test V2.5 POST Domain Dynamic Group Invalid"""
        data = {
            "name": "test_dynamic_group_domain",
            "type": "domain",
            "regex": self.regex,
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups': SimpleFlag(lambda: False)})
    def test_api_v2_5_groups_create_account_invalid_key(self):
        """Test V2.5 POST Account Group invalid key with dynamic_groups flag off"""
        data = {
            "name": "test_group_account_invalid_key",
            "type": "account",
            "members": [],
            "description": "Test Description",
            "invalid key": "",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 400)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups': SimpleFlag(lambda: False)})
    def test_api_v2_5_groups_create_account_dynamic_groups_off(self):
        """Test V2.5 POST Account Group success with dynamic_groups flag off"""
        data = {
            "name": "test_group_account_feature_flag_off",
            "type": "account",
            "members": [],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)

    def test_api_v2_5_groups_create_account_invalid_members(self):
        """Test V2.5 POST Account Group success with invalid members"""
        data = {
            "name": "test_account_group_invalid_members",
            "type": "account",
            "members": [str(self.account_0.uid), "invalid", str(self.account_1.uid)],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        resp_data = json.loads(resp.content)
        self.assertEqual('Account ids not found: invalid', resp_data['_meta']['message']['members'])

    def test_api_v2_5_groups_create_host_invalid_members_id(self):
        """Test V2.5 POST Host Group with invalid members (id)"""
        invalid_id = "234345"
        data = {
            "name": "test_host_group_invalid_members",
            "type": "host",
            "members": [str(self.test_host.id), invalid_id],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)
        resp_data = json.loads(resp.content)
        self.assertEqual(f'Host ids not found: {invalid_id}', resp_data['_meta']['message']['members'])

    def test_api_v2_5_groups_create_host_invalid_members_name(self):
        """Test V2.5 POST Host Group with invalid members (name)"""
        invalid_name = "invalid"
        data = {
            "name": "test_host_group_invalid_members",
            "type": "host",
            "members": [str(self.test_host.name), invalid_name],
            "description": "Test Description",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 422)
        resp_data = json.loads(resp.content)
        self.assertEqual(f'Host ids not found: {invalid_name}', resp_data['_meta']['message']['members'])

    def test_api_v2_5_post_host_group_with_empty_type(self):
        """Test V2.5 POST with empty type, which is not valid"""
        data = {
            "name": "test_empty_type",
            "members": [],
            "description": "Test Description",
            "invalid key": "",
        }
        resp = self.client.post(self.url, json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 400)

    def test_api_v2_5_get_groups_limit(self):
        """Test V2.5 GET Groups with 2000 member limit"""
        accounts_to_create = []
        for i in range(2001):
            account = {
                'uid': f'test{i}@account.com',
                'account_type': account_type.AccountType.KERBEROS,
                'first_seen': self.now,
                'last_seen': self.now,
            }
            accounts_to_create.append(Account(**account))

        accts = Account.objects.bulk_create(accounts_to_create, 2001)
        self.accountgroup.accounts.add(*accts)
        self.accountgroup.save()
        url = reverse('api-v2.5:api-groups-v2.5')
        resp = self.client.get(url)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp_data["count"], 9)
        for group in resp_data["results"]:
            self.assertLessEqual(len(group["members"]), 2000)

    def test_api_v2_5_get_groups_empty_members(self):
        """Test V2.5 GET Groups with empty members"""
        self.no_member_account = AccountGroup.objects.create(
            name='no_member_account',
            last_modified_by=self.user,
            member_type='static',
        )

        self.no_member_ip = IPGroup.objects.create(name='no_member_ip', description='Test 1 Internal IP Group', last_modified_by=self.user)
        self.no_member_domain = ExternalDomainGroup.objects.create(
            name='no_member_domain', description='Test 1 Internal Domain Group', last_modified_by=self.user
        )

        self.no_member_host = HostGroup.objects.create(
            name='no_member_host', description='Test 1 Internal Host Group', last_modified_by=self.user
        )
        url = reverse('api-v2.5:api-groups-v2.5')
        resp = self.client.get(url)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

    def test_api_v2_5_ad_groups_is_ad_group_param(self):
        """Test V2.5 Groups with is_ad_group param"""
        url = reverse('api-v2.5:api-groups-v2.5')
        url_params = {'is_ad_group': 'True'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        resp = self.client.get(full_url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 2)

    def test_api_v2_5_ad_groups_ad_group_dn_param(self):
        """Test V2.5 AD Groups with ad_group_dn param"""
        url = reverse('api-v2.5:api-groups-v2.5')
        url_params = {'ad_group_dn': 'TestGroup'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        resp = self.client.get(full_url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 1)

    def test_api_v2_5_ad_group(self):
        """Test V2.5 Single AD Group"""
        url = reverse('api-v2.5:api-group-v2.5', kwargs={'pk': self.ad_acct_group1.id})
        resp = self.client.get(url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.ad_acct_group1.name)
        self.assertEqual(resp_data["ad_group_dn"], 'CN=TestGroup,OU=Groups,DC=example,DC=com')
        self.assertEqual(resp_data["built_using"], 'ad_group')


@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'ad_groups': SimpleFlag(lambda: True)})
class ActiveDirectoryGroupsV2_5Test(BasicAPIV2_5Tests):
    """
    Test settings/active_directory/groups/ Endpoint in API v2.5
    """

    def setUp(self):
        super().setUp()
        self.url = reverse('api-v2.5:api-settings-groups-active-directory-v2.5')
        self.get_ldap_service_top_config_multi_result = {
            '_id': 'ldap_service_multi',
            'name': 'ldap_service_multi',
            'profiles': {
                'sdasjlj2': {
                    "autobind": "NONE",
                    "basedns": ["DC=qe-ad,DC=test"],
                    "bind_dn": "CN=Albert Einstein,CN=Users,DC=qe-ad,DC=test",
                    "bind_pwd": "encr:5dS3FIA+dX0zzmR6s4ua82cET0OOVDv2MQmtyeVKkXVnBE9DjlQ79jEJrcnlSpF1ZwRPQ45UO/YxCa3J5UqRdWcET0OOVDv2MQmtyeVKkXVnBE9DjlQ79jEJrcnlSpF1ZwRPQ45UO/YxCa3J5UqRdWcET0OOVDv2MQmtyeVKkXU=",
                    "cache_update_speed": 7200,
                    "connection_timeout": 180,
                    "enabled": True,
                    "ldaps": False,
                    "profile_name": "queryprofile",
                    "ad_profile_name": "ad-profile-1",
                    "query_timeout": 30,
                    "search_param": "sAMAccountName",
                    "server_uris": ["192.168.49.204"],
                    "starttls": False,
                },
            },
        }
        self.ldap_groups_multi_all_result = {
            'sdasjlj2': [
                "cn=aws-sso-vectra-log-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
                "cn=data-science-team,ou=security groups,ou=vectra,dc=vectra,dc=io",
                "cn=aws-sso-saas-dns-root-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
            ],
        }
        self.settings_groups_active_directory_result = {
            'ad-profile-1': {
                'groups': [
                    "cn=aws-sso-vectra-log-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
                    "cn=data-science-team,ou=security groups,ou=vectra,dc=vectra,dc=io",
                    "cn=aws-sso-saas-dns-root-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
                ],
            },
        }

    @patch.object(LdapClient, 'get_ldap_service_top_config_multi')
    @patch.object(LdapClient, 'get_ldap_groups_multi')
    def test_api_v2_5_get_settings_groups_active_directory(self, ldap_groups_multi_get_request, get_ldap_service_top_config_multi_request):
        """Test V2.5 GET settings groups active directory"""
        ldap_groups_multi_get_request.return_value.status_code = 200
        ldap_groups_multi_get_request.return_value = self.ldap_groups_multi_all_result
        get_ldap_service_top_config_multi_request.return_value = self.get_ldap_service_top_config_multi_result
        resp = self.client.get(self.url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data, self.settings_groups_active_directory_result)

    @patch.object(LdapClient, 'get_ldap_groups_multi')
    def test_api_v2_5_get_settings_groups_active_directory_exception(self, ldap_groups_multi_get_request):
        """Test V2.5 GET settings groups active directory exception"""
        ldap_groups_multi_get_request.return_value.status_code = 500
        ldap_groups_multi_get_request.return_value = Exception()

        resp = self.client.get(self.url)
        # verify endpoint
        self.assertEqual(resp.status_code, 500)
        self.assertEqual(resp.json(), {'error': 'Unable to fetch AD groups. Please retry.'})


@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'platform/algo_health/enabled': SimpleFlag(lambda: True)})
class APIV2_5HealthTest(BasicAPIV2_5Tests):
    """
    Test Health Endpoint in API v2.5
    """

    def setUp(self):
        super().setUp()

        self.now_dt = datetime(2024, 3, 8, 11, 0, tzinfo=timezone.utc)

        # create health data
        self.cpu_data = {"user_percent": 13.8, "nice_percent": 0, "system_percent": 5.6, "idle_percent": 79.9}
        self.cpu_health = HealthSubject.objects.create(subject_id="v2-health-api_cpu")
        HealthSubjectHistory.objects.create(health_subject=self.cpu_health, timestamp=self.now_dt, content=self.cpu_data, expires=None)

        self.disk_data = {
            "disk_utilization": {"total_bytes": 67306565632, "free_bytes": 39748980736, "used_bytes": 27557584896, "usage_percent": 40.94}
        }
        self.disk_health = HealthSubject.objects.create(subject_id="v2-health-api_disk")
        HealthSubjectHistory.objects.create(health_subject=self.disk_health, timestamp=self.now_dt, content=self.disk_data, expires=None)

        self.memory_data = {"usage_percent": 39.8, "free_bytes": 13021868032, "used_bytes": 28262363136, "total_bytes": 67444695040}
        self.memory_health = HealthSubject.objects.create(subject_id="v2-health-api_memory")
        HealthSubjectHistory.objects.create(
            health_subject=self.memory_health, timestamp=self.now_dt, content=self.memory_data, expires=None
        )

        self.network_data = {
            "interfaces": {"brain": {}, "sensors": {}},
            "traffic": {
                "brain": {"aggregated_peak_traffic_mbps": 0, "interface_peak_traffic": {}},
                "sensors": {
                    "*************": {"aggregated_peak_traffic_mbps": 0, "interface_peak_traffic": {"eth0": {"peak_traffic_mbps": 0}}},
                    "sc-dogfood-vs5": {"aggregated_peak_traffic_mbps": 570, "interface_peak_traffic": {"eth0": {"peak_traffic_mbps": 570}}},
                },
            },
            "vlans": {"vlan_ids": [], "count": 0},
        }
        self.network_health = HealthSubject.objects.create(subject_id="v2-health-api_network")
        HealthSubjectHistory.objects.create(
            health_subject=self.network_health, timestamp=self.now_dt, content=self.network_data, expires=None
        )

        self.power_data = {
            "status": "OK",
            "error": "Power supply OK",
            "power_supplies": {"1": {"faults": [], "present": True}, "2": {"faults": [], "present": True}},
        }
        self.power_health = HealthSubject.objects.create(subject_id="v2-health-api_power")
        HealthSubjectHistory.objects.create(health_subject=self.power_health, timestamp=self.now_dt, content=self.power_data, expires=None)

        self.sensor_data = [
            {
                "id": 13,
                "location": None,
                "serial_number": "V422c6a70147dadaa2c502f570478836b",
                "luid": "xxcbwo6u",
                "status": "paired",
                "version": "8.8.0-7-13",
                "ip_address": "**************",
                "ssh_tunnel_port": "36389",
                "public_key": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxzOuTirVKEfCOFk5Rsgm\nnLmE0rB2nTXMiUWNLMfSvfhzgEhxtLoZt6gjVkus3ozco7gQC5V5tpiUcEPfkAAx\napFC3BHtKlN7cHKrHyXubCd2w/io8jb8n8c8nvvDyHx1h4+ixvQeaGy08rlXGxxP\nxOmJaHYfVVgX6kLNmQ9gQBdE1YysHY4/xWCrTneoAFt9dAH2E+qhKYkQLaW9MCU5\nMjpqI7kz6+N3WT+OKBQxqkGCDFRyRMC9lpPnNBp22CDrFwSVCDxSFAbuFLR8IDZP\n6v84DPFBml1vD4x3um76uUWAkaIdHia8CThdh3NTFI1AnGz3jsPX//zD5SOCQNXs\nfwIDAQAB\n-----END PUBLIC KEY-----\n",
                "product_name": "DCS",
                "mode": "sensor",
                "headend_uri": "*************",
                "original_version": "6.2.0-18-33",
                "last_seen": "2024-09-23T12:02:10.049Z",
                "update_count": 0,
                "name": "*************",
            }
        ]
        self.sensors_health = HealthSubject.objects.create(subject_id="v2-health-api_sensors")
        HealthSubjectHistory.objects.create(
            health_subject=self.sensors_health, timestamp=self.now_dt, content=self.sensor_data, expires=None
        )

        self.system_data = {
            "uptime": "2 hours, 23 minutes",
            "serial_number": "VHEf36c4f65e571181cbc756454aa1ecaa3",
            "version": {
                "last_update": "Sat Sep  7 04:52:50 2024",
                "model": "VHE",
                "mode": "brain",
                "cloud_bridge": False,
                "gmt": "2024-09-23T12:00:55.388751Z",
                "vm_type": "kvm",
                "vectra_instance_type": "dev",
                "vectra_version": "8.7.1-1-29",
            },
        }
        self.system_health = HealthSubject.objects.create(subject_id="v2-health-api_system")
        HealthSubjectHistory.objects.create(
            health_subject=self.system_health, timestamp=self.now_dt, content=self.system_data, expires=None
        )

        self.hostid_data = {
            "artifact_counts": {
                "TestEDR": 0,
                "TestEDR_0000": 0,
                "TestEDR_0001": 0,
                "TestEDR_0002": 0,
                "arsenic": 0,
                "carbon_black": 0,
                "cb_cloud": 0,
                "clear_state": 0,
                "cookie": 0,
                "crowdstrike": 56,
                "cybereason": 0,
                "dhcp": 41946,
                "dns": 6516954,
                "end_time": 0,
                "fireeye": 0,
                "generic_edr": 8300,
                "idle_end": 140544,
                "idle_start": 141939,
                "invalid": 0,
                "kerberos": 25031,
                "kerberos_user": 12669,
                "mdns": 7615,
                "netbios": 33964,
                "proxy_ip": 0,
                "rdns": 122309,
                "sentinelone": 0,
                "split": 6,
                "src_port": 0,
                "static_ip": 0,
                "total": 7469678,
                "uagent": 418291,
                "vmachine_info": 54,
                "windows_defender": 0,
                "zpa_user": 0,
            },
            "ip_always_percent": 68.49,
            "ip_sometimes_percent": 0.58,
            "ip_never_percent": 30.93,
        }
        self.hostid_health = HealthSubject.objects.create(subject_id="v2-health-api_hostid")
        HealthSubjectHistory.objects.create(
            health_subject=self.hostid_health, timestamp=self.now_dt, content=self.hostid_data, expires=None
        )

        self.connectivity_data = {
            "sensors": [
                {
                    "name": "*************",
                    "output": {"last_check": "2024-09-23T11"},
                    "error": "metadata replicaton seems fine",
                    "status": "OK",
                    "serial_number": "V422c6a70147dadaa2c502f570478836b",
                    "luid": "xxcbwo6u",
                    "ip_address": "**************",
                }
            ]
        }
        self.connectivity_health = HealthSubject.objects.create(subject_id="v2-health-api_connectivity")
        HealthSubjectHistory.objects.create(
            health_subject=self.connectivity_health, timestamp=self.now_dt, content=self.connectivity_data, expires=None
        )

        self.trafficdrop_data = {
            "sensors": {
                "name": "*************",
                "output": [],
                "error": "All interfaces have traffic volume within range",
                "status": "OK",
                "serial_number": "V422c6a70147dadaa2c502f570478836b",
                "luid": "xxcbwo6u",
                "ip_address": "**************",
            }
        }
        self.trafficdrop_health = HealthSubject.objects.create(subject_id="v2-health-api_trafficdrop")
        HealthSubjectHistory.objects.create(
            health_subject=self.trafficdrop_health, timestamp=self.now_dt, content=self.trafficdrop_data, expires=None
        )

        self.detection_data = {
            "check_results": [
                {
                    "name": "Detection Model Unhealthy",
                    "check_type": None,
                    "message": "All detection models are healthy.",
                    "status": "OK",
                },
            ]
        }
        self.detection_health = HealthSubject.objects.create(subject_id="v2-health-api_detection")
        HealthSubjectHistory.objects.create(
            health_subject=self.detection_health, timestamp=self.now_dt, content=self.detection_data, expires=None
        )

        self.health_data = {
            k: {**v, "updated_at": str(self.now_dt)} if isinstance(v, dict) else v
            for k, v in {
                "cpu": self.cpu_data,
                "disk": self.disk_data,
                "memory": self.memory_data,
                "network": self.network_data,
                "power": self.power_data,
                "sensors": self.sensor_data,
                "system": self.system_data,
                "hostid": self.hostid_data,
                "connectivity": self.connectivity_data,
                "trafficdrop": self.trafficdrop_data,
                "detection": self.detection_data,
            }.items()
        }

    def tearDown(self):
        HealthSubject.objects.all().delete()
        HealthSubjectHistory.objects.all().delete()

    def test_api_v2_5_health_unauthorized_fail(self):
        """Test V2.5 /health endpoint GET unauthorize fail"""
        # unauthorize health
        self.mock_perm_check.return_value = False

        health_url = reverse('api-v2.5:api-health-v2.5')
        check_url = reverse('api-v2.5:api-health-check-v2.5', args=["disk"])
        health_resp = self.client.get(health_url)
        check_resp = self.client.get(check_url)
        # verify response unauthorized
        self.assertEqual(health_resp.status_code, 403)
        self.assertEqual(check_resp.status_code, 403)

        # reset permissions
        self.mock_perm_check.return_value = True

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'platform/algo_health/enabled': SimpleFlag(lambda: False)})
    def test_api_v2_5_health_no_detection_data_wo_flag(self):
        url = reverse("api-v2.5:api-health-check-v2.5", args=["detection"])
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.data, {"detection": {}})

    def test_api_v2_5_health_detection(self):
        url = reverse("api-v2.5:api-health-check-v2.5", args=["detection"])
        resp = self.client.get(url, {"cache": True})
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.data, {"detection": {**self.detection_data, "updated_at": str(self.now_dt)}})

    def test_api_v2_5_detection_included_in_health(self):
        url = reverse("api-v2.5:api-health-v2.5")
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.data, self.health_data)
        self.assertTrue("detection" in resp.data)

    def test_api_v2_5_invalid_check(self):
        url = reverse("api-v2.5:api-health-check-v2.5", args=["nonexistent"])
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 400)


class APIV2_5GroupMembersTests(BasicAPIV2_5Tests):

    def setUp(self):
        super(APIV2_5GroupMembersTests, self).setUp()

        # feature flags
        setting.objects.create(group='feature', key='host_groups', value='on')
        setting.objects.create(group='feature', key='account_groups', value='on')

        # create accounts
        self.account_0 = Account.objects.create(uid='test_account_0', account_type=Account.TYPE_KERBEROS)
        self.account_1 = Account.objects.create(uid='test_account_1', account_type=Account.TYPE_KERBEROS)
        self.account_2 = Account.objects.create(uid='test_account_2', account_type=Account.TYPE_KERBEROS)

        # create smart rules
        self.sr = smart_rule.objects.create(
            type='typeA',
            priority=40,
            conditions=testing_utils.get_smart_rule_conditions(source_conditions=None, additional_conditions=None),
        )

        # create account group
        self.accountgroup_last_modified = self.now
        self.accountgroup_modified_by_username = 'vadmin'
        self.accountgroup_description = 'description'
        self.accountgroup_members = [self.account_0, self.account_2]
        self.accountgroup_rules = []
        self.accountgroup_name = 'Test Name'
        self.accountgroup_type = 'account'
        self.accountgroup_last_modified_by = self.user
        self.accountgroup_family = GroupCollection.FAMILY_CUSTOMER
        self.accountgroup_sr = [{'description': self.sr.description, 'id': self.sr.id, 'triage_category': self.sr.category}]
        self.acct_rule = {"rule_conditions": {"OR": [{"AND": ["test_account_[\d]"]}]}}
        self.dynamic_acct_group = AccountGroup.objects.create(
            name='testAccountGroup', last_modified_by=self.user, rule=self.acct_rule, member_type='dynamic'
        )
        self.dynamic_acct_group1 = AccountGroup.objects.create(
            name='testAccountGroup1',
            last_modified_by=self.user,
            rule=self.acct_rule,
            member_type='dynamic',
            member_eval_pending='1111-2222-333-444',
        )
        # create static account group
        self.static_acct_group1 = AccountGroup.objects.create(
            name='staticTestAccountGroup',
            last_modified_by=self.user,
        )
        self.static_acct_group1.accountgroup.accounts.set(self.accountgroup_members)

        # create dynamic host group
        self.host_rule = {"rule_conditions": {"OR": [{"AND": ["VHE[\d]*"]}]}}
        self.dynamic_host_group1 = HostGroup.objects.create(
            name='testHostGroup1',
            last_modified_by=self.user,
            rule=self.host_rule,
            member_type='dynamic',
            member_eval_pending=None,
        )

        # create static host group
        self.host_group1 = HostGroup.objects.create(
            name='testStaticHostGroup1',
            last_modified_by=self.user,
            member_type='static',
        )

        self.test_ip_groups = IPGroup.objects.create(
            name='ip_grp1', description='Test 1 Internal IP Group', last_modified_by=self.user, ips='*************'
        )

        # create account group collection
        self.accountgroup = AccountGroup.objects.create(
            name=self.accountgroup_name,
            type=self.accountgroup_type,
            last_modified_timestamp=self.accountgroup_last_modified,
            last_modified_by=self.accountgroup_last_modified_by,
            family=self.accountgroup_family,
            description=self.accountgroup_description,
        )
        self.accountgroup.accountgroup.accounts.set(self.accountgroup_members)
        self.accountgroup.accountgroup.smart_rule_set.set([self.sr])

        self.test_host = host.objects.create(name='host', state='active', last_source='*************')
        self.test_host_2 = host.objects.create(name='host2', state='active', last_source='*************')

        self.dynamic_host_group1.hosts.add(self.test_host)
        self.dynamic_host_group1.hosts.add(self.test_host_2)
        self.host_group1.hosts.add(self.test_host)
        self.host_group1.hosts.add(self.test_host_2)

        self.regex = '(\w\d)*@vectra.ai'

        self.ipgroup = IPGroup.objects.create(
            name='Company IPS',
            type=GroupCollection.IP,
            description='ip list',
            last_modified_by=self.user,
            ips=['*******', '*******', '*******'],
        )

        self.ext_domain_group1 = ExternalDomainGroup.objects.create(
            name='Company Datacenter Domains',
            type=GroupCollection.DOMAIN,
            description='domain list',
            last_modified_by=self.user,
            domains=['apple.com', 'vectra.ai'],
        )

    def tearDown(self):
        host.objects.all().delete()

    def test_api_v2_5_group_members(self):
        """Test V2.5 /groups members endpoint GET"""

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.static_acct_group1.id})
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == self.static_acct_group1.accounts.count()

    def test_api_v2_5_group_members_pagination(self):
        """Test V2.5 /groups members endpoint GET pagination"""

        # test pagination query params
        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.dynamic_acct_group.id}) + '?page_size=1&page=1'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)

    def test_api_v2_5_group_members_host(self):
        """Test V2.5 /groups members endpoint GET host group"""

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id})
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == self.host_group1.hosts.count()

    def test_api_v2_5_group_members_host_pagination(self):
        """Test V2.5 /groups members endpoint GET host group pagination"""
        page_size = 5
        hosts = []

        for i in range(20):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}'))

        self.host_group1.hosts.set(hosts)

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id}) + f'?page_size={page_size}&page=2'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == page_size

    def test_api_v2_5_group_members_host_ordering(self):
        """Test V2.5 /groups members endpoint GET host group ordering"""
        ordering = 'name'
        hosts = []

        for i in range(20):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}'))

        self.host_group1.hosts.set(hosts)

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id}) + f'?ordering={ordering}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        result = resp.data['results']
        assert all(result[i]['name'] <= result[i + 1]['name'] for i in range(len(result) - 1))

    def test_api_v2_5_group_members_host_filter_by_name(self):
        """Test V2.5 /groups members endpoint GET host group filtering by name"""
        name = 'host-3'
        hosts = []

        for i in range(20):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}'))

        self.host_group1.hosts.set(hosts)

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id}) + f'?name={name}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == 1

    def test_api_v2_5_group_members_host_filter_by_is_key_asset_false(self):
        """Test V2.5 /groups members endpoint GET host group filtering by if key asset is false"""
        is_key_asset = 'true'
        hosts = []

        for i in range(5):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}', key_asset=True))

        for i in range(5, 20):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}', key_asset=False))

        self.host_group1.hosts.set(hosts)

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id}) + f'?is_key_asset={is_key_asset}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == 5

        is_key_asset = 'false'
        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id}) + f'?is_key_asset={is_key_asset}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == 15

    def test_api_v2_5_group_members_host_filter_by_name_and_key_asset(self):
        """Test V2.5 /groups members endpoint GET host group filtering by name"""
        name = 'host'
        is_key_asset = 'true'
        hosts = []

        for i in range(10):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}', key_asset=False))

        for i in range(10, 20):
            hosts.append(host.objects.create(name=f'host-{i}', state='active', last_source=f'*************{i}', key_asset=True))

        self.host_group1.hosts.set(hosts)

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.host_group1.id}) + f'?name={name}&is_key_asset={is_key_asset}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == 10

    def test_api_v2_5_group_members_account(self):
        """Test V2.5 /groups members endpoint GET account group"""

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.static_acct_group1.id})
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == self.static_acct_group1.accounts.count()

    def test_api_v2_5_group_members_ip(self):
        """Test V2.5 /groups members endpoint GET ip group"""

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.ipgroup.id})
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == len(self.ipgroup.ips)

    def test_api_v2_5_group_members_ip_filter_by_name(self):
        """Test V2.5 /groups members endpoint GET ip group filter by name"""
        name = '1.14'
        ips = []

        for i in range(20):
            ips.append(f'1.{i}')

        ipgroup = IPGroup.objects.create(
            name='IPS',
            type=GroupCollection.IP,
            description='ip list',
            last_modified_by=self.user,
            ips=ips,
        )

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': ipgroup.id}) + f'?name={name}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == 1

    def test_api_v2_5_group_members_domain(self):
        """Test V2.5 /groups members endpoint GET domain group"""

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': self.ext_domain_group1.id})
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == len(self.ext_domain_group1.domains)

    def test_api_v2_5_group_members_domain_filter_by_name(self):
        """Test V2.5 /groups members endpoint GET domain group filter by name"""
        name = '1.14'
        domains = []

        for i in range(20):
            domains.append(f'1.{i}')

        domaingroup = ExternalDomainGroup.objects.create(
            name='Domains',
            type=GroupCollection.DOMAIN,
            description='domain list',
            last_modified_by=self.user,
            domains=domains,
        )

        url = reverse('api-v2.5:api-group-members-v2.5', kwargs={'pk': domaingroup.id}) + f'?name={name}'
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        assert len(resp.data['results']) == 1


class DetectionsV2_5Tests(BasicAPIV2_5Tests):
    """
    Test Detections Endpoint
    """

    def setUp(self):
        super().setUp()

        self.host = host.objects.create(name='testhost1', host_luid='hluid1', state='active', last_source='*******', t_score=77, c_score=42)

        self.host_session = host_session.objects.create(
            host=self.host, session_luid='6', ip_address='*******', start=self.now - timedelta(days=3), end=self.now
        )

        self.host_group = HostGroup.objects.create(
            name='Test Host Group',
            type=GroupCollection.HOST,
            description='Test Host Group Description',
            last_modified_by=self.user,
        )
        self.host_group.hosts.add(self.host)
        # Create detections for tests

        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            t_score=10,
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            host_session=self.host_session,
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            state=detection.ACTIVE,
        )
        self.dd = detection_detail.objects.create(
            type='sql_inject',
            src_ip='*******',
            dst_ip='*******',
            dst_port='80',
            total_bytes_sent=144,
            total_bytes_rcvd=10192,
            host_detection=self.detection_1,
            couch_note_id='18xsqli94',
            dst_dns='vectra.ai',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
        )

        self.src_ip_group = IPGroup.objects.create(
            name='Test IP Group',
            type=GroupCollection.IP,
            description='Test IP Group Description',
            last_modified_by=self.user,
            ips=[self.detection_1.src_ip],
        )
        self.dst_ip_group = IPGroup.objects.create(
            name='Test Destination IP Group',
            type=GroupCollection.IP,
            description='Test Destination IP Group Description',
            last_modified_by=self.user,
            ips=[self.dd.dst_ip],
        )
        self.dst_dns_group = ExternalDomainGroup.objects.create(
            name='Test Destination DNS Group',
            type=GroupCollection.DOMAIN,
            description='Test Destination DNS Group Description',
            last_modified_by=self.user,
            domains=[self.dd.dst_dns],
        )

        self.detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            t_score=10,
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            state=detection.FIXED,
            state_reason=StateReasons.REMEDIATED.value,
        )

    def tearDown(self):
        detection.objects.all().delete()

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
    )
    def test_detections_reason_filtering(self):
        """Test detections endpoint with reason query param"""
        # Arrange
        query_params = {'reason': StateReasons.REMEDIATED.value}
        url = reverse('api-v2.5:api-detections-v2.5')
        full_url = f'{url}?{urllib.parse.urlencode(query_params)}'

        # Act
        resp = self.client.get(full_url)

        # Assert
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(len(resp_data['results']), 1)
        self.assertEqual(resp_data['results'][0]['reason'], StateReasons.REMEDIATED.value)

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
    )
    def test_detections_with_invalid_reason_filtering(self):
        """Test detections endpoint with reason query param"""
        # Arrange
        query_params = {'reason': 'invalid_reason'}
        url = reverse('api-v2.5:api-detections-v2.5')
        full_url = f'{url}?{urllib.parse.urlencode(query_params)}'

        # Act
        resp = self.client.get(full_url)

        # Assert
        self.assertEqual(resp.status_code, 400)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['reason'], "Invalid value given for paramater 'reason'. Valid values: ['benign', 'remediated'].")

    def test_include_src_dst_group_query_params(self):
        """Test detections endpoint with src_ip_group, dst_ip_group, and dst_dns_group query params"""
        url = reverse('api-v2.5:api-detections-v2.5')
        full_url = f'{url}?include_src_dst_groups=True'

        # Act
        resp = self.client.get(full_url)
        resp_data = json.loads(resp.render().content)
        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertEqual(len(resp_data['results']), 2)
        self.assertEqual(len(resp_data['results'][0]['src_groups']), 2)
        self.assertEqual(len(resp_data['results'][0]['dst_groups']), 2)

    def test_include_src_dst_group_query_params_off(self):
        """Test detections endpoint without include_src_dst_groups query param"""
        url = reverse('api-v2.5:api-detections-v2.5')
        full_url = f'{url}?include_src_dst_groups=0'

        # Act
        resp = self.client.get(full_url)
        resp_data = json.loads(resp.render().content)
        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertEqual(len(resp_data['results']), 2)
        self.assertNotIn('src_groups', dict(resp_data['results'][0]).keys())
        self.assertNotIn('dst_groups', dict(resp_data['results'][0]).keys())


class DetectionV2_5Tests(BasicAPIV2_5Tests):
    """
    Test Detections Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            t_score=10,
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            sensor_luid='1234',
            state=detection.FIXED,
            state_reason=StateReasons.REMEDIATED.value,
        )

        self.host = host.objects.create(name='testhost1', host_luid='hluid1', state='active', last_source='*******', t_score=77, c_score=42)

        self.host_session = host_session.objects.create(
            host=self.host, session_luid='6', ip_address='*******', start=self.now - timedelta(days=3), end=self.now
        )

        self.host_group = HostGroup.objects.create(
            name='Test Host Group',
            type=GroupCollection.HOST,
            description='Test Host Group Description',
            last_modified_by=self.user,
        )
        self.host_group.hosts.add(self.host)
        # Create detections for tests

        self.detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            t_score=10,
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            host_session=self.host_session,
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            state=detection.ACTIVE,
        )
        self.dd = detection_detail.objects.create(
            type='sql_inject',
            src_ip='*******',
            dst_ip='*******',
            dst_port='80',
            total_bytes_sent=144,
            total_bytes_rcvd=10192,
            host_detection=self.detection_2,
            couch_note_id='18xsqli94',
            dst_dns='vectra.ai',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
        )

        self.src_ip_group = IPGroup.objects.create(
            name='Test IP Group',
            type=GroupCollection.IP,
            description='Test IP Group Description',
            last_modified_by=self.user,
            ips=[self.detection_2.src_ip],
        )
        self.dst_ip_group = IPGroup.objects.create(
            name='Test Destination IP Group',
            type=GroupCollection.IP,
            description='Test Destination IP Group Description',
            last_modified_by=self.user,
            ips=[self.dd.dst_ip],
        )
        self.dst_dns_group = ExternalDomainGroup.objects.create(
            name='Test Destination DNS Group',
            type=GroupCollection.DOMAIN,
            description='Test Destination DNS Group Description',
            last_modified_by=self.user,
            domains=[self.dd.dst_dns],
        )

    def tearDown(self):
        detection.objects.all().delete()

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
    )
    def test_detections_with_reason(self):
        """Test detection endpoint with reason field included"""
        # Arrange
        url = reverse('api-v2.5:api-detection-v2.5', kwargs={'pk': self.detection_1.id})

        # Act
        resp = self.client.get(url)

        # Assert
        self.assertEqual(resp.status_code, 200)
        resp_data = resp.json()
        self.assertEqual(resp_data['reason'], StateReasons.REMEDIATED.value)

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: False), 'signal_efficacy_public_preview': SimpleFlag(lambda: False)},
    )
    def test_detections_with_reason_when_flag_is_disabled(self):
        """Test detection endpoint with reason and signal efficacy flags diabled"""
        # Arrange
        url = reverse('api-v2.5:api-detection-v2.5', kwargs={'pk': self.detection_1.id})

        # Act
        resp = self.client.get(url)

        # Assert
        self.assertEqual(resp.status_code, 200)
        resp_data = resp.json()
        self.assertEqual(resp_data['reason'], None)

    def test_include_src_dst_group_query_params_single_det(self):
        """Test detection endpoint with src_ip_group, dst_ip_group, and dst_dns_group query params"""
        url = reverse('api-v2.5:api-detection-v2.5', kwargs={'pk': self.detection_2.id})
        full_url = f'{url}?include_src_dst_groups=True'
        # Act
        resp = self.client.get(full_url)
        resp_data = json.loads(resp.render().content)
        print(resp_data)
        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertEqual(len(resp_data['src_groups']), 2)
        self.assertEqual(len(resp_data['dst_groups']), 2)

    def test_include_src_dst_group_query_params_off_single_det(self):
        """Test detection endpoint without include_src_dst_groups query param"""
        url = reverse('api-v2.5:api-detection-v2.5', kwargs={'pk': self.detection_2.id})
        full_url = f'{url}?include_src_dst_groups=0'

        # Act
        resp = self.client.get(full_url)
        resp_data = json.loads(resp.render().content)
        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertNotIn('src_groups', dict(resp_data).keys())
        self.assertNotIn('dst_groups', dict(resp_data).keys())


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class DetectionCloseV2_5Test(BasicAPIV2_5Tests):
    """
    Test Detection/<id>/close Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.url = reverse('api-v2.5:api-detection-close-v2.5', kwargs={'pk': self.detection_1.id})

    def tearDown(self):
        detection.objects.all().delete()

    def test_detection_close_as_remediated(self):
        """
        Test correct values are set when closing a detection as remediated
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.FIXED)
        self.assertEqual(self.detection_1.state_reason, StateReasons.REMEDIATED.value)
        history = CloseHistory.objects.get(detection_id=self.detection_1.id)
        self.assertEqual(history.reason, StateReasons.REMEDIATED.value)

    def test_detection_close_as_benign(self):
        """
        Test correct values are set when closing a detection as benign
        """
        data = json.dumps({'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertTrue(self.detection_1.is_filtered_by_user)
        self.assertEqual(self.detection_1.state_reason, StateReasons.BENIGN.value)
        history = CloseHistory.objects.get(detection_id=self.detection_1.id)
        self.assertEqual(history.reason, StateReasons.BENIGN.value)

    def test_detection_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_detection_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_detection_close_detection_does_not_exist(self):
        """
        Test Exception is raised detection does not exist for given detection id
        """
        url = reverse('api-v2.5:api-detection-close-v2.5', kwargs={'pk': **********})
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class DetectionOpenV2_5Test(BasicAPIV2_5Tests):
    """
    Test Detection/<id>/open Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            description='test detection serializer description',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            state=detection.ACTIVE,
        )

        self.url = reverse('api-v2.5:api-detection-open-v2.5', kwargs={'pk': self.detection_1.id})

    def test_detection_open_remediated_detection(self):
        """
        Test user can open a remediated detection
        """
        self.detection_1.state = detection.FIXED
        self.detection_1.state_reason = StateReasons.REMEDIATED.value
        self.detection_1.save()

        resp = self.client.patch(self.url, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)

    def test_detection_open_benign_detection(self):
        """
        Test correct values are set when opening a detection as benign
        """

        self.detection_1.state = detection.ACTIVE
        self.detection_1.state_reason = StateReasons.BENIGN.value
        sr_rule = smart_rule.objects.create(family=smart_rule.FAMILY_CUSTOMER)
        self.detection_1.smart_rule = sr_rule
        self.detection_1.save()

        resp = self.client.patch(self.url, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state_reason, None)
        self.assertEqual(self.detection_1.smart_rule, None)

    def test_detection_open_detection_does_not_exist(self):
        """
        Test Exception is raised detection does not exist for given detection id
        """
        url = reverse('api-v2.5:api-detection-open-v2.5', kwargs={'pk': **********})
        resp = self.client.patch(url, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class AccountCloseV2_5Test(BasicAPIV2_5Tests):

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_obj = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)
        self.linked_account_obj = LinkedAccount.objects.get(subaccounts__id=self.account_obj.id)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            t_score=10,
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_obj,
            state=detection.ACTIVE,
        )

        self.url = reverse('api-v2.5:api-account-close-v2.5', kwargs={'pk': self.linked_account_obj.id})

        self.patch_rescore_accounts = patch('base_tvui.lib_account.rescore_accounts')
        self.patch_rescore_accounts.start()
        self.addCleanup(self.patch_rescore_accounts.stop)

    def test_account_close_as_remediated(self):
        """
        Test correct values are set when closing an account as remediated
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.linked_account_obj.refresh_from_db()
        self.detection_1.refresh_from_db()

        closed_account = CloseHistory.objects.filter(linked_account_id=self.linked_account_obj.id).exclude(detection_id=self.detection_1.id)
        closed_detection = CloseHistory.objects.get(detection_id=self.detection_1.id)
        resp_json = resp.json()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(closed_account.count(), 1)
        self.assertEqual(self.linked_account_obj.state, 'inactive')
        self.assertEqual(closed_account[0].reason, StateReasons.REMEDIATED.value)
        self.assertEqual(closed_detection.linked_account_id, self.linked_account_obj.id)
        self.assertEqual(closed_detection.reason, StateReasons.REMEDIATED.value)
        self.assertEqual(resp_json['detections_closed'], [self.detection_1.id])

    def test_account_close_as_benign(self):
        """
        Test correct values are set when closing an account as benign
        """
        data = json.dumps({'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.linked_account_obj.refresh_from_db()
        self.detection_1.refresh_from_db()

        closed_account = CloseHistory.objects.filter(linked_account_id=self.linked_account_obj.id).exclude(detection_id=self.detection_1.id)
        closed_detection = CloseHistory.objects.get(detection_id=self.detection_1.id)
        resp_json = resp.json()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(closed_account.count(), 1)
        self.assertEqual(self.linked_account_obj.state, 'inactive')
        self.assertEqual(closed_account[0].reason, StateReasons.BENIGN.value)
        self.assertEqual(closed_detection.linked_account_id, self.linked_account_obj.id)
        self.assertEqual(closed_detection.reason, StateReasons.BENIGN.value)
        self.assertTrue(self.detection_1.is_filtered_by_user)
        self.assertEqual(resp_json['detections_closed'], [self.detection_1.id])

    def test_account_does_not_exist(self):
        """
        Test Exception is raised when account does not exist for given account id
        """
        url = reverse('api-v2.5:api-account-close-v2.5', kwargs={'pk': **********})
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(url, data=data, content_type='application/json')

        resp_json = resp.json()
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')

    def test_account_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = resp.json()

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_account_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = resp.json()
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_account_close_invalid_json(self):
        """
        Test Exception is raised when invalid JSON is provided
        """
        data = '{'
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = resp.json()
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message'], 'Payload must be valid JSON')


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class HostCloseV2_5Test(BasicAPIV2_5Tests):
    """
    Test hosts/<id>/close Endpoint
    """

    def setUp(self):
        super().setUp()
        self.test_host = host.objects.create(
            last_source='*********', name='cmp_host', t_score=42, c_score=83, host_luid='nh1', key_asset=0, state='active'
        )

        self.test_host_session = host_session.objects.create(
            ip_address='*******', start=self.now - timedelta(days=1), end=self.now, session_luid='rhs1', host=self.test_host
        )

        # Create detections for tests
        self.test_detection_1 = detection.objects.create(
            type='hidden_http_tunnel_cnc',
            type_vname='Hidden HTTP Tunnel',
            src_ip='**********',
            first_timestamp=timezone.now() - timedelta(days=2),
            last_timestamp=timezone.now() - timedelta(days=2),
            t_score=0,
            c_score=0,
            category='COMMAND & CONTROL',
            host_session=self.test_host_session,
            state='active',
            sensor_luid='snsr2',
            targets_key_asset=0,
            data_source_type=DataSourceType.NETWORK,
        )

        self.test_detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            host_session=self.test_host_session,
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )
        self.detection_obj_list = [self.test_detection_1, self.test_detection_2]
        self.detection_id_list = [self.test_detection_1.id, self.test_detection_2.id]
        self.url = reverse('api-v2.5:api-host-close-v2.5', kwargs={'pk': self.test_host.id})

    def tearDown(self):
        detection.objects.all().delete()
        host.objects.all().delete()
        host_session.objects.all().delete()

    @patch("host_scoring_v2.host_scoring.score_host")
    def test_host_close_as_remediated(self, _):
        """
        Test correct values are set when closing a host as remediated
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.test_host.refresh_from_db()
        for detection_obj in self.detection_obj_list:
            detection_obj.refresh_from_db()

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detection_obj_list:
            self.assertEqual(detection_obj.state, detection.FIXED)
            self.assertEqual(detection_obj.state_reason, StateReasons.REMEDIATED.value)

        for detection_id in self.detection_id_list:
            history = CloseHistory.objects.get(detection_id=detection_id)
            self.assertEqual(history.reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(host_id=self.test_host.id).exclude(detection_id__in=self.detection_id_list)
        self.assertEqual(history[0].reason, StateReasons.REMEDIATED.value)

    @patch("host_scoring_v2.host_scoring.score_host")
    def test_host_close_as_benign(self, _):
        """
        Test correct values are set when closing a host as benign
        """
        data = json.dumps({'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.test_host.refresh_from_db()
        for detection_obj in self.detection_obj_list:
            detection_obj.refresh_from_db()

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detection_obj_list:
            self.assertTrue(detection_obj.is_filtered_by_user)
            self.assertEqual(detection_obj.state_reason, StateReasons.BENIGN.value)

        for detection_id in self.detection_id_list:
            history = CloseHistory.objects.get(detection_id=detection_id)
            self.assertEqual(history.reason, StateReasons.BENIGN.value)

        history = CloseHistory.objects.filter(host_id=self.test_host.id).exclude(detection_id__in=self.detection_id_list)
        self.assertEqual(history[0].reason, StateReasons.BENIGN.value)

    def test_host_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_host_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_host_close_host_does_not_exist(self):
        """
        Test Exception is raised host does not exist for given host id
        """
        url = reverse('api-v2.5:api-host-close-v2.5', kwargs={'pk': **********})
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {
        'signal_efficacy_closed_as': SimpleFlag(lambda: True),
        'signal_efficacy_public_preview': SimpleFlag(lambda: True),
    },
)
class DetectionsOpenV2_5Test(BasicAPIV2_5Tests):
    """
    Test Detection/open Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state=detection.FIXED,
            state_reason=StateReasons.REMEDIATED.value,
            targets_key_asset=True,
            t_score=10,
        )

        self.url = reverse('api-v2.5:api-detections-open-v2.5')

    def tearDown(self):
        detection.objects.all().delete()
        smart_rule.objects.all().delete()

    def test_detections_open_fixed_detection(self):
        """
        Test detection open endpoint, can open a fixed detection
        """
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)

    def test_detections_open_an_already_open_detection(self):
        """
        Test detection open endpoint, can open a detection which is already open
        """
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        self.detection_1.state = detection.ACTIVE
        self.detection_1.state_reason = None
        self.detection_1.save()
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)

    def test_detections_open_filtered_by_user_detection(self):
        """
        Test detection open endpoint, can open a filtered by user detection
        """
        # Arrange
        # Create smart rules
        self.sr = smart_rule.objects.create(priority=None)

        self.detection_1.smart_rule = self.sr
        self.detection_1.state = detection.ACTIVE
        self.detection_1.state_reason = None
        self.detection_1.save()

        # assert detection is filtered by user
        self.assertTrue(self.detection_1.is_filtered_by_user)
        # assert detection is active
        self.assertEqual(self.detection_1.state, detection.ACTIVE)

        # Act
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # Assert
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)
        # Assert detection is not filtered by user
        self.assertFalse(self.detection_1.is_filtered_by_user)
        # Assert smart rule is removed from detection
        self.assertEqual(self.detection_1.smart_rule, None)
        # Assert smart rule is deleted
        self.assertEqual(smart_rule.objects.filter(id=self.sr.id).count(), 0)

    def test_detections_open_fixed_and_filtered_by_user_detection(self):
        """
        Test detection open endpoint, can open a simultaneously fixed and filtered by user detection
        """
        # Arrange
        # Create smart rules
        self.sr = smart_rule.objects.create(priority=None)

        self.detection_1.smart_rule = self.sr
        self.detection_1.save()

        # assert detection is filtered by user - beforehand
        self.assertTrue(self.detection_1.is_filtered_by_user)
        # assert detection is fixed - beforehand
        self.assertEqual(self.detection_1.state, detection.FIXED)

        # Act
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # Assert
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)
        # Assert detection is not filtered by user
        self.assertFalse(self.detection_1.is_filtered_by_user)
        # Assert smart rule is removed from detection
        self.assertEqual(self.detection_1.smart_rule, None)
        # Assert smart rule is deleted
        self.assertEqual(smart_rule.objects.filter(id=self.sr.id).count(), 0)

    def test_detections_open_unparsable_json(self):
        """
        Test detection open endpoint, responds with 400 when JSON is not parsable
        """
        self.maxDiff = None
        data = "}{"
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(json.loads(resp.content)['_meta']['message'], "Payload must be valid JSON")
        self.assertEqual(resp.status_code, 400)

    def test_detections_open_missing_body_param(self):
        """
        Test detection open endpoint, responds with 400 when body param is missing
        """
        self.maxDiff = None
        data = {}
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList is required")
        self.assertEqual(resp.status_code, 400)

    def test_detections_open_param_must_be_a_list(self):
        """
        Test detection open endpoint, responds with 400 when body param is missing
        """
        self.maxDiff = None
        data = json.dumps({'detectionIdList': 'not_a_list'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must be a list")
        self.assertEqual(resp.status_code, 400)

    def test_detections_open_multiple_bad_detection_id_list(self):
        """
        Test detection open endpoint, responds with 400 when detectionIdList is not a list of integers
        """
        expected_invalid_ids = ['bad_detection_id', 'ZZZ', -999]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must contain only non-negative integers"
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_open_single_bad_detection_id_list(self):
        """
        Test detection open endpoint, responds with 400 when detectionIdList is not a list of positive integers
        """
        expected_invalid_ids = [-999]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must contain only non-negative integers"
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_open_single_bad_detection_id_list_B(self):
        """
        Test detection open endpoint, responds with 400 when detectionIdList is not a list of positive integers
        """
        expected_invalid_ids = [-999]
        detection_ids = expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must contain only non-negative integers"
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_open_single_detection_doesnt_exist(self):
        """
        Test detection open endpoint, responds with 404 when single detection does not exist
        """
        detection_ids = [self.detection_1.id, 88888888]
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'],
            "Some detection IDs were not found.",
        )
        self.assertEqual(json.loads(resp.content)['errors']['invalid_ids'], [88888888])

    def test_detections_open_doesnt_exist(self):
        """
        Test detection open endpoint, responds with 404 when detection does not exist
        """
        non_existent_detection_ids = [99999999, 88888888]
        data = json.dumps({'detectionIdList': non_existent_detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['errors']['invalid_ids'], [88888888, 99999999])
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'],
            "Some detection IDs were not found.",
        )

    def test_detections_open_duplicate_ids(self):
        """
        Test detection open endpoint, can open a fixed detection with duplicate IDs in the list
        """
        data = json.dumps({'detectionIdList': [self.detection_1.id, self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_public_preview': SimpleFlag(lambda: True), 'signal_efficacy_closed_as': SimpleFlag(lambda: True)},
)
class DetectionsCloseV2_5Test(BasicAPIV2_5Tests):
    """
    Test detections/close Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 2',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.url = reverse('api-v2.5:api-detections-close-v2.5')
        self.detections_id_list = [self.detection_1.id, self.detection_2.id]
        self.detections_obj_list = [self.detection_1, self.detection_2]

    def tearDown(self):
        detection.objects.all().delete()

    def test_detections_close_as_remediated(self):
        """
        Test correct values are set when closing detections as remediated
        """
        data = json.dumps({'detectionIdList': self.detections_id_list, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detections_obj_list:
            detection_obj.refresh_from_db()
            self.assertEqual(detection_obj.state, detection.FIXED)
            self.assertEqual(detection_obj.state_reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(id__in=self.detections_id_list)
        for historyObject in history:
            self.assertEqual(historyObject.reason, StateReasons.REMEDIATED.value)

    def test_detections_close_as_benign(self):
        """
        Test correct values are set when closing detections as benign
        """
        data = json.dumps({'detectionIdList': self.detections_id_list, 'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detections_obj_list:
            detection_obj.refresh_from_db()
            self.assertTrue(detection_obj.is_filtered_by_user)
            self.assertEqual(detection_obj.state_reason, StateReasons.BENIGN.value)

        history = CloseHistory.objects.filter(id__in=self.detections_id_list)
        for historyObject in history:
            self.assertEqual(historyObject.reason, StateReasons.BENIGN.value)

    def test_detections_close_invalid_json(self):
        """
        Test detection close endpoint responds with 400 when JSON is not parsable
        """
        data = "}{"
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 400)
        self.assertEqual(json.loads(resp.content)['_meta']['message'], 'Payload must be valid JSON')

    def test_detections_close_detectionIdList_is_missing(self):
        """
        Test missing detectionIdList returns a 400 with correct message
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['detectionIdList'], 'detectionIdList is required')

    def test_detections_close_detectionIdList_is_not_a_list(self):
        """
        Test detectionIdList is not a list returns a 400 with correct message
        """
        data = json.dumps({'detectionIdList': "1234,1235", 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['detectionIdList'], 'detectionIdList must be a list')

    def test_detections_close_detectionIdList_is_empty(self):
        """
        Test empty detectionIdList returns a 400 with correct message
        """
        data = json.dumps({'detectionIdList': [], 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['detectionIdList'], 'detectionIdList cannot be empty')

    def test_detections_close_multiple_invalid_detection_ids(self):
        """
        Test multiple invalid detection ids return a 400 with correct message
        """
        expected_invalid_ids = ['bad_detection_id', -999, 0.89]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], 'detectionIdList must contain only non-negative integers'
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_close_single_invalid_detection_id_A(self):
        """
        Test single invalid detection id return a 400 with correct message
        """
        expected_invalid_ids = [0.89]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], 'detectionIdList must contain only non-negative integers'
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_close_single_invalid_detection_id_B(self):
        """
        Test only one single invalid detection id returns a 400 with correct message
        """
        expected_invalid_ids = [0.89]
        detection_ids = expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], 'detectionIdList must contain only non-negative integers'
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_close_multiple_detection_ids_dont_exist(self):
        """
        Test multiple non-existent detection Ids returns 404 with correct message
        """
        expected_nonexistent_ids = [888888, 999999]
        detection_ids = [self.detection_1.id] + expected_nonexistent_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], 'Some detection IDs were not found.')
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_nonexistent_ids)
        self.assertEqual(len(invalid_ids), len(expected_nonexistent_ids))

    def test_detections_close_single_detection_id_doesnt_exist_A(self):
        """
        Test single non-existent detection id returns 404 with correct message
        """
        expected_nonexistent_ids = [888888]
        detection_ids = [self.detection_1.id] + expected_nonexistent_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], 'Some detection IDs were not found.')
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_nonexistent_ids)
        self.assertEqual(len(invalid_ids), len(expected_nonexistent_ids))

    def test_detections_close_single_detection_id_doesnt_exist_B(self):
        """
        Test the one and only non-existent detection id returns 404 with correct message
        """
        expected_nonexistent_ids = [888888]
        detection_ids = expected_nonexistent_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], 'Some detection IDs were not found.')
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_nonexistent_ids)
        self.assertEqual(len(invalid_ids), len(expected_nonexistent_ids))

    def test_detections_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'detectionIdList': self.detections_id_list, 'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_detections_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({'detectionIdList': self.detections_id_list})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_detections_close_duplicate_ids(self):
        """
        Test detection close endpoint can close detections with duplicate IDs in the list
        """
        duplicate_list = [self.detection_1.id, self.detection_1.id]
        data = json.dumps({'detectionIdList': duplicate_list, 'reason': 'remediated'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        self.detection_1.refresh_from_db()
        self.assertEqual(self.detection_1.state, detection.FIXED)
        self.assertEqual(self.detection_1.state_reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(detection_id__in=duplicate_list)
        # only one record inserted in close history because duplicate ids filter to one id
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].reason, StateReasons.REMEDIATED.value)

    def test_detections_close_as_benign_already_remediated_detection(self):
        """
        Test that already closed detections (as remediated) can be closed as benign (or not_valuable) again
        """
        # close detection as remediated first
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # close the same detection as benign (or not_valuable)
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        self.detection_1.refresh_from_db()
        self.assertEqual(self.detection_1.state_reason, StateReasons.BENIGN.value)

        history = CloseHistory.objects.filter(detection_id=self.detection_1.id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0].reason, StateReasons.REMEDIATED.value)
        self.assertEqual(history[1].reason, StateReasons.BENIGN.value)

    def test_detections_close_as_remediated_already_benign_detection(self):
        """
        Test that already closed detections (as benign/not_valubale) can be closed as remediated again
        """
        # close detection as benign (or not_valuable) first
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # close the same detection as remediated again
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        self.detection_1.refresh_from_db()
        self.assertEqual(self.detection_1.state_reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(detection_id=self.detection_1.id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0].reason, StateReasons.BENIGN.value)
        self.assertEqual(history[1].reason, StateReasons.REMEDIATED.value)
