# -*- coding: utf-8 -*-
# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential


from unittest.mock import patch
from urllib.parse import urlparse, parse_qs
from django.utils import timezone
from django.test.utils import override_settings
from django.db.models import Value, Max, F
import pytz
import copy
from rest_framework.authtoken.models import Token
from rest_framework.test import APIClient, APIRequestFactory, force_authenticate
from tvui.models import (
    Account,
    AccountGroup,
    LinkedAccount,
    Assignment,
    notes,
    smart_rule,
    GroupCollection,
    host,
    host_session,
    HostGroup,
    IPGroup,
    ExternalDomainGroup,
    ApiClientProfile,
    CloudSensor,
    User,
    VUIGroup,
    group_extend,
    SaasLocalUserProfile,
    SaasSAMLProfile,
    detection,
    LinkedAccountScoreHistory,
    DetectionEvent,
    AccountLockdownQueue,
    StateReasons,
)
from datetime import datetime, timedelta
from freezegun import freeze_time
from vui_tests.base_tvui.smart_rules import testing_utils
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import (
    AccountSerializerV3_4,
    UserRolesSerializerV3_4,
    ActiveDirectoryHealthDetailsSerializerV3_4,
    AzureAdLockdownHealthDetailsSerializerV3_4,
    HealthGenericIntegrationSerializerV3_4,
    HostLockdownSerializerV3_4,
    EntityScoringEventsSerializerV3_4,
    UserSerializerV3_4,
    EntitySerializerV3_4,
    DetectionEventSerializerV3_4,
    LockdownSerializerV3_4,
    DetectionSerializerV3_4,
    GroupSerializerV3_4,
)
from tvui.helpers import DETECTION_CATEGORY_DB_TO_LONG
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils import (
    ENABLED,
    DISABLED,
    ERROR_STATES,
    GET_EXCEPTION,
    CONNECTION_STATUS,
    CONNECTOR_TYPE,
    TYPE_AZURE_AD_LOCKDOWN,
    UNKNOWN,
)
from pydantic import ValidationError
from unittest import skip
from vui_tests.vui_testcase import VuiTestCase
from tvui.detections.detection_types import DetectionType
from schwepp_client import APIHostType

AWS_SENSOR_TYPE = 'aws'
AWS_SENSOR_DATA_TEMPLATE = {
    "sensor_name": "AWS Test Sensor Template Data",
    "serial_number": "AWSTestSensorTemplateData",
    "luid": "testawss",
    "product_name": "AWS",
    "is_virtual": True,
    "is_saas": True,
    "last_seen": "2021-04-29T00:00:00Z",
    "location": "northeurope",
    "region": "northeurope",
    "sensor_type": "aws",
    "setup_link": "https://test.com/test_sensor",
    "status": "INGESTING",
    "token": None,
}


class BaseTests(VuiTestCase):
    def setUp(self):
        self.now = timezone.now()

        self.patch_request = patch('schwepp_client.client_base.SchweppRequest')
        self.patch_request.start()
        self.addCleanup(self.patch_request.stop)

        sensor_data = AWS_SENSOR_DATA_TEMPLATE.copy()
        sensor_data['sensor_name'] = "Test Sensor"
        sensor_data['serial_number'] = "TestSenSerial"
        sensor_data['luid'] = "testsen4"
        sensor_data['api_host'] = APIHostType.SENSIBLE.value

        self.test_sensor = CloudSensor.objects.create(
            sensor_type=sensor_data['sensor_type'],
            name=sensor_data['sensor_name'],
            serial_number=sensor_data['serial_number'],
            luid=sensor_data['luid'],
            sensor_data=sensor_data,
        )

        # Empty sensor
        self.empty_sensor_luid = '1hsluid'

        # data source
        self.data_source = {
            'type': self.test_sensor.sensor_type,
            'connection_name': self.test_sensor.name,
            'connection_id': self.test_sensor.luid,
        }
        self.empty_data_source = {
            'type': 'Unknown sensor type',
            'connection_name': 'Unknown sensor name',
            'connection_id': self.empty_sensor_luid,
        }

        # permissions
        self.mock_jwt = 'mock_jwt'
        self.add_patch('vectra_auth', patch('base_tvui.providers.models.sensor_base.get_authgw_vectra_auth', lambda x: None))
        self.add_patch('deployment_env', patch('base_tvui.providers.lc39_helpers.get_deployment_env', lambda: "dev"))


class BasicAPIV3SerializerTests(BaseTests):
    def setUp(self):
        super(BasicAPIV3SerializerTests, self).setUp()

        # user
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True
        self.client_user = User.objects.create_user(
            username='api_client_asdf',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='asdf', name='My Test Client', description='blah blah', created_by=self.user
            ),
        )
        self.client = APIClient(REMOTE_ADDR='************')
        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)

        # hostname patch for get_absolute_url in lib_tv
        self.hostname_value = 'testserver'
        self.hostname = self.add_patch('hostname', patch('base_tvui.lib_tv.get_hostname_and_default_ip'))
        self.hostname.return_value = (self.hostname_value, '*********')

        self.get_version_patch = self.add_patch(
            'get_version', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.api_v3_utils.get_version', return_value='v3.4')
        )


class EntityScoringEventsV3_4SerializerTests(BasicAPIV3SerializerTests):
    """
    Entity Scoring Events Serializer Tests for API V3_4
    """

    def setUp(self):
        super(EntityScoringEventsV3_4SerializerTests, self).setUp()

        self.o365_account = Account.objects.create(uid='<EMAIL>', account_type=Account.TYPE_O365)
        self.account_url = f'https://testserver/accounts/{self.o365_account.linked_account.id}'
        self.version = '2022.0.0'
        self.test_det_1 = detection.objects.create(
            account=self.o365_account,
            type=DetectionType.O365_SUSPICIOUS_MAILBOX_RULE,
            type_vname='O365 Suspicious Mailbox Rule',
            src_ip='**********',
            last_timestamp=self.now - timedelta(minutes=1),
        )

        self.event_data = {
            'account_id': self.o365_account.linked_account.id,
            'account_uid': '<EMAIL>',
            'category': 'ACCOUNT SCORING',
            'last_detection_type': self.test_det_1.type_vname,
            'last_detection_id': self.test_det_1.id,
            'active_detection_types': ['O365 Suspect eDiscovery Usage', 'O365 Suspicious Mailbox Rule'],
            'version': self.version,
        }

        self.eight_hours_ago = self.now - timedelta(hours=8)

        self.first_score_event = LinkedAccountScoreHistory.objects.create(
            account=self.o365_account.linked_account,
            c_score=56,
            t_score=47,
            score_date=self.eight_hours_ago,
            data=self.event_data,
            score_decrease=False,
        )

        self.account_events = (
            LinkedAccountScoreHistory.objects.filter(id=self.first_score_event.id)
            .annotate(entity_type=Value('account'))
            .select_related('account')
        )

        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET
        request.api_version = 3.4
        request.user = self.user

        # entity scoring serializer
        self.ent_scoring_serializer = EntityScoringEventsSerializerV3_4(
            instance=self.account_events, context={'request': request}, many=True
        )

    def test_entity_scoring_events_serializer(self):
        """Test V3.4 Entity Scoring Events serializer"""
        with self.cloud_env():
            data = self.ent_scoring_serializer.data[0]
            self.assertEqual(data['id'], self.first_score_event.id)
            self.assertEqual(data['entity_id'], self.o365_account.linked_account.id)
            self.assertEqual(data['name'], self.o365_account.uid)
            self.assertEqual(data['type'], "account")
            self.assertEqual(data['is_prioritized'], self.first_score_event.is_prioritized)
            self.assertEqual(data['importance'], self.first_score_event.entity_importance)
            self.assertEqual(data['severity'], self.o365_account.severity)
            self.assertEqual(data['urgency_score'], self.first_score_event.urgency_score)
            self.assertEqual(data['velocity_contrib'], self.first_score_event.velocity_contrib)
            self.assertEqual(data['attack_rating'], self.first_score_event.attack_rating)
            self.assertEqual(data['breadth_contrib'], self.first_score_event.breadth_contrib)
            self.assertEqual(data['url'], self.account_url)
            self.assertIsNone(data.get('entity_type', None))
            self.assertIsNone(data.get('entity_importance', None))
            self.assertIsNone(data.get('last_detection_id', None))
            self.assertIsNone(data.get('last_detection_type', None))
            self.assertIsNone(data.get('last_detection_url', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class EntityAPIV3_4SerializerTests(BasicAPIV3SerializerTests):
    """
    Entity Serializer Tests for API V3_4
    """

    def setUp(self):
        super().setUp()

        self.account_name = 'test_account1'
        self.account_state = 'active'
        self.account_t_score = 10
        self.account_c_score = 10
        self.acct = Account.objects.create(
            uid=self.account_name,
            state=self.account_state,
            t_score=self.account_t_score,
            c_score=self.account_c_score,
            account_type=Account.TYPE_KERBEROS,
        )

        self.account_url = f'https://testserver/api/v3.4/accounts/{self.acct.linked_account.id}'

        linked_account_id = self.acct.linked_account.id
        self.account = (
            LinkedAccount.objects.filter(id=linked_account_id)
            .annotate(entity_type=Value('account'), last_modified_timestamp=Max('subaccounts__updated_date'))
            .first()
        )
        self.acct_attack_profile = None
        if archetype := self.acct.get_account_archetype_details():
            self.acct_attack_profile = archetype.get('vname')

        # mock for boto client
        self.mock_local_experiment = self.add_patch('mock_local_experiment', patch('base_tvui.lib_tv.in_local_experiment'))
        self.mock_local_experiment.return_value = True

        # request
        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET
        request.api_version = 3.4
        request.user = self.user

        # serializer
        self.ent_serializer2 = EntitySerializerV3_4(instance=self.account, context={'request': request})

    def tearDown(self) -> None:
        Account.objects.filter(id=self.acct.id).delete()

    def test_entity_serializer_account(self):
        """Test v3.4 Entity Serializer for accounts"""
        with self.cloud_env():
            # retrieve serialized data
            response_data = self.ent_serializer2.data
            # generate expected data
            expected_data = {
                'id': self.account.id,
                'name': self.account_name,
                'breadth_contrib': self.account.breadth_contrib,
                'importance': self.account.entity_importance,
                'type': self.account.entity_type,
                'is_prioritized': self.account.is_prioritized,
                'severity': self.account.severity,
                'urgency_score': self.account.urgency_score,
                'velocity_contrib': self.account.velocity_contrib,
                'detection_set': [name for name in self.account.detection_set.all()],
                'last_detection_timestamp': self.account.last_detection_timestamp,
                'last_modified_timestamp': self.account.last_modified_timestamp,
                'attack_rating': self.account.attack_rating,
                'privilege_level': self.account.priv_level,
                'privilege_category': self.account.privilege_category,
                'attack_profile': self.acct_attack_profile,
                'sensors': self.account.sensors,
                'state': self.account.state,
                'tags': list(self.account.tags.names()),
                'url': self.account_url,
                'account_type': [sub.account_type for sub in self.account.subaccounts.all()],
                'host_type': None,
                'ip': None,
            }
            # verify expected data
            for entry in expected_data:
                message = f'Entity serializer v3.4 failed for field {entry}'
                self.assertEqual(response_data.get(entry), expected_data[entry], message)
                self.assertEqual(type(response_data.get(entry)), type(expected_data[entry]), message)

            self.assertIsNone(response_data.get('entity_type', None))
            self.assertIsNone(response_data.get('entity_importance', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class DetectionEventAPIV3_4SerializerTests(BasicAPIV3SerializerTests):
    """
    Detection Event Serializer Tests for API V3_4
    """

    def setUp(self):
        super(DetectionEventAPIV3_4SerializerTests, self).setUp()
        # account object setup
        self.account_iud = 'test_account_event_detection'
        self.account_state = 'active'
        self.account_t_score = 9
        self.account_c_score = 8
        self.account_type = 'aws'
        self.acc = Account.objects.create(
            uid=self.account_iud,
            state=self.account_state,
            t_score=self.account_t_score,
            c_score=self.account_c_score,
            account_type=self.account_type,
        )
        self.acc_url = f'https://testserver/accounts/{self.acc.linked_account_id}'

        # account event object setup
        self.event_entity_type_account = 'account'
        self.event_threat_account = 10
        self.event_certainty_account = 10
        self.event_category_account = "COMMAND & CONTROL"
        self.event_detection_type_account = DetectionType.O365_SUSPICIOUS_POWER_AUTOMATE
        self.event_triage_id_account = 1235
        self.event_ip_address_account = '**********'
        self.event_account_uid = 'AWS:1234/serializer-unit-test-event-detections-account'
        self.detection_event_account = DetectionEvent.objects.create(
            category=self.event_category_account,
            entity_type=self.event_entity_type_account,
            threat=self.event_threat_account,
            certainty=self.event_certainty_account,
            type=self.event_detection_type_account,
            triage_id=self.event_triage_id_account,
            ip_address=self.event_ip_address_account,
            account_id=self.acc.id,
            account_uid=self.event_account_uid,
        )

        # mock for boto client
        self.mock_local_experiment = self.add_patch('mock_local_experiment', patch('base_tvui.lib_tv.in_local_experiment'))
        self.mock_local_experiment.return_value = True

        # request
        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET
        request.api_version = 3.4
        request.user = self.user
        self.request = request

        # serializer
        self.ent_serializer_account = DetectionEventSerializerV3_4(instance=self.detection_event_account, context={'request': request})

    def tearDown(self) -> None:
        DetectionEvent.objects.filter(id=self.detection_event_account.id).delete()

    def test_detection_event_serializer_account(self):
        """Test V3.4 detection event serializer for accounts"""
        # retrieve serialized data
        response_data = self.ent_serializer_account.data
        # generate expected data
        expected_data = {
            'id': self.detection_event_account.id,
            'category': self.event_category_account,
            'threat': self.detection_event_account.threat,
            'certainty': self.detection_event_account.certainty,
            'triaged': True,
            'detection_type': 'M365 Suspect Power Automate Activity',
            'd_type_vname': 'M365 Suspect Power Automate Activity',
            'entity_id': self.acc.linked_account_id,
            'type': self.event_entity_type_account,
            'url': self.acc_url,
            'severity': self.detection_event_account.severity,
            'detection_id': self.detection_event_account.detection_id,
            'entity_uid': self.detection_event_account.account_uid,
        }

        # verify expected data
        for entry in expected_data:
            message = f'Detection event (account) serializer v3.4 failed for field {entry}'
            self.assertEqual(response_data.get(entry), expected_data[entry], message)
            self.assertEqual(type(response_data.get(entry)), type(expected_data[entry]), message)

        self.assertIsNone(response_data.get('entity_type', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class LockdownAPIV3_4SerializerTests(BasicAPIV3SerializerTests):
    """
    Lockdown Serializer Tests for API V3_4
    """

    def setUp(self):
        super().setUp()

        self.now = datetime(2023, 3, 17, 8, 0, 0)

        self.account_0 = Account.objects.create(uid='test_account_0', account_type=Account.TYPE_KERBEROS)

        with freeze_time(self.now):
            self.acct_lock = AccountLockdownQueue.objects.create(
                account=self.account_0, expiration=(self.now + timedelta(days=1)), user=self.user
            )

        self.lock_2 = (
            AccountLockdownQueue.objects.filter(id=self.acct_lock.id)
            .values(
                'id',
                entity_type=Value('account'),
                entity_id=F('account__linked_account_id'),
                name=F('account__uid'),
                username=F('user__username'),
                unlock_event_timestamp=F('expiration'),
                lock_event_timestamp=F('locked_timestamp'),
            )
            .first()
        )

        self.linked_acct_id = self.account_0.linked_account.id
        # request
        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET
        request.api_version = 3.4
        request.user = self.user

        # serializer
        self.acct_lock_serializer = LockdownSerializerV3_4(instance=self.lock_2, context={'request': request})

    def tearDown(self) -> None:
        AccountLockdownQueue.objects.filter(id=self.acct_lock.id).delete()

    def test_lockdown_serializer_account(self):
        """Test v3.4 Lockdown Serializer for account lockdown"""
        with self.cloud_env():
            # retrieve serialized data
            response_data = self.acct_lock_serializer.data
            # generate expected data
            expected_data = {
                'id': self.lock_2['id'],
                'entity_id': self.linked_acct_id,
                'entity_name': self.account_0.uid,
                'type': 'account',
                'lock_event_timestamp': '2023-03-17T08:00:00Z',
                'unlock_event_timestamp': '2023-03-18T08:00:00Z',
                'locked_by': self.user.username,
            }
            # verify expected data
            for entry in expected_data:
                message = f'Lockdown serializer v3.4 failed for field {entry}'
                self.assertEqual(response_data.get(entry), expected_data[entry], message)
                self.assertEqual(type(response_data.get(entry)), type(expected_data[entry]), message)

            self.assertIsNone(response_data.get('entity_type', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class AccountV3_4SerializerTest(BasicAPIV3SerializerTests):
    """
    Account Serializer Tests for API V3_4
    """

    def setUp(self):
        super(AccountV3_4SerializerTest, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

        # mocks
        self.mock_sensors = self.add_patch('sensors', patch('tvui.models.LinkedAccount.sensors'))
        self.mock_sensors.return_value = None

        self.mock_get_url = self.add_patch(
            'data_source', patch('base_tvui.rest.api_v2.api_v2_0_3.api_v2_serializers.DetectionSerializerV2.get_url')
        )
        self.mock_get_url.return_value = None

        self.mock_sub_accounts = self.add_patch(
            'sub_accounts', patch('base_tvui.rest.api_v2.api_v2_0_3.api_v2_serializers.AccountSerializerV2_2._list_account_types')
        )
        self.mock_sub_accounts.return_value = None

        self.mock_get_home_name = self.add_patch(
            'sub_accounts', patch('base_tvui.rest.api_v2.api_v2_0_3.api_v2_serializers.AccountSerializerV2.get_probable_home_name')
        )
        self.mock_get_home_name.return_value = None

        # account
        self.account = Account.objects.create(uid='test_account_1', account_type=Account.TYPE_AWS)
        self.linked_account = LinkedAccount.objects.get(display_uid='test_account_1')
        self.linked_account.sensors = None
        # request
        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET

        # serializer
        self.acc_serializer = AccountSerializerV3_4(instance=self.linked_account, context={'request': request})

    def test_account_serializer(self):
        """Test V3.4 account serializer"""
        with self.cloud_env():
            data = self.acc_serializer.data
            self.assertNotIn('subaccounts', data)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class DetectionAPIV3_4SerializerTests(BasicAPIV3SerializerTests):
    """
    Detection Event Serializer Tests for API V3_4
    """

    def setUp(self):
        super(DetectionAPIV3_4SerializerTests, self).setUp()
        self.generate_all_detection_schemas()
        # user object setup
        self.assignment_account_user = User.objects.create(
            username='testdetectionsaccountuser', deactivated_ts=1, password="testauthpass281!352"
        )
        self.assignment_account_assigned_by = User.objects.create(
            username='testdetectionshaccountassignedby', deactivated_ts=1, password="testauthpass391!352"
        )

        # linked account object setup
        self.linked_account = LinkedAccount.objects.create(display_uid='test linked account detections serializer')

        # account object setup
        self.account_name = 'test_account'
        self.account_state = 'active'
        self.account_t_score = 10
        self.account_c_score = 10
        self.acct = Account.objects.create(
            uid=self.account_name,
            state=self.account_state,
            t_score=self.account_t_score,
            c_score=self.account_c_score,
            account_type=Account.TYPE_KERBEROS,
            linked_account=self.linked_account,
        )

        # account detection object setup
        self.detection_account_type = DetectionType.BINARYLOADER
        self.detection_account_type_vname = 'Malware Update'
        self.detection_account_category = 'RECONNAISSANCE'
        self.detection_account_c_score = 10
        self.detection_account_description = 'test detection serializer description'
        self.detection_account_description2 = 'description part 2'
        self.detection_account_src_ip = '*******'
        self.detection_account_last_timestamp = datetime(2023, 5, 4, 12, 0, 0, tzinfo=timezone.utc)
        self.detection_account_targets_key_asset = True
        self.detection_account_account = self.acct
        self.detection_account_sensor_luid = '1234'
        self.detection_account_state = 'active'
        self.detection_account_summary = 'test account detection summary'
        self.detection_account_tags = ['tag1', 'tag2']
        self.detection_account_t_score = 10
        self.detection_account = detection.objects.create(
            type=self.detection_account_type,
            type_vname=self.detection_account_type_vname,
            category=self.detection_account_category,
            c_score=self.detection_account_c_score,
            description=self.detection_account_description,
            description2=self.detection_account_description2,
            src_ip=self.detection_account_src_ip,
            smart_rule=smart_rule.objects.create(family='FAMILY_CUSTOMER'),
            last_timestamp=self.detection_account_last_timestamp,
            targets_key_asset=self.detection_account_targets_key_asset,
            account=self.acct,
            sensor_luid=self.detection_account_sensor_luid,
            state=self.detection_account_state,
            tags=self.detection_account_tags,
            t_score=self.detection_account_t_score,
        )

        self.acc_url = f'https://testserver/api/v3.4/detections/{self.detection_account.id}'
        self.src_acc_url = f'https://testserver/api/v3.4/accounts/{self.acct.linked_account.id}'

        # note object setup
        self.account_note_note = 'detections account note'
        self.note_modified_timestamp = datetime(2022, 5, 4, 12, 0, 0, tzinfo=timezone.utc)
        self.account_note = notes.objects.create(
            date_modified=self.note_modified_timestamp,
            note=self.account_note_note,
            type='detection',
            type_id=self.detection_account.id,
            modified_by=self.assignment_account_user,
        )

        # assignment object setup
        self.detection_account_assignment_date = datetime(2022, 5, 4, 12, 0, 0, tzinfo=timezone.utc)
        self.assignment = Assignment.objects.create(
            user=self.assignment_account_user,
            type_id=self.acct.id,
        )
        self.assignment.save()
        # mock for boto client
        self.mock_local_experiment = self.add_patch('mock_local_experiment', patch('base_tvui.lib_tv.in_local_experiment'))
        self.mock_local_experiment.return_value = True

        # cloud sensor object setup
        self.cloud_sensor = CloudSensor.objects.create(
            name='test-con',
            source_id='**********',
            luid=self.detection_account_sensor_luid,
            sensor_type='azure-cp',
            serial_number='1214151',
            sensor_data={'test': 1},
        )

        # request
        factory = APIRequestFactory()
        self.request = factory.get('/')
        self.request.query_params = self.request.GET
        self.request.api_version = 3.4
        self.request.user = self.user

        # serializer
        self.det_serializer_account = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})

    def tearDown(self):
        detection.objects.all().delete()

    def test_detection_serializer_account(self):
        """Test V3.4 detection serializer for account detection"""
        # retrieve serialized data
        response_data = self.det_serializer_account.data
        # generate expected data
        expected_data = {
            'id': self.detection_account.id,
            'assigned_date': self.detection_account.assigned_date,
            'assigned_to': self.detection_account.assigned_to_username,
            'certainty': self.detection_account_c_score,
            'created_timestamp': self.detection_account.created_datetime,
            'description': self.detection_account_description,
            'detection': self.detection_account_type_vname,
            'detection_category': DETECTION_CATEGORY_DB_TO_LONG[self.detection_account_category],
            'detection_type': self.detection_account_type_vname,
            # 'grouped_details': self.detection_account.grouped_details,
            'is_targeting_key_asset': self.detection_account_targets_key_asset,
            'is_triaged': True,
            'last_timestamp': "2023-05-04T12:00:00Z",
            'note': self.account_note_note,
            'note_modified_by': 'testdetectionsaccountuser',
            'note_modified_timestamp': "2022-05-04T12:00:00Z",
            'sensor_name': 'Vectra X',
            'src_account': {
                'id': self.acct.linked_account.id,
                'name': 'test_account',
                'url': self.src_acc_url,
                'threat': self.acct.linked_account.t_score,
                'certainty': self.acct.linked_account.c_score,
                'privilege_level': None,
                'privilege_category': None,
            },
            'state': self.account_state,
            # 'summary': "{'external_cnc_servers': [], 'files': [],[45 chars]None}",
            'src_ip': self.detection_account_src_ip,
            'tags': self.detection_account_tags,
            'threat': self.detection_account_t_score,
            'type': 'account',
            'url': self.acc_url,
        }

        # verify expected data
        for entry in expected_data:
            message = f'Detection (account) serializer v3.4 failed for field {entry}'
            self.assertEqual(response_data.get(entry), expected_data[entry], message)
            self.assertEqual(type(response_data.get(entry)), type(expected_data[entry]), message)

        self.assertIsNone(response_data.get(expected_data.get('targets_key_asset', None), None))
        self.assertIsNone(response_data.get(expected_data.get('t_score', None), None))
        self.assertIsNone(response_data.get(expected_data.get('c_score', None), None))
        self.assertIsNone(response_data.get(expected_data.get('category', None), None))

    def test_detection_serializer_pivot_implemented_enabled(self):
        # Ensure that the pivot link is included when ff is enabled for detections that have pivots implemented
        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        now = datetime(2024, 10, 24, 1, 2, 3)
        detection_obj = detection.objects.create(
            type=DetectionType.AZURE_LOGGING_DISABLED,
            first_timestamp=now,
            last_timestamp=now,
            account=self.acct,
            sensor_luid=self.cloud_sensor.luid,
            data_source_type='azure-cp',
        )
        detection_serializer = DetectionSerializerV3_4(instance=detection_obj, context={'request': self.request})

        response_data = detection_serializer.data

        self.assertIn('investigation_pivot_link', response_data)
        pivot_link = response_data['investigation_pivot_link']
        parsed_link = urlparse(pivot_link)
        self.assertEqual(parsed_link.netloc, self.hostname_value)
        self.assertEqual(parsed_link.path, '/investigate')
        self.assertEqual(
            parse_qs(parsed_link.query),
            {
                'a': ['operations'],
                'ds': ['azure-cp'],
                'c': [f'["{self.cloud_sensor.source_id}"]'],
                'cl': [
                    '["timestamp", "Actor", "objectid", "applicationid", "rolename", "operationname", "resulttype", "resultsignature", "resourceid", "properties"]'
                ],
                'dt': ['*************.0'],
                'df': ['*************.0'],
                'q': [
                    '[["objectid", "is", "test_account"], ["operationname", "is", "MICROSOFT.INSIGHTS/DIAGNOSTICSETTINGS/DELETE", false]]'
                ],
            },
        )

    def test_detection_serializer_pivot_implemented_disabled(self):
        # Ensure that the pivot link is not included when ff is disabled, even for detections that have pivots implemented
        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=False))

        detection_obj = detection.objects.create(
            type=DetectionType.AZURE_LOGGING_DISABLED,
            first_timestamp=self.now,
            last_timestamp=self.now,
            account=self.acct,
            sensor_luid=self.cloud_sensor.luid,
            data_source_type='azure-cp',
        )
        detection_serializer = DetectionSerializerV3_4(instance=detection_obj, context={'request': self.request})

        response_data = detection_serializer.data

        self.assertNotIn('investigation_pivot_link', response_data)

    def test_detection_serializer_pivot_not_implemented_enabled(self):
        # Ensure that the pivot link is null when ff is enabled for detections that don't have pivots implemented
        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('investigation_pivot_link', response_data)
        self.assertEqual(response_data['investigation_pivot_link'], None)

    def test_detection_serializer_reason_field_when_closed_as_remediated(self):
        """Test reason field when detection was closed as remediated"""

        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        self.detection_account.state = detection.FIXED
        self.detection_account.state_reason = StateReasons.REMEDIATED.value
        self.detection_account.save()

        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('reason', response_data)
        self.assertEqual(response_data['reason'], StateReasons.REMEDIATED.value)

    def test_detection_serializer_reason_field_when_marked_as_fixed(self):
        """Test reason field when detection was previously marked as fixed"""

        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        self.detection_account.state = detection.FIXED
        self.detection_account.save()

        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('reason', response_data)
        self.assertEqual(response_data['reason'], StateReasons.REMEDIATED.value)

    def test_detection_serializer_reason_field_when_closed_as_benign(self):
        """Test reason field when detection was closed as benign"""

        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        self.detection_account.smart_rule = smart_rule.objects.create(family=smart_rule.FAMILY_CUSTOMER)
        self.detection_account.state_reason = StateReasons.BENIGN.value
        self.detection_account.save()

        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('reason', response_data)
        self.assertEqual(response_data['reason'], StateReasons.BENIGN.value)

    def test_detection_serializer_reason_field_when_filtered_by_user(self):
        """Test reason field when detection was previously filtered by user"""

        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        self.detection_account.smart_rule = smart_rule.objects.create(family=smart_rule.FAMILY_CUSTOMER)
        self.detection_account.save()

        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('reason', response_data)
        self.assertEqual(response_data['reason'], StateReasons.BENIGN.value)

    def test_detection_serializer_reason_field_when_closed_as_not_valuable(self):
        """Test reason field when detection was closed as not valuable"""

        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=True))
        self.detection_account.smart_rule = smart_rule.objects.create(family=smart_rule.FAMILY_CUSTOMER)
        self.detection_account.state_reason = StateReasons.NOT_VALUABLE.value
        self.detection_account.save()

        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('reason', response_data)
        self.assertEqual(response_data['reason'], StateReasons.NOT_VALUABLE.value)

    def test_detection_serializer_reason_field_when_disabled(self):
        """Ensure that the reason field is null when ff is disabled"""

        self.add_patch('mock_has_feature', patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers.flag_enabled', return_value=False))
        self.detection_account.state = detection.FIXED
        self.detection_account.state_reason = StateReasons.REMEDIATED.value
        self.detection_account.save()

        detection_serializer = DetectionSerializerV3_4(instance=self.detection_account, context={'request': self.request})
        response_data = detection_serializer.data
        self.assertIn('reason', response_data)
        self.assertEqual(response_data['reason'], None)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class UserAPIV3_4SerlializerTest(BasicAPIV3SerializerTests):
    """
    User Serializer Tests for API V3_4
    """

    def setUp(self):
        super(UserAPIV3_4SerlializerTest, self).setUp()

        view_group = VUIGroup.pure.create(name='auditor')
        view_group.name = view_group.id
        group_extend.objects.create(group=view_group, vname='Auditor')

        self.test_user_a = User.objects.create_user(
            '<EMAIL>', email='<EMAIL>', password='cognito', first_name='Vectra', last_name='AI'
        )

        self.now_timestamp = timezone.now()
        self.test_user_b = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect',
            first_name='Fancy',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )

        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET
        request.api_version = 3.4
        request.user = self.user
        self.test_user_a.groups.add(view_group)
        self.test_user_b.groups.add(view_group)
        # serializer
        self.user_serializer_a = UserSerializerV3_4(instance=self.test_user_a, context={'request': request})
        self.user_serializer_b = UserSerializerV3_4(instance=self.test_user_b, context={'request': request})

    def test_user_serializer_output(self):
        """Test V3.4 user serializer"""
        response_data_a = self.user_serializer_a.data
        expected_response_a = {
            'id': self.test_user_a.id,
            'email': '<EMAIL>',
            'role': 'auditor',
            'last_login_timestamp': None,
            'name': 'Vectra AI',
            'verified': False,
            'identities': [],
        }
        self.assertEqual(response_data_a, expected_response_a)

    def test_verified_user_output(self):
        """Test V3.4 user serializer with verified user"""
        response_data_b = self.user_serializer_b.data
        self.assertEqual(response_data_b.get('verified'), True)
        self.assertEqual(response_data_b.get('last_login_timestamp'), self.now_timestamp)

    def test_user_get_identities(self):
        """Test V3.4 user serializer get_identites method"""
        # setup user with local and saml account for testing

        local_profile = SaasLocalUserProfile.objects.create(email='<EMAIL>', email_verified=True)
        saml_profile = SaasSAMLProfile.objects.create(name='test_idp')
        self.user.saas_local_profile = local_profile
        self.user.saas_saml_profiles.set([saml_profile])
        self.user.save()
        expected_result = [{'type': 'LOCAL'}, {'saml_profile_id': saml_profile.id, 'type': 'SAML'}]
        actual_result = UserSerializerV3_4().get_identities(self.user)
        self.assertEqual(expected_result, actual_result)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class UserRolesAPIV3_4SerializerTest(BasicAPIV3SerializerTests):
    def setUp(self):
        super().setUp()

        self.view_group = VUIGroup.pure.create(name='auditor')
        self.group_extend = group_extend.objects.create(group=self.view_group, vname='Auditor')

        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET
        request.user = self.user
        # serializer
        self.role_serializer = UserRolesSerializerV3_4(instance=self.view_group, context={'request': request})

    def test_user_roles_serializer_output(self):
        """Test V3.4 Roles Serializer"""
        response_data = self.role_serializer.data
        expected_response = {
            'id': self.view_group.id,
            'name': self.group_extend.vname,
            'standardized_name': self.view_group.name,
        }
        self.assertEqual(response_data, expected_response)


class AzureAdLockdownHealthDetailsSerializerV3_4Test(BasicAPIV3SerializerTests):
    def test_azure_ad_lockdown_serializer_extra_fields(self):
        """Test V3.4 azure ad lockdown serializer when passing fields not defined in the model"""
        data = {'not_implemented': "testing", 'additional_data': "foo"}
        model_output = AzureAdLockdownHealthDetailsSerializerV3_4(**data)
        assert model_output.not_implemented == "testing"
        assert model_output.additional_data == "foo"


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class ActiveDirectoryHealthDetailsSerializerV3_4Test(BasicAPIV3SerializerTests):
    def test_active_diretcory_serializer_all_fields_included(self):
        """Test V3.4 active directory serializer when passing all accepted fields to the model"""

        health_output = {
            'ad_servers': [{'1.2.3.4': {'severity': 'none', 'status': 'enabled'}}, {'2.3.4.5': {'severity': 'none', 'status': 'enabled'}}],
            'metrics': {'total_accounts_kerberos_last_30_days': 0, 'current_kerberos_ldap_overlap': 0, 'total_ad_accounts_found': 0},
            'account_lockdown': 'enabled',
        }
        model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)
        expected_dict = {
            'ad_servers': [{'1.2.3.4': {'severity': 'none', 'status': 'enabled'}}, {'2.3.4.5': {'severity': 'none', 'status': 'enabled'}}],
            'metrics': {'total_accounts_kerberos_last_30_days': 0, 'current_kerberos_ldap_overlap': 0, 'total_ad_accounts_found': 0},
            'account_lockdown': 'enabled',
        }
        self.assertEqual(expected_dict, model_output.dict())

    def test_active_diretcory_serializer_optional_fields(self):
        """Test V3.4 active directory serializer when not passing all optional fields to the model"""

        health_output = {}
        model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)
        expected_dict = {'ad_servers': None, 'metrics': None, 'account_lockdown': None}
        self.assertEqual(expected_dict, model_output.dict())

    def test_active_diretcory_serializer_invalid_value(self):
        """Test V3.4 active directory serializer extra fields input"""
        self.maxDiff = None
        health_output = {
            'ad_servers': [
                {'1.2.3.4': {'severity': 'none', 'status': 'enabled', 'extra': 'extra'}},
                {'2.3.4.5': {'severity': 'none', 'status': 'enabled'}},
                {'extra_key': {'extra_dict': 'extra'}},
            ],
            'metrics': {
                'total_accounts_kerberos_last_30_days': 0,
                'current_kerberos_ldap_overlap': 0,
                'total_ad_accounts_found': 0,
                'extra_metric': 'extra',
            },
            'account_lockdown': 'enabled',
            'extra_top_level': 'extra',
        }
        model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)
        expected_dict = copy.deepcopy(health_output)
        expected_dict['ad_servers'][2]['extra_key'] = {'extra_dict': 'extra', 'severity': None, 'status': None}
        self.assertEqual(expected_dict, model_output.dict())

    def test_active_diretcory_serializer_invalid_values(self):
        """Test V3.4 active directory serializer passing invalid value types"""

        health_output = {'account_lockdown': 'invalid_value'}
        with self.assertRaises(ValidationError):
            model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)

        health_output = {'ad_servers': 1}
        with self.assertRaises(ValidationError):
            model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)

        health_output = {'metrics': 1}
        with self.assertRaises(ValidationError):
            model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)

        health_output = {'ad_servers': [{'1.2.3.4': 'test'}]}
        with self.assertRaises(ValidationError):
            model_output = ActiveDirectoryHealthDetailsSerializerV3_4(**health_output)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class HealthExternalConnectorsSerializerV3_4Test(BasicAPIV3SerializerTests):

    expected_connection_status = ENABLED
    expected_connection_status_disabled = DISABLED
    expected_connection_status_exception = UNKNOWN
    expected_auth_status = {'ad_1': 'Not Authorized', 'ad_2': 'Not Configured'}
    expected_error_states = {'ad_1': 'this went bad', 'ad_2': 'something went wrong'}
    expected_error_states_default = None
    expected_lockdown_status = ENABLED

    def test_health_connector_serializer_all_fields_included(self):
        """Test V3.4 Health connector serializer when passing all accepted fields to the model"""

        health_output = {
            'connection_status': 'enabled',
            'auth_status': {'ad_1': 'Not Authorized', 'ad_2': 'Not Configured'},
            'error_states': {'ad_1': 'this went bad', 'ad_2': 'something went wrong'},
            'lockdown_status': 'enabled',
        }
        model_output = HealthGenericIntegrationSerializerV3_4(**health_output)
        assert model_output.connection_status == self.expected_connection_status
        assert model_output.auth_status == self.expected_auth_status
        assert model_output.error_states == self.expected_error_states
        assert model_output.lockdown_status == self.expected_lockdown_status

    def test_health_connector_serializer_default_field_values(self):
        """Test V3.4 Health connector serializer for default error state value"""

        health_output = {
            'connection_status': 'enabled',
            'auth_status': {'ad_1': 'Not Authorized', 'ad_2': 'Not Configured'},
            'lockdown_status': 'enabled',
        }
        model_output = HealthGenericIntegrationSerializerV3_4(**health_output)
        assert model_output.connection_status == self.expected_connection_status
        assert model_output.auth_status == self.expected_auth_status
        assert model_output.error_states == self.expected_error_states_default
        assert model_output.lockdown_status == self.expected_lockdown_status

    def test_health_connector_serializer_optional_field_values(self):
        """Test V3.4 Health connector serializer for missing optional fields"""

        health_output = {
            'connection_status': 'enabled',
        }
        model_output = HealthGenericIntegrationSerializerV3_4(**health_output)
        assert model_output.connection_status == self.expected_connection_status
        assert model_output.auth_status == self.expected_error_states_default
        assert model_output.error_states == self.expected_error_states_default
        assert model_output.lockdown_status == None

    def test_health_connector_serializer_extra_field_values(self):
        """Test V3.4 Health connector serializer for extra field"""

        health_output = {
            'connection_status': 'enabled',
            'auth_status': {'ad_1': 'Not Authorized', 'ad_2': 'Not Configured'},
            'lockdown_status': 'enabled',
            'extra': 'extra',
        }
        model_output = HealthGenericIntegrationSerializerV3_4(**health_output)
        assert model_output.connection_status == self.expected_connection_status
        assert model_output.auth_status == self.expected_auth_status
        assert model_output.error_states == self.expected_error_states_default
        assert model_output.lockdown_status == self.expected_lockdown_status
        with self.assertRaises(AttributeError):
            model_output.extra

    def test_health_connector_serializer_invalid_status(self):
        """Test V3.4 Health connector serializer for invalid connection status value"""

        health_output = {
            'connection_status': 'invalid value',
            'auth_status': 'healthy',
            'lockdown_status': 'locked',
            'logs_last_seen_date': datetime(2024, 5, 14, 15, 30, 45, 123456),
        }
        with self.assertRaises(ValidationError):
            model_output = HealthGenericIntegrationSerializerV3_4(**health_output)

    @skip  # reactivate when last logs times are added
    def test_health_connector_serializer_invalid_datetime(self):
        """Test V3.4 Health connector serializer for invalid logs_last_seen_date value"""

        health_output = {
            'connection_status': 'enabled',
            'auth_status': {'ad_1': 'Not Authorized', 'ad_2': 'Not Configured'},
            'lockdown_status': 'enabled',
            'logs_last_seen_date': 'not a datetime',
        }
        with self.assertRaises(ValidationError):
            model_output = HealthGenericIntegrationSerializerV3_4(**health_output)

    def test_health_connector_serializer_disabled(self):
        """Test V3.4 Health connector serializer for disabled status"""

        health_output = {
            'connection_status': 'disabled',
        }

        model_output = HealthGenericIntegrationSerializerV3_4(**health_output)
        assert model_output.connection_status == self.expected_connection_status_disabled
        assert model_output.auth_status == None
        assert model_output.error_states == None
        assert model_output.lockdown_status == None

    def test_health_connector_serializer_exception(self):
        """Test V3.4 Health connector serializer for disabled status"""

        health_output = {CONNECTOR_TYPE: TYPE_AZURE_AD_LOCKDOWN, CONNECTION_STATUS: UNKNOWN, ERROR_STATES: {"status": GET_EXCEPTION}}

        model_output = HealthGenericIntegrationSerializerV3_4(**health_output)
        assert model_output.connection_status == self.expected_connection_status_exception
        assert model_output.auth_status == None
        assert model_output.error_states == {"status": GET_EXCEPTION}
        assert model_output.lockdown_status == None


class HealthEDRDetailsSerializerV3_4Test(BasicAPIV3SerializerTests):
    """
    Health EDR Details Serializer Tests for API V3_4
    """

    def test_host_lockdown_not_implemented(self):
        """Test V3.4 HostLockdown Pydantic Model"""
        data = {'not_implemented': "testing", 'additional_data': "foo"}
        model_output = HostLockdownSerializerV3_4(**data)
        assert model_output.not_implemented == "testing"
        assert model_output.additional_data == "foo"


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class GroupV3_4SerializerTests(BasicAPIV3SerializerTests):
    def setUp(self):
        super(GroupV3_4SerializerTests, self).setUp()

        # create account
        self.account = Account.objects.create(uid='test_account_1', account_type=Account.TYPE_KERBEROS)

        # create smart rules
        self.sr = smart_rule.objects.create(
            type='typeA',
            priority=40,
            conditions=testing_utils.get_smart_rule_conditions(source_conditions=None, additional_conditions=None),
        )

        # create Account group
        self.group_description = None
        self.group_last_modified = self.now
        self.group_modified_by_username = 'API Client asdf'
        self.group_members = [self.account]
        self.group_rules = [self.sr]
        self.group_importance = 'high'
        self.group_name = 'Test Name'
        self.group_type = 'account'
        self.group_last_modified_by = self.client_user
        self.group_family = GroupCollection.FAMILY_CUSTOMER
        self.group_sr = [{'description': self.sr.description, 'id': self.sr.id, 'triage_category': self.sr.category}]

        self.group = AccountGroup.objects.create(
            name=self.group_name,
            type=self.group_type,
            last_modified_timestamp=self.group_last_modified,
            last_modified_by=self.group_last_modified_by,
            family=self.group_family,
            importance=self.group_importance,
            description=self.group_description,
        )
        self.group.accountgroup.accounts.set(self.group_members)
        self.group.accountgroup.smart_rule_set.set([self.sr])

        # create host
        self.host_name = 'test_host_event_detection'
        self.host_state = 'active'
        self.host_t_score = 10
        self.host_c_score = 10
        self.hst = host.objects.create(name=self.host_name, state=self.host_state, t_score=self.host_t_score, c_score=self.host_c_score)
        self.host_url = f'https://testserver/hosts/{self.hst.id}'

        # host session setup
        self.host_session = host_session.objects.create(
            host=self.hst, ip_address='***********', start=datetime(2023, 1, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC)
        )

        # create Host group
        self.host_group_description = None
        self.host_group_last_modified = self.now
        self.host_group_members = [self.hst]
        self.host_group_rules = [self.sr]
        self.host_group_name = 'Test Host Group Name'
        self.host_group_type = 'host'
        self.host_group_last_modified_by = self.client_user
        self.host_group_family = GroupCollection.FAMILY_CUSTOMER

        self.host_group = HostGroup.objects.create(
            name=self.host_group_name,
            type=self.host_group_type,
            last_modified_timestamp=self.host_group_last_modified,
            last_modified_by=self.host_group_last_modified_by,
            family=self.host_group_family,
            importance=self.group_importance,
            description=self.host_group_description,
        )
        self.host_group.hostgroup.hosts.set(self.host_group_members)
        self.host_group.hostgroup.smart_rule_set.set([self.sr])

        # create IP group
        self.ip_group_description = "IP Group"
        self.ip_group_last_modified = self.now
        self.ip_group_members = ['*******']
        self.ip_group_rules = [self.sr]
        self.ip_group_name = 'Test IP Group Name'
        self.ip_group_type = 'ip'
        self.ip_group_last_modified_by = self.client_user
        self.ip_group_family = GroupCollection.FAMILY_CUSTOMER

        self.ip_group = IPGroup.objects.create(
            name=self.ip_group_name,
            type=self.ip_group_type,
            last_modified_timestamp=self.ip_group_last_modified,
            last_modified_by=self.ip_group_last_modified_by,
            family=self.ip_group_family,
            importance=self.group_importance,
            description=self.ip_group_description,
            ips=self.ip_group_members,
        )
        self.ip_group.ipgroup.ip_smart_rules.set([self.sr])

        # create domain group
        self.domain_group_description = "Domain Group"
        self.domain_group_last_modified = self.now
        self.domain_group_members = ['domain1.com']
        self.domain_group_rules = [self.sr]
        self.domain_group_name = 'Test Domain Group Name'
        self.domain_group_type = 'domain'
        self.domain_group_last_modified_by = self.client_user
        self.domain_group_family = GroupCollection.FAMILY_CUSTOMER

        self.domain_group = ExternalDomainGroup.objects.create(
            name=self.domain_group_name,
            type=self.domain_group_type,
            last_modified_timestamp=self.domain_group_last_modified,
            last_modified_by=self.domain_group_last_modified_by,
            family=self.domain_group_family,
            importance=self.group_importance,
            description=self.domain_group_description,
            domains=self.domain_group_members,
        )
        self.domain_group.externaldomaingroup.remote1_dns_smart_rules.set([self.sr])

        # request
        factory = APIRequestFactory()
        request = factory.get('/')
        request.query_params = request.GET

        # serializer
        self.account_ent_serializer = GroupSerializerV3_4(instance=self.group, context={'request': request})
        self.host_ent_serializer = GroupSerializerV3_4(instance=self.host_group, context={'request': request})
        self.ip_ent_serializer = GroupSerializerV3_4(instance=self.ip_group, context={'request': request})
        self.domain_ent_serializer = GroupSerializerV3_4(instance=self.domain_group, context={'request': request})

    def tearDown(self):
        Account.objects.all().delete()
        AccountGroup.objects.all().delete()
        GroupCollection.objects.all().delete()
        smart_rule.objects.all().delete()

    def test_group_serializer(self):
        """Test V3.4 group serializer"""
        with self.cloud_env():
            # retrieve serialized data
            data = self.account_ent_serializer.data
            # verify data
            self.assertEqual(data['last_modified'][0:12], self.group_last_modified.isoformat()[0:12])
            data.pop('last_modified')
            assert data == {
                'id': self.group.id,
                'name': self.group_name,
                'description': self.group_description,
                'last_modified_by': self.group_modified_by_username,
                'type': self.group_type,
                'members': [{'uid': self.account.uid}],
                'rules': self.group_sr,
                'importance': self.group_importance,
                'built_using': 'static_members',
                'member_count': 1,
                'membership_evaluation_ongoing': False,
                'regex': None,
                'members_truncated': False,
                'ad_group_dn': None,
            }

    def test_host_group_serializer(self):
        """Test V3.4 group serializer"""
        with self.cloud_env():
            # retrieve serialized data
            data = self.host_ent_serializer.data
            # verify data
            self.assertEqual(data['last_modified'][0:12], self.host_group_last_modified.isoformat()[0:12])
            data.pop('last_modified')
            assert data == {
                'id': self.host_group.id,
                'name': self.host_group_name,
                'description': self.host_group_description,
                'last_modified_by': self.group_modified_by_username,
                'type': self.host_group_type,
                'members': [
                    {
                        'id': self.hst.id,
                        'is_key_asset': False,
                        'name': 'test_host_event_detection',
                        'url': f'https://testserver/api/v3.4/hosts/{self.hst.id}',
                    }
                ],
                'rules': self.group_sr,
                'importance': self.group_importance,
                'built_using': 'static_members',
                'member_count': 1,
                'membership_evaluation_ongoing': False,
                'regex': None,
                'members_truncated': False,
                'ad_group_dn': None,
            }

    def test_ip_group_serializer(self):
        """Test V3.4 group serializer"""
        with self.cloud_env():
            # retrieve serialized data
            data = self.ip_ent_serializer.data
            # verify data
            self.assertEqual(data['last_modified'][0:12], self.ip_group_last_modified.isoformat()[0:12])
            data.pop('last_modified')
            assert data == {
                'id': self.ip_group.id,
                'name': self.ip_group_name,
                'description': self.ip_group_description,
                'last_modified_by': self.group_modified_by_username,
                'type': self.ip_group_type,
                'members': ['*******'],
                'rules': self.group_sr,
                'importance': self.group_importance,
                'built_using': 'static_members',
                'member_count': 1,
                'membership_evaluation_ongoing': False,
                'regex': None,
                'members_truncated': False,
                'ad_group_dn': None,
            }

    def test_domain_group_serializer(self):
        """Test V3.4 group serializer"""
        with self.cloud_env():
            # retrieve serialized data
            data = self.domain_ent_serializer.data
            # verify data
            self.assertEqual(data['last_modified'][0:12], self.domain_group_last_modified.isoformat()[0:12])
            data.pop('last_modified')
            assert data == {
                'id': self.domain_group.id,
                'name': self.domain_group_name,
                'description': self.domain_group_description,
                'last_modified_by': self.group_modified_by_username,
                'type': self.domain_group_type,
                'members': ["domain1.com"],
                'rules': self.group_sr,
                'importance': self.group_importance,
                'built_using': 'static_members',
                'member_count': 1,
                'membership_evaluation_ongoing': False,
                'regex': None,
                'members_truncated': False,
                'ad_group_dn': None,
            }
