# -*- coding: utf-8 -*-
# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest import mock
import urllib
from django.http import JsonResponse
from django.urls import reverse
from django.utils import timezone
from django.test.utils import override_settings
from requests import Response
import random
import pytz
from rest_framework.authtoken.models import Token
from rest_framework.test import APIClient, APIRequestFactory, force_authenticate
from unittest.mock import patch, Mock
from celery.exceptions import TimeoutError
from base_tvui.feature_flipper import view_requires_flag

from base_tvui.settings import PLATFORM_REST_URL
from base_tvui.platform_api import PlatformClient
from base_tvui.bin_utils import compress_list
from datetime import datetime, timedelta
from base_tvui import account_type
from freezegun import freeze_time
from base_tvui.lib_ldap import LdapClient
from base_tvui.rest.custom_exceptions import InvalidQueryParams, InvalidQueryParamValues
from vui_tests.base_tvui.smart_rules import testing_utils
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils import (
    get_azure_ad_lockdown_fields,
    get_aws_fields,
    get_azure_fields,
    get_google_cloud_fields,
    get_reverse_lookup_dns_fields,
    get_siem_fields,
    get_vcenter_fields,
    get_window_events_log_ingestion_fields,
    get_zscaler_private_access_fields,
)

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import (
    DetectionsV3_4,
    UserV3_4,
    UsersV3_4,
    UserRolesV3_4,
    NetworkBrainHealthPingV3_4,
    EntityV3_4,
    EntitiesV3_4,
    EntityScoringEventsV3_4,
    DetectionEventsV3_4,
    EntityLockdownV3_4,
    DetectionV3_4,
    ExternalConnectorsHealthV3_4,
    ExternalConnectorsHealthDetailsV3_4,
    EDRHealthV3_4,
    EDRHealthDetailsV3_4,
    HealthV3_4,
    UniqueHostCountTimespanV3_4,
    UniqueHostAuditTimespanV3_4,
    UniqueHostCountMonthlyV3_4,
    UniqueHostAuditMonthlyV3_4,
    GroupsV3_4,
    GroupV3_4,
    NotificationReceiversBaseView,
)
from base_tvui.rest.api_v3.api_v3_3.api_v3_3_views import CloudbridgeRouter
import base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils as health_utils
from base_tvui import settings
from vui_tests.vui_testcase import VuiTestCase
from tvui.detections.detection_types import DetectionType
from tvui.settings.lib_settings import Misc
from tvui.models import (
    notes,
    Account,
    ApiClientProfile,
    detection,
    HealthSubject,
    HealthSubjectHistory,
    host,
    host_session,
    User,
    SaasSAMLProfile,
    VUIGroup,
    group_extend,
    setting,
    LinkedAccount,
    LinkedAccountScoreHistory,
    DetectionEvent,
    AccountLockdownQueue,
    HostAudit,
    HostAuditSnapshot,
    HostGroup,
    IPGroup,
    AccountGroup,
    GroupCollection,
    ExternalDomainGroup,
    smart_rule,
    CloseHistory,
    StateReasons,
    DataSourceType,
    detection_detail,
    LdapContext,
)
from base_tvui.feature_flipper.base import SimpleFlag
from base_tvui.feature_flipper import Flags
from vui_tests.tvui.test_cloud_user_management_views import MockBotoSession
from tvui.async_tasks.celery_tasks import refresh_external_connector_fields
from vui_tests.base_tvui.smart_rules import testing_utils

from taggit.models import Tag
from django_celery_results.models import TaskResult

import json

INVALID_PARAM_DETAIL = InvalidQueryParams('').detail


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@override_settings(MIDDLEWARE={})
class BaseAPIV3_4Tests(VuiTestCase):
    """
    Base setup for API V3_4 tests
    """

    def setUp(self):
        self.now = timezone.now()


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class BasicAPIV3_4Tests(BaseAPIV3_4Tests):
    """
    Basic tests for API V3_4 (invalid urls, authentication, etc.)
    """

    def setUp(self):
        super(BasicAPIV3_4Tests, self).setUp()

        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        # ignore permissions
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True

        self.client_user = User.objects.create_user(
            username='api_client_asdf',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='asdf', name='My Test Client', description='blah blah', created_by=self.user
            ),
        )

        self.client = APIClient(REMOTE_ADDR='************')

        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class EntityV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Entity Endpoint in API v3.4
    """

    def setUp(self):
        super(EntityV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.network_account_1 = Account.objects.create(uid='acc@network_1.com', account_type=Account.TYPE_KERBEROS)

    def tearDown(self):
        LinkedAccount.objects.all().delete()
        Account.objects.all().delete()

    def test_entity_account(self):
        """Test get account entity, verify fields"""
        url = "{}?type=account".format(reverse('api-v3.4:api-entity-v3.4', kwargs={'pk': self.network_account_1.linked_account.id}))
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntityV3_4.as_view()(req, pk=self.network_account_1.linked_account.id)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertIsNone(resp_data.get('entity_type', None))
        self.assertIsNone(resp_data.get('entity_importance', None))
        self.assertIsNotNone(resp_data.get('type', None))
        self.assertIsNotNone(resp_data.get('importance', None))

    def test_entity_account_invalid_query_param(self):
        """Test get account entity"""
        url = "{}?entity_type=account".format(reverse('api-v3.4:api-entity-v3.4', kwargs={'pk': self.network_account_1.linked_account.id}))
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntityV3_4.as_view()(req, pk=self.network_account_1.linked_account.id)
        self.assertEqual(resp.status_code, 400)
        resp_data = json.loads(resp.render().content)
        self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class EntitiesV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Entities Endpoint in API v3.4
    """

    def setUp(self):
        super(EntitiesV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.now = datetime(2020, 1, 1, 8, 0, 0, tzinfo=timezone.utc)
        # create accounts
        self.account_1 = Account.objects.create(uid='test_account_1', account_type=Account.TYPE_KERBEROS)

    def tearDown(self):
        detection.objects.all().delete()
        Account.objects.all().delete()
        host.objects.all().delete()

    def test_api_v3_4_entities(self):
        """Test V3.4 /entities endpoint GET, verify fields"""
        url = reverse('api-v3.4:api-entities-v3.4') + '/?type=account'
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntitiesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertIsNone(resp_data['results'][0].get('entity_importance', None))
        self.assertIsNone(resp_data['results'][0].get('entity_type', None))

    def test_api_v3_4_entities_invalid_param(self):
        """Test V3.4 /entities endpoint GET invalid entity_type params"""
        url = reverse('api-v3.4:api-entities-v3.4') + '/?entity_type=account'
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntitiesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_data = json.loads(resp.render().content)
        self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))

    def test_entities_v3_4_fields(self):
        """Test entities v3.4 endpoint with fields param"""
        url = reverse('api-v3.4:api-entities-v3.4')
        url_params = {'fields': 'id,name,ip'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntitiesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(set(resp_data['results'][0].keys()), set(['id', 'name', 'ip']))

    def test_entities_v3_4_fields_invalid(self):
        """Test entities v3.4 endpoint with fields param invalid value"""
        url = reverse('api-v3.4:api-entities-v3.4')
        url_params = {'fields': 'id,name,ip,nonsense'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntitiesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)

    def test_entities_v3_4_exclude_fields(self):
        """Test entities v3.4 endpoint with exclude_fields param"""
        url = reverse('api-v3.4:api-entities-v3.4')
        url_params = {'exclude_fields': 'id,name,ip'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntitiesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        key_set = set(resp_data['results'][0].keys())
        assert 'id' not in key_set
        assert 'name' not in key_set
        assert 'ip' not in key_set

    def test_entities_v3_4_excluded_fields_invalid(self):
        """Test entities v3.4 endpoint with exclude_fields param invalid valur"""
        url = reverse('api-v3.4:api-entities-v3.4')
        url_params = {'exclude_fields': 'id,name,ip,nonsense'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntitiesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class EntityScoringEventsV3_4Tests(BasicAPIV3_4Tests):
    def setUp(self):
        super(EntityScoringEventsV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

        self.o365_account = Account.objects.create(uid='<EMAIL>', account_type=Account.TYPE_O365)
        self.version = '2022.0.0'

        # this is the last detection
        self.test_det_1 = detection.objects.create(
            account=self.o365_account,
            type=DetectionType.O365_SUSPICIOUS_MAILBOX_RULE,
            type_vname='O365 Suspicious Mailbox Rule',
            src_ip='**********',
            last_timestamp=self.now - timedelta(minutes=1),
        )

        self.test_det_2 = detection.objects.create(
            account=self.o365_account,
            type=DetectionType.O365_SUSPECT_E_DISCOVERY_USAGE,
            type_vname='O365 Suspect eDiscovery Usage',
            src_ip='**********',
            last_timestamp=self.now - timedelta(minutes=2),
        )

        self.event_data = {
            'account_id': self.o365_account.linked_account.id,
            'account_uid': '<EMAIL>',
            'category': 'ACCOUNT SCORING',
            'last_detection_type': self.test_det_1.type_vname,
            'last_detection_id': self.test_det_1.id,
            'active_detection_types': ['O365 Suspect eDiscovery Usage', 'O365 Suspicious Mailbox Rule'],
            'version': self.version,
        }

        self.eight_hours_ago = self.now - timedelta(hours=8)
        self.four_hours_ago = self.now - timedelta(hours=4)

        self.first_score_event = LinkedAccountScoreHistory.objects.create(
            account=self.o365_account.linked_account,
            c_score=56,
            t_score=47,
            score_date=self.eight_hours_ago,
            data=self.event_data,
            score_decrease=False,
        )

        self.second_score_event = LinkedAccountScoreHistory.objects.create(
            account=self.o365_account.linked_account,
            c_score=30,
            t_score=30,
            score_date=self.four_hours_ago,
            data=None,
            score_decrease=True,
        )

    def test_entity_scoring_events_v3_4_invalid_param(self):
        """Test entity scoring events v3.4 endpoint with invalid param"""
        url = reverse('api-v3.4:api-entity-scoring-events-v3.4') + '/?entity_type=account'
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntityScoringEventsV3_4.as_view()(req)
        # verify endpoint
        resp_data = json.loads(resp.render().content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))

    def test_entity_scoring_events_v3_4_check_fields(self):
        """Test entity scoring events v3.4 endpoint response fields"""
        url = reverse('api-v3.4:api-entity-scoring-events-v3.4') + '/?type=account'
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntityScoringEventsV3_4.as_view()(req)
        # verify endpoint
        resp_data = json.loads(resp.content)
        first_event = resp_data['events'][0]
        self.assertIsNone(first_event.get('entity_importance', None))
        self.assertIsNone(first_event.get('entity_type', None))
        self.assertIsNone(first_event.get('last_detection_id', None))
        self.assertIsNone(first_event.get('last_detection_type', None))
        self.assertIsNone(first_event.get('last_detection_url', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class DetectionEventsV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Detection Event Endpoints in API v3.4.
    """

    def setUp(self):
        super(DetectionEventsV3_4Tests, self).setUp()

        self.url = reverse('api-v3.4:api-detection-events-v3.4')
        self.factory = APIRequestFactory()
        self.api_version = '3.4'
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.two_hours_ago = self.now - timedelta(hours=2)
        self.four_hours_ago = self.now - timedelta(hours=4)
        self.mock_perm_check.return_value = True

        self.test_linked_accounts = [
            LinkedAccount.objects.create(id=11, display_uid='linked_account_0'),
        ]

        self.test_accounts = [
            Account.objects.create(uid='account@network_1.com', account_type=Account.TYPE_KERBEROS),
        ]

        self.test_accounts[0].linked_account = self.test_linked_accounts[0]
        self.test_accounts[0].save()

        self.test_account_events = [
            DetectionEvent.objects.create(
                id=1,
                category="recon",
                type=DetectionType.SW_O365_RANSOMWARE,
                entity_type=DetectionEvent.ACCOUNT,
                detection_detail_id=1,
                detection_id=1,
                threat=80,
                certainty=80,
                ip_address="*******",
                timestamp=self.four_hours_ago,
                data={'foo': 'bar', 'foo2': 'bar2'},
                account_id=self.test_accounts[0].id,
                account_uid=self.test_accounts[0].uid,
            ),
        ]

    def test_get_detection_events_invalid_param(self):
        """
        Test detection events with invalid query param
        """
        query_params = {
            'entity_type': 'account',
        }

        req = self.factory.get(self.url, query_params)
        req.api_version = self.api_version
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionEventsV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp.status_code, 400)
        self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))

    def test_get_detection_events_validate_params(self):
        """
        Test detection events to validate response fields
        """
        query_params = {
            'type': 'account',
        }

        req = self.factory.get(self.url, query_params)
        req.api_version = self.api_version
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionEventsV3_4.as_view()(req)

        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        first_event = resp_data['events'][0]
        self.assertIsNone(first_event.get('entity_type', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class EntityLockdownV3_4Tests(BasicAPIV3_4Tests):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.now = datetime(2023, 3, 17, 8, 0, 0)

        self.account_0 = Account.objects.create(uid='test_account_0', account_type=Account.TYPE_KERBEROS)

        with freeze_time(self.now):
            self.acct_lock = AccountLockdownQueue.objects.create(
                account=self.account_0, expiration=(self.now + timedelta(days=1)), user=self.user
            )

    def test_entity_lockdown_v3_4_invalid_param(self):
        """Test lockdown v3.4 endpoint invalid param entity_type"""
        url = reverse('api-v3.4:api-entity-lockdown-v3.4') + '/?entity_type=account'
        factory = APIRequestFactory()
        req = factory.get(url)
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntityLockdownV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)
        self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))
        # verify endpoint
        self.assertEqual(resp.status_code, 400)

    def test_entity_lockdown_v3_4_validate_fields(self):
        """Test lockdown v3.4 endpoint validate fields"""
        url = reverse('api-v3.4:api-entity-lockdown-v3.4') + '/?type=account'
        factory = APIRequestFactory()
        req = factory.get(url)
        force_authenticate(req, token=self.token, user=self.user)
        resp = EntityLockdownV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        first_lock = resp_data[0]
        self.assertEqual(resp.status_code, 200)
        self.assertIsNone(first_lock.get('entity_type', None))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class NotesV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Notes Endpoint Api V3.4
    """

    def setUp(self):
        super(NotesV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.now = datetime(2023, 3, 17, 8, 0, 0)

        # create hosts
        self.host_0 = host.objects.create(name='mostly_mchostface', state='active', t_score=99, c_score=50, key_asset=True)
        # create notes for host
        with freeze_time(self.now):
            self.note1 = notes.objects.create(type='host', type_id=self.host_0.id, note='this is note 1', created_by=self.user)
        with freeze_time(self.now + timedelta(hours=1)):
            self.note2 = notes.objects.create(type='host', type_id=self.host_0.id, note='this is note 2', created_by=self.user)

    def test_get_notes_v3_4_status_code(self):

        url = reverse('api-v3.4:api-notes-v3.4-listcreate', kwargs={'tvui_type': 'entitie', 'type_id': self.host_0.id})
        url_params = {'entity_type': 'host'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        resp = self.client.get(full_url)
        self.assertEqual(resp.status_code, 400)
        resp_json = resp.json()
        self.assertIn('The following query parameter(s) are required', resp_json.get('detail', ''))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
class UserAPIV3_4Tests(BasicAPIV3_4Tests):
    """
    Test User Endpoint
    """

    def setUp(self):
        super(UserAPIV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

        self.view_group = VUIGroup.pure.create(name='auditor')
        group_extend.objects.create(group=self.view_group, vname='Auditor')

        self.now_timestamp = timezone.now()
        self.test_user = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect',
            first_name='Fancy',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )
        self.test_user.groups.add(self.view_group)

        # delete setup
        self.delete_user = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect',
            first_name='Energetic',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )

        self.cognito_delete_expected_params = {
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': self.delete_user.username,
        }
        self.delete_user.groups.add(self.view_group)

        self.delete_super_user_a = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect',
            first_name='Super',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )

        self.cognito_delete_super_a_expected_params = {
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': self.delete_super_user_a.username,
        }

        self.delete_super_user_b = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect',
            first_name='Super Duper',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )

        self.cognito_delete_super_b_expected_params = {
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': self.delete_super_user_b.username,
        }

        super_group = VUIGroup.pure.create(name='Super Admin')
        super_group.name = super_group.id
        group_extend.objects.create(group=super_group, vname='Super Admin')
        self.delete_super_user_b.groups.add(super_group)
        self.delete_super_user_a.groups.add(super_group)

        # patch setup
        security_analyst_group = VUIGroup.pure.create(name='security_analyst')
        group_extend.objects.create(group=security_analyst_group, vname='Security Analyst')

        self.cognito_update_expected_params = {
            'UserAttributes': [{'Name': 'name', 'Value': 'Test Testerson'}],
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': '<EMAIL>',
        }

        self.cognito_update_name_expected_params = {
            'UserAttributes': [{'Name': 'name', 'Value': 'Test Testerson'}],
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': '<EMAIL>',
        }

        self.cognito_update_role_expected_params = {
            'UserAttributes': [],
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': '<EMAIL>',
        }

        self.patch_user = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            account_type=User.JWT,
            first_name='Happy',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )

        self.patch_user.groups.add(self.view_group)

    def test_api_v3_4_user(self):
        """Test V3.4 User"""
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.test_user.id})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.test_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content)
        expected_response = {
            'id': self.test_user.id,
            'email': '<EMAIL>',
            'name': 'Fancy Bear',
            'role': 'auditor',
            'last_login_timestamp': self.now_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ"),
            'verified': True,
            'identities': [],
        }
        self.assertEqual(expected_response, resp_data)

    def test_api_v3_4_user_404(self):
        """Test V3.4 User Not Found"""
        existing_ids = [
            self.test_user.id,
            self.user.id,
            self.delete_user.id,
            self.delete_super_user_a.id,
            self.delete_super_user_b.id,
            self.patch_user.id,
        ]
        new_id = random.randint(1, 1000)
        while new_id in existing_ids:
            new_id = random.randint(1, 1000)
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': new_id})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=new_id)
        # verify endpoint
        self.assertEqual(resp.status_code, 404)
        resp_data = json.loads(resp.render().content)
        expected_error = {"detail": f"Not found."}
        self.assertEqual(resp_data, expected_error)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_delete_user(self, session):
        """Test V3.4 Delete User"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_delete_user",
            expected_params=self.cognito_delete_expected_params,
            service_response={},
        )

        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.delete_user.id})
        factory = APIRequestFactory()
        req = factory.delete(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.delete_user.id)
        # verify endpoint success
        self.assertEqual(resp.status_code, 204)
        # verfiy 404 response after deletion
        resp = UserV3_4.as_view()(req, pk=self.delete_user.id)
        self.assertEqual(resp.status_code, 404)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_delete_super_users(self, session):
        """Test V3.4 Delete Super Users"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_delete_user",
            expected_params=self.cognito_delete_super_a_expected_params,
            service_response={},
        )

        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.delete_super_user_a.id})
        factory = APIRequestFactory()
        req = factory.delete(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.delete_super_user_a.id)
        # verify super user deletion
        self.assertEqual(resp.status_code, 204)
        # verify super user deletion blocked for last super user
        cognito_stub.add_response(
            method="admin_delete_user",
            expected_params=self.cognito_delete_super_b_expected_params,
            service_response={},
        )
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.delete_super_user_b.id})
        req = factory.delete(url)
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.delete_super_user_b.id)
        self.assertEqual(resp.status_code, 403)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['error'], 'Cannot delete last remaining verified Super Admin user.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @mock.patch('tvui.cloud_user_management_views.handle_response')
    def test_api_v3_4_delete_users_cognito_error(self, mock_resp, session):
        """Test V3.4 Delete Users Cognito Error"""
        mock_resp.return_value = JsonResponse({}, status=500)
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_delete_user",
            expected_params=self.cognito_delete_expected_params,
            service_response={},
        )

        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.delete_user.id})
        factory = APIRequestFactory()
        req = factory.delete(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.delete_user.id)
        # verify endpoint error
        self.assertEqual(resp.status_code, 500)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Error deleting user, please retry.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_success(self, session):
        """Test V3.4 Patch User Success"""
        # Cognito patch
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_expected_params,
            service_response={},
        )

        # new data payload
        new_name = 'Test Testerson'
        new_role = 'security_analyst'
        payload = {'name': new_name, 'role': new_role}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

        resp_obj = resp.content.decode('utf-8')
        resp_user = json.loads(resp_obj)
        expected_usr = {
            'id': User.objects.conditional_users().get(username='<EMAIL>').id,
            'email': '<EMAIL>',
            'name': new_name,
            'role': new_role,
            'last_login_timestamp': self.now_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ"),
            'verified': True,
            'identities': [],
        }
        self.assertEqual(resp_user, expected_usr)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_name_success(self, session):
        """Test V3.4 Patch User Name Success"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_name_expected_params,
            service_response={},
        )

        # new data payload
        new_name = 'Test Testerson'
        payload = {'name': new_name}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

        resp_obj = resp.content.decode('utf-8')
        resp_user = json.loads(resp_obj)
        expected_usr = {
            'id': User.objects.conditional_users().get(username='<EMAIL>').id,
            'email': '<EMAIL>',
            'name': new_name,
            'role': self.view_group.name,
            'last_login_timestamp': self.now_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ"),
            'verified': True,
            'identities': [],
        }
        self.assertEqual(resp_user, expected_usr)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_role_success(self, session):
        """Test V3.4 Patch User Role Success"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_role_expected_params,
            service_response={},
        )

        # new data payload
        new_role = 'security_analyst'
        payload = {'role': new_role}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

        resp_obj = resp.content.decode('utf-8')
        resp_user = json.loads(resp_obj)
        expected_usr = {
            'id': User.objects.conditional_users().get(username='<EMAIL>').id,
            'email': '<EMAIL>',
            'name': self.patch_user.first_name + ' ' + self.patch_user.last_name,
            'role': new_role,
            'last_login_timestamp': self.now_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ"),
            'verified': True,
            'identities': [],
        }
        self.assertEqual(resp_user, expected_usr)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_no_change_success(self, session):
        """Test V3.4 Patch User No Change Success"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session

        # should make no calls if no changes
        # NO stub add_response()
        payload = {}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

        resp_obj = resp.content.decode('utf-8')
        resp_user = json.loads(resp_obj)
        expected_usr = {
            'id': User.objects.conditional_users().get(username='<EMAIL>').id,
            'email': '<EMAIL>',
            'name': self.patch_user.first_name + ' ' + self.patch_user.last_name,
            'role': self.view_group.name,
            'last_login_timestamp': self.now_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ"),
            'verified': True,
            'identities': [],
        }
        self.assertEqual(resp_user, expected_usr)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @mock.patch('tvui.cloud_user_management_views.handle_response')
    def test_api_v3_4_patch_users_cognito_error(self, mock_resp, session):
        """Test V3.4 Patch User Cognito Error"""
        mock_resp.return_value = JsonResponse({}, status=500)
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_expected_params,
            service_response={},
        )

        # new data payload
        payload = {'name': 'Test Testerson', 'role': 'security_analyst'}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 500)

        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Error patching user, please retry.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_invalid_payload(self, session):
        """Test V3.4 Patch User with invalid content type"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_expected_params,
            service_response={},
        )

        payload = {'name': 'Test Testerson', 'role': 'security_analyst'}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=payload, content_type='application/json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Payload must be valid JSON')

    def test_api_v3_4_patch_users_404(self):
        """Test V3.4 Patch User Not Found"""
        existing_ids = [
            self.test_user.id,
            self.user.id,
            self.delete_user.id,
            self.delete_super_user_a.id,
            self.delete_super_user_b.id,
            self.patch_user.id,
        ]
        new_id = random.randint(1, 1000)
        while new_id in existing_ids:
            new_id = random.randint(1, 1000)
        name = self.patch_user.first_name + ' ' + self.patch_user.last_name
        role = self.view_group.name
        payload = {'name': name, 'role': role}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': new_id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=new_id)
        # verify endpoint
        self.assertEqual(resp.status_code, 404)
        resp_data = json.loads(resp.render().content)
        expected_error = {"detail": "Not found."}
        self.assertEqual(resp_data, expected_error)

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_invalid_role(self, session):
        """Test V3.4 Patch User with invalid role"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_expected_params,
            service_response={},
        )

        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        payload = {'role': 'new_role'}
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Invalid role provided. Query api/v3.4/users/roles for the role\'s standardized_name.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_invalid_name(self, session):
        """Test V3.4 Patch User with invalid name"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_expected_params,
            service_response={},
        )

        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        payload = {'name': 'Test'}
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'The name field requires space separated first and last name.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_invalid_body(self, session):
        """Test V3.4 Patch User with invalid body"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_expected_params,
            service_response={},
        )

        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        payload = {'name': 'Test Testerson', 'role': 'security_analyst', 'extra_field': 'extra'}
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.render().content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('detail')
        self.assertEqual(resp_err, 'The following request body fields are invalid/unsupported: extra_field')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_role_saml_user_fail(self, session):
        """Test V3.4 Patch User Role when user has saml enabled should be blocked"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_role_expected_params,
            service_response={},
        )

        # Add active saml profile to user
        saml_profile = SaasSAMLProfile.objects.create(name='test_idp', enabled=True)
        self.patch_user.saas_saml_profiles.set([saml_profile])
        self.patch_user.save()

        # new data payload
        new_role = 'security_analyst'
        payload = {'role': new_role}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Cannot modify the role of a currently active SAML user.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_name_saml_user_fail(self, session):
        """Test V3.4 Patch User name when user has saml enabled should be blocked"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_role_expected_params,
            service_response={},
        )

        # Add active saml profile to user
        saml_profile = SaasSAMLProfile.objects.create(name='test_idp', enabled=True)
        self.patch_user.saas_saml_profiles.set([saml_profile])
        self.patch_user.save()

        # new data payload
        payload = {'name': 'new name'}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Cannot modify the name of a currently active SAML user.')

    @patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    def test_api_v3_4_patch_users_same_name_role_saml_user(self, session):
        """Test V3.4 Patch User name and role to the current values when user has saml enabled"""
        mock_session = MockBotoSession(['cognito-idp'])
        session.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_update_user_attributes",
            expected_params=self.cognito_update_role_expected_params,
            service_response={},
        )

        # Add active saml profile to user
        saml_profile = SaasSAMLProfile.objects.create(name='test_idp', enabled=True)
        self.patch_user.saas_saml_profiles.set([saml_profile])
        self.patch_user.save()

        # new data payload
        payload = {'name': 'Happy Bear', 'role': "auditor"}
        url = reverse('api-v3.4:api-user-v3.4', kwargs={'pk': self.patch_user.id})
        factory = APIRequestFactory()
        req = factory.patch(url, data=json.dumps(payload), content_type='application/json')
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserV3_4.as_view()(req, pk=self.patch_user.id)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@patch('tvui.cloud_user_management_views.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'saas_unified_user_management': SimpleFlag(lambda: True)})
class UsersAPIV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Users Endpoint
    """

    def setUp(self):
        super(UsersAPIV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

        view_group = VUIGroup.pure.create(name='auditor')
        group_extend.objects.create(group=view_group, vname='Auditor')

        view_group_analyst = VUIGroup.pure.create(name='security_analyst')
        view_group_analyst.name = view_group_analyst.id
        group_extend.objects.create(group=view_group_analyst, vname='Security Analyst')

        self.now_timestamp = timezone.now()
        self.test_user_a = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect',
            first_name='Fancy',
            last_name='Bear',
            first_login=self.now_timestamp,
            last_login=self.now_timestamp,
        )
        self.test_user_a.groups.add(view_group)

        self.yesterday_timestamp = datetime.now(pytz.utc) - timedelta(days=1)
        self.test_user_b = User.objects.create_user(
            '<EMAIL>',
            email='<EMAIL>',
            password='detect_b',
            first_name='Cozy',
            last_name='Bear',
            first_login=self.yesterday_timestamp,
            last_login=self.yesterday_timestamp,
        )
        self.test_user_b.groups.add(view_group_analyst)

        self.cognito_create_user_expected_params = {
            'MessageAction': 'SUPPRESS',
            'TemporaryPassword': 's3cr3t',
            'UserAttributes': [
                {'Name': 'custom:external_vui_id', 'Value': '0**********0'},
                {'Name': 'name', 'Value': 'Test Testerson'},
                {'Name': 'email', 'Value': '<EMAIL>'},
                {'Name': 'email_verified', 'Value': 'true'},
            ],
            'UserPoolId': 'us-west-2_CJ18X55Zq',
            'Username': '<EMAIL>',
        }

        self.ses_send_templated_email_expected_params = {
            'Destination': {'ToAddresses': ['<EMAIL>']},
            'ReplyToAddresses': ['<EMAIL>'],
            'ReturnPathArn': 'arn:aws:ses:us-west-2:************:identity/vectra.ai',
            'Source': '<EMAIL>',
            'SourceArn': 'arn:aws:ses:us-west-2:************:identity/vectra.ai',
            'Template': 'vui_new_user_template',
            'TemplateData': '{"company": "<EMAIL>", "user": "<EMAIL>", "name": '
            '"<EMAIL>", "password": "s3cr3t", "account": '
            '"0**********0", "url": "https:///signIn?local=True"}',
        }

    def test_api_v3_4_users_invalid_last_login(self):
        """Test V3.4 Users invalid last login with a space"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}?last_login_gte=2024-06-01T 2:00")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_error = json.loads(resp.render().content).get('last_login_gte')
        self.assertEqual(resp_error, "Invalid value given for parameter 'last_login_gte'. Must be a valid timestamp: 2017-12-31T16:55:50Z.")

        # url encoded '+' is processed as a space
        req = factory.get(f"{url}?last_login_gte=2024-06-01T+2:00")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_error = json.loads(resp.render().content).get('last_login_gte')
        self.assertEqual(resp_error, "Invalid value given for parameter 'last_login_gte'. Must be a valid timestamp: 2017-12-31T16:55:50Z.")

        req = factory.get(f"{url}?last_login_gte=2024")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_error = json.loads(resp.render().content).get('last_login_gte')
        self.assertEqual(resp_error, "Invalid value given for parameter 'last_login_gte'. Must be a valid timestamp: 2017-12-31T16:55:50Z.")

    def test_api_v3_4_users_valid_last_login(self):
        """Test V3.4 Users valid last login"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}?last_login_gte=2024-06-01T15%3A30%3A00%2B02%3A00")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

    def test_api_v3_4_users(self):
        """Test V3.4 Users"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)

        self.assertEqual(resp_data['count'], 2)
        expected_keys = ['last_login_timestamp', 'role', 'id', 'email', 'name', 'verified', 'identities']
        self.assertEqual(set(resp_data['results'][0].keys()), set(expected_keys))

    def test_api_v3_4_users_invalid_param(self):
        """Test V3.4 Users invalid query param"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}?account_type=JWT")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)

    def test_api_v3_4_users_email_param(self):
        """Test V3.4 Users email query param"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}?email=<EMAIL>")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 1)

        req = factory.get(f"{url}?email=<EMAIL>")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)

        self.assertEqual(resp_data['count'], 0)

    def test_api_v3_4_users_role_param(self):
        """Test V3.4 Users role query param"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}?role=security_analyst")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 1)

    def test_api_v3_4_users_invalid_role_param(self):
        """Test V3.4 Users with diaplay role name (vname) query param"""
        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        req = factory.get(f"{url}?role=Security Analyst")
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 400)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(
            resp_data[0],
            'Invalid role provided. Query api/v3.4/users/roles for a list of valid roles. Filter with a role\'s standardized_name.',
        )

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_users(self, session_cognito, session_ses, _):
        """Test V3.4 POST User"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {'email': '<EMAIL>', 'name': 'Test Testerson', 'role': 'security_analyst'}
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint success
        self.assertEqual(resp.status_code, 200)

        resp_obj = resp.content.decode('utf-8')
        resp_user = json.loads(resp_obj)
        expected_usr = {
            'id': User.objects.conditional_users().get(username='<EMAIL>').id,
            'email': '<EMAIL>',
            'name': 'Test Testerson',
            'role': 'security_analyst',
            'last_login_timestamp': None,
            'verified': False,
            'identities': [],
        }
        self.assertEqual(resp_user, expected_usr)

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_invalid_role(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with invalid role"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {
            'email': '<EMAIL>',
            'name': 'Test Testerson',
            'role': 'new_role',
        }
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Invalid role provided. Query api/v3.4/users/roles for the role\'s standardized_name.')

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_invalid_email(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with invalid email"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {
            'email': 'tester&test.test',
            'name': 'Test Testerson',
            'role': 'new_role',
        }
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 422)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Invalid email address.')

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_existing_email(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with existing email"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {
            'email': '<EMAIL>',
            'name': 'Test Testerson',
            'role': 'security_analyst',
        }
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'A user profile with this email already exists.')

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_invalid_name(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with invalid name"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {
            'email': '<EMAIL>',
            'name': 'Test',
            'role': 'security_analyst',
        }
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'The name field requires space separated first and last name.')

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_users_invalid_body(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with invalid body"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {'email': '<EMAIL>', 'name': 'Test Testerson', 'role': 'security_analyst', 'extra_field': 'extra'}
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)

        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 400)
        resp.render()
        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('detail')
        self.assertEqual(resp_err, 'The following request body fields are invalid/unsupported: extra_field')

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_users_cognito_error(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with cognito error"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {'email': '<EMAIL>', 'name': 'Test Testerson', 'role': 'security_analyst'}
        req = factory.post(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)

        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 500)

        resp_obj = resp.content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('error')
        self.assertEqual(resp_err, 'Error creating user, please check your payload and retry.')

    @patch("tvui.cloud_user_management_views._generate_password", return_value='s3cr3t')
    @patch("base_tvui.lib_aws_cognito.get_cognito_scoped_session")
    @patch("tvui.cloud_user_management_views.get_user_management_session")
    def test_api_v3_4_post_users_invalid_content_type(self, session_cognito, session_ses, _):
        """Test V3.4 POST User with invalid content type"""
        mock_session = MockBotoSession(['cognito-idp', 'ses'])
        session_cognito.return_value = mock_session
        session_ses.return_value = mock_session
        cognito_stub = mock_session.stubs['cognito-idp']
        cognito_stub.add_response(
            method="admin_create_user",
            expected_params=self.cognito_create_user_expected_params,
            service_response={},
        )

        ses_stub = mock_session.stubs['ses']
        ses_stub.add_response(
            method='send_templated_email',
            expected_params=self.ses_send_templated_email_expected_params,
            service_response={'MessageId': 'asdf'},
        )

        url = reverse(
            'api-v3.4:api-users-v3.4',
        )
        factory = APIRequestFactory()
        data = {'email': '<EMAIL>', 'name': 'Test Testerson', 'role': 'security_analyst'}
        req = factory.post(url, data, content_type='text/plain')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = UsersV3_4.as_view()(req)
        # verify endpoint error
        self.assertEqual(resp.status_code, 415)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class UserRolesAPIV3_4Tests(BasicAPIV3_4Tests):
    """
    Test User Roles Endpoint
    """

    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

        self.view_group_admin = VUIGroup.pure.create(name='admins')
        self.group_extend_admin = group_extend.objects.create(group=self.view_group_admin, vname='Admin')

        self.view_group_analyst = VUIGroup.pure.create(name='security_analyst')
        self.group_extend_analyst = group_extend.objects.create(group=self.view_group_analyst, vname='Security Analyst')

    def test_api_v3_4_user_roles(self):
        """Test V3.4 Get User Roles"""
        url = reverse('api-v3.4:api-user-roles-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        force_authenticate(req, token=self.token, user=self.user)
        resp = UserRolesV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

        resp_data = json.loads(resp.content)
        expected_response = [
            {
                'id': self.view_group_admin.id,
                'name': self.group_extend_admin.vname,
                'standardized_name': self.view_group_admin.name,
            },
            {
                'id': self.view_group_analyst.id,
                'name': self.group_extend_analyst.vname,
                'standardized_name': self.view_group_analyst.name,
            },
        ]
        self.assertEqual(expected_response, resp_data)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class DetectionV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Detection Endpoint
    """

    def setUp(self):
        super().setUp()

        # setup cloud env
        self.cloud_env_patch = patch('base_tvui.feature_flipper.attributes.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
        self.cloud_env_patch.start()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        self.host = host.objects.create(name='testhost1', host_luid='hluid1', state='active', last_source='*******', t_score=77, c_score=42)

        self.host_session = host_session.objects.create(
            host=self.host, session_luid='6', ip_address='*******', start=self.now - timedelta(days=3), end=self.now
        )

        self.host_group = HostGroup.objects.create(
            name='Test Host Group',
            type=GroupCollection.HOST,
            description='Test Host Group Description',
            last_modified_by=self.user,
        )
        self.host_group.hosts.add(self.host)
        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            host_session=self.host_session,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )
        self.dd = detection_detail.objects.create(
            type='sql_inject',
            src_ip='*******',
            dst_ip='*******',
            dst_port='80',
            total_bytes_sent=144,
            total_bytes_rcvd=10192,
            host_detection=self.detection_1,
            couch_note_id='18xsqli94',
            dst_dns='vectra.ai',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
        )

        self.src_ip_group = IPGroup.objects.create(
            name='Test IP Group',
            type=GroupCollection.IP,
            description='Test IP Group Description',
            last_modified_by=self.user,
            ips=[self.detection_1.src_ip],
        )
        self.dst_ip_group = IPGroup.objects.create(
            name='Test Destination IP Group',
            type=GroupCollection.IP,
            description='Test Destination IP Group Description',
            last_modified_by=self.user,
            ips=[self.dd.dst_ip],
        )
        self.dst_dns_group = ExternalDomainGroup.objects.create(
            name='Test Destination DNS Group',
            type=GroupCollection.DOMAIN,
            description='Test Destination DNS Group Description',
            last_modified_by=self.user,
            domains=[self.dd.dst_dns],
        )

        self.url = reverse('api-v3.4:api-detection-v3.4', kwargs={'pk': self.detection_1.id})

    def tearDown(self):
        detection.objects.all().delete()

    def test_detections_deprecated_fields(self):
        """Test get detection, check fields"""
        factory = APIRequestFactory()
        req = factory.get(self.url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertIsNone(resp_data.get('t_score'))
        self.assertIsNone(resp_data.get('c_score'))
        self.assertIsNone(resp_data.get('targets_key_asset'))
        self.assertIsNone(resp_data.get('category'))

    def test_detections_with_fields_param(self):
        """Test /v3.4/detections/<id>?fields query param"""
        invalid_param_value = ['t_score', 'c_score', 'targets_key_asset', 'category']
        for value in invalid_param_value:
            query_params = f'?fields={value}'
            factory = APIRequestFactory()
            req = factory.get(self.url + query_params)
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
            # Verify response
            self.assertEqual(resp.status_code, 400)

        query_params = '?fields=certainty'
        factory = APIRequestFactory()
        req = factory.get(self.url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)

        query_params = '?fields=certainty,id'
        factory = APIRequestFactory()
        req = factory.get(self.url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)

    def test_detections_with_exclude_fields_param(self):
        """Test /v3.4/detections/<id>?exclude_fields query param"""
        invalid_param_value = ['t_score', 'c_score', 'targets_key_asset', 'category']
        for value in invalid_param_value:
            query_params = f'?exclude_fields={value}'
            factory = APIRequestFactory()
            req = factory.get(self.url + query_params)
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
            # Verify response
            self.assertEqual(resp.status_code, 400)

        query_params = '?exclude_fields=certainty'
        factory = APIRequestFactory()
        req = factory.get(
            self.url + query_params,
        )
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)

        query_params = '?exclude_fields=certainty,id'
        factory = APIRequestFactory()
        req = factory.get(
            self.url + query_params,
        )
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
    )
    def test_detection_with_reason(self):
        # Ensure that the reason field is included when ff is enabled

        self.detection_1.state = detection.FIXED
        self.detection_1.state_reason = StateReasons.REMEDIATED.value
        self.detection_1.save()

        factory = APIRequestFactory()
        req = factory.get(self.url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertIn('reason', resp_data)
        self.assertEqual(resp_data['reason'], StateReasons.REMEDIATED.value)

    def test_detection_serializer_reason_field_when_flag_disabled(self):
        # Ensure that the reason field is null when ff is disabled

        self.detection_1.state = detection.FIXED
        self.detection_1.state_reason = StateReasons.REMEDIATED.value
        self.detection_1.save()

        factory = APIRequestFactory()
        req = factory.get(self.url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertIn('reason', resp_data)
        self.assertEqual(resp_data['reason'], None)

    def test_include_src_dst_group_query_params_single_det(self):
        """Test detection endpoint with src_ip_group, dst_ip_group, and dst_dns_group query params"""
        query_params = '?include_src_dst_groups=1'
        factory = APIRequestFactory()
        req = factory.get(
            self.url + query_params,
        )
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        resp_data = json.loads(resp.render().content)
        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertEqual(len(resp_data['src_groups']), 2)
        self.assertEqual(len(resp_data['dst_groups']), 2)

    def test_include_src_dst_group_query_params_off_single_det(self):
        """Test detection endpoint without include_src_dst_groups query param"""
        query_params = '?include_src_dst_groups=0'
        factory = APIRequestFactory()
        req = factory.get(
            self.url + query_params,
        )
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionV3_4.as_view()(req, pk=self.detection_1.id)
        resp_data = json.loads(resp.render().content)

        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertNotIn('src_groups', dict(resp_data).keys())
        self.assertNotIn('dst_groups', dict(resp_data).keys())


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class DetectionCloseV3_4Test(BasicAPIV3_4Tests):
    """
    Test Detection/<id>/close Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.url = reverse('api-v3.4:api-detection-close-v3.4', kwargs={'pk': self.detection_1.id})

    def tearDown(self):
        detection.objects.all().delete()

    def test_detection_close_as_remediated(self):
        """
        Test correct values are set when closing a detection as remediated
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.FIXED)
        self.assertEqual(self.detection_1.state_reason, StateReasons.REMEDIATED.value)
        history = CloseHistory.objects.get(detection_id=self.detection_1.id)
        self.assertEqual(history.reason, StateReasons.REMEDIATED.value)

    def test_detection_close_as_benign(self):
        """
        Test correct values are set when closing a detection as benign
        """
        data = json.dumps({'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertTrue(self.detection_1.is_filtered_by_user)
        self.assertEqual(self.detection_1.state_reason, StateReasons.BENIGN.value)
        history = CloseHistory.objects.get(detection_id=self.detection_1.id)
        self.assertEqual(history.reason, StateReasons.BENIGN.value)

    def test_detection_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_detection_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_detection_close_detection_does_not_exist(self):
        """
        Test Exception is raised detection does not exist for given detection id
        """
        url = reverse('api-v3.4:api-detection-close-v3.4', kwargs={'pk': **********})
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class DetectionOpenV3_4Test(BasicAPIV3_4Tests):
    """
    Test Detection/<id>/open Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            description='test detection serializer description',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            state=detection.ACTIVE,
        )

        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.url = reverse('api-v3.4:api-detection-open-v3.4', kwargs={'pk': self.detection_1.id})

    def test_detection_open_remediated_detection(self):
        """
        Test user can open a remediated detection
        """
        self.detection_1.state = detection.FIXED
        self.detection_1.state_reason = StateReasons.REMEDIATED.value
        self.detection_1.save()

        resp = self.client.patch(self.url, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)

    def test_detection_open_benign_detection(self):
        """
        Test correct values are set when opening a detection as benign
        """

        self.detection_1.state = detection.ACTIVE
        self.detection_1.state_reason = StateReasons.BENIGN.value
        sr_rule = smart_rule.objects.create(family=smart_rule.FAMILY_CUSTOMER)
        self.detection_1.smart_rule = sr_rule
        self.detection_1.save()

        resp = self.client.patch(self.url, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state_reason, None)
        self.assertEqual(self.detection_1.smart_rule, None)

    def test_detection_open_detection_does_not_exist(self):
        """
        Test Exception is raised detection does not exist for given detection id
        """
        url = reverse('api-v3.4:api-detection-open-v3.4', kwargs={'pk': **********})
        resp = self.client.patch(url, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class HostCloseV3_4Test(BasicAPIV3_4Tests):
    """
    Test hosts/<id>/close Endpoint
    """

    def setUp(self):
        super().setUp()
        self.test_host = host.objects.create(
            last_source='*********', name='cmp_host', t_score=42, c_score=83, host_luid='nh1', key_asset=0, state='active'
        )

        self.test_host_session = host_session.objects.create(
            ip_address='*******', start=self.now - timedelta(days=1), end=self.now, session_luid='rhs1', host=self.test_host
        )

        # Create detections for tests
        self.test_detection_1 = detection.objects.create(
            type='hidden_http_tunnel_cnc',
            type_vname='Hidden HTTP Tunnel',
            src_ip='**********',
            first_timestamp=timezone.now() - timedelta(days=2),
            last_timestamp=timezone.now() - timedelta(days=2),
            t_score=0,
            c_score=0,
            category='COMMAND & CONTROL',
            host_session=self.test_host_session,
            state='active',
            sensor_luid='snsr2',
            targets_key_asset=0,
            data_source_type=DataSourceType.NETWORK,
        )

        self.test_detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            host_session=self.test_host_session,
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.detection_obj_list = [self.test_detection_1, self.test_detection_2]
        self.detection_id_list = [self.test_detection_1.id, self.test_detection_2.id]
        self.url = reverse('api-v3.4:api-host-close-v3.4', kwargs={'pk': self.test_host.id})

    def tearDown(self):
        detection.objects.all().delete()
        host.objects.all().delete()
        host_session.objects.all().delete()

    @patch("host_scoring_v2.host_scoring.score_host")
    def test_host_close_as_remediated(self, _):
        """
        Test correct values are set when closing a host as remediated
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.test_host.refresh_from_db()
        for detection_obj in self.detection_obj_list:
            detection_obj.refresh_from_db()

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detection_obj_list:
            self.assertEqual(detection_obj.state, detection.FIXED)
            self.assertEqual(detection_obj.state_reason, StateReasons.REMEDIATED.value)

        for detection_id in self.detection_id_list:
            history = CloseHistory.objects.get(detection_id=detection_id)
            self.assertEqual(history.reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(host_id=self.test_host.id).exclude(detection_id__in=self.detection_id_list)
        self.assertEqual(history[0].reason, StateReasons.REMEDIATED.value)

    @patch("host_scoring_v2.host_scoring.score_host")
    def test_host_close_as_benign(self, _):
        """
        Test correct values are set when closing a host as benign
        """
        data = json.dumps({'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.test_host.refresh_from_db()
        for detection_obj in self.detection_obj_list:
            detection_obj.refresh_from_db()

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detection_obj_list:
            self.assertTrue(detection_obj.is_filtered_by_user)
            self.assertEqual(detection_obj.state_reason, StateReasons.BENIGN.value)

        for detection_id in self.detection_id_list:
            history = CloseHistory.objects.get(detection_id=detection_id)
            self.assertEqual(history.reason, StateReasons.BENIGN.value)

        history = CloseHistory.objects.filter(host_id=self.test_host.id).exclude(detection_id__in=self.detection_id_list)
        self.assertEqual(history[0].reason, StateReasons.BENIGN.value)

    def test_host_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_host_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_host_close_host_does_not_exist(self):
        """
        Test Exception is raised host does not exist for given host id
        """
        url = reverse('api-v3.4:api-host-close-v3.4', kwargs={'pk': **********})
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class DeprecatedEndpointsV3_4Tests(BasicAPIV3_4Tests):
    """
    Verify Endpoints deprecated in 3.4 return 404s
    """

    def setUp(self):
        super().setUp()

    def test_deprecated_urls_return_404(self):
        deprecated_urls = [
            '/api/v3.4/events/account_scoring/',
            '/api/v3.4/events/account_detection/',
        ]

        for url in deprecated_urls:
            self.client.force_authenticate(user=self.client_user, token=self.token)

            response = self.client.get(url)

            # Verify that the response status code is 404
            self.assertEqual(response.status_code, 404)


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class DetectionsOpenV3_4Test(BasicAPIV3_4Tests):
    """
    Test Detection/open Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state=detection.FIXED,
            state_reason=StateReasons.REMEDIATED.value,
            targets_key_asset=True,
            t_score=10,
        )

        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.url = reverse('api-v3.4:api-detections-open-v3.4')

    def tearDown(self):
        detection.objects.all().delete()
        smart_rule.objects.all().delete()

    def test_detections_open_fixed_detection(self):
        """
        Test detection open endpoint, can open a fixed detection
        """
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)

    def test_detections_open_an_already_open_detection(self):
        """
        Test detection open endpoint, can open a detection which is already open
        """
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        self.detection_1.state = detection.ACTIVE
        self.detection_1.state_reason = None
        self.detection_1.save()
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)

    def test_detections_open_filtered_by_user_detection(self):
        """
        Test detection open endpoint, can open a filtered by user detection
        """
        # Arrange
        # Create smart rules
        self.sr = smart_rule.objects.create(priority=None)

        self.detection_1.smart_rule = self.sr
        self.detection_1.state = detection.ACTIVE
        self.detection_1.state_reason = None
        self.detection_1.save()

        # assert detection is filtered by user
        self.assertTrue(self.detection_1.is_filtered_by_user)
        # assert detection is active
        self.assertEqual(self.detection_1.state, detection.ACTIVE)

        # Act
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # Assert
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)
        # Assert detection is not filtered by user
        self.assertFalse(self.detection_1.is_filtered_by_user)
        # Assert smart rule is removed from detection
        self.assertEqual(self.detection_1.smart_rule, None)
        # Assert smart rule is deleted
        self.assertEqual(smart_rule.objects.filter(id=self.sr.id).count(), 0)

    def test_detections_open_fixed_and_filtered_by_user_detection(self):
        """
        Test detection open endpoint, can open a simultaneously fixed and filtered by user detection
        """
        # Arrange
        # Create smart rules
        self.sr = smart_rule.objects.create(priority=None)

        self.detection_1.smart_rule = self.sr
        self.detection_1.save()

        # assert detection is filtered by user - beforehand
        self.assertTrue(self.detection_1.is_filtered_by_user)
        # assert detection is fixed - beforehand
        self.assertEqual(self.detection_1.state, detection.FIXED)

        # Act
        data = json.dumps({'detectionIdList': [self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # Assert
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)
        # Assert detection is not filtered by user
        self.assertFalse(self.detection_1.is_filtered_by_user)
        # Assert smart rule is removed from detection
        self.assertEqual(self.detection_1.smart_rule, None)
        # Assert smart rule is deleted
        self.assertEqual(smart_rule.objects.filter(id=self.sr.id).count(), 0)

    def test_detections_open_unparsable_json(self):
        """
        Test detection open endpoint, responds with 400 when JSON is not parsable
        """
        self.maxDiff = None
        data = "}{"
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(json.loads(resp.content)['_meta']['message'], "Payload must be valid JSON")
        self.assertEqual(resp.status_code, 400)

    def test_detections_open_missing_body_param(self):
        """
        Test detection open endpoint, responds with 400 when body param is missing
        """
        self.maxDiff = None
        data = {}
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList is required")
        self.assertEqual(resp.status_code, 400)

    def test_detections_open_param_must_be_a_list(self):
        """
        Test detection open endpoint, responds with 400 when body param is missing
        """
        self.maxDiff = None
        data = json.dumps({'detectionIdList': 'not_a_list'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must be a list")
        self.assertEqual(resp.status_code, 400)

    def test_detections_open_multiple_bad_detection_id_list(self):
        """
        Test detection open endpoint, responds with 400 when detectionIdList is not a list of integers
        """
        expected_invalid_ids = ['bad_detection_id', 'ZZZ', -999]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must contain only non-negative integers"
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_open_single_bad_detection_id_list(self):
        """
        Test detection open endpoint, responds with 400 when detectionIdList is not a list of positive integers
        """
        expected_invalid_ids = [-999]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must contain only non-negative integers"
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_open_single_bad_detection_id_list_B(self):
        """
        Test detection open endpoint, responds with 400 when detectionIdList is not a list of positive integers
        """
        expected_invalid_ids = [-999]
        detection_ids = expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], "detectionIdList must contain only non-negative integers"
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_open_single_detection_doesnt_exist(self):
        """
        Test detection open endpoint, responds with 404 when single detection does not exist
        """
        detection_ids = [self.detection_1.id, 88888888]
        data = json.dumps({'detectionIdList': detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'],
            "Some detection IDs were not found.",
        )
        self.assertEqual(json.loads(resp.content)['errors']['invalid_ids'], [88888888])

    def test_detections_open_doesnt_exist(self):
        """
        Test detection open endpoint, responds with 404 when detection does not exist
        """
        non_existent_detection_ids = [99999999, 88888888]
        data = json.dumps({'detectionIdList': non_existent_detection_ids})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['errors']['invalid_ids'], [88888888, 99999999])
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'],
            "Some detection IDs were not found.",
        )

    def test_detections_open_duplicate_ids(self):
        """
        Test detection open endpoint, can open a fixed detection with duplicate IDs in the list
        """
        data = json.dumps({'detectionIdList': [self.detection_1.id, self.detection_1.id]})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(self.detection_1.state, detection.ACTIVE)
        self.assertEqual(self.detection_1.state_reason, None)


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_public_preview': SimpleFlag(lambda: True), 'signal_efficacy_closed_as': SimpleFlag(lambda: True)},
)
class DetectionsCloseV3_4Test(BasicAPIV3_4Tests):
    """
    Test detections/close Endpoint
    """

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 2',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )

        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.url = reverse('api-v3.4:api-detections-close-v3.4')
        self.detections_id_list = [self.detection_1.id, self.detection_2.id]
        self.detections_obj_list = [self.detection_1, self.detection_2]

    def tearDown(self):
        detection.objects.all().delete()

    def test_detections_close_as_remediated(self):
        """
        Test correct values are set when closing detections as remediated
        """
        data = json.dumps({'detectionIdList': self.detections_id_list, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detections_obj_list:
            detection_obj.refresh_from_db()
            self.assertEqual(detection_obj.state, detection.FIXED)
            self.assertEqual(detection_obj.state_reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(id__in=self.detections_id_list)
        for historyObject in history:
            self.assertEqual(historyObject.reason, StateReasons.REMEDIATED.value)

    def test_detections_close_as_benign(self):
        """
        Test correct values are set when closing detections as benign
        """
        data = json.dumps({'detectionIdList': self.detections_id_list, 'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        for detection_obj in self.detections_obj_list:
            detection_obj.refresh_from_db()
            self.assertTrue(detection_obj.is_filtered_by_user)
            self.assertEqual(detection_obj.state_reason, StateReasons.BENIGN.value)

        history = CloseHistory.objects.filter(id__in=self.detections_id_list)
        for historyObject in history:
            self.assertEqual(historyObject.reason, StateReasons.BENIGN.value)

    def test_detections_close_invalid_json(self):
        """
        Test detection close endpoint responds with 400 when JSON is not parsable
        """
        data = "}{"
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 400)
        self.assertEqual(json.loads(resp.content)['_meta']['message'], 'Payload must be valid JSON')

    def test_detections_close_detectionIdList_is_missing(self):
        """
        Test missing detectionIdList returns a 400 with correct message
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['detectionIdList'], 'detectionIdList is required')

    def test_detections_close_detectionIdList_is_not_a_list(self):
        """
        Test detectionIdList is not a list returns a 400 with correct message
        """
        data = json.dumps({'detectionIdList': "1234,1235", 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['detectionIdList'], 'detectionIdList must be a list')

    def test_detections_close_detectionIdList_is_empty(self):
        """
        Test empty detectionIdList returns a 400 with correct message
        """
        data = json.dumps({'detectionIdList': [], 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['detectionIdList'], 'detectionIdList cannot be empty')

    def test_detections_close_multiple_invalid_detection_ids(self):
        """
        Test multiple invalid detection ids return a 400 with correct message
        """
        expected_invalid_ids = ['bad_detection_id', -999, 0.89]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], 'detectionIdList must contain only non-negative integers'
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_close_single_invalid_detection_id_A(self):
        """
        Test single invalid detection id return a 400 with correct message
        """
        expected_invalid_ids = [0.89]
        detection_ids = [self.detection_1.id] + expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], 'detectionIdList must contain only non-negative integers'
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_close_single_invalid_detection_id_B(self):
        """
        Test only one single invalid detection id returns a 400 with correct message
        """
        expected_invalid_ids = [0.89]
        detection_ids = expected_invalid_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(
            json.loads(resp.content)['_meta']['message']['detectionIdList'], 'detectionIdList must contain only non-negative integers'
        )
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_invalid_ids)
        self.assertEqual(len(invalid_ids), len(expected_invalid_ids))

    def test_detections_close_multiple_detection_ids_dont_exist(self):
        """
        Test multiple non-existent detection ids returns 404 with correct message
        """
        expected_nonexistent_ids = [888888, 999999]
        detection_ids = [self.detection_1.id] + expected_nonexistent_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], 'Some detection IDs were not found.')
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_nonexistent_ids)
        self.assertEqual(len(invalid_ids), len(expected_nonexistent_ids))

    def test_detections_close_single_detection_id_doesnt_exist_A(self):
        """
        Test single non-existent detection id returns 404 with correct message
        """
        expected_nonexistent_ids = [888888]
        detection_ids = [self.detection_1.id] + expected_nonexistent_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], 'Some detection IDs were not found.')
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_nonexistent_ids)
        self.assertEqual(len(invalid_ids), len(expected_nonexistent_ids))

    def test_detections_close_single_detection_id_doesnt_exist_B(self):
        """
        Test the one and only non-existent detection id returns 404 with correct message
        """
        expected_nonexistent_ids = [888888]
        detection_ids = expected_nonexistent_ids
        data = json.dumps({'detectionIdList': detection_ids, 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(json.loads(resp.content)['_meta']['message']['detectionIdList'], 'Some detection IDs were not found.')
        invalid_ids = json.loads(resp.content)['errors']['invalid_ids']
        for invalid_id in invalid_ids:
            self.assertIn(invalid_id, expected_nonexistent_ids)
        self.assertEqual(len(invalid_ids), len(expected_nonexistent_ids))

    def test_detections_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'detectionIdList': self.detections_id_list, 'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = json.loads(resp.content.decode('utf-8'))

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_detections_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({'detectionIdList': self.detections_id_list})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = json.loads(resp.content.decode('utf-8'))
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_detections_close_duplicate_ids(self):
        """
        Test detection close endpoint can close detections with duplicate IDs in the list
        """
        duplicate_list = [self.detection_1.id, self.detection_1.id]
        data = json.dumps({'detectionIdList': duplicate_list, 'reason': 'remediated'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        self.detection_1.refresh_from_db()
        self.assertEqual(self.detection_1.state, detection.FIXED)
        self.assertEqual(self.detection_1.state_reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(detection_id__in=duplicate_list)
        # only one record inserted in close history because duplicate ids filter to one id
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].reason, StateReasons.REMEDIATED.value)

    def test_detections_close_as_benign_already_remediated_detection(self):
        """
        Test that already closed detections (as remediated) can be closed as benign (or not_valuable) again
        """
        # close detection as remediated first
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # close the same detection as benign (or not_valuable)
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        self.detection_1.refresh_from_db()
        self.assertEqual(self.detection_1.state_reason, StateReasons.BENIGN.value)

        history = CloseHistory.objects.filter(detection_id=self.detection_1.id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0].reason, StateReasons.REMEDIATED.value)
        self.assertEqual(history[1].reason, StateReasons.BENIGN.value)

    def test_detections_close_as_remediated_already_benign_detection(self):
        """
        Test that already closed detections (as benign/not_valubale) can be closed as remediated again
        """
        # close detection as benign (or not_valuable) first
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.detection_1.refresh_from_db()

        # close the same detection as remediated again
        data = json.dumps({'detectionIdList': [self.detection_1.id], 'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        self.assertEqual(resp.status_code, 200)

        self.detection_1.refresh_from_db()
        self.assertEqual(self.detection_1.state_reason, StateReasons.REMEDIATED.value)

        history = CloseHistory.objects.filter(detection_id=self.detection_1.id)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0].reason, StateReasons.BENIGN.value)
        self.assertEqual(history[1].reason, StateReasons.REMEDIATED.value)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class DetectionsV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Detections Endpoint
    """

    def setUp(self):
        super().setUp()

        # setup cloud env
        self.cloud_env_patch = patch('base_tvui.feature_flipper.attributes.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars())
        self.cloud_env_patch.start()

        # Create accounts for detections
        self.account_1 = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)
        self.account_2 = Account.objects.create(uid='account@o365_2.com', account_type=Account.TYPE_O365)

        self.host = host.objects.create(name='testhost1', host_luid='hluid1', state='active', last_source='*******', t_score=77, c_score=42)

        self.host_session = host_session.objects.create(
            host=self.host, session_luid='6', ip_address='*******', start=self.now - timedelta(days=3), end=self.now
        )

        self.host_group = HostGroup.objects.create(
            name='Test Host Group',
            type=GroupCollection.HOST,
            description='Test Host Group Description',
            last_modified_by=self.user,
        )
        self.host_group.hosts.add(self.host)
        # Create detections for tests
        self.detection_2 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='LATERAL MOVEMENT',
            c_score=10,
            description='test detection serializer description',
            description2='description 2',
            src_ip='*******',
            last_timestamp=datetime(2020, 5, 4, 11, 0, 0, tzinfo=timezone.utc),
            targets_key_asset=True,
            host_session=self.host_session,
            sensor_luid='1234',
            state='inactive',
            t_score=11,
        )

        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1234',
            state='active',
            targets_key_asset=True,
            t_score=10,
        )
        self.dd = detection_detail.objects.create(
            type='sql_inject',
            src_ip='*******',
            dst_ip='*******',
            dst_port='80',
            total_bytes_sent=144,
            total_bytes_rcvd=10192,
            host_detection=self.detection_2,
            couch_note_id='18xsqli94',
            dst_dns='vectra.ai',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
        )

        self.src_ip_group = IPGroup.objects.create(
            name='Test IP Group',
            type=GroupCollection.IP,
            description='Test IP Group Description',
            last_modified_by=self.user,
            ips=[self.detection_2.src_ip],
        )
        self.dst_ip_group = IPGroup.objects.create(
            name='Test Destination IP Group',
            type=GroupCollection.IP,
            description='Test Destination IP Group Description',
            last_modified_by=self.user,
            ips=[self.dd.dst_ip],
        )
        self.dst_dns_group = ExternalDomainGroup.objects.create(
            name='Test Destination DNS Group',
            type=GroupCollection.DOMAIN,
            description='Test Destination DNS Group Description',
            last_modified_by=self.user,
            domains=[self.dd.dst_dns],
        )

        self.detection_3 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=9,
            description='test detection serializer description',
            description2='description 1',
            src_ip='*******',
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_1,
            sensor_luid='1235',
            state='active',
            targets_key_asset=True,
            t_score=13,
        )

    def tearDown(self):
        detection.objects.all().delete()

    def test_detections_default_success(self):
        """Test /v3.4/detections (now excluding info detections by default)"""
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        # Should only contain lateral movement detection
        self.assertEqual(resp_data.get('count'), 1)
        for det in resp_data['results']:
            self.assertNotEqual(det['detection_category'], 'info')

    def test_detections_with_include_category_info_false_success(self):
        """Test /v3.4/detections?include_info_category=false query param"""
        query_params = '?include_info_category=false'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        # Should only contain lateral movement detection
        self.assertEqual(resp_data.get('count'), 1)
        for det in resp_data['results']:
            self.assertNotEqual(det['detection_category'], 'info')

    def test_detections_with_include_category_info_true_success(self):
        """Test /v3.4/detections?include_info_category=true query param"""
        query_params = '?include_info_category=true'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        # Should contain all detections
        self.assertEqual(resp_data.get('count'), 3)

    def test_detections_with_include_category_info_and_info_success(self):
        """Test /v3.4/detections?include_info_category=true&detection_category=info query param"""
        query_params = '?include_info_category=true&detection_category=info'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        # Should contain all detections
        self.assertEqual(resp_data.get('count'), 2)
        self.assertEqual(resp_data['results'][0]['detection_category'], 'info')
        self.assertEqual(resp_data['results'][1]['detection_category'], 'info')

    def test_detections_without_include_category_info_and_with_info_success(self):
        """Test /v3.4/detections?detection_category=info query param"""
        query_params = '?detection_category=info'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        # Should contain all detections
        self.assertEqual(resp_data.get('count'), 2)
        self.assertEqual(resp_data['results'][0]['detection_category'], 'info')
        self.assertEqual(resp_data['results'][1]['detection_category'], 'info')

        # assert threat default ordering
        assert resp_data['results'][0]['threat'] > resp_data['results'][1]['threat']

    def test_valid_field_param_value(self):
        valid_param_value = ['threat', 'certainty', 'id']
        for value in valid_param_value:
            query_params = f'?fields={value}'
            url = reverse('api-v3.4:api-detections-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url + query_params)
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = DetectionsV3_4.as_view()(req)
            # Verify response
            self.assertEqual(resp.status_code, 200)

        query_params = f'?fields=id,certainty'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)

    def test_invalid_field_param_value(self):
        invalid_param_value = ['t_score', 'c_score', 'targets_key_asset', 'category']
        for value in invalid_param_value:
            query_params = f'?fields={value}'
            url = reverse('api-v3.4:api-detections-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url + query_params)
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = DetectionsV3_4.as_view()(req)
            # Verify response
            self.assertEqual(resp.status_code, 400)

    def test_invalid_params(self):
        invalid_params = {'t_score': 0, 'c_score': 0, 'targets_key_asset': True, 't_score_gte': 0, 'c_score_gte': 0, 'category': 'INFO'}
        for param, value in invalid_params.items():
            query_params = f'?{param}={value}'
            url = reverse('api-v3.4:api-detections-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url + query_params)
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = DetectionsV3_4.as_view()(req)
            # Verify response
            self.assertEqual(resp.status_code, 400)
            resp_data = json.loads(resp.render().content)
            self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))

    def test_detections_deprecated_fields(self):
        """Test get detections, check fields"""
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        first_result = resp_data['results'][0]

        self.assertIsNone(first_result.get('t_score', None))
        self.assertIsNone(first_result.get('c_score', None))
        self.assertIsNone(first_result.get('targets_key_asset', None))
        self.assertIsNone(first_result.get('category', None))

    def test_detections_with_ordering_and_fields_param(self):
        """Test /v3.4/detections?ordering&fields query param"""
        query_params = '?ordering=-threat&fields=threat&detection_category=info'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        assert resp_data['results'][0]['threat'] > resp_data['results'][1]['threat']

        query_params = '?ordering=-certainty&fields=certainty&detection_category=info'
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url + query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        # Verify response
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        assert resp_data['results'][0]['certainty'] > resp_data['results'][1]['certainty']

    def test_detections_created_timestamp_lte(self):
        """Test detections endpoint list query param created_timestamp_lte"""
        created_timestamp_lte = datetime(2023, 5, 4, 12, 0, 0, tzinfo=timezone.utc)
        query_params = {'created_timestamp_lte': created_timestamp_lte}
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(len(resp_data['results']), 0)

    def test_detections_created_timestamp_gte(self):
        """Test detections endpoint list query param created_timestamp_gte"""
        created_timestamp_gte = datetime(2022, 5, 4, 12, 0, 0, tzinfo=timezone.utc)
        query_params = {'created_timestamp_gte': created_timestamp_gte}
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = DetectionsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(len(resp_data['results']), 1)

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
    )
    def test_detections_reason_filtering(self):
        """Test detections endpoint with reason query param"""
        self.detection_1.state = detection.FIXED
        self.detection_1.state_reason = StateReasons.REMEDIATED.value
        self.detection_1.save()

        sr_rule = smart_rule.objects.create(family=smart_rule.FAMILY_CUSTOMER)
        self.detection_2.state_reason = StateReasons.BENIGN.value
        self.detection_2.smart_rule = sr_rule
        self.detection_2.save()

        query_params = {'reason': StateReasons.BENIGN.value}
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)

        resp = DetectionsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(len(resp_data['results']), 1)
        self.assertEqual(resp_data['results'][0]['reason'], StateReasons.BENIGN.value)

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
    )
    def test_detections_with_invalid_reason_filtering(self):
        """Test detections endpoint with reason query param"""
        query_params = {'reason': "invalid_reason"}
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)

        resp = DetectionsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 400)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['reason'], "Invalid value given for paramater 'reason'. Valid values: ['benign', 'remediated'].")

    def test_include_src_dst_group_query_params(self):
        """Test detections endpoint with src_ip_group, dst_ip_group, and dst_dns_group query params"""
        query_params = {'include_src_dst_groups': "True"}
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        # Act
        resp = DetectionsV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)
        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertEqual(len(resp_data['results']), 1)
        self.assertEqual(len(resp_data['results'][0]['src_groups']), 2)
        self.assertEqual(len(resp_data['results'][0]['dst_groups']), 2)

    def test_include_src_dst_group_query_params_off(self):
        """Test detections endpoint without include_src_dst_groups query param"""
        query_params = {'include_src_dst_groups': "False"}
        url = reverse('api-v3.4:api-detections-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)

        resp = DetectionsV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)

        # Assert
        self.assertEqual(resp.status_code, 200)

        self.assertEqual(len(resp_data['results']), 1)
        self.assertNotIn('src_groups', dict(resp_data['results'][0]).keys())
        self.assertNotIn('dst_groups', dict(resp_data['results'][0]).keys())


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class TaggingV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Tagging Endpoint in API v3.4
    """

    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        # create hosts
        self.host1 = host.objects.create(name='taggy_mchostface', state='active', t_score=99, c_score=50, key_asset=True)
        self.host2 = host.objects.create(name='bulky_mctagger', state='active', t_score=99, c_score=50, key_asset=True)
        # expected data
        self.invalid_entity_type_response = "The provided entity type is invalid, please use one of the following"

    def test_get_entity_tagging_host_entity_type_success_v3_4(self):
        """Test GET tagging endpoint for host entity when entity_type provided"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': self.host1.id})
        query_params = '?type=host'
        resp = self.client.get(url + query_params)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        expected_response = {'status': 'success', 'tags': [], 'entity_id': str(self.host1.id)}
        self.assertEqual(expected_response, resp_data)

    def test_get_entity_tagging_missing_param_fail_v3_4(self):
        """Test GET host tagging endpoint for entity when type not provided"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': self.host1.id})
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 400)

    def test_get_entity_tagging_not_found_fail_v3_4(self):
        """Test GET host tagging endpoint not found fail"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': 99999})
        query_params = '?type=host'
        resp = self.client.get(f'{url}{query_params}')
        self.assertEqual(resp.status_code, 404)

    def test_patch_entity_tagging_host_entity_type_success_v3_4(self):
        """Test PATCH tagging endpoint for host entity when entity_type provided"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': self.host1.id})
        query_params = '?type=host'
        tags = ['test-host-tag', 'tag2', 'tag3']
        json_data = json.dumps({'tags': tags})
        resp = self.client.patch(f'{url}{query_params}', data=json_data, content_type='application/json')
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        expected_response = {'status': 'success', 'entity_id': self.host1.id, 'tags': tags}
        self.assertEqual(expected_response, resp_data)

    def test_patch_entity_tagging_invalid_query_param_v3_4(self):
        """Test PATCH tagging endpoint for entity when invalid query param provided"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': self.host1.id})
        query_params = '?entity_type=test'
        tags = ['test-account-tag', 'tag2', 'tag3']
        json_data = json.dumps({'tags': tags})
        resp = self.client.patch(f'{url}{query_params}', data=json_data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)

    def test_patch_entity_tagging_invalid_empty_tags_v3_4(self):
        """Test PATCH tagging endpoint for entity when no tags provided"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': self.host1.id})
        query_params = '?type=host'
        resp = self.client.patch(f'{url}{query_params}', data={}, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        resp_err = json.loads(resp.content.decode('utf-8'))['message']
        self.assertEqual(resp_err, 'no tags contained in request')

    def test_patch_entity_tagging_not_found_fail_v3_4(self):
        """Test PATCH tagging endpoint not found fail"""
        url = reverse('api-v3.4:api-tagging-v3.4', kwargs={'object_type': 'entity', 'entity_id': 99999})
        query_params = '?type=host'
        tags = ['test-account-tag', 'tag2', 'tag3']
        json_data = json.dumps({'tags': tags})
        resp = self.client.patch(url + query_params, data=json_data, content_type='application/json')
        self.assertEqual(resp.status_code, 404)

    def test_bulk_tags_v3_4_invalid_query_param(self):
        """Test POST bulk tagging endpoint with invalid query param"""
        url1 = reverse('api-v3.4:api-bulk-tagging-v3.4', kwargs={'table': 'entity'})
        query_params = '?entity_type=host'
        json_data = json.dumps({'tag': 'bulk tag test', "objectIds": [self.host1.id, self.host2.id]})
        resp = self.client.post(f"{url1}{query_params}", json_data, content_type='application/json')
        self.assertEqual(resp.status_code, 400)
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertIn(INVALID_PARAM_DETAIL, resp_data.get('detail', ''))


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class NetworkBrainHealthPingV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Health Network Brain ping endpoint
    """

    class MockRespGet:
        def __init__(self, json_resp, raise_err=False):
            self.json_resp = json_resp
            self.raise_err = raise_err

        def json(self):
            return self.json_resp

        def raise_for_status(self):
            if self.raise_err:
                raise Exception("Error getting data")
            return

    def setUp(self):
        super(NetworkBrainHealthPingV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.make_get_request')
    def test_health_brain_v3_4_success(self, mock_request_get):
        """Test health brain ping v3.4 endpoint success"""
        fixed_time = datetime(2024, 5, 17, 12, 0, 0)  # Specify any desired time
        with patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.datetime') as mock_datetime:
            mock_datetime.now.return_value = fixed_time
            mock_health_data = {'system': {'uptime': '2 days, 13 hours, 51 minutes'}}
            mock_request_get.return_value = self.MockRespGet(mock_health_data)
            url = reverse('api-v3.4:api-health-network-brain-ping-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url)
            force_authenticate(req, token=self.token, user=self.user)
            response = NetworkBrainHealthPingV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            resp_data = json.loads(response.render().content)
            expected_response = {'results': {'ping': 'Brain enabled', "latency": "{:.2f}ms".format(0.0)}}
            self.assertEqual(expected_response, resp_data)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.make_get_request')
    def test_health_brain_v3_4_no_data(self, mock_request_get):
        """Test health brain ping v3.4 endpoint no data returned"""
        fixed_time = datetime(2024, 5, 17, 12, 0, 0)  # Specify any desired time
        with patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.datetime') as mock_datetime:
            mock_datetime.now.return_value = fixed_time
            mock_health_data = {'system': {'uptime': None}}
            mock_request_get.return_value = self.MockRespGet(mock_health_data)
            url = reverse('api-v3.4:api-health-network-brain-ping-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url)
            force_authenticate(req, token=self.token, user=self.user)
            response = NetworkBrainHealthPingV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            resp_data = json.loads(response.render().content)
            expected_response = {'results': {'ping': 'Brain connection failed', "latency": "{:.2f}ms".format(0.0)}}
            self.assertEqual(expected_response, resp_data)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.make_get_request')
    def test_health_brain_v3_4_exception(self, mock_request_get):
        """Test health brain ping v3.4 endpoint exception returned"""
        error_message = "Encountered an error running health check"
        mock_response = Response()
        mock_response.status_code = 500
        mock_request_get.return_value = mock_response

        url = reverse('api-v3.4:api-health-network-brain-ping-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        force_authenticate(req, token=self.token, user=self.user)

        # Call the view function
        response = NetworkBrainHealthPingV3_4.as_view()(req)

        # Verify that the exception is raised
        self.assertEqual(response.status_code, 500)
        self.assertEqual(response.data['error'], error_message)

    def test_health_brain_v3_4_non_cbi(self):
        """Test health brain ping v3.4 endpoint non-cloudbridge"""
        url = reverse('api-v3.4:api-health-network-brain-ping-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        force_authenticate(req, token=self.token, user=self.user)
        response = NetworkBrainHealthPingV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)

        self.assertEqual(data['message'], CloudbridgeRouter.cloudbridge_disabled_message)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@patch('base_tvui.feature_flipper.conditions.is_cloudbridge', return_value=True)
class ExternalConnectorsHealthUtilsTests(BasicAPIV3_4Tests):

    # Status Mock Returns
    test_ad_status = {
        "id": "activeDirectoryStatusMultiAllGlobal",
        "category": "activeDirectory",
        "values": {
            "profiles": {
                "qe-ad-test": {
                    "uriConnectionStatus": {"**************": {"message": "Connected", "severity": "misc-yep"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Connected", "severity": "misc-yep"},
                },
                "redwoods-test": {
                    "uriConnectionStatus": {"**************": {"message": "Connected", "severity": "misc-yep"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Connected", "severity": "misc-yep"},
                },
            },
            "metrics": {"numKerberosLast30Days": 0, "numADMetricOverlap": 0},
            "numAdServersConnected": 1,
            "numAdServersDisconnected": 1,
            "numAdServersDisabled": 0,
            "numUrisDisconnected": 0,
        },
    }

    test_ad_status_error = {
        "id": "activeDirectoryStatusMultiAllGlobal",
        "category": "activeDirectory",
        "values": {
            "profiles": {
                "qe-ad-test": {
                    "uriConnectionStatus": {"**************": {"message": "Connected", "severity": "misc-yep"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Connected", "severity": "misc-yep"},
                },
                "redwoods-test": {
                    "uriConnectionStatus": {"**************": {"message": "Not connected", "severity": "misc-alert"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Not connected", "severity": "misc-alert"},
                },
            },
            "metrics": {"numKerberosLast30Days": 0, "numADMetricOverlap": 0},
            "numAdServersConnected": 1,
            "numAdServersDisconnected": 1,
            "numAdServersDisabled": 0,
            "numUrisDisconnected": 0,
        },
    }

    # Config Mock Returns
    multi_global_config = {
        "id": "activeDirectoryConfigMultiGlobal",
        "category": "activeDirectory",
        "values": {
            "enabled": True,
            "lockdownEnabled": True,
            "lockdownAutoEnabled": True,
            "lockdownAutoTimeoutMinutes": 60,
            "lockdownAutoThreat": 75,
            "lockdownAutoCertainty": 75,
            "lockdownAutoPrivilege": 5,
            "mappingEnabled": True,
            "manual_associations": [],
            "association_type": "automatic",
            'infoAttributeEnabled': True,
            'accountExpiresAttributeEnabled': True,
        },
    }

    disabled_global_config = {
        "id": "activeDirectoryConfigMultiGlobal",
        "category": "activeDirectory",
        "values": {
            "enabled": False,
            "lockdownEnabled": False,
            "lockdownAutoEnabled": False,
            "lockdownAutoTimeoutMinutes": 0,
            "lockdownAutoThreat": 0,
            "lockdownAutoCertainty": 0,
            "lockdownAutoPrivilege": 0,
            "mappingEnabled": False,
            "manual_associations": [],
            "association_type": "automatic",
            'infoAttributeEnabled': False,
            'accountExpiresAttributeEnabled': False,
        },
    }

    # Expected Responses
    test_external_connectors_disabled_expected = {
        "active_directory": None,
    }

    test_ad_status_expected = {
        "details": {
            "profiles": {
                "qe-ad-test": {
                    "uriConnectionStatus": {"**************": {"message": "Connected", "severity": "misc-yep"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Connected", "severity": "misc-yep"},
                },
                "redwoods-test": {
                    "uriConnectionStatus": {"**************": {"message": "Connected", "severity": "misc-yep"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Connected", "severity": "misc-yep"},
                },
            },
            "metrics": {"numKerberosLast30Days": 0, "numADMetricOverlap": 0},
            "numAdServersConnected": 1,
            "numAdServersDisconnected": 1,
            "numAdServersDisabled": 0,
            "numUrisDisconnected": 0,
        },
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_ACTIVE_DIRECTORY,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {"qe-ad-test": health_utils.AUTHENTICATED, "redwoods-test": health_utils.AUTHENTICATED},
    }

    test_ad_status_error_expected = {
        "details": {
            "profiles": {
                "qe-ad-test": {
                    "uriConnectionStatus": {"**************": {"message": "Connected", "severity": "misc-yep"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Connected", "severity": "misc-yep"},
                },
                "redwoods-test": {
                    "uriConnectionStatus": {"**************": {"message": "Not connected", "severity": "misc-alert"}},
                    "metrics": {},
                    "connectionStatus": {"message": "Not connected", "severity": "misc-alert"},
                },
            },
            "metrics": {"numKerberosLast30Days": 0, "numADMetricOverlap": 0},
            "numAdServersConnected": 1,
            "numAdServersDisconnected": 1,
            "numAdServersDisabled": 0,
            "numUrisDisconnected": 0,
        },
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_ACTIVE_DIRECTORY,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {"redwoods-test": "Not connected"},
        health_utils.AUTH_STATUS: {"qe-ad-test": health_utils.AUTHENTICATED, "redwoods-test": health_utils.NOT_CONFIGURED},
    }

    test_ad_status_disabled_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_ACTIVE_DIRECTORY,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
    }

    test_external_connectors_disabled_expected = {health_utils.TYPE_ACTIVE_DIRECTORY: test_ad_status_disabled_expected}

    test_azure_ad_lockdown_enabled_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE_AD_LOCKDOWN,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {
            'lockdownEnabled': True,
            'lockdownManualEnabled': False,
            'lockdownAutoEnabled': False,
            'lockdownAutoType': 'lock',
            'lockdownAutoUrgency': 90,
            'consentState': 'CONSENT_NOT_ATTEMPTED',
        },
    }

    test_azure_ad_lockdown_disabled_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE_AD_LOCKDOWN,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_azure_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {'status': 'Connected', 'severity': Misc.YEP},
    }

    test_azure_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_azure_unauth = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {'status': 'Permission denied'},
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_AUTHENTICATED},
        "details": {'status': 'Permission denied', 'severity': Misc.PERMISSION},
    }

    test_azure_connection_alert = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {'status': 'Status unknown'},
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        "details": {'status': 'Status unknown', 'severity': Misc.ALERT},
    }

    test_aws_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AWS,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {'status': 'Connected', 'severity': Misc.YEP, 'coverageReportAvailable': False},
    }

    test_aws_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AWS,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_aws_unauth = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AWS,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_AUTHENTICATED},
        "details": {'status': 'Permission denied', 'severity': Misc.PERMISSION, 'coverageReportAvailable': False},
        health_utils.ERROR_STATES: {'status': 'Permission denied'},
    }

    test_aws_connection_alert = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_AWS,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        "details": {'status': 'Status unknown', 'severity': Misc.ALERT, 'coverageReportAvailable': False},
        health_utils.ERROR_STATES: {'status': 'Status unknown'},
    }

    test_gcp_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_GOOGLE_CLOUD,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {'status': 'Connected', 'severity': Misc.YEP},
    }

    test_gcp_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_GOOGLE_CLOUD,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_gcp_unauth = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_GOOGLE_CLOUD,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {'status': 'Permission denied'},
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_AUTHENTICATED},
        "details": {'status': 'Permission denied', 'severity': Misc.PERMISSION},
    }

    test_gcp_connection_alert = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_GOOGLE_CLOUD,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {'status': 'Status unknown'},
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        "details": {'status': 'Status unknown', 'severity': Misc.ALERT},
    }

    test_rdns_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_RDNS,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {'enabled': True, 'dns_list': ['*******']},
    }

    test_rdns_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_RDNS,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_siem_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_SIEM,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {
            "siems": {
                "*******": {"siem_name": "test", "endpoint": "*******", "protocol": "tcp", "port": 9500, "id": None, "timestamp": None},
                "2.2.2.2": {"siem_name": "test2", "endpoint": "2.2.2.2", "protocol": "udp", "port": 9501, "id": None, "timestamp": None},
                "3.3.3.3": {"endpoint": "3.3.3.3", "port": 9502, "protocol": "udp", "siem_name": "test3", "id": None, "timestamp": None},
            },
            "enabled": True,
            "siems_tiemstamp": {},
            'used_ports_tcp': [],
            'used_ports_udp': [],
        },
    }

    test_siem_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_SIEM,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_vcenter_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_VCENTER,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {'vcenters': {'vcenter.io': {'connected': True}, 'test.com': {'connected': False, 'message': 'Timeout error'}}},
        health_utils.ERROR_STATES: {'test.com': 'Timeout error'},
    }

    test_vcenter_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_VCENTER,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_vcenter_error = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_VCENTER,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {'status': PlatformClient.VCENTER_FAIL_MESSAGE},
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_ad_log_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        "details": {
            "status": 'Connected',
            "severity": Misc.YEP,
            "show_stats": True,
            "count": 100,
            "count_4768": 100,
            "count_4769": 100,
        },
    }

    test_ad_log_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_ad_log_error = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        "details": {'status': 'Status unknown', 'severity': Misc.ALERT},
        health_utils.ERROR_STATES: {'status': 'Status unknown'},
    }

    test_zpa_expected = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_ZPA,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        'details': {"status": 'Connected', "logsIngestedInLastHour": 151 * 60},
    }

    test_zpa_disabled = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_ZPA,
        health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
    }

    test_zpa_error = {
        health_utils.CONNECTOR_TYPE: health_utils.TYPE_ZPA,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        'details': {"status": 'Error', "logsIngestedInLastHour": 0},
        health_utils.ERROR_STATES: {"status": 'Error'},
    }

    test_external_connectors_expected = {
        health_utils.TYPE_ACTIVE_DIRECTORY: test_ad_status_expected,
        health_utils.TYPE_AZURE_AD_LOCKDOWN: test_azure_ad_lockdown_enabled_expected,
        health_utils.TYPE_AWS: test_aws_expected,
        health_utils.TYPE_AZURE: test_azure_expected,
        health_utils.TYPE_GOOGLE_CLOUD: test_gcp_expected,
        health_utils.TYPE_RDNS: test_rdns_expected,
        health_utils.TYPE_SIEM: test_siem_expected,
        health_utils.TYPE_VCENTER: test_vcenter_expected,
        health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION: test_ad_log_expected,
        health_utils.TYPE_ZPA: test_zpa_expected,
    }

    def setUp(self):
        super(ExternalConnectorsHealthUtilsTests, self).setUp()
        self.patch_flag_enabled = patch('base_tvui.feature_flipper.flag_enabled')
        self.mock_flag_enabled = self.patch_flag_enabled.start()
        self.addCleanup(self.patch_flag_enabled.stop)
        self.mock_flag_enabled.return_value = True

        # Create mock setting objects
        setting.objects.create(group='azure_ad_lockdown', key='lockdown_enabled', value='on')
        setting.objects.create(group='azure_ad_lockdown', key='lockdown_auto_enabled', value='off')
        setting.objects.create(group='azure_ad_lockdown', key='lockdown_manual_enabled', value='off')
        setting.objects.create(group='azure_ad_lockdown', key='lockdown_auto_type', value='lock')
        setting.objects.create(group='azure_ad_lockdown', key='lockdown_auto_urgency', value=90)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_ad_lockdown_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_azure_ad_lockdown_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_aws_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_azure_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_google_cloud_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_reverse_lookup_dns_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_siem_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_vcenter_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_window_events_log_ingestion_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_zscaler_private_access_fields')
    def test_get_external_connector_health_fields(
        self,
        get_zpa_mock,
        get_windows_log_mock,
        get_vcenter_mock,
        get_siem_mock,
        get_rdns_mock,
        get_google_cloud_mock,
        get_azure_mock,
        get_aws_mock,
        get_azure_ad_lockdown_mock,
        get_ad_lockdown_fields_mock,
        mock_cbi,
    ):
        """Test get all external connector fields"""
        get_ad_lockdown_fields_mock.return_value = self.test_ad_status_expected
        get_azure_ad_lockdown_mock.return_value = self.test_azure_ad_lockdown_enabled_expected
        get_aws_mock.return_value = self.test_aws_expected
        get_azure_mock.return_value = self.test_azure_expected
        get_google_cloud_mock.return_value = self.test_gcp_expected
        get_rdns_mock.return_value = self.test_rdns_expected
        get_siem_mock.return_value = self.test_siem_expected
        get_vcenter_mock.return_value = self.test_vcenter_expected
        get_windows_log_mock.return_value = self.test_ad_log_expected
        get_zpa_mock.return_value = self.test_zpa_expected
        assert health_utils.get_external_connector_fields() == self.test_external_connectors_expected

    def test_get_external_connector_health_fields_exception(self, mock_cbi):
        """Test get all external connector fields with exception"""
        with mock.patch.object(health_utils, "get_ad_lockdown_fields") as get_ad_lockdown_fields_mock:
            get_ad_lockdown_fields_mock.return_value = self.test_ad_status_error_expected
            get_ad_lockdown_fields_mock.side_effect = Exception("Test exception from test get_ad_lockdown_fields")
            assert health_utils.get_external_connector_fields().get(health_utils.TYPE_ACTIVE_DIRECTORY) == {}

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoConfigView')
    def test_get_ad_lockdown_fields_disabled(self, config_mock, mock_cbi):
        """Test get all external connector fields when disabled"""
        config_mock.retrieve_active_directory_config_multi_global = Mock(return_value=self.disabled_global_config)
        assert health_utils.get_external_connector_fields() == self.test_external_connectors_expected_disabled

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoStatusView')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoConfigView')
    def test_get_ad_lockdown_fields(self, config_mock, status_mock, mock_cbi):
        """Test get ad lockdown fields"""
        config_mock.retrieve_active_directory_config_multi_global = Mock(return_value=self.multi_global_config)
        status_mock.retrieve_active_directory_status_multi_all_global = Mock(return_value=self.test_ad_status)
        assert health_utils.get_ad_lockdown_fields() == self.test_ad_status_expected

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoStatusView')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoConfigView')
    def test_get_ad_lockdown_fields_error(self, config_mock, status_mock, mock_cbi):
        """Test get ad lockdown fields with errors"""
        config_mock.retrieve_active_directory_config_multi_global = Mock(return_value=self.multi_global_config)
        status_mock.retrieve_active_directory_status_multi_all_global = Mock(return_value=self.test_ad_status_error)
        assert health_utils.get_ad_lockdown_fields() == self.test_ad_status_error_expected

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoConfigView')
    def test_get_ad_lockdown_fields_disabled(self, config_mock, mock_cbi):
        """Test get ad lockdown fields when disabled"""
        config_mock.retrieve_active_directory_config_multi_global = Mock(return_value=self.disabled_global_config)
        assert health_utils.get_ad_lockdown_fields() == self.test_ad_status_disabled_expected

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.CognitoConfigView')
    def test_get_ad_lockdown_fields_exception(self, config_mock, mock_cbi):
        """Test get ad lockdown fields when exception occurs"""
        config_mock.retrieve_active_directory_config_multi_global = Mock(
            side_effect=Exception("Test exception from test CognitoConfigView")
        )
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_ACTIVE_DIRECTORY,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        assert health_utils.get_ad_lockdown_fields() == expected

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    @patch('tvui.views.conditions.is_cloud')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_ad_lockdown_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_azure_ad_lockdown_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_aws_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_azure_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_google_cloud_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_reverse_lookup_dns_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_siem_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_vcenter_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_window_events_log_ingestion_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_zscaler_private_access_fields')
    def test_refresh_external_connector_health_task(
        self,
        get_zpa_mock,
        get_windows_log_mock,
        get_vcenter_mock,
        get_siem_mock,
        get_rdns_mock,
        get_google_cloud_mock,
        get_azure_mock,
        get_aws_mock,
        get_azure_ad_lockdown_mock,
        get_ad_lockdown_fields_mock,
        mock_is_cloud,
        mock_cbi,
    ):
        """Test the celery task for external connector health fields"""
        mock_is_cloud.return_value = True
        get_ad_lockdown_fields_mock.return_value = self.test_ad_status_expected
        get_azure_ad_lockdown_mock.return_value = self.test_azure_ad_lockdown_enabled_expected
        get_aws_mock.return_value = self.test_aws_expected
        get_azure_mock.return_value = self.test_azure_expected
        get_google_cloud_mock.return_value = self.test_gcp_expected
        get_rdns_mock.return_value = self.test_rdns_expected
        get_siem_mock.return_value = self.test_siem_expected
        get_vcenter_mock.return_value = self.test_vcenter_expected
        get_windows_log_mock.return_value = self.test_ad_log_expected
        get_zpa_mock.return_value = self.test_zpa_expected
        res = refresh_external_connector_fields.apply().get()
        assert res == self.test_external_connectors_expected

    def test_get_azure_ad_lockdown_fields(self, mock_cbi):
        # Given: Settings exist in the database
        setting.objects.filter(group='azure_ad_lockdown').exists()
        result = get_azure_ad_lockdown_fields()
        self.assertEqual(
            result,
            self.test_azure_ad_lockdown_enabled_expected,
        )

    def test_get_azure_ad_lockdown_field_no_lockdown_settings(self, mock_cbi):
        # Given: Settings dont exist in the database
        setting.objects.filter(group='azure_ad_lockdown').delete()
        result = get_azure_ad_lockdown_fields()
        self.assertEqual(result, self.test_azure_ad_lockdown_disabled_expected)

    @patch('tvui.azure_ad_lockdown.models.settings.LockdownSettings.get_lockdown_settings')
    def test_get_azure_ad_lockdown_field_exception(self, get_settings_mock, mock_cbi):
        # Given: Settings dont exist in the database
        get_settings_mock.side_effect = Exception("Generic exception")
        result = get_azure_ad_lockdown_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE_AD_LOCKDOWN,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('base_tvui.platform_api.PlatformClient.test_aws_connection')
    @patch('base_tvui.platform_api.PlatformClient.aws_coverage_csv_report_status')
    def test_get_aws_fields(self, mock_aws_coverage_report, mock_aws_connection, mock_cbi):
        mock_aws_connection.return_value = {'status_code': 200}
        mock_aws_coverage_report.return_value = {'ready': False}
        result = get_aws_fields()
        self.assertEqual(result, self.test_aws_expected)

    @patch('base_tvui.platform_api.PlatformClient.test_aws_connection')
    @patch('base_tvui.platform_api.PlatformClient.aws_coverage_csv_report_status')
    def test_get_aws_fields_disabled(self, mock_aws_coverage_report, mock_aws_connection, mock_cbi):
        mock_aws_connection.return_value = {'status_code': 204}
        mock_aws_coverage_report.return_value = {'ready': False}
        result = get_aws_fields()
        self.assertEqual(result, self.test_aws_disabled)

    @patch('base_tvui.platform_api.PlatformClient.test_aws_connection')
    @patch('base_tvui.platform_api.PlatformClient.aws_coverage_csv_report_status')
    def test_get_aws_fields_unauth(self, mock_aws_coverage_report, mock_aws_connection, mock_cbi):
        mock_aws_connection.return_value = {'status_code': 403}
        mock_aws_coverage_report.return_value = {'ready': False}
        result = get_aws_fields()
        self.assertEqual(result, self.test_aws_unauth)

    @patch('base_tvui.platform_api.PlatformClient.test_aws_connection')
    @patch('base_tvui.platform_api.PlatformClient.aws_coverage_csv_report_status')
    def test_get_aws_fields_connection_alert(self, mock_aws_coverage_report, mock_aws_connection, mock_cbi):
        mock_aws_connection.return_value = {'status_code': 500}
        mock_aws_coverage_report.return_value = {'ready': False}
        result = get_aws_fields()
        self.assertEqual(result, self.test_aws_connection_alert)

    @patch('tvui.settings.cognito_views.CognitoStatusView.retrieve_aws_status')
    def test_get_aws_fields_exception(self, mock_aws_status, mock_cbi):
        mock_aws_status.side_effect = Exception("Generic exception")
        result = get_aws_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_AWS,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('base_tvui.platform_api.PlatformClient.test_azure_connection')
    def test_get_azure_fields(self, mock_azure_connection, mock_cbi):
        mock_azure_connection.return_value = {'status_code': 200}
        result = get_azure_fields()
        self.assertEqual(result, self.test_azure_expected)

    @patch('base_tvui.platform_api.PlatformClient.test_azure_connection')
    def test_get_azure_fields_disabled(self, mock_azure_connection, mock_cbi):
        mock_azure_connection.return_value = {'status_code': 204}
        result = get_azure_fields()
        self.assertEqual(result, self.test_azure_disabled)

    @patch('base_tvui.platform_api.PlatformClient.test_azure_connection')
    def test_get_azure_fields_unauth(self, mock_azure_connection, mock_cbi):
        mock_azure_connection.return_value = {'status_code': 403}
        result = get_azure_fields()
        self.assertEqual(result, self.test_azure_unauth)

    @patch('base_tvui.platform_api.PlatformClient.test_azure_connection')
    def test_get_azure_fields_connection_alert(self, mock_azure_connection, mock_cbi):
        mock_azure_connection.return_value = {'status_code': 500}
        result = get_azure_fields()
        self.assertEqual(result, self.test_azure_connection_alert)

    @patch('tvui.settings.cognito_views.CognitoStatusView.retrieve_azure_status')
    def test_get_azure_fields_exception(self, mock_azure_status, mock_cbi):
        mock_azure_status.side_effect = Exception("Generic exception")
        result = get_azure_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_AZURE,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('base_tvui.platform_api.PlatformClient.test_gcp_connection')
    def test_get_gcp_fields(self, mock_gcp_connection, mock_cbi):
        mock_gcp_connection.return_value = {'status_code': 200}
        result = get_google_cloud_fields()
        self.assertEqual(result, self.test_gcp_expected)

    @patch('base_tvui.platform_api.PlatformClient.test_gcp_connection')
    def test_get_gcp_fields_disabled(self, mock_gcp_connection, mock_cbi):
        mock_gcp_connection.return_value = {'status_code': 204}
        result = get_google_cloud_fields()
        self.assertEqual(result, self.test_gcp_disabled)

    @patch('base_tvui.platform_api.PlatformClient.test_gcp_connection')
    def test_get_gcp_fields_unauth(self, mock_gcp_connection, mock_cbi):
        mock_gcp_connection.return_value = {'status_code': 403}
        result = get_google_cloud_fields()
        self.assertEqual(result, self.test_gcp_unauth)

    @patch('base_tvui.platform_api.PlatformClient.test_gcp_connection')
    def test_get_gcp_fields_connection_alert(self, mock_gcp_connection, mock_cbi):
        mock_gcp_connection.return_value = {'status_code': 500}
        result = get_google_cloud_fields()
        self.assertEqual(result, self.test_gcp_connection_alert)

    @patch('tvui.settings.cognito_views.CognitoStatusView.retrieve_gcp_status')
    def test_get_gcp_fields_exception(self, mock_gcp_status, mock_cbi):
        mock_gcp_status.side_effect = Exception("Generic exception")
        result = get_google_cloud_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_GOOGLE_CLOUD,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_rdns_config')
    def test_get_rdns_fields(self, mock_rdns_config, mock_cbi):
        mock_rdns_config.return_value = {'id': 'rdns', 'category': 'rdns', 'values': {'enabled': True, 'dns_list': ['*******']}}
        result = get_reverse_lookup_dns_fields()
        self.assertEqual(result, self.test_rdns_expected)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_rdns_config')
    def test_get_rdns_fields_disabled(self, mock_rdns_config, mock_cbi):
        mock_rdns_config.return_value = {'id': 'rdns', 'category': 'rdns', 'values': {'enabled': False, 'dns_list': []}}
        result = get_reverse_lookup_dns_fields()
        self.assertEqual(result, self.test_rdns_disabled)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_rdns_config')
    def test_get_rdns_fields_exception(self, mock_rdns_config, mock_cbi):
        mock_rdns_config.side_effect = Exception("Generic exception")
        result = get_reverse_lookup_dns_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_RDNS,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('tvui.settings.cognito_views.psutil.net_connections')
    @patch('base_tvui.platform_api.PlatformClient.get_siem_config')
    def test_get_siem_fields(self, mock_get_siem_config, mock_psutil, mock_cbi):
        mock_psutil.return_value = []
        mock_get_siem_config.return_value = {
            "siems": {
                "*******": {"siem_name": "test", "endpoint": "*******", "protocol": "tcp", "port": 9500, "id": None},
                "2.2.2.2": {"siem_name": "test2", "endpoint": "2.2.2.2", "protocol": "udp", "port": 9501, "id": None},
                "3.3.3.3": {"endpoint": "3.3.3.3", "port": 9502, "protocol": "udp", "siem_name": "test3", "id": None},
            },
            "enabled": True,
            "siems_tiemstamp": {},
        }
        result = get_siem_fields()
        self.assertEqual(result, self.test_siem_expected)

    @patch('base_tvui.platform_api.PlatformClient.get_siem_config')
    def test_get_siem_fields_disabled(self, mock_get_siem_config, mock_cbi):

        mock_get_siem_config.return_value = {
            "siems": {
                "*******": {"siem_name": "test", "endpoint": "*******", "protocol": "tcp", "port": 9500, "id": None},
                "2.2.2.2": {"siem_name": "test2", "endpoint": "2.2.2.2", "protocol": "udp", "port": 9501, "id": None},
                "3.3.3.3": {"endpoint": "3.3.3.3", "port": 9502, "protocol": "udp", "siem_name": "test3", "id": None},
            },
            "enabled": False,
            "siems_tiemstamp": {},
        }
        result = get_siem_fields()
        self.assertEqual(result, self.test_siem_disabled)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_siem_config')
    def test_get_siem_fields_exception(self, mock_get_siem_config, mock_cbi):

        mock_get_siem_config.side_effect = Exception("Generic exception")
        result = get_siem_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_SIEM,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('base_tvui.platform_api.PlatformClient.test_vsphere_connection')
    def test_get_vcenter_fields(self, mock_test_vsphere_connection, mock_cbi):
        mock_test_vsphere_connection.return_value = {
            'vcenters': {'vcenter.io': {'connected': True}, 'test.com': {'connected': False, 'message': 'Timeout error'}},
        }
        result = get_vcenter_fields()
        self.assertEqual(result, self.test_vcenter_expected)

    @patch('base_tvui.platform_api.PlatformClient.test_vsphere_connection')
    def test_get_vcenter_fields_disabled(self, mock_test_vsphere_connection, mock_cbi):
        mock_test_vsphere_connection.return_value = None
        result = get_vcenter_fields()
        self.assertEqual(result, self.test_vcenter_disabled)

    @patch('base_tvui.platform_api.PlatformClient.test_vsphere_connection')
    def test_get_vcenter_fields_error(self, mock_test_vsphere_connection, mock_cbi):
        mock_test_vsphere_connection.return_value = {
            'vcenters': {PlatformClient.VCENTER_FAIL_KEY: {'message': PlatformClient.VCENTER_FAIL_MESSAGE}}
        }
        result = get_vcenter_fields()
        self.assertEqual(result, self.test_vcenter_error)

    @patch('tvui.settings.cognito_views.CognitoStatusView.retrieve_vsphere_status')
    def test_get_vcenter_fields_exception(self, mock_test_vsphere_connection, mock_cbi):
        mock_test_vsphere_connection.side_effect = Exception("Generic exception")
        result = get_vcenter_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_VCENTER,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('base_tvui.platform_api.PlatformClient.get_ad_log_ingestion_status')
    @patch('base_tvui.platform_api.PlatformClient.get_ad_log_ingestion_config')
    def test_get_window_events_log_ingestion_fields(self, mock_ad_log_config, mock_ad_log_status, mock_cbi):
        mock_ad_log_config.return_value = {'enabled': True}
        mock_ad_log_status.return_value = {"status": True, "recieved": 300, "4768_recieved": 100, "4769_recieved": 100}
        result = get_window_events_log_ingestion_fields()
        self.assertEqual(result, self.test_ad_log_expected)

    @patch('base_tvui.platform_api.PlatformClient.get_ad_log_ingestion_config')
    def test_get_window_events_log_ingestion_fields_disabled(self, mock_ad_log_config, mock_cbi):
        mock_ad_log_config.return_value = {'enabled': False}

        result = get_window_events_log_ingestion_fields()
        self.assertEqual(result, self.test_ad_log_disabled)

    @patch('base_tvui.platform_api.PlatformClient.get_ad_log_ingestion_status')
    @patch('base_tvui.platform_api.PlatformClient.get_ad_log_ingestion_config')
    def test_get_window_events_log_ingestion_fields_error(self, mock_ad_log_config, mock_ad_log_status, mock_cbi):
        mock_ad_log_config.return_value = {'enabled': True}
        mock_ad_log_status.return_value = {"status": False}
        result = get_window_events_log_ingestion_fields()
        self.assertEqual(result, self.test_ad_log_error)

    @patch('tvui.settings.cognito_views.CognitoStatusView.retrieve_ad_log_ingestion_status')
    def test_get_window_events_log_ingestion_fields_exception(self, mock_ad_log_status, mock_cbi):
        mock_ad_log_status.side_effect = Exception("Generic exception")
        result = get_window_events_log_ingestion_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)

    @patch('base_tvui.platform_api.PlatformClient.get_zpa_log_ingestion_status')
    @patch('base_tvui.platform_api.PlatformClient.get_zpa_log_ingestion_config')
    def test_get_zpa_fields(self, mock_zpa_config, mock_zpa_status, mock_cbi):
        mock_zpa_config.return_value = {'enabled': True}
        mock_zpa_status.return_value = {"status": True, "received": 151, "zpa_received": 151}
        result = get_zscaler_private_access_fields()
        self.assertEqual(result, self.test_zpa_expected)

    @patch('base_tvui.platform_api.PlatformClient.get_zpa_log_ingestion_config')
    def test_get_zpa_fields_disabled(self, mock_zpa_config, mock_cbi):
        mock_zpa_config.return_value = {'enabled': False}
        result = get_zscaler_private_access_fields()
        self.assertEqual(result, self.test_zpa_disabled)

    @patch('base_tvui.platform_api.PlatformClient.get_zpa_log_ingestion_status')
    @patch('base_tvui.platform_api.PlatformClient.get_zpa_log_ingestion_config')
    def test_get_zpa_fields_error(self, mock_zpa_config, mock_zpa_status, mock_cbi):
        mock_zpa_config.return_value = {'enabled': True}
        mock_zpa_status.return_value = {"error": 'unable to contact zpa', "received": 0, "zpa_received": 0}
        result = get_zscaler_private_access_fields()
        self.assertEqual(result, self.test_zpa_error)

    @patch('tvui.settings.cognito_views.CognitoStatusView.retrieve_zpa_status')
    def test_get_zpa_fields_exception(self, mock_zpa_status, mock_cbi):
        mock_zpa_status.side_effect = Exception("Generic exception")
        result = get_zscaler_private_access_fields()
        expected = {
            health_utils.CONNECTOR_TYPE: health_utils.TYPE_ZPA,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(result, expected)


class ExternalConnectorsHealthV3_4Tests(BasicAPIV3_4Tests):

    task_result_expected = ExternalConnectorsHealthUtilsTests.test_external_connectors_expected

    expected_response_external_connectors = {
        'results': {
            health_utils.TYPE_ACTIVE_DIRECTORY: {
                health_utils.AUTH_STATUS: {'qe-ad-test': health_utils.AUTHENTICATED, 'redwoods-test': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_AZURE_AD_LOCKDOWN: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_AWS: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_AZURE: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_GOOGLE_CLOUD: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_RDNS: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_SIEM: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_VCENTER: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {'test.com': 'Timeout error'},
            },
            health_utils.TYPE_WINDOWS_EVENT_LOG_INGESTION: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_ZPA: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
        },
    }

    expected_response_external_connectors_connector_type = {
        'results': {
            health_utils.TYPE_ACTIVE_DIRECTORY: {
                health_utils.AUTH_STATUS: {'qe-ad-test': health_utils.AUTHENTICATED, 'redwoods-test': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_AWS: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
        },
    }

    expected_response_external_connectors_data_type = {
        'results': {
            health_utils.TYPE_ACTIVE_DIRECTORY: {
                health_utils.AUTH_STATUS: {'qe-ad-test': health_utils.AUTHENTICATED, 'redwoods-test': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
            health_utils.TYPE_AWS: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            },
        },
    }

    expected_response_external_connectors_aws_exception = {
        'results': {
            health_utils.TYPE_AWS: {
                health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
                health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
            }
        }
    }

    def setUp(self):
        super().setUp()
        self.maxDiff = None
        self.url = reverse('api-v3.4:api-health-external_connectors-v3.4')
        self.factory = APIRequestFactory()
        self.api_version = 3.4
        self.client.force_authenticate(user=self.client_user, token=self.token)
        # Insert old task into the table.
        # Better simulates real environment since this table will rarely be empty.
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='227e217f-e6a7-4704-85fb-072af39ab75f',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )

    def tearDown(self):
        TaskResult.objects.all().delete()

    def test_get_health_external_connectors(self):
        """Test GET health external connectors endpoint"""
        req = self.factory.get(self.url)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop(
            'updated_at'
        )  # celery overwrites the django object and prevents freezetime from working: https://stackoverflow.com/questions/65039089/freezegun-does-not-work-on-django-orm-create-at-field,
        self.assertEqual(resp_data, self.expected_response_external_connectors)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_external_connector_fields')
    def test_get_health_external_connectors_live(self, refresh_mock):
        """Test GET health external connectors with live param"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.return_value = self.task_result_expected
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        # Assert 'live' logic did run
        assert task_mock.get.called
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_external_connectors)

    def test_get_health_external_connectors_connector_type(self):
        """Test GET health external connectors endpoint with connector_type param"""
        query_params = {'connector_type': "active_directory, aws"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_external_connectors_connector_type)

    def test_get_health_external_connectors_data_type(self):
        """Test GET health external connectors endpoint with data_type param"""
        query_params = {'data_type': "connection_status, auth_status", 'connector_type': "active_directory, aws"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_external_connectors_data_type)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_external_connector_fields')
    def test_get_health_external_connectors_live_falsy_strings(self, refresh_mock):
        """Test GET health external connectors with live param with a string 'false' or 'False'"""
        query_params = {'live': 0}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.return_value = self.task_result_expected
        refresh_mock.apply_async.return_value = task_mock
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee11',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
        )
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        # Assert 'live' logic did not run
        assert not task_mock.get.called
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_external_connectors)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_external_connector_fields')
    def test_get_health_external_connectors_live_timeout(self, refresh_mock):
        """Test GET health external connectors with live param with a timeout"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = TimeoutError('The operation timed out.')
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Retrieving data." in resp_data.get("detail"))

    def test_get_health_external_connectors_no_task_result(self):
        """Test GET health external connectors endpoint when there are no task results in the Task Table"""
        req = self.factory.get(self.url)
        force_authenticate(req, user=self.client_user, token=self.token)
        # remove buffer task
        TaskResult.objects.all().delete()
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        resp.render()
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("No cached integration information at this time." in resp_data.get("detail"))
        # add back buffer task
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='227e217f-e6a7-4704-85fb-072af39ab75f',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_external_connector_fields')
    def test_get_health_external_connectors_exception(self, refresh_mock):
        """Test GET health external connectors with live param with an unexpected exception"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = Exception("generic exception")
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 500)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Please contact support if issue persists." in resp_data.get("detail"))

    def test_get_health_external_connectors_exception(self):
        """Test GET health external connectors endpoint with an exception"""
        query_params = {'connector_type': "aws"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert aws exception task into result table
        task_result_expected_exception = self.task_result_expected.copy()
        task_result_expected_exception[health_utils.TYPE_AWS] = {
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(task_result_expected_exception),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = ExternalConnectorsHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_external_connectors_aws_exception)


class ExternalConnectorsHealthDetailsV3_4Tests(BasicAPIV3_4Tests):

    task_result_expected = ExternalConnectorsHealthUtilsTests.test_external_connectors_expected
    ad_result = ExternalConnectorsHealthUtilsTests.test_ad_status_expected
    aws_result = ExternalConnectorsHealthUtilsTests.test_aws_expected

    expected_response_external_connectors_connector_type = {
        'results': {
            health_utils.TYPE_ACTIVE_DIRECTORY: {health_utils.CONNECTION_STATUS: health_utils.ENABLED, "details": ad_result["details"]},
            health_utils.TYPE_AWS: {health_utils.CONNECTION_STATUS: health_utils.ENABLED, "details": aws_result["details"]},
        },
    }

    def setUp(self):
        super().setUp()
        self.maxDiff = None
        self.url = reverse('api-v3.4:api-health-external_connectors-details-v3.4')
        self.factory = APIRequestFactory()
        self.api_version = 3.4
        self.client.force_authenticate(user=self.client_user, token=self.token)

    def test_get_health_external_connectors_details(self):
        """Test GET health external connectors details"""
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        url = reverse('api-v3.4:api-health-external_connectors-details-v3.4')
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))

    def test_get_health_external_connectors_details_connector_type(self):
        """Test GET health external connectors details endpoint with connector_type param"""
        query_params = {'connector_type': "active_directory, aws"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = ExternalConnectorsHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_external_connectors_connector_type)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_external_connector_fields')
    def test_get_health_external_connectors_details_live_timeout(self, refresh_mock):
        """Test GET health external connectors details with live param with a timeout"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = TimeoutError('The operation timed out.')
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = ExternalConnectorsHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Retrieving data." in resp_data.get("detail"))

    def test_get_health_external_connectors_details_no_task_result(self):
        """Test GET health external connectors details endpoint when there are no task results in the Task Table"""
        req = self.factory.get(self.url)
        force_authenticate(req, user=self.client_user, token=self.token)
        # remove buffer task
        TaskResult.objects.all().delete()
        resp = ExternalConnectorsHealthDetailsV3_4.as_view()(req)
        resp.render()
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("No cached integration information at this time." in resp_data.get("detail"))
        # add back buffer task
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_external_connector_fields',
            task_id='227e217f-e6a7-4704-85fb-072af39ab75f',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_external_connector_fields')
    def test_get_health_external_connectors_details_exception(self, refresh_mock):
        """Test GET health external connectors details with live param with an unexpected exception"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = Exception("generic exception")
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = ExternalConnectorsHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 500)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Please contact support if issue persists." in resp_data.get("detail"))


@patch('base_tvui.feature_flipper.conditions.is_cloudbridge', return_value=True)
class EDRHealthUtilsTests(BasicAPIV3_4Tests):

    host_lockdown_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_HOST_LOCKDOWN,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': {'activeIntegrations': ["Microsoft Defender ATP", "Carbon Black"], 'lockdownAutoEnabled': True},
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }
    windows_defender_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_WINDOWS_DEFENDER,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': {'status': 'Connected', 'severity': Misc.YEP, 'hostLockdown': {'enabled': False}},
        health_utils.LOCKDOWN_STATUS: health_utils.DISABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }
    cb_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': {
            'carbon_blacks': {
                'demo': {'status': 'Connected', 'severity': Misc.YEP},
                'demo2': {'status': 'Connected', 'severity': Misc.YEP},
            },
            'hostLockdown': {'enabled': True},
        },
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'demo': health_utils.AUTHENTICATED, 'demo2': health_utils.AUTHENTICATED},
    }
    cb_cloud_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK_CLOUD,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'Carbon Black Cloud connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }
    fireeye_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_FIREEYE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'FireEye Endpoint Security connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }
    sentinelone_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_SENTINELONE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'SentinelOne connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }
    cybereason_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_CYBEREASON,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'Cybereason connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }
    crowdstrike_expected_status = {
        health_utils.EDR_TYPE: health_utils.TYPE_CROWDSTRIKE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'Crowdstrike connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.generate_all_edr_schemas()

    def setUp(self):
        super().setUp()

    test_host_lockdown_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_HOST_LOCKDOWN,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': {'activeIntegrations': ["Microsoft Defender ATP", "Carbon Black"], 'lockdownAutoEnabled': True},
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    test_windows_defender_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_WINDOWS_DEFENDER,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': {'status': 'Connected', 'severity': Misc.YEP, 'hostLockdown': {'enabled': False}},
        health_utils.LOCKDOWN_STATUS: health_utils.DISABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    test_carbon_black_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': {
            'carbon_blacks': {
                'demo': {'status': 'Connected', 'severity': Misc.YEP},
                'demo2': {'status': 'Connected', 'severity': Misc.YEP},
            },
            'hostLockdown': {'enabled': True},
        },
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'demo': health_utils.AUTHENTICATED, 'demo2': health_utils.AUTHENTICATED},
    }

    test_carbon_black_cloud_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK_CLOUD,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'Carbon Black Cloud connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    test_fireeye_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_FIREEYE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'FireEye Endpoint Security connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    test_sentinelone_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_SENTINELONE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'SentinelOne connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    test_sentinelone_expected_exception = {
        health_utils.EDR_TYPE: health_utils.TYPE_SENTINELONE,
        health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
        health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
    }

    test_cybereason_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_CYBEREASON,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'Cybereason connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    test_crowdstrike_expected = {
        health_utils.EDR_TYPE: health_utils.TYPE_CROWDSTRIKE,
        health_utils.CONNECTION_STATUS: health_utils.ENABLED,
        'details': [
            {'statusType': 'success', 'text': 'Crowdstrike connected'},
            {'statusType': 'success', 'text': 'Host Lockdown'},
        ],
        health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
        health_utils.ERROR_STATES: {},
        health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
    }

    # test one edr with an exception
    test_edr_expected = {
        health_utils.TYPE_CARBON_BLACK: test_carbon_black_expected,
        health_utils.TYPE_CARBON_BLACK_CLOUD: test_carbon_black_cloud_expected,
        health_utils.TYPE_CROWDSTRIKE: test_crowdstrike_expected,
        health_utils.TYPE_CYBEREASON: test_cybereason_expected,
        health_utils.TYPE_FIREEYE: test_fireeye_expected,
        health_utils.TYPE_HOST_LOCKDOWN: test_host_lockdown_expected,
        health_utils.TYPE_SENTINELONE: test_sentinelone_expected_exception,
        health_utils.TYPE_WINDOWS_DEFENDER: test_windows_defender_expected,
    }

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_host_lockdown_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_windows_defender_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_carbon_black_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_carbon_black_cloud_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_fireeye_endpoint_security_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_sentinelone_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_cybereason_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_crowdstrike_fields')
    def test_get_edr_fields(
        self,
        mock_get_crowdstrike_fields,
        mock_get_cybereason_fields,
        mock_get_sentinelone_fields,
        mock_get_fireeye_endpoint_security_fields,
        mock_get_carbon_black_cloud_fields,
        mock_get_carbon_black_fields,
        mock_get_windows_defender_fields,
        mock_get_host_lockdown_fields,
        mock_cbi,
    ):
        mock_get_host_lockdown_fields.return_value = {'field': 'value1'}
        mock_get_windows_defender_fields.return_value = {'field': 'value2'}
        mock_get_carbon_black_fields.return_value = {'field': 'value3'}
        mock_get_carbon_black_cloud_fields.return_value = {'field': 'value4'}
        mock_get_fireeye_endpoint_security_fields.return_value = {'field': 'value5'}
        mock_get_sentinelone_fields.return_value = {'field': 'value6'}
        mock_get_cybereason_fields.return_value = {'field': 'value7'}
        mock_get_crowdstrike_fields.return_value = {'field': 'value8'}
        result = health_utils.get_edr_fields()

        # Assert the expected results
        expected_result = {
            health_utils.TYPE_HOST_LOCKDOWN: {'field': 'value1'},
            health_utils.TYPE_WINDOWS_DEFENDER: {'field': 'value2'},
            health_utils.TYPE_CARBON_BLACK: {'field': 'value3'},
            health_utils.TYPE_CARBON_BLACK_CLOUD: {'field': 'value4'},
            health_utils.TYPE_FIREEYE: {'field': 'value5'},
            health_utils.TYPE_SENTINELONE: {'field': 'value6'},
            health_utils.TYPE_CYBEREASON: {'field': 'value7'},
            health_utils.TYPE_CROWDSTRIKE: {'field': 'value8'},
        }
        self.assertEqual(result, expected_result)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_host_lockdown_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_windows_defender_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_carbon_black_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_carbon_black_cloud_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_fireeye_endpoint_security_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_sentinelone_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_cybereason_fields')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.get_crowdstrike_fields')
    def test_get_edr_fields_exception(
        self,
        mock_get_crowdstrike_fields,
        mock_get_cybereason_fields,
        mock_get_sentinelone_fields,
        mock_get_fireeye_endpoint_security_fields,
        mock_get_carbon_black_cloud_fields,
        mock_get_carbon_black_fields,
        mock_get_windows_defender_fields,
        mock_get_host_lockdown_fields,
        mock_cbi,
    ):
        mock_get_host_lockdown_fields.return_value = {'field': 'value1'}
        mock_get_windows_defender_fields.return_value = {'field': 'value2'}
        mock_get_carbon_black_fields.return_value = {'field': 'value3'}
        mock_get_carbon_black_cloud_fields.return_value = {'field': 'value4'}
        mock_get_fireeye_endpoint_security_fields.return_value = {'field': 'value5'}
        mock_get_sentinelone_fields.return_value = {'field': 'value6'}
        mock_get_cybereason_fields.return_value = {'field': 'value7'}
        mock_get_crowdstrike_fields.side_effect = Exception("Generic exception")
        result = health_utils.get_edr_fields()

        # Assert the expected results
        expected_result = {
            health_utils.TYPE_HOST_LOCKDOWN: {'field': 'value1'},
            health_utils.TYPE_WINDOWS_DEFENDER: {'field': 'value2'},
            health_utils.TYPE_CARBON_BLACK: {'field': 'value3'},
            health_utils.TYPE_CARBON_BLACK_CLOUD: {'field': 'value4'},
            health_utils.TYPE_FIREEYE: {'field': 'value5'},
            health_utils.TYPE_SENTINELONE: {'field': 'value6'},
            health_utils.TYPE_CYBEREASON: {'field': 'value7'},
            health_utils.TYPE_CROWDSTRIKE: {},
        }
        self.assertEqual(result, expected_result)

    @patch('base_tvui.platform_api.PlatformClient.get_carbon_black_config')
    @patch('base_tvui.platform_api.PlatformClient.get_windows_defender_config')
    def test_get_host_lockdown_fields(self, mock_get_defender_config, mock_get_cb_config, mock_cbi):
        """Test get host lockdown fields"""
        # Microsoft Defender and Carbon Black on
        mock_get_defender_config.return_value = {'enabled': True}
        mock_get_cb_config.return_value = {'enabled': True, 'carbon_blacks': []}
        setting.objects.create(group='host_lockdown', key='lockdown_enabled', value='on')
        setting.objects.create(group='host_lockdown', key='lockdown_auto_enabled', value='on')

        self.assertEqual(health_utils.get_host_lockdown_fields(), self.host_lockdown_expected_status)

        # Just Carbon Black on
        mock_get_defender_config.return_value = {'enabled': False}
        mock_get_cb_config.return_value = {'enabled': True, 'carbon_blacks': []}

        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_HOST_LOCKDOWN,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': {'activeIntegrations': ["Carbon Black"], 'lockdownAutoEnabled': True},
            health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
        }
        self.assertEqual(health_utils.get_host_lockdown_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_carbon_black_config')
    @patch('base_tvui.platform_api.PlatformClient.get_windows_defender_config')
    def test_get_host_lockdown_fields_disabled(self, mock_get_defender_config, mock_get_cb_config, mock_cbi):
        """Test get host lockdown fields when disabled"""
        # No EDR on
        mock_get_defender_config.return_value = {'enabled': False}
        mock_get_cb_config.return_value = {'enabled': False, 'carbon_blacks': []}

        # Default setting values (not connected)
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_HOST_LOCKDOWN,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_host_lockdown_fields(), expected_status)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_host_lockdown_config')
    def test_get_host_lockdown_fields_exception(self, config_mock, mock_cbi):
        """Test get host lockdown fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from test CognitoConfigView")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_HOST_LOCKDOWN,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_host_lockdown_fields(), expected)

    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_windows_defender_config')
    def test_get_windows_defender_fields(self, mock_get_config, mock_get, mock_cbi):
        """Test get windows defender fields"""
        mock_get_config.return_value = {'enabled': True}
        mock_get.return_value = MockResponse({'check': True, 'errors': []})

        self.assertEqual(health_utils.get_windows_defender_fields(), self.windows_defender_expected_status)

    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_windows_defender_config')
    def test_get_windows_defender_fields_error(self, mock_get_config, mock_get, mock_cbi):
        """Test get windows defender fields with errors"""
        mock_get_config.return_value = {'enabled': True}
        mock_get.return_value = MockResponse({'check': False, 'errors': ["Invalid Tenant ID"]})
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_WINDOWS_DEFENDER,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': {'status': 'Invalid Tenant ID', 'severity': Misc.NOPE, 'hostLockdown': {'enabled': False}},
            health_utils.LOCKDOWN_STATUS: health_utils.DISABLED,
            health_utils.ERROR_STATES: {'status': 'Invalid Tenant ID'},
            health_utils.AUTH_STATUS: {'status': health_utils.NOT_AUTHENTICATED},
        }
        self.assertEqual(health_utils.get_windows_defender_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_windows_defender_config')
    def test_get_windows_defender_fields_disabled(self, mock_get_config, mock_cbi):
        """Test get windows defender fields when disabled"""
        mock_get_config.return_value = {'enabled': False}
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_WINDOWS_DEFENDER,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_windows_defender_fields(), expected_status)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_windows_defender_config')
    def test_get_windows_defender_fields_exception(self, config_mock, mock_cbi):
        """Test get windows defender fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from test CognitoConfigView")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_WINDOWS_DEFENDER,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_windows_defender_fields(), expected)

    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_carbon_black_config')
    def test_get_carbon_black_fields(self, mock_get_config, mock_get, mock_cbi):
        """Test get carbon black fields"""
        # get_carbon_black_config
        mock_get_config.return_value = {
            'enabled': True,
            'use_proxy': False,
            'carbon_blacks': [
                {
                    'endpoint': '',
                    'port': '443',
                    'api_key': '',
                    'ssl_verify': False,
                    'cb_poll_interval': 60,
                    'config_poll_interval': 5,
                    'time_window': 600,
                    'sensor_groups': [],
                }
            ],
        }
        # make_get_request
        mock_get.return_value = MockResponse(
            {'check': True, 'carbon_blacks': {'demo': {'status_code': 200}, 'demo2': {'status_code': 200}}}
        )

        # host lockdown on
        setting.objects.create(group='host_lockdown', key='lockdown_enabled', value='on')
        self.assertEqual(health_utils.get_carbon_black_fields(), self.test_carbon_black_expected)

    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_carbon_black_config')
    def test_get_carbon_black_fields_error(self, mock_get_config, mock_get, mock_cbi):
        """Test get carbon black fields with errors"""
        # get_carbon_black_config
        mock_get_config.return_value = {
            'enabled': True,
            'use_proxy': False,
            'carbon_blacks': [
                {
                    'endpoint': '',
                    'port': '443',
                    'api_key': '',
                    'ssl_verify': False,
                    'cb_poll_interval': 60,
                    'config_poll_interval': 5,
                    'time_window': 600,
                    'sensor_groups': [],
                }
            ],
        }
        # make_get_request
        mock_get.return_value = MockResponse(
            {'check': True, 'carbon_blacks': {'demo': {'status_code': 403}, 'demo2': {'status_code': 500}, 'demo3': {'status_code': 200}}}
        )

        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': {
                'carbon_blacks': {
                    'demo': {'status': 'Permission denied', 'severity': Misc.PERMISSION},
                    'demo2': {'status': 'Status unknown', 'severity': Misc.ALERT},
                    'demo3': {'status': 'Connected', 'severity': Misc.YEP},
                },
                'hostLockdown': {'enabled': False},
            },
            health_utils.LOCKDOWN_STATUS: health_utils.DISABLED,
            health_utils.ERROR_STATES: {'demo': 'Permission denied', 'demo2': 'Status unknown'},
            health_utils.AUTH_STATUS: {
                'demo': health_utils.NOT_AUTHENTICATED,
                'demo2': health_utils.NOT_CONFIGURED,
                'demo3': health_utils.AUTHENTICATED,
            },
        }
        self.assertEqual(health_utils.get_carbon_black_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_carbon_black_config')
    def test_get_carbon_black_fields_disabled(self, mock_get_config, mock_cbi):
        """Test get carbon black fields when disabled"""
        # get_carbon_black_config
        mock_get_config.return_value = {
            'enabled': False,
            'use_proxy': False,
            'carbon_blacks': [
                {
                    'endpoint': '',
                    'port': '443',
                    'api_key': '',
                    'ssl_verify': False,
                    'cb_poll_interval': 60,
                    'config_poll_interval': 5,
                    'time_window': 600,
                    'sensor_groups': [],
                }
            ],
        }
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_carbon_black_fields(), expected_status)

    @patch('tvui.settings.cognito_views.CognitoConfigView.retrieve_carbon_black_config')
    def test_get_carbon_black_fields_exception(self, config_mock, mock_cbi):
        """Test get carbon black fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from test CognitoConfigView")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_carbon_black_fields(), expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_carbon_black_cloud_fields(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get carbon black cloud fields"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'success', 'message': 'Carbon Black Cloud connected'})
        mock_lockdown.return_value = {'lockdown_enabled': True}
        self.assertEqual(health_utils.get_carbon_black_cloud_fields(), self.test_carbon_black_cloud_expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_carbon_black_cloud_fields_error(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get carbon black cloud fields with errors"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'alert', 'message': 'Invalid credentials'})
        mock_lockdown.return_value = {'lockdown_enabled': True}
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK_CLOUD,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': [{'statusType': 'alert', 'text': 'Invalid credentials'}],
            health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            health_utils.ERROR_STATES: {'status': 'Invalid credentials'},
            health_utils.AUTH_STATUS: {'status': health_utils.NOT_AUTHENTICATED},
        }
        self.assertEqual(health_utils.get_carbon_black_cloud_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_carbon_black_cloud_fields_disabled(self, mock_get_config, mock_cbi):
        """Test get carbon black cloud fields when disabled"""
        mock_get_config.return_value = {'enabled': False}, ''
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK_CLOUD,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_carbon_black_cloud_fields(), expected_status)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.retrieve_generic_edr_config')
    def test_get_carbon_black_cloud_fields_exception(self, config_mock, mock_cbi):
        """Test get carbon black cloud fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from retrieve_generic_edr_config")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK_CLOUD,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_carbon_black_cloud_fields(), expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_fireeye_endpoint_security_fields(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get fireeye endpoint security fields"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'success', 'message': 'FireEye Endpoint Security connected'})
        mock_lockdown.return_value = {'lockdown_enabled': True}
        self.assertEqual(health_utils.get_fireeye_endpoint_security_fields(), self.test_fireeye_expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_fireeye_endpoint_security_fields_error(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get fireeye endpoint security fields with errors"""
        mock_get_config.return_value = {'enabled': True}, ''
        err_message = PlatformClient.EDR_STATUS_UNABLE_TO_CONNECT.format('FireEye Endpoint Security')
        mock_get.return_value = MockResponse({'level': 'alert', 'message': err_message})
        mock_lockdown.return_value = {'lockdown_enabled': True}

        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_FIREEYE,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': [
                {'statusType': 'alert', 'text': err_message},
            ],
            health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            health_utils.ERROR_STATES: {'status': err_message},
            health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        }
        self.assertEqual(health_utils.get_fireeye_endpoint_security_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_fireeye_endpoint_security_fields_disabled(self, mock_get_config, mock_cbi):
        """Test get fireeye endpoint security fields when disabled"""
        mock_get_config.return_value = {'enabled': False}, ''
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_FIREEYE,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_fireeye_endpoint_security_fields(), expected_status)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.retrieve_generic_edr_config')
    def test_get_fireeye_endpoint_security_fields_exception(self, config_mock, mock_cbi):
        """Test get fireeye endpoint security fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from retrieve_generic_edr_config")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_FIREEYE,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_fireeye_endpoint_security_fields(), expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_sentinelone_fields(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get sentinelone fields"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'success', 'message': 'SentinelOne connected'})
        mock_lockdown.return_value = {'lockdown_enabled': True}
        self.assertEqual(health_utils.get_sentinelone_fields(), self.test_sentinelone_expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_sentinelone_fields_error(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get sentinelone fields with errors"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'alert', 'message': 'Invalid credentials'})
        mock_lockdown.return_value = {'lockdown_enabled': False}

        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_SENTINELONE,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': [{'statusType': 'alert', 'text': 'Invalid credentials'}],
            health_utils.LOCKDOWN_STATUS: health_utils.DISABLED,
            health_utils.ERROR_STATES: {'status': 'Invalid credentials'},
            health_utils.AUTH_STATUS: {'status': health_utils.NOT_AUTHENTICATED},
        }
        self.assertEqual(health_utils.get_sentinelone_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_sentinelone_fields_disabled(self, mock_get_config, mock_cbi):
        """Test get sentinelone fields when disabled"""
        mock_get_config.return_value = {'enabled': False}, ''
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_SENTINELONE,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_sentinelone_fields(), expected_status)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.retrieve_generic_edr_config')
    def test_get_sentinelone_fields_exception(self, config_mock, mock_cbi):
        """Test get sentinelone fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from retrieve_generic_edr_config")
        self.assertEqual(health_utils.get_sentinelone_fields(), self.test_sentinelone_expected_exception)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_cybereason_fields(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get cybereason fields"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'success', 'message': 'Cybereason connected'})
        mock_lockdown.return_value = {'lockdown_enabled': True}
        self.assertEqual(health_utils.get_cybereason_fields(), self.test_cybereason_expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_cybereason_fields_error(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get cybereason fields with errors"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'alert', 'message': PlatformClient.EDR_STATUS_CONNECTION_UNAVAILABLE})
        mock_lockdown.return_value = {'lockdown_enabled': True}

        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CYBEREASON,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': [{'statusType': 'alert', 'text': PlatformClient.EDR_STATUS_CONNECTION_UNAVAILABLE}],
            health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            health_utils.ERROR_STATES: {'status': PlatformClient.EDR_STATUS_CONNECTION_UNAVAILABLE},
            health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        }
        self.assertEqual(health_utils.get_cybereason_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_cybereason_disabled(self, mock_get_config, mock_cbi):
        """Test get cybereason fields when disabled"""
        mock_get_config.return_value = {'enabled': False}, ''
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CYBEREASON,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_cybereason_fields(), expected_status)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.retrieve_generic_edr_config')
    def test_get_cybereason_fields_exception(self, config_mock, mock_cbi):
        """Test get cybereason fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from retrieve_generic_edr_config")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_CYBEREASON,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_cybereason_fields(), expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_crowdstrike_fields(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get crowdstrike fields"""
        mock_get_config.return_value = {'enabled': True}, ''
        mock_get.return_value = MockResponse({'level': 'success', 'message': 'Crowdstrike connected'})
        mock_lockdown.return_value = {'lockdown_enabled': True}
        self.assertEqual(health_utils.get_crowdstrike_fields(), self.test_crowdstrike_expected)

    @patch('lockdown.lockdown_utils.get_host_lockdown_settings')
    @patch('base_tvui.platform_api.make_get_request')
    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_crowdstrike_fields_error(self, mock_get_config, mock_get, mock_lockdown, mock_cbi):
        """Test get crowdstrike fields with errors"""
        mock_get_config.return_value = {'enabled': True}, ''
        err_message = PlatformClient.EDR_STATUS_UNABLE_TO_CONNECT.format('Crowdstrike')
        mock_get.return_value = MockResponse({'level': 'alert', 'message': err_message})
        mock_lockdown.return_value = {'lockdown_enabled': True}

        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CROWDSTRIKE,
            health_utils.CONNECTION_STATUS: health_utils.ENABLED,
            'details': [
                {'statusType': 'alert', 'text': err_message},
            ],
            health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
            health_utils.ERROR_STATES: {'status': err_message},
            health_utils.AUTH_STATUS: {'status': health_utils.NOT_CONFIGURED},
        }
        self.assertEqual(health_utils.get_crowdstrike_fields(), expected_status)

    @patch('base_tvui.platform_api.PlatformClient.get_generic_edr_config')
    def test_get_crowdstrike_disabled(self, mock_get_config, mock_cbi):
        """Test get crowdstrike fields when disabled"""
        mock_get_config.return_value = {'enabled': False}, ''
        expected_status = {
            health_utils.EDR_TYPE: health_utils.TYPE_CROWDSTRIKE,
            health_utils.CONNECTION_STATUS: health_utils.DISABLED,
        }
        self.assertEqual(health_utils.get_crowdstrike_fields(), expected_status)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_health_utils.retrieve_generic_edr_config')
    def test_get_crowdstrike_fields_exception(self, config_mock, mock_cbi):
        """Test get crowdstrike fields when exception occurs"""
        config_mock.side_effect = Exception("Test exception from retrieve_generic_edr_config")
        expected = {
            health_utils.EDR_TYPE: health_utils.TYPE_CROWDSTRIKE,
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
        }
        self.assertEqual(health_utils.get_crowdstrike_fields(), expected)


class EDRHealthV3_4Tests(BasicAPIV3_4Tests):
    def setUp(self):
        super().setUp()
        self.maxDiff = None
        self.url = reverse('api-v3.4:api-health-edr-v3.4')
        self.factory = APIRequestFactory()
        self.api_version = 3.4
        self.client.force_authenticate(user=self.client_user, token=self.token)
        # Insert old task into the table.
        # Better simulates real environment since this table will rarely be empty.
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='227e217f-e6a7-4704-85fb-072af39ab75f',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )

    def tearDown(self):
        TaskResult.objects.all().delete()

    task_result_expected = EDRHealthUtilsTests.test_edr_expected

    expected_response_edr = {
        'results': {
            health_utils.TYPE_HOST_LOCKDOWN: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_WINDOWS_DEFENDER: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.DISABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CARBON_BLACK: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'demo': health_utils.AUTHENTICATED, 'demo2': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CARBON_BLACK_CLOUD: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_FIREEYE: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_SENTINELONE: {
                health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
                health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
            },
            health_utils.TYPE_CYBEREASON: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CROWDSTRIKE: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
        },
    }

    expected_response_edr_edr_type = {
        'results': {
            health_utils.TYPE_CROWDSTRIKE: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_HOST_LOCKDOWN: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CARBON_BLACK_CLOUD: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.LOCKDOWN_STATUS: health_utils.ENABLED,
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
        },
    }

    expected_response_edr_data_type = {
        'results': {
            health_utils.TYPE_HOST_LOCKDOWN: {
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_WINDOWS_DEFENDER: {
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CARBON_BLACK: {
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'demo': health_utils.AUTHENTICATED, 'demo2': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CARBON_BLACK_CLOUD: {
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_FIREEYE: {
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_SENTINELONE: {
                health_utils.ERROR_STATES: {"status": health_utils.GET_EXCEPTION},
            },
            health_utils.TYPE_CYBEREASON: {
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
            health_utils.TYPE_CROWDSTRIKE: {
                health_utils.ERROR_STATES: {},
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
        },
    }

    expected_response_edr_and_data_type = {
        'results': {
            health_utils.TYPE_SENTINELONE: {
                health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            },
            health_utils.TYPE_WINDOWS_DEFENDER: {
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                health_utils.AUTH_STATUS: {'status': health_utils.AUTHENTICATED},
            },
        }
    }

    def test_get_edr_health(self):
        """Test GET EDR health"""
        req = self.factory.get(self.url)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_live(self, refresh_mock):
        """Test GET health edr with live param"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.return_value = self.task_result_expected
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        # Assert 'live' logic did run
        assert task_mock.get.called
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr)

    def test_get_health_edr_edr_type(self):
        """Test GET health edr endpoint with edr_type param"""
        query_params = {'edr_type': "crowdstrike, host_lockdown, cb_cloud"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr_edr_type)

    def test_get_health_edr_data_type(self):
        """Test GET health edr endpoint with data_type param"""
        query_params = {'data_type': "auth_status, error_states"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr_data_type)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_live_falsy_strings(self, refresh_mock):
        """Test GET health edr with live param with a false string"""
        query_params = {'live': 'false_string'}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.return_value = self.task_result_expected
        refresh_mock.apply_async.return_value = task_mock
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
        )
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        # Assert 'live' logic did not run
        assert not task_mock.get.called
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr)

    def test_get_health_edr_and_data_type(self):
        """Test GET health edr endpoint with edr_type and data_type param"""
        query_params = {'edr_type': "sentinelone,windows_defender", 'data_type': "auth_status,connection_status"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert Task into result table:
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr_and_data_type)

    def test_get_health_edr_data_type_error(self):
        """Test GET health edr endpoint with data_type param error"""
        query_params = {'data_type': "auth_status,errors"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.render().content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('_meta')
        expected = {'level': 'error', 'message': 'Invalid field(s) found'}
        self.assertEqual(resp_err, expected)

    def test_get_health_edr_edr_type_error(self):
        """Test GET health edr endpoint with edr_type param error"""
        query_params = {'edr_type': "crowdstrike,host_lockdown,error"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 400)
        resp_obj = resp.render().content.decode('utf-8')
        resp_err = json.loads(resp_obj).get('_meta')
        expected = {'level': 'error', 'message': 'Invalid field(s) found'}
        self.assertEqual(resp_err, expected)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_live_timeout(self, refresh_mock):
        """Test GET health edr with live param with a timeout"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = TimeoutError('The operation timed out.')
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Retrieving data." in resp_data.get("detail"))

    def test_get_health_edr_no_task_result(self):
        """Test GET health edr endpoint when there are no task results in the Task Table"""
        req = self.factory.get(self.url)
        force_authenticate(req, user=self.client_user, token=self.token)
        # remove buffer task
        TaskResult.objects.all().delete()
        resp = EDRHealthV3_4.as_view()(req)
        resp.render()
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("No cached integration information at this time." in resp_data.get("detail"))
        # add back buffer task
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='227e217f-e6a7-4704-85fb-072af39ab75f',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_exception(self, refresh_mock):
        """Test GET health edr with live param with an unexpected exception"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = Exception("generic exception")
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 500)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Please contact support if issue persists." in resp_data.get("detail"))


class MockResponse:
    def __init__(self, resp):
        self.resp = resp

    def json(self):
        return self.resp


class EDRHealthDetailsV3_4Tests(BasicAPIV3_4Tests):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

    def test_get_edr_health_details(self):
        url = reverse('api-v3.4:api-health-edr-details-v3.4')
        resp = self.client.get(url)
        self.assertEqual(resp.status_code, 501)
        resp_data = json.loads(resp.content.decode('utf-8'))
        expected_response = {'501': 'Not Implemented'}
        self.assertEqual(resp_data, expected_response)


class EDRHealthDetailsV3_4Tests(BasicAPIV3_4Tests):

    task_result_expected = {
        health_utils.TYPE_HOST_LOCKDOWN: EDRHealthUtilsTests.test_host_lockdown_expected,
        health_utils.TYPE_WINDOWS_DEFENDER: EDRHealthUtilsTests.test_windows_defender_expected,
        health_utils.TYPE_CARBON_BLACK: EDRHealthUtilsTests.test_carbon_black_expected,
        health_utils.TYPE_CARBON_BLACK_CLOUD: EDRHealthUtilsTests.test_carbon_black_cloud_expected,
        health_utils.TYPE_FIREEYE: EDRHealthUtilsTests.test_fireeye_expected,
        health_utils.TYPE_SENTINELONE: EDRHealthUtilsTests.test_sentinelone_expected,
        health_utils.TYPE_CYBEREASON: EDRHealthUtilsTests.test_cybereason_expected,
        health_utils.TYPE_CROWDSTRIKE: EDRHealthUtilsTests.test_crowdstrike_expected,
    }
    carbonblack_result = EDRHealthUtilsTests.test_carbon_black_expected
    crowdstrike_result = EDRHealthUtilsTests.test_crowdstrike_expected

    expected_response_edr_type = {
        'results': {
            health_utils.TYPE_CARBON_BLACK: {
                health_utils.EDR_TYPE: health_utils.TYPE_CARBON_BLACK,
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                "details": carbonblack_result["details"],
            },
            health_utils.TYPE_CROWDSTRIKE: {
                health_utils.EDR_TYPE: health_utils.TYPE_CROWDSTRIKE,
                health_utils.CONNECTION_STATUS: health_utils.ENABLED,
                "details": crowdstrike_result["details"],
            },
        },
    }

    expected_response_edr_details_carbon_black_exception = {
        'results': {
            health_utils.TYPE_CARBON_BLACK: {
                health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
            }
        }
    }

    def setUp(self):
        super().setUp()
        self.maxDiff = None
        self.url = reverse('api-v3.4:api-health-edr-details-v3.4')
        self.factory = APIRequestFactory()
        self.api_version = 3.4
        self.client.force_authenticate(user=self.client_user, token=self.token)

    def tearDown(self):
        TaskResult.objects.all().delete()

    def test_get_health_all_edr_details(self):
        """Test GET health all EDR details"""
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = self.client.get(self.url)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')

    def test_get_health_edr_details_edr_types(self):
        """Test GET health EDR details with edr_types"""
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = self.client.get(self.url + f'?edr_type={health_utils.TYPE_CARBON_BLACK},{health_utils.TYPE_CROWDSTRIKE}')
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr_type)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_details_live(self, refresh_mock):
        """Test GET health edr details with live param"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.return_value = self.task_result_expected
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        # Assert 'live' logic did run
        assert task_mock.get.called
        assert resp_data.pop('updated_at')

    def test_get_health_edr_details_invalid_edr_type(self):
        """Test GET health EDR details with invalid edr_types"""
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee47',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = self.client.get(self.url + f'?edr_type=fake_edr')
        self.assertEqual(resp.status_code, 400)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_details_live_timeout(self, refresh_mock):
        """Test GET health edr details with live param with a timeout"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = TimeoutError('The operation timed out.')
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Retrieving data." in resp_data.get("detail"))

    def test_get_health_edr_details_no_task_result(self):
        """Test GET health edr details endpoint when there are no task results in the Task Table"""
        req = self.factory.get(self.url)
        force_authenticate(req, user=self.client_user, token=self.token)
        # remove buffer task
        TaskResult.objects.all().delete()
        resp = EDRHealthDetailsV3_4.as_view()(req)
        resp.render()
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("No cached integration information at this time." in resp_data.get("detail"))
        # add back buffer task
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='227e217f-e6a7-4704-85fb-072af39ab75f',
            status='SUCCESS',
            result=json.dumps(self.task_result_expected),
            date_created=datetime(2013, 3, 3, 3, 13),
        )

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.refresh_edr_health_fields')
    def test_get_health_edr_details_unexpected_exception(self, refresh_mock):
        """Test GET health edr details with live param with an unexpected exception"""
        query_params = {'live': True}
        req = self.factory.get(self.url, query_params)
        task_mock = Mock()
        task_mock.get.side_effect = Exception("generic exception")
        refresh_mock.apply_async.return_value = task_mock
        force_authenticate(req, user=self.client_user, token=self.token)
        resp = EDRHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 500)
        resp.render()
        resp_data = json.loads(resp.content.decode('utf-8'))
        self.assertTrue("Please contact support if issue persists." in resp_data.get("detail"))

    def test_get_health_edr_details_exception(self):
        """Test GET health edr details endpoint with an exception"""
        query_params = {'edr_type': "carbon_black"}
        req = self.factory.get(self.url, query_params)
        force_authenticate(req, user=self.client_user, token=self.token)
        # Insert aws exception task into result table
        task_result_expected_exception = self.task_result_expected.copy()
        task_result_expected_exception[health_utils.TYPE_CARBON_BLACK] = {
            health_utils.CONNECTION_STATUS: health_utils.UNKNOWN,
        }
        TaskResult.objects.create(
            task_name='tvui.async_tasks.celery_tasks.refresh_edr_health_fields',
            task_id='5f3bf3ee-bd17-412a-8cec-12c8b0aaee46',
            status='SUCCESS',
            result=json.dumps(task_result_expected_exception),
            date_created=datetime(2013, 3, 3, 3, 13),
        )
        resp = EDRHealthDetailsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.content.decode('utf-8'))
        assert resp_data.pop('updated_at')
        self.assertEqual(resp_data, self.expected_response_edr_details_carbon_black_exception)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'v3_4_unique_hosts_timespan': SimpleFlag(lambda: True)})
class UniqueHostCountTimespanV3_4Test(BasicAPIV3_4Tests):
    def setUp(self):
        super(UniqueHostCountTimespanV3_4Test, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.mock_get_hostname_and_default_ip = self.add_patch(
            'get_hostname_and_default_ip', patch('base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('myhostname', '*********'))
        )
        self.mock_requests_post = self.add_patch('post', mock.patch('base_tvui.lib_cloudbridge.make_post_request'))
        self.mock_requests_get = self.add_patch('get', mock.patch('base_tvui.lib_cloudbridge.make_get_request'))
        self.now = datetime(2023, 9, 30, 8, 0, 0)
        with freeze_time(self.now):
            for i in range(1, 12):
                HostAudit.objects.create(
                    month=f"2023-{i:02}",
                    host_id=1,
                    host_luid='xyz123',
                    host_session_luid="123xyz",
                    ip_address="*******",
                    host_artifact_value='test.vectra.io',
                    host_artifact_type='generic',
                    start=datetime(2023, i, 12, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                    end=datetime(2023, i, 17, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                )
                HostAudit.objects.create(
                    month=f"2023-{i:02}",
                    host_id=1,
                    host_luid=None,
                    host_session_luid="123xyz",
                    ip_address="*******",
                    host_artifact_value='test.vectra.io',
                    host_artifact_type='kerberos',
                    start=datetime(2023, i, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                    end=datetime(2023, i, 7, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                )
                HostAudit.objects.create(
                    month=f"2023-{i:02}",
                    host_id=1,
                    host_luid=None,
                    host_session_luid="456xyz",
                    ip_address="*******",
                    host_artifact_value='test.vectra.io',
                    host_artifact_type='kerberos',
                    start=datetime(2023, i, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                )

            # Started and ended early
            HostAudit.objects.create(
                month=f"2023-08",
                host_id=1,
                host_luid=None,
                host_session_luid="7891011xyz",
                ip_address="*******",
                host_artifact_value='test.vectra.io',
                host_artifact_type='kerberos',
                start=datetime(2023, 2, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                end=datetime(2023, 2, 15, 1, 8, 7, 127325, tzinfo=pytz.UTC),
            )
            # Started early, ongoing
            HostAudit.objects.create(
                month=f"2023-08",
                host_id=1,
                host_luid=None,
                host_session_luid="789xyz",
                ip_address="*******",
                host_artifact_value='test.vectra.io',
                host_artifact_type='kerberos',
                start=datetime(2022, 2, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
            )
            # Started and ended halfway through August
            HostAudit.objects.create(
                month=f"2023-{i:}",
                host_id=1,
                host_luid='123abd',
                host_session_luid="987xyz",
                ip_address="*******",
                host_artifact_value='test.vectra.io',
                host_artifact_type='kerberos',
                start=datetime(2023, 8, 15, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                end=datetime(2023, 8, 31, 1, 8, 7, 127325, tzinfo=pytz.UTC),
            )

    def tearDown(self):
        HostAudit.objects.all().delete()

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_get_one_year_host_audit(self):
        """Test Get One year of Host Audit Data"""
        with freeze_time(self.now):
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data['unique_hosts_observed'], 2)

    def test_cloudbridge_disabled(self):
        """Test endpoint for non CB enabled tenant"""
        url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        force_authenticate(req, token=self.token, user=self.user)
        response = UniqueHostCountTimespanV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(response.status_code, 200)
        resp_data = json.loads(response.content)
        self.assertEqual(resp_data['message'], CloudbridgeRouter.cloudbridge_disabled_message)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_start_date_provided(self):
        """Test start date query param"""
        with freeze_time(self.now):
            query_params = {'start': '2023-06-01'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data['unique_hosts_observed'], 3)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_get(self):
        """Test range query param"""
        with freeze_time(self.now):
            query_params = {'end': '2023-06-15'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data['unique_hosts_observed'], 1)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_get_after_out_of_range(self):
        """Test date range captures ongoing host but not ongoing host that hasn't started"""
        with freeze_time(self.now):
            query_params = {'start': '2023-08-01', 'end': '2023-08-14'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data['unique_hosts_observed'], 2)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_offset(self):
        """Test date range with offset"""
        with freeze_time(self.now):
            query_params = {'start': '2023-08-01', 'end': '2023-08-14', 'offset': +1230}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data['unique_hosts_observed'], 2)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_offset_negative(self):
        """Test date range with negative offset"""
        with freeze_time(self.now):
            query_params = {'start': '2023-08-01', 'end': '2023-08-14', 'offset': '-1230'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data['unique_hosts_observed'], 3)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_start_end_bad_param(self):
        """Test start after end date"""
        with freeze_time(self.now):
            query_params = {'start': '2024-07-15', 'end': '2023-08-14'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 400)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_offset_bad_param(self):
        """Test date range with invalid offset"""
        with freeze_time(self.now):
            query_params = {'start': '2023-07-15', 'end': '2023-08-14', 'offset': '%1230'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 400)
            query_params = {'start': '2023-07-15', 'end': '2023-08-14', 'offset': '+123990'}
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_count_month_boundary_query_validation(self):
        """Test unique host count month boundary query validation"""
        with freeze_time(self.now):
            query_params = {'start': '2024-09-15', 'end': '2024-10-14'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.content)
            self.assertEqual(data.get('error'), 'Invalid timerange provided')

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_count_no_data_error(self):
        """Test unique host count no data error"""
        with freeze_time(self.now):
            query_params = {'start': '2024-10-15', 'end': '2024-10-20'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 409)
            data = json.loads(response.content)
            self.assertEqual(data.get('error'), 'No host data available for given timerange. Start: 2024-10-15, End: 2024-10-20')

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_count_no_data_error_no_query_params(self):
        """Test unique host count no data error with no query params"""
        with freeze_time(self.now):
            HostAudit.objects.all().delete()
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            start, end = UniqueHostCountTimespanV3_4()._get_start_and_end_date(None, None, None)
            self.assertEqual(response.status_code, 409)
            data = json.loads(response.content)
            self.assertEqual(data.get('error'), f"No host data available for given timerange. Start: {start.date()}, End: {end.date()}")

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_count_success(self):
        """Test unique host count success"""
        with freeze_time(self.now):
            # Max value snapshot
            HostAuditSnapshot.objects.create(day='2024-10-15', max_count=10, max_count_timestamp=self.now, actual_count=10, audit_data=[])
            # Value that should not be returned
            HostAuditSnapshot.objects.create(day='2024-10-18', max_count=3, max_count_timestamp=self.now, actual_count=3, audit_data=[])
            query_params = {'start': '2024-10-15', 'end': '2024-10-20'}
            url = reverse('api-v3.4:api-uniqueHostCountTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostCountTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.content)
            self.assertEqual(data.get('unique_hosts_observed'), 10)


@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'v3_4_unique_hosts_timespan': SimpleFlag(lambda: True)})
class UniqueHostAuditTimespanV3_4Test(BasicAPIV3_4Tests):
    def setUp(self):
        super(UniqueHostAuditTimespanV3_4Test, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.mock_get_hostname_and_default_ip = self.add_patch(
            'get_hostname_and_default_ip', patch('base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('myhostname', '*********'))
        )
        self.mock_requests_post = self.add_patch('post', mock.patch('base_tvui.lib_cloudbridge.make_post_request'))
        self.mock_requests_get = self.add_patch('get', mock.patch('base_tvui.lib_cloudbridge.make_get_request'))
        self.now = datetime(2023, 9, 30, 8, 0, 0)
        with freeze_time(self.now):
            for i in range(1, 12):
                HostAudit.objects.create(
                    month=f"2023-{i:02}",
                    host_id=1,
                    host_luid='xyz123',
                    host_session_luid="123xyz",
                    ip_address="*******",
                    host_artifact_value='test.vectra.io',
                    host_artifact_type='generic',
                    start=datetime(2023, i, 12, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                    end=datetime(2023, i, 17, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                )
                HostAudit.objects.create(
                    month=f"2023-{i:02}",
                    host_id=1,
                    host_luid=None,
                    host_session_luid="123xyz",
                    ip_address="*******",
                    host_artifact_value='test.vectra.io',
                    host_artifact_type='kerberos',
                    start=datetime(2023, i, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                    end=datetime(2023, i, 7, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                )
                HostAudit.objects.create(
                    month=f"2023-{i:02}",
                    host_id=1,
                    host_luid=None,
                    host_session_luid="456xyz",
                    ip_address="*******",
                    host_artifact_value='test.vectra.io',
                    host_artifact_type='kerberos',
                    start=datetime(2023, i, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                )

            # Started and ended early
            HostAudit.objects.create(
                month=f"2023-08",
                host_id=1,
                host_luid=None,
                host_session_luid="7891011xyz",
                ip_address="*******",
                host_artifact_value='test.vectra.io',
                host_artifact_type='kerberos',
                start=datetime(2023, 2, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                end=datetime(2023, 2, 15, 1, 8, 7, 127325, tzinfo=pytz.UTC),
            )
            # Started early, ongoing
            HostAudit.objects.create(
                month=f"2023-08",
                host_id=1,
                host_luid=None,
                host_session_luid="789xyz",
                ip_address="*******",
                host_artifact_value='test.vectra.io',
                host_artifact_type='kerberos',
                start=datetime(2022, 2, 1, 1, 8, 7, 127325, tzinfo=pytz.UTC),
            )
            # Started and ended halfway through August
            HostAudit.objects.create(
                month=f"2023-{i:}",
                host_id=1,
                host_luid='123abd',
                host_session_luid="987xyz",
                ip_address="*******",
                host_artifact_value='test.vectra.io',
                host_artifact_type='kerberos',
                start=datetime(2023, 8, 15, 1, 8, 7, 127325, tzinfo=pytz.UTC),
                end=datetime(2023, 8, 31, 1, 8, 7, 127325, tzinfo=pytz.UTC),
            )

    def tearDown(self):
        HostAudit.objects.all().delete()

    def test_cloudbridge_disabled(self):
        """Test endpoint for non CB enabled tenant"""
        url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
        query_params = {'start': '2024-07-15', 'end': '2023-08-14'}
        factory = APIRequestFactory()
        req = factory.get(url, query_params)
        force_authenticate(req, token=self.token, user=self.user)
        response = UniqueHostAuditTimespanV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(response.status_code, 200)
        resp_data = json.loads(response.content)
        self.assertEqual(resp_data['message'], CloudbridgeRouter.cloudbridge_disabled_message)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_start_date_provided(self):
        """Test start date query param"""
        with freeze_time(self.now):
            query_params = {'start': '2023-06-01'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(len(data['results']), 15)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_get(self):
        """Test range query param"""
        with freeze_time(self.now):
            query_params = {'end': '2023-06-15'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(len(data['results']), 7)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_get_after_out_of_range(self):
        """Test date range captures ongoing host but not ongoing host that hasn't started"""
        with freeze_time(self.now):
            query_params = {'start': '2023-08-01', 'end': '2023-08-14'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(len(data['results']), 10)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_offset(self):
        """Test date range with offset"""
        with freeze_time(self.now):
            query_params = {'start': '2023-08-01', 'end': '2023-08-14', 'offset': +1230}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(len(data['results']), 10)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_offset_negative(self):
        """Test date range with negative offset"""
        with freeze_time(self.now):
            query_params = {'start': '2023-08-01', 'end': '2023-08-14', 'offset': '-1230'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(len(data['results']), 11)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_start_end_bad_param(self):
        """Test start after end date"""
        with freeze_time(self.now):
            query_params = {'start': '2024-07-15', 'end': '2023-08-14'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 400)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_range_offset_bad_param(self):
        """Test date range with invalid offset"""
        with freeze_time(self.now):
            query_params = {'start': '2023-07-15', 'end': '2023-08-14', 'offset': '%1230'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 400)
            query_params = {'start': '2023-07-15', 'end': '2023-08-14', 'offset': '+123990'}
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_audit_month_boundary_query_validation(self):
        """Test unique host audit month boundary query validation"""
        with freeze_time(self.now):
            query_params = {'start': '2024-09-15', 'end': '2024-10-14'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.content)
            self.assertEqual(data.get('error'), 'Invalid timerange provided')

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_audit_no_data_fail(self):
        """Test unique host audit no data fail"""
        with freeze_time(self.now):
            query_params = {'start': '2024-10-15', 'end': '2024-10-20'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 409)
            data = json.loads(response.content)
            self.assertEqual(data.get('error'), 'No host data available for given timerange. Start: 2024-10-15, End: 2024-10-20')

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_audit_success(self):
        """Test unique host audit success"""
        with freeze_time(self.now):
            # Max value snapshot
            HostAuditSnapshot.objects.create(
                day='2024-10-15', max_count=10, max_count_timestamp=self.now, actual_count=10, audit_data=[{'mock_data': 'mock_result'}]
            )
            # Value that should not be returned
            HostAuditSnapshot.objects.create(day='2024-10-18', max_count=3, max_count_timestamp=self.now, actual_count=3, audit_data=[{}])
            query_params = {'start': '2024-10-15', 'end': '2024-10-20'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(data['results'], [{'mock_data': 'mock_result'}])

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_unique_host_audit_compressed_data_success(self):
        """Test unique host audit with compressed data success"""
        with freeze_time(self.now):
            # Max value snapshot
            compressed_data = compress_list([{'mock_data': 'mock_result', 'last_seen': self.now}], 'gzip')
            HostAuditSnapshot.objects.create(
                day='2024-12-10',
                max_count=10,
                max_count_timestamp=self.now,
                actual_count=10,
                audit_data=None,
                compressed_audit_data=compressed_data,
                compression_type='gzip',
            )
            query_params = {'start': '2024-12-09', 'end': '2024-12-11'}
            url = reverse('api-v3.4:api-uniqueHostAuditTimespan-v3.4')
            factory = APIRequestFactory()
            req = factory.get(url, query_params)
            force_authenticate(req, token=self.token, user=self.user)
            response = UniqueHostAuditTimespanV3_4.as_view()(req)
            # verify endpoint
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.render().content)
            self.assertEqual(data['results'], [{'last_seen': '2023-09-30T08:00:00', 'mock_data': 'mock_result'}])


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class UniqueHostCountMonthlyV3_4Test(BasicAPIV3_4Tests):
    def setUp(self):
        super(UniqueHostCountMonthlyV3_4Test, self).setUp()
        self.url = '/v3.4/unique_hosts_observed'
        self.view_class = UniqueHostCountMonthlyV3_4
        self.cloud_api_client_setup()

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_permantly_moved_response(self):
        """Verify permantly moved response payload"""
        response = self.make_request(self.url, None, self.view_class, None)
        # verify endpoint
        self.assertEqual(response.status_code, 301)
        data = json.loads(response.content)
        self.assertEqual(data.get('message'), 'Moved permanently to /v3.4/unique_hosts_observed')

    def test_cloudbridge_disabled(self):
        """Test endpoint for non CB enabled tenant"""
        response = self.make_request(self.url, None, self.view_class, None)
        # verify endpoint
        self.assertEqual(response.status_code, 200)
        resp_data = json.loads(response.content)
        self.assertEqual(resp_data['message'], CloudbridgeRouter.cloudbridge_disabled_message)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class UniqueHostAuditMonthlyV3_4Test(BasicAPIV3_4Tests):
    def setUp(self):
        super(UniqueHostAuditMonthlyV3_4Test, self).setUp()
        self.url = '/v3.3/unique_hosts_observed/audit'
        self.view_class = UniqueHostAuditMonthlyV3_4
        self.cloud_api_client_setup()

    @patch('base_tvui.settings.CLOUD_ENV', new=VuiTestCase.get_dummy_cloud_envvars(CBI_MODE_ENABLED='YES'))
    def test_permantly_moved_response(self):
        """Verify permantly moved response payload"""
        response = self.make_request(self.url, None, self.view_class, None)
        # verify endpoint
        self.assertEqual(response.status_code, 301)
        data = json.loads(response.content)
        self.assertEqual(data.get('message'), 'Moved permanently to /v3.4/unique_hosts_observed/audit')

    def test_cloudbridge_disabled(self):
        """Test endpoint for non CB enabled tenant"""
        response = self.make_request(self.url, None, self.view_class, None)
        # verify endpoint
        self.assertEqual(response.status_code, 200)
        resp_data = json.loads(response.content)
        self.assertEqual(resp_data['message'], CloudbridgeRouter.cloudbridge_disabled_message)


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'platform/algo_health/enabled': SimpleFlag(lambda: True), 'cloudbridge': SimpleFlag(lambda: True)},
)
class APIV3_4HealthTest(BasicAPIV3_4Tests):
    """
    Test Health Endpoint in API v3.4
    """

    def setUp(self):
        super().setUp()

        self.now_dt = datetime(2024, 3, 8, 11, 0, tzinfo=timezone.utc)

        # create health data
        self.cpu_data = {"user_percent": 13.8, "nice_percent": 0, "system_percent": 5.6, "idle_percent": 79.9}
        self.cpu_health = HealthSubject.objects.create(subject_id="v2-health-api_cpu")
        HealthSubjectHistory.objects.create(health_subject=self.cpu_health, timestamp=self.now_dt, content=self.cpu_data, expires=None)

        self.disk_data = {
            "disk_utilization": {"total_bytes": 67306565632, "free_bytes": 39748980736, "used_bytes": 27557584896, "usage_percent": 40.94}
        }
        self.disk_health = HealthSubject.objects.create(subject_id="v2-health-api_disk")
        HealthSubjectHistory.objects.create(health_subject=self.disk_health, timestamp=self.now_dt, content=self.disk_data, expires=None)

        self.memory_data = {"usage_percent": 39.8, "free_bytes": 13021868032, "used_bytes": 28262363136, "total_bytes": 67444695040}
        self.memory_health = HealthSubject.objects.create(subject_id="v2-health-api_memory")
        HealthSubjectHistory.objects.create(
            health_subject=self.memory_health, timestamp=self.now_dt, content=self.memory_data, expires=None
        )

        self.network_data = {
            "interfaces": {"brain": {}, "sensors": {}},
            "traffic": {
                "brain": {"aggregated_peak_traffic_mbps": 0, "interface_peak_traffic": {}},
                "sensors": {
                    "*************": {"aggregated_peak_traffic_mbps": 0, "interface_peak_traffic": {"eth0": {"peak_traffic_mbps": 0}}},
                    "sc-dogfood-vs5": {"aggregated_peak_traffic_mbps": 570, "interface_peak_traffic": {"eth0": {"peak_traffic_mbps": 570}}},
                },
            },
            "vlans": {"vlan_ids": [], "count": 0},
        }
        self.network_health = HealthSubject.objects.create(subject_id="v2-health-api_network")
        HealthSubjectHistory.objects.create(
            health_subject=self.network_health, timestamp=self.now_dt, content=self.network_data, expires=None
        )

        self.power_data = {
            "status": "OK",
            "error": "Power supply OK",
            "power_supplies": {"1": {"faults": [], "present": True}, "2": {"faults": [], "present": True}},
        }
        self.power_health = HealthSubject.objects.create(subject_id="v2-health-api_power")
        HealthSubjectHistory.objects.create(health_subject=self.power_health, timestamp=self.now_dt, content=self.power_data, expires=None)

        self.sensor_data = [
            {
                "id": 13,
                "location": None,
                "serial_number": "V422c6a70147dadaa2c502f570478836b",
                "luid": "xxcbwo6u",
                "status": "paired",
                "version": "8.8.0-7-13",
                "ip_address": "**************",
                "ssh_tunnel_port": "36389",
                "public_key": "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxzOuTirVKEfCOFk5Rsgm\nnLmE0rB2nTXMiUWNLMfSvfhzgEhxtLoZt6gjVkus3ozco7gQC5V5tpiUcEPfkAAx\napFC3BHtKlN7cHKrHyXubCd2w/io8jb8n8c8nvvDyHx1h4+ixvQeaGy08rlXGxxP\nxOmJaHYfVVgX6kLNmQ9gQBdE1YysHY4/xWCrTneoAFt9dAH2E+qhKYkQLaW9MCU5\nMjpqI7kz6+N3WT+OKBQxqkGCDFRyRMC9lpPnNBp22CDrFwSVCDxSFAbuFLR8IDZP\n6v84DPFBml1vD4x3um76uUWAkaIdHia8CThdh3NTFI1AnGz3jsPX//zD5SOCQNXs\nfwIDAQAB\n-----END PUBLIC KEY-----\n",
                "product_name": "DCS",
                "mode": "sensor",
                "headend_uri": "*************",
                "original_version": "6.2.0-18-33",
                "last_seen": "2024-09-23T12:02:10.049Z",
                "update_count": 0,
                "name": "*************",
            }
        ]
        self.sensors_health = HealthSubject.objects.create(subject_id="v2-health-api_sensors")
        HealthSubjectHistory.objects.create(
            health_subject=self.sensors_health, timestamp=self.now_dt, content=self.sensor_data, expires=None
        )

        self.system_data = {
            "uptime": "2 hours, 23 minutes",
            "serial_number": "VHEf36c4f65e571181cbc756454aa1ecaa3",
            "version": {
                "last_update": "Sat Sep  7 04:52:50 2024",
                "model": "VHE",
                "mode": "brain",
                "cloud_bridge": False,
                "gmt": "2024-09-23T12:00:55.388751Z",
                "vm_type": "kvm",
                "vectra_instance_type": "dev",
                "vectra_version": "8.7.1-1-29",
            },
        }
        self.system_health = HealthSubject.objects.create(subject_id="v2-health-api_system")
        HealthSubjectHistory.objects.create(
            health_subject=self.system_health, timestamp=self.now_dt, content=self.system_data, expires=None
        )

        self.hostid_data = {
            "artifact_counts": {
                "TestEDR": 0,
                "TestEDR_0000": 0,
                "TestEDR_0001": 0,
                "TestEDR_0002": 0,
                "arsenic": 0,
                "carbon_black": 0,
                "cb_cloud": 0,
                "clear_state": 0,
                "cookie": 0,
                "crowdstrike": 56,
                "cybereason": 0,
                "dhcp": 41946,
                "dns": 6516954,
                "end_time": 0,
                "fireeye": 0,
                "generic_edr": 8300,
                "idle_end": 140544,
                "idle_start": 141939,
                "invalid": 0,
                "kerberos": 25031,
                "kerberos_user": 12669,
                "mdns": 7615,
                "netbios": 33964,
                "proxy_ip": 0,
                "rdns": 122309,
                "sentinelone": 0,
                "split": 6,
                "src_port": 0,
                "static_ip": 0,
                "total": 7469678,
                "uagent": 418291,
                "vmachine_info": 54,
                "windows_defender": 0,
                "zpa_user": 0,
            },
            "ip_always_percent": 68.49,
            "ip_sometimes_percent": 0.58,
            "ip_never_percent": 30.93,
        }
        self.hostid_health = HealthSubject.objects.create(subject_id="v2-health-api_hostid")
        HealthSubjectHistory.objects.create(
            health_subject=self.hostid_health, timestamp=self.now_dt, content=self.hostid_data, expires=None
        )

        self.connectivity_data = {
            "sensors": [
                {
                    "name": "*************",
                    "output": {"last_check": "2024-09-23T11"},
                    "error": "metadata replicaton seems fine",
                    "status": "OK",
                    "serial_number": "V422c6a70147dadaa2c502f570478836b",
                    "luid": "xxcbwo6u",
                    "ip_address": "**************",
                }
            ]
        }
        self.connectivity_health = HealthSubject.objects.create(subject_id="v2-health-api_connectivity")
        HealthSubjectHistory.objects.create(
            health_subject=self.connectivity_health, timestamp=self.now_dt, content=self.connectivity_data, expires=None
        )

        self.trafficdrop_data = {
            "sensors": {
                "name": "*************",
                "output": [],
                "error": "All interfaces have traffic volume within range",
                "status": "OK",
                "serial_number": "V422c6a70147dadaa2c502f570478836b",
                "luid": "xxcbwo6u",
                "ip_address": "**************",
            }
        }
        self.trafficdrop_health = HealthSubject.objects.create(subject_id="v2-health-api_trafficdrop")
        HealthSubjectHistory.objects.create(
            health_subject=self.trafficdrop_health, timestamp=self.now_dt, content=self.trafficdrop_data, expires=None
        )

        self.detection_data = {
            "check_results": [
                {
                    "name": "Detection Model Unhealthy",
                    "check_type": None,
                    "message": "All detection models are healthy.",
                    "status": "OK",
                },
            ]
        }
        self.detection_health = HealthSubject.objects.create(subject_id="v2-health-api_detection")
        HealthSubjectHistory.objects.create(
            health_subject=self.detection_health, timestamp=self.now_dt, content=self.detection_data, expires=None
        )

        self.health_data = {
            k: {**v, "updated_at": str(self.now_dt)} if isinstance(v, dict) else v
            for k, v in {
                "cpu": self.cpu_data,
                "disk": self.disk_data,
                "memory": self.memory_data,
                "network": self.network_data,
                "power": self.power_data,
                "sensors": self.sensor_data,
                "system": self.system_data,
                "hostid": self.hostid_data,
                "connectivity": self.connectivity_data,
                "trafficdrop": self.trafficdrop_data,
                "detection": self.detection_data,
            }.items()
        }

    def tearDown(self):
        HealthSubject.objects.all().delete()
        HealthSubjectHistory.objects.all().delete()

    def test_api_v3_4_health_unauthorized_fail(self):
        """Test V3.4 /health endpoint GET unauthorize fail"""
        # unauthorize health
        self.mock_perm_check.return_value = False

        health_url = reverse('api-v3.4:api-health-v3.4')
        check_url = reverse('api-v3.4:api-health-check-v3.4', args=["disk"])
        health_resp = self.client.get(health_url)
        check_resp = self.client.get(check_url)
        # verify response unauthorized
        self.assertEqual(health_resp.status_code, 403)
        self.assertEqual(check_resp.status_code, 403)

        # reset permissions
        self.mock_perm_check.return_value = True

    @patch.dict(
        'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
        {'platform/algo_health/enabled': SimpleFlag(lambda: False), 'cloudbridge': SimpleFlag(lambda: True)},
    )
    def test_api_v3_4_health_no_detection_data_wo_flag(self):
        url = reverse("api-v3.4:api-health-check-v3.4", kwargs={"check_type": "detection"})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = HealthV3_4.as_view()(req, check_type="detection")
        resp_json = json.loads(resp.render().content)
        # resp = self.client.get(url)
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_json, {"detection": {}})

    def test_api_v3_4_health_detection(self):
        url = reverse("api-v3.4:api-health-check-v3.4", kwargs={"check_type": "detection"})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = HealthV3_4.as_view()(req, check_type="detection")
        resp_json = json.loads(resp.render().content)
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_json, {"detection": {**self.detection_data, "updated_at": str(self.now_dt)}})

    def test_api_v3_4_detection_included_in_health(self):
        url = reverse("api-v3.4:api-health-v3.4")
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = HealthV3_4.as_view()(req)
        resp_json = json.loads(resp.render().content)
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_json, self.health_data)
        self.assertTrue("detection" in resp_json)

    def test_api_v3_4_invalid_check(self):
        url = reverse("api-v3.4:api-health-check-v3.4", kwargs={"check_type": "nonexistent"})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = '3.4'
        force_authenticate(req, token=self.token, user=self.user)
        resp = HealthV3_4.as_view()(req, check_type="nonexistent")
        resp_json = json.loads(resp.render().content)
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json, {"error": '"nonexistent" is not a valid health check'})


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups': SimpleFlag(lambda: True)})
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'unified_prioritization': SimpleFlag(lambda: True)})
@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'ad_groups': SimpleFlag(lambda: True)})
class GroupsV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Groups Endpoint in API v3.4
    """

    def setUp(self):
        super(GroupsV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)

        # feature flags
        setting.objects.create(group='feature', key='host_groups', value='on')
        setting.objects.create(group='feature', key='account_groups', value='on')

        # create accounts
        self.account_0 = Account.objects.create(uid='test_account_0', account_type=Account.TYPE_KERBEROS)
        self.account_1 = Account.objects.create(uid='test_account_1', account_type=Account.TYPE_KERBEROS)
        self.account_2 = Account.objects.create(uid='test_account_2', account_type=Account.TYPE_KERBEROS)

        # create smart rules
        self.sr = smart_rule.objects.create(
            type='typeA',
            priority=40,
            conditions=testing_utils.get_smart_rule_conditions(source_conditions=None, additional_conditions=None),
        )

        # create group
        self.accountgroup_description = 'desc'
        self.accountgroup_last_modified = self.now
        self.accountgroup_modified_by_username = 'vadmin'
        self.accountgroup_members = [self.account_0, self.account_2]
        self.accountgroup_rules = []
        self.accountgroup_name = 'Test Name'
        self.accountgroup_type = 'account'
        self.accountgroup_last_modified_by = self.user
        self.accountgroup_family = GroupCollection.FAMILY_CUSTOMER
        self.accountgroup_sr = [{'description': self.sr.description, 'id': self.sr.id, 'triage_category': self.sr.category}]
        self.accountgroup_importance = "medium"
        self.acct_rule = {"rule_conditions": {"OR": [{"AND": ["test_account_[\d]"]}]}}
        self.dynamic_acct_group = AccountGroup.objects.create(
            id=1, name='testAccountGroup', last_modified_by=self.user, rule=self.acct_rule, member_type='dynamic', description='desc'
        )
        self.dynamic_acct_group1 = AccountGroup.objects.create(
            name='testAccountGroup1',
            last_modified_by=self.user,
            rule=self.acct_rule,
            member_type='dynamic',
            member_eval_pending='1111-2222-333-444',
        )

        # AD groups
        self.ad_acct_group1 = AccountGroup.objects.create(
            name='testAdGroup1',
            last_modified_by=self.user,
            importance='low',
            member_type='active_directory',
            ad_group_dn='CN=TestGroup,OU=Groups,DC=example,DC=com',
        )

        self.ad_host_group1 = HostGroup.objects.create(
            name='testAdGroup2',
            last_modified_by=self.user,
            importance='high',
            member_type='active_directory',
            ad_group_dn='CN=Testing,OU=Groups,DC=example,DC=com',
        )

        self.test_ip_groups = IPGroup.objects.create(
            name='ip_grp1', description='Test 1 Internal IP Group', last_modified_by=self.user, ips='*************'
        )
        self.test_domain_groups = ExternalDomainGroup.objects.create(
            name='domain_grp1', description='Test 1 Internal Domain Group', last_modified_by=self.user, domains='domain_1.com'
        )
        # create account group collection
        self.accountgroup = AccountGroup.objects.create(
            name=self.accountgroup_name,
            type=self.accountgroup_type,
            last_modified_timestamp=self.accountgroup_last_modified,
            last_modified_by=self.accountgroup_last_modified_by,
            family=self.accountgroup_family,
            description=self.accountgroup_description,
            importance=self.accountgroup_importance,
            member_type='static',
            rule=None,
        )
        self.accountgroup.accountgroup.accounts.set(self.accountgroup_members)
        self.accountgroup.accountgroup.smart_rule_set.set([self.sr])

        self.group_list = [self.accountgroup]

        self.test_host = host.objects.create(name='host', state='active', last_source='*************')
        self.test_host2 = host.objects.create(name='host2', state='active', last_source='*************')
        self.test_host_group1 = HostGroup.objects.create(name='test1', type='host', last_modified_by=self.user, description='desc')
        self.test_host_group2 = HostGroup.objects.create(
            name='test2', type='host', description='Test 2 Host Group', last_modified_by=self.user
        )
        self.host_rule = {"rule_conditions": {"OR": [{"AND": ["VHE[\d]*"]}]}}
        self.dynamic_host_group1 = HostGroup.objects.create(
            name='testHostGroup1',
            last_modified_by=self.user,
            rule=self.host_rule,
            member_type='dynamic',
            member_eval_pending=None,
            description='desc',
        )

        self.test_host_group1.hosts.add(self.test_host)
        self.test_host_group2.hosts.add(self.test_host)

        self.url = reverse('api-v3.4:api-groups-v3.4')
        self.regex = '(\w\d)*@vectra.ai'
        self.invalid_regex = '(\w\d)*@(vectra.ai'
        self.factory = APIRequestFactory()

    def _validate_account_group_creation(self, response_group_id, post_payload):
        """
        uses the id from the response to locate the created account group,
        then compares the created account group object with data from the post_payload
        """
        account_group = AccountGroup.objects.get(id=response_group_id)
        self.assertEqual(account_group.name, post_payload['name'])
        self.assertEqual(account_group.description, post_payload['description'])
        self.assertEqual(account_group.type, post_payload['type'])
        self.assertEqual(account_group.last_modified_by.display_username, self.user.display_username)

    def _validate_host_group_creation(self, response_group_id, post_payload):
        """
        uses the id from the response to locate the created host group,
        then compares the created host group object with data from the post_payload
        """
        host_group = HostGroup.objects.get(id=response_group_id)
        self.assertEqual(host_group.name, post_payload['name'])
        self.assertEqual(host_group.description, post_payload['description'])
        self.assertEqual(host_group.type, post_payload['type'])
        self.assertEqual(host_group.last_modified_by.display_username, self.user.display_username)

    def test_api_v3_4_groups(self):
        """Test V3.4 Groups"""
        url = reverse('api-v3.4:api-groups-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(len(resp_data['results']), 10)

    def test_api_v3_4_groups_include_members_param(self):
        """Test V3.4 Groups with include_members param"""
        url = reverse('api-v3.4:api-groups-v3.4')
        url_params = {'include_members': 'false'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['results'][0]['members'], [])

    def test_api_v3_4_groups_is_regex_param(self):
        """Test V3.4 Groups with is_regex param"""
        url = reverse('api-v3.4:api-groups-v3.4')
        url_params = {'is_regex': 'True'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 3)

    def test_api_v3_4_groups_is_membership_evaluation_ongoing_param(self):
        """Test V3.4 Groups with is_membership_evaluation_ongoing param"""
        url = reverse('api-v3.4:api-groups-v3.4')
        url_params = {'is_membership_evaluation_ongoing': 'True'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 1)

    def test_api_v3_4_group(self):
        """Test V3.4 Single Group"""
        url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.test_host_group1.id})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupV3_4.as_view()(req, pk=self.test_host_group1.id)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.test_host_group1.name)
        self.assertEqual(resp_data["regex"], None)
        self.assertEqual(resp_data["member_count"], 1)
        self.assertEqual(resp_data["membership_evaluation_ongoing"], False)
        self.assertEqual(resp_data["built_using"], 'static_members')

    def test_api_v3_4_group_patch_members_host(self):
        """Test V3.4 PATCH Single Group with members for Host group"""
        url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.test_host_group1.id})
        data = {
            "name": "new static hg",
            "type": "host",
            "members": [self.test_host.id, self.test_host2.id],
        }
        factory = APIRequestFactory()
        req = factory.patch(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupV3_4.as_view()(req, pk=self.test_host_group1.id)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(len(resp_data["members"]), len(data["members"]))

    def test_api_v3_4_group_patch_members_domain(self):
        """Test V3.4 PATCH Single Group with members for domain groups"""
        url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.test_domain_groups.id})
        data = {
            "name": "new static domain group",
            "type": "domain",
            "members": ["domain1.com", "domain2.com"],
        }
        factory = APIRequestFactory()
        req = factory.patch(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupV3_4.as_view()(req, pk=self.test_domain_groups.id)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(len(resp_data["members"]), len(data["members"]))

    def test_api_v3_4_group_patch_members_ip(self):
        """Test V3.4 PATCH Single Group with members for domain groups"""
        url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.test_ip_groups.id})
        data = {
            "name": "new static ip group",
            "type": "ip",
            "members": ['************', '************'],
        }
        factory = APIRequestFactory()
        req = factory.patch(url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupV3_4.as_view()(req, pk=self.test_ip_groups.id)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(len(resp_data["members"]), len(data["members"]))

    def test_api_v3_4_dynamic_group(self):
        """Test V3.4 Single Dynamic Group"""
        url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_acct_group.id})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupV3_4.as_view()(req, pk=self.dynamic_acct_group.id)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.dynamic_acct_group.name)
        self.assertIsNotNone(resp_data["regex"])
        self.assertEqual(resp_data["built_using"], 'regex')

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_account_group(self):
        """Test V3.4 PATCH Account Single Dynamic Group"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_acct_group.id})
            data = {
                "name": self.dynamic_acct_group.name,
                "type": "account",
                "members": [],
                "description": "Different Description",
                "importance": "high",
                "regex": "test_account_[\d]",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_acct_group.id)
            resp_data = json.loads(resp.render().content)
            # verify endpoint
            self.assertEqual(resp.status_code, 200)
            self.assertEqual(resp_data["name"], self.dynamic_acct_group.name)
            self.assertEqual(resp_data["regex"], "test_account_[\d]")
            self.assertEqual(resp_data['description'], "Different Description")

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_account_group_invalid_regex(self):
        """Test V3.4 PATCH Account Single Dynamic Group with invalid regex"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_acct_group.id})
            data = {
                "name": self.dynamic_acct_group.name,
                "type": "account",
                "members": [],
                "description": "Different Description",
                "importance": "high",
                "regex": "(test_account_[\d]",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_acct_group.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_host_group(self):
        """Test V3.4 PATCH Host Single Dynamic Group"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_host_group1.id})
            data = {
                "name": "new hg name",
                "type": "host",
                "members": [],
                "description": "Different Description",
                "importance": "high",
                "regex": "host_[\d]?",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_host_group1.id)
            resp_data = json.loads(resp.render().content)
            # verify endpoint
            self.assertEqual(resp.status_code, 200)
            self.assertEqual(resp_data["name"], "new hg name")
            self.assertEqual(resp_data["regex"], "host_[\d]?")
            self.assertEqual(resp_data['description'], "Different Description")

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_host_group_invalid_regex(self):
        """Test V3.4 PATCH Host Single Dynamic Group errors with invalid regex"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_host_group1.id})
            data = {
                "name": "new hg name",
                "type": "host",
                "members": [],
                "description": "Different Description",
                "importance": "high",
                "regex": "host_[\d]?)",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_host_group1.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_host_group_with_members_errors(self):
        """Test V3.4 PATCH Host Single Dynamic Group errors if members included"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_host_group1.id})
            data = {
                "name": "new hg name",
                "type": "host",
                "members": [self.test_host.id],
                "description": "Different Description",
                "importance": "high",
                "regex": "host_[\d]?",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_host_group1.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_host_group_no_regex_errors(self):
        """Test V3.4 PATCH Host Single Dynamic Group errors with no regex"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_host_group1.id})
            data = {
                "name": "new hg name",
                "type": "host",
                "description": "Different Description",
                "importance": "high",
                "regex": None,
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_host_group1.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 400)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_IP_group_error(self):
        """Test V3.4 PATCH IP Single Dynamic Group"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.test_ip_groups.id})
            data = {
                "name": "ip group",
                "type": "IP",
                "members": [],
                "description": "Different Description",
                "importance": "high",
                "regex": "host_[\d]?",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.test_ip_groups.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 400)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_static_group_regex_errors(self):
        """Test V3.4 PATCH Account Single Dynamic Group with invalid regex"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.accountgroup.id})
            data = {
                "name": self.accountgroup.name,
                "type": "account",
                "members": [],
                "importance": "high",
                "regex": "(test_account_[\d])",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.accountgroup.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 400)

    def test_api_v3_4_groups_create_account(self):
        """Test V3.4 POST Account Group success"""
        data = {
            "name": "test_group_account_success",
            "type": "account",
            "members": [str(self.account_0.uid)],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 201)
        resp_data = json.loads(resp.content)
        self._validate_account_group_creation(resp_data['group']['id'], data)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_dynamic_groups_create_account(self):
        """Test V3.4 POST Account Dynamic Group success"""
        data = {
            "name": "test_dynamic_group_account_success",
            "type": "account",
            "regex": self.regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        resp_data = json.loads(resp.content)
        self.assertEqual(resp.status_code, 201)
        self._validate_account_group_creation(resp_data['group']['id'], data)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_dynamic_groups_create_account_invalid(self):
        """Test V3.4 POST Account Dynamic Group Invalid Regex"""
        data = {
            "name": "test_group_account_invalid_regex",
            "type": "account",
            "regex": self.invalid_regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_dynamic_groups_create_host(self):
        """Test V3.4 POST Host Dynamic Group success"""
        data = {
            "name": "test_dynamic_group_host_success",
            "type": "host",
            "regex": self.regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 201)
        resp_data = json.loads(resp.content)
        self._validate_host_group_creation(resp_data['group']['id'], data)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    def test_api_v3_4_dynamic_groups_create_host_with_members_errors(self):
        """Test V3.4 POST Host Dynamic Group success"""
        data = {
            "name": "test_dynamic_group_host_success",
            "type": "host",
            "members": [self.test_host.id],
            "regex": self.regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)

    def test_api_v3_4_groups_create_host(self):
        """Test V3.4 POST Host Group success"""
        data = {
            "name": "test_group_host_success",
            "type": "host",
            "members": [self.test_host.id],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        resp_data = json.loads(resp.content)
        self.assertEqual(resp.status_code, 201)
        self._validate_host_group_creation(resp_data['group']['id'], data)

    def test_api_v3_4_groups_create_host_invalid(self):
        """Test V3.4 POST Host Groups cannot have members and regex"""
        data = {
            "name": "test_group_host_members_and_regex",
            "type": "host",
            "members": [self.test_host.id],
            "regex": self.regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)

    def test_api_v3_4_dynamic_groups_create_host_invalid(self):
        """Test V3.4 POST Dynamic Group Host Invalid Regex"""
        data = {
            "name": "test_group_host_invalid_regex",
            "type": "host",
            "regex": self.invalid_regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)

    def test_api_v3_4_dynamic_groups_create_host_invalid_IP_type(self):
        """Test V3.4 POST Dynamic Group Host Invalid IP type"""
        data = {
            "name": "test_group_host_invalid_ip_type",
            "type": "IP",
            "regex": self.regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)

    def test_api_v3_4_groups_create_ip(self):
        """Test V3.4 POST IP Group success"""
        data = {
            "name": "test_group_ip",
            "type": "ip",
            "members": [],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 201)

    def test_api_v3_4_groups_create_domain(self):
        """Test V3.4 POST Domain Group success"""
        data = {
            "name": "test_group_domain",
            "type": "domain",
            "members": [],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 201)

    def test_api_v3_4_dynamic_groups_create_domain_invalid(self):
        """Test V3.4 POST Domain Dynamic Group Invalid"""
        data = {
            "name": "test_dynamic_group_domain",
            "type": "domain",
            "regex": self.regex,
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups': SimpleFlag(lambda: False)})
    def test_api_v3_4_groups_create_account_invalid_key(self):
        """Test V3.4 POST Account Group invalid key with dynamic_groups flag off"""
        data = {
            "name": "test_group_account_invalid_key",
            "type": "account",
            "members": [],
            "description": "Test Description",
            "importance": "high",
            "invalid key": "",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 400)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups': SimpleFlag(lambda: False)})
    def test_api_v3_4_groups_create_account_dynamic_groups_off(self):
        """Test V3.4 POST Account Group success with dynamic_groups flag off"""
        data = {
            "name": "test_group_account_feature_flag_off",
            "type": "account",
            "members": [],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 201)

    def test_api_v3_4_groups_create_account_invalid_members(self):
        """Test V3.4 POST Account Group success with invalid members"""
        data = {
            "name": "test_account_group_invalid_members",
            "type": "account",
            "members": [str(self.account_0.uid), "invalid", str(self.account_1.uid)],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)
        resp_data = json.loads(resp.content)
        self.assertEqual('Account ids not found: invalid', resp_data['_meta']['message']['members'])

    def test_api_v3_4_groups_create_host_invalid_members_id(self):
        """Test V3.4 POST Host Group with invalid members (id)"""
        invalid_id = "234345"
        data = {
            "name": "test_host_group_invalid_members",
            "type": "host",
            "members": [str(self.test_host.id), invalid_id],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)
        resp_data = json.loads(resp.content)
        self.assertEqual(f'Host ids not found: {invalid_id}', resp_data['_meta']['message']['members'])

    def test_api_v3_4_groups_create_host_invalid_members_name(self):
        """Test V3.4 POST Host Group with invalid members (name)"""
        invalid_name = "invalid"
        data = {
            "name": "test_host_group_invalid_members",
            "type": "host",
            "members": [str(self.test_host.name), invalid_name],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 422)
        resp_data = json.loads(resp.content)
        self.assertEqual(f'Host ids not found: {invalid_name}', resp_data['_meta']['message']['members'])

    def test_api_v3_4_post_host_group_with_empty_type(self):
        """Test V3.4 POST with empty type, which is not valid"""
        data = {
            "name": "test_empty_type",
            "members": [],
            "description": "Test Description",
            "importance": "high",
            "invalid key": "",
        }
        req = self.factory.post(self.url, data, format='json')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 400)

    def test_api_v3_4_post_host_group_with_invalid_content_type(self):
        """Test V3.4 POST with invalid content type type, which is not valid"""

        data = {
            "name": "test_group_account_feature_flag_off",
            "type": "account",
            "members": [],
            "description": "Test Description",
            "importance": "high",
        }
        req = self.factory.post(self.url, data, content_type='text/plain')
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        self.assertEqual(resp.status_code, 415)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups_member_transition': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_static_group_transition_to_dynamic_acct(self):
        """Test V3.4 PATCH Account Static to Dynamic Transition"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.accountgroup.id})
            data = {
                "name": self.accountgroup.name,
                "type": "account",
                "members": [],
                "importance": "high",
                "regex": "(test_account_[\d])",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.accountgroup.id)
            resp_data = json.loads(resp.render().content)
            # verify endpoint
            self.assertEqual(resp.status_code, 200)
            self.assertEqual(resp_data['built_using'], "regex")
            self.assertEqual(resp_data["regex"], "(test_account_[\d])")

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups_member_transition': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_group_transition_to_static_acct(self):
        """Test V3.4 PATCH Account Dynamic to Static Transition"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_acct_group.id})
            data = {
                "name": self.dynamic_acct_group.name,
                "type": "account",
                "members": [],
                "importance": "high",
                "regex": None,
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_acct_group.id)
            resp_data = json.loads(resp.render().content)
            # verify endpoint
            print(resp_data)
            self.assertEqual(resp.status_code, 200)

            self.assertEqual(resp_data['built_using'], "static_members")
            self.assertEqual(resp_data["regex"], None)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups_member_transition': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_static_group_transition_to_dynamic_host(self):
        """Test V3.4 PATCH Host Static to Dynamic Transition"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.test_host_group1.id})
            data = {
                "name": self.test_host_group1.name,
                "type": "host",
                "members": [],
                "importance": "high",
                "regex": "VHE[\d]*",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.test_host_group1.id)
            resp_data = json.loads(resp.render().content)
            # verify endpoint
            self.assertEqual(resp.status_code, 200)
            self.assertEqual(resp_data['built_using'], "regex")
            self.assertEqual(resp_data["regex"], "VHE[\d]*")

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups_member_transition': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_group_transition_to_static_host(self):
        """Test V3.4 PATCH Host Dynamic to Static Transition"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_host_group1.id})
            data = {
                "name": self.dynamic_host_group1.name,
                "type": "host",
                "members": [],
                "importance": "high",
                "regex": None,
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_host_group1.id)
            resp_data = json.loads(resp.render().content)
            # verify endpoint
            self.assertEqual(resp.status_code, 200)
            self.assertEqual(resp_data['built_using'], "static_members")
            self.assertEqual(resp_data["regex"], None)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups_member_transition': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_dynamic_without_rule(self):
        """Test V3.4 PATCH Host Dynamic Without Rule Change"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.dynamic_host_group1.id})
            data = {
                "name": self.dynamic_host_group1.name,
                "type": "host",
                "members": [],
                "importance": "medium",
            }
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.dynamic_host_group1.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 422)
            # If regex is not explicitly set to None, we do not transition the group
            host_group = GroupCollection.objects.get(id=self.dynamic_host_group1.id)
            self.assertEqual(host_group.member_type, "dynamic")
            self.assertEqual(host_group.rule, self.host_rule)

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'enable_async_tasks': SimpleFlag(lambda: True)})
    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'dynamic_groups_member_transition': SimpleFlag(lambda: True)})
    def test_api_v3_4_patch_transition_static_with_invalid_rule(self):
        """Test V3.4 PATCH Host Static Transition with Invalid Rule"""
        with self.cloud_env():
            url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.accountgroup.id})
            data = {"name": self.accountgroup.name, "type": "account", "members": [], "importance": "medium", "regex": self.invalid_regex}
            factory = APIRequestFactory()
            req = factory.patch(url, data, format='json')
            req.api_version = 3.4
            force_authenticate(req, token=self.token, user=self.user)
            resp = GroupV3_4.as_view()(req, pk=self.accountgroup.id)
            # verify endpoint
            self.assertEqual(resp.status_code, 422)
            # Expect the static group to stay as is, until valid regex used in body
            acct_group = GroupCollection.objects.get(id=self.accountgroup.id)
            self.assertEqual(acct_group.member_type, "static")
            self.assertEqual(acct_group.rule, {})

    @override_settings(CELERY_TASK_EAGER_PROPAGATES=True, CELERY_TASK_ALWAYS_EAGER=True, BROKER_BACKEND='memory')
    def test_api_v3_4_get_groups_limit(self):
        """Test V3.4 GET Groups with 2000 member limit"""
        accounts_to_create = []
        for i in range(2001):
            account = {
                'uid': f'test{i}@account.com',
                'account_type': account_type.AccountType.KERBEROS,
                'first_seen': self.now,
                'last_seen': self.now,
            }
            accounts_to_create.append(Account(**account))

        accts = Account.objects.bulk_create(accounts_to_create, 2001)
        self.accountgroup.accounts.add(*accts)
        self.accountgroup.save()
        url = reverse('api-v3.4:api-groups-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["count"], 10)
        for group in resp_data["results"]:
            self.assertLessEqual(len(group["members"]), 2000)

    def test_api_v3_4_get_groups_empty_members(self):
        """Test V3.4 GET Groups with empty members"""
        self.no_member_account = AccountGroup.objects.create(
            name='no_member_account',
            last_modified_by=self.user,
            member_type='static',
        )

        self.no_member_ip = IPGroup.objects.create(name='no_member_ip', description='Test 1 Internal IP Group', last_modified_by=self.user)
        self.no_member_domain = ExternalDomainGroup.objects.create(
            name='no_member_domain', description='Test 1 Internal Domain Group', last_modified_by=self.user
        )

        self.no_member_host = HostGroup.objects.create(
            name='no_member_host', description='Test 1 Internal Host Group', last_modified_by=self.user
        )
        url = reverse('api-v3.4:api-groups-v3.4')
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)

    def test_api_v3_4_ad_groups_is_ad_group_param(self):
        """Test V3.4 Groups with is_ad_group param"""
        url = reverse('api-v3.4:api-groups-v3.4')
        url_params = {'is_ad_group': 'True'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 2)

    def test_api_v3_4_ad_groups_ad_group_dn_param(self):
        """Test V3.4 AD Groups with ad_group_dn param"""
        url = reverse('api-v3.4:api-groups-v3.4')
        url_params = {'ad_group_dn': 'TestGroup'}
        full_url = f'{url}?{urllib.parse.urlencode(url_params)}'
        factory = APIRequestFactory()
        req = factory.get(full_url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupsV3_4.as_view()(req)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        resp_data = json.loads(resp.render().content)
        self.assertEqual(resp_data['count'], 1)

    def test_api_v3_4_ad_group(self):
        """Test V3.4 Single AD Group"""
        url = reverse('api-v3.4:api-group-v3.4', kwargs={'pk': self.ad_acct_group1.id})
        factory = APIRequestFactory()
        req = factory.get(url)
        req.api_version = 3.4
        force_authenticate(req, token=self.token, user=self.user)
        resp = GroupV3_4.as_view()(req, pk=self.ad_acct_group1.id)
        resp_data = json.loads(resp.render().content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data["name"], self.ad_acct_group1.name)
        self.assertEqual(resp_data["ad_group_dn"], 'CN=TestGroup,OU=Groups,DC=example,DC=com')
        self.assertEqual(resp_data["built_using"], 'ad_group')


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class BulkNotesV3_4Tests(BasicAPIV3_4Tests):
    """
    Test Bulk Notes Endpoint Api V3.4
    """

    def setUp(self):
        super(BulkNotesV3_4Tests, self).setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.now = datetime(2023, 3, 17, 8, 0, 0)

        self.account_0 = LinkedAccount.objects.create(id=11, display_uid='linked_account_0')

        self.o365_account = Account.objects.create(uid='<EMAIL>', account_type=Account.TYPE_O365)

        self.test_det_0 = detection.objects.create(
            account=self.o365_account,
            type=DetectionType.O365_SUSPICIOUS_MAILBOX_RULE,
            type_vname='O365 Suspicious Mailbox Rule',
            src_ip='**********',
            last_timestamp=self.now - timedelta(minutes=1),
        )

        # create hosts
        self.host_0 = host.objects.create(name='mostly_mchostface', state='active', t_score=99, c_score=50, key_asset=True)
        self.host_1 = host.objects.create(name='other_host', state='active', t_score=99, c_score=50, key_asset=True)
        # create notes for host
        with freeze_time(self.now):
            self.note1 = notes.objects.create(type='host', type_id=self.host_0.id, note='this is note 1', created_by=self.user)
        with freeze_time(self.now + timedelta(hours=1)):
            self.note2 = notes.objects.create(type='host', type_id=self.host_0.id, note='this is note 2', created_by=self.user)

    def tearDown(self):
        host.objects.all().delete()
        notes.objects.all().delete()

    def test_create_notes_v3_4(self):
        # create a host note using endpoint
        url = reverse('api-v3.4:api-notes-v3.4-bulk-create', kwargs={'tvui_type': 'host'})
        resp = self.client.post(url, {'note': 'test create note', 'objectIds': [self.host_0.id, self.host_1.id]}, format='json')
        self.assertEqual(resp.status_code, 201)
        self.assertEqual(notes.objects.count(), 4)

    def test_create_type_notes_v3_4(self):
        # create a host note with the type query param
        url = (
            reverse(
                'api-v3.4:api-notes-v3.4-bulk-create',
                kwargs={
                    'tvui_type': 'entitie',
                },
            )
            + "?type=host"
        )
        resp = self.client.post(url, {'note': 'test create note', 'objectIds': [self.host_0.id, self.host_1.id]}, format='json')
        self.assertEqual(resp.status_code, 201)
        self.assertEqual(notes.objects.count(), 4)
        expected_resp = {'status': "success"}
        self.assertEqual(expected_resp, resp.json())

    def test_create_notes_invalid_entity_type_query_param_v3_3(self):
        # verify the entyt_type query param is invalid
        url = (
            reverse(
                'api-v3.4:api-notes-v3.4-bulk-create',
                kwargs={
                    'tvui_type': 'host',
                },
            )
            + "?entity_type=host"
        )
        resp = self.client.post(url, {'note': 'test create note', 'objectIds': [self.host_0.id, self.host_1.id]}, format='json')
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(notes.objects.count(), 2)
        expected_resp = {'detail': "The following query parameter(s) are invalid: entity_type"}
        self.assertEqual(expected_resp, resp.json())


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class NotificationRecieverV3_4Tests(BasicAPIV3_4Tests):
    def setUp(self):
        super(NotificationRecieverV3_4Tests, self).setUp()
        self.url = '/v3.4/notification/receivers'
        self.view_class = NotificationReceiversBaseView
        self.cloud_api_client_setup()
        self.list_post_actions = {'get': 'list', 'post': 'post'}
        self.get_patch_delete_actions = {'get': 'get', 'patch': 'patch', 'delete': 'delete'}

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'nes_events': SimpleFlag(lambda: True)})
    def test_not_implemented_response_list(self):
        """Verify v3.4/notifiation/receivers GET not implemented response payload"""
        response = self.make_request(self.url, None, self.view_class, None, 'GET', self.list_post_actions)
        # verify endpoint
        self.assertEqual(response.status_code, 501)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'nes_events': SimpleFlag(lambda: True)})
    def test_not_implemented_response_post(self):
        """Verify v3.4/notifiation/receivers POST not implemented response payload"""
        response = self.make_request(self.url, None, self.view_class, None, 'POST', self.list_post_actions)
        # verify endpoint
        self.assertEqual(response.status_code, 501)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'nes_events': SimpleFlag(lambda: True)})
    def test_not_implemented_response_get(self):
        """Verify v3.4/notifiation/receivers/id GET not implemented response payload"""
        dummy_id_url = self.url + '/1'
        response = self.make_request(dummy_id_url, None, self.view_class, 1, 'GET', self.get_patch_delete_actions)
        # verify endpoint
        self.assertEqual(response.status_code, 501)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'nes_events': SimpleFlag(lambda: True)})
    def test_not_implemented_response_patch(self):
        """Verify v3.4/notifiation/receivers/id PATCH not implemented response payload"""
        dummy_id_url = self.url + '/1'
        response = self.make_request(dummy_id_url, None, self.view_class, 1, 'PATCH', self.get_patch_delete_actions)
        # verify endpoint
        self.assertEqual(response.status_code, 501)

    @patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'nes_events': SimpleFlag(lambda: True)})
    def test_not_implemented_response_delete(self):
        """Verify v3.4/notifiation/receivers/id DELETE not implemented response payload"""
        dummy_id_url = self.url + '/1'
        response = self.make_request(dummy_id_url, None, self.view_class, 1, 'DELETE', self.get_patch_delete_actions)
        # verify endpoint
        self.assertEqual(response.status_code, 501)


@patch.dict('base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags', {'ad_groups': SimpleFlag(lambda: True)})
class ActiveDirectoryGroupsV3_4Test(BasicAPIV3_4Tests):
    """
    Test settings/active_directory/groups/ Endpoint in API v3.4
    """

    def setUp(self):
        super().setUp()
        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.url = reverse('api-v3.4:api-settings-groups-active-directory-v3.4')
        self.get_ldap_service_top_config_multi_result = {
            '_id': 'ldap_service_multi',
            'name': 'ldap_service_multi',
            'profiles': {
                'sdasjlj2': {
                    "autobind": "NONE",
                    "basedns": ["DC=qe-ad,DC=test"],
                    "bind_dn": "CN=Albert Einstein,CN=Users,DC=qe-ad,DC=test",
                    "bind_pwd": "encr:5dS3FIA+dX0zzmR6s4ua82cET0OOVDv2MQmtyeVKkXVnBE9DjlQ79jEJrcnlSpF1ZwRPQ45UO/YxCa3J5UqRdWcET0OOVDv2MQmtyeVKkXVnBE9DjlQ79jEJrcnlSpF1ZwRPQ45UO/YxCa3J5UqRdWcET0OOVDv2MQmtyeVKkXU=",
                    "cache_update_speed": 7200,
                    "connection_timeout": 180,
                    "enabled": True,
                    "ldaps": False,
                    "profile_name": "queryprofile",
                    "ad_profile_name": "ad-profile-1",
                    "query_timeout": 30,
                    "search_param": "sAMAccountName",
                    "server_uris": ["**************"],
                    "starttls": False,
                },
            },
        }
        self.ldap_groups_multi_all_result = {
            'sdasjlj2': [
                "cn=aws-sso-vectra-log-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
                "cn=data-science-team,ou=security groups,ou=vectra,dc=vectra,dc=io",
                "cn=aws-sso-saas-dns-root-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
            ],
        }
        self.settings_groups_active_directory_result = {
            'ad-profile-1': {
                'groups': [
                    "cn=aws-sso-vectra-log-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
                    "cn=data-science-team,ou=security groups,ou=vectra,dc=vectra,dc=io",
                    "cn=aws-sso-saas-dns-root-admin,ou=aws,ou=security groups,ou=vectra,dc=vectra,dc=io",
                ],
            },
        }

    @patch.object(LdapClient, 'get_ldap_service_top_config_multi')
    @patch.object(LdapClient, 'get_ldap_groups_multi')
    def test_api_v3_4_get_settings_groups_active_directory(self, ldap_groups_multi_get_request, get_ldap_service_top_config_multi_request):
        """Test V3.4 GET settings groups active directory"""
        ldap_groups_multi_get_request.return_value.status_code = 200
        ldap_groups_multi_get_request.return_value = self.ldap_groups_multi_all_result
        get_ldap_service_top_config_multi_request.return_value = self.get_ldap_service_top_config_multi_result
        resp = self.client.get(self.url)
        resp_data = json.loads(resp.content)
        # verify endpoint
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp_data, self.settings_groups_active_directory_result)

    @patch.object(LdapClient, 'get_ldap_groups_multi')
    def test_api_v3_4_get_settings_groups_active_directory_exception(self, ldap_groups_multi_get_request):
        """Test V3.4 GET settings groups active directory exception"""
        ldap_groups_multi_get_request.return_value.status_code = 500
        ldap_groups_multi_get_request.return_value = Exception()
        resp = self.client.get(self.url)
        # verify endpoint
        self.assertEqual(resp.status_code, 500)
        self.assertEqual(resp.json(), {'error': 'Unable to fetch AD groups. Please retry.'})


@patch.dict(
    'base_tvui.feature_flipper.flags.DEFAULT_REGISTRY._flags',
    {'signal_efficacy_closed_as': SimpleFlag(lambda: True), 'signal_efficacy_public_preview': SimpleFlag(lambda: True)},
)
class AccountCloseV3_4Test(BasicAPIV3_4Tests):

    def setUp(self):
        super().setUp()

        # Create accounts for detections
        self.account_obj = Account.objects.create(uid='account@o365_1.com', account_type=Account.TYPE_O365)
        self.linked_account_obj = LinkedAccount.objects.get(subaccounts__id=self.account_obj.id)

        # Create detections for tests
        self.detection_1 = detection.objects.create(
            type=DetectionType.HIDDEN_DNS_TUNNEL_CNC,
            type_vname='M365 Suspect Power Automate Activity',
            category='INFO',
            c_score=10,
            t_score=10,
            last_timestamp=datetime(2019, 5, 4, 12, 0, 0, tzinfo=timezone.utc),
            account=self.account_obj,
            state=detection.ACTIVE,
        )

        self.client.force_authenticate(user=self.client_user, token=self.token)
        self.url = reverse('api-v3.4:api-account-close-v3.4', kwargs={'pk': self.linked_account_obj.id})

        self.patch_rescore_accounts = patch('base_tvui.lib_account.rescore_accounts')
        self.patch_rescore_accounts.start()
        self.addCleanup(self.patch_rescore_accounts.stop)

    def test_account_close_as_remediated(self):
        """
        Test correct values are set when closing an account as remediated
        """
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.linked_account_obj.refresh_from_db()
        self.detection_1.refresh_from_db()

        closed_account = CloseHistory.objects.filter(linked_account_id=self.linked_account_obj.id).exclude(detection_id=self.detection_1.id)
        closed_detection = CloseHistory.objects.get(detection_id=self.detection_1.id)
        resp_json = resp.json()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(closed_account.count(), 1)
        self.assertEqual(self.linked_account_obj.state, 'inactive')
        self.assertEqual(closed_account[0].reason, StateReasons.REMEDIATED.value)
        self.assertEqual(closed_detection.linked_account_id, self.linked_account_obj.id)
        self.assertEqual(closed_detection.reason, StateReasons.REMEDIATED.value)
        self.assertEqual(resp_json['detections_closed'], [self.detection_1.id])

    def test_account_close_as_benign(self):
        """
        Test correct values are set when closing an account as benign
        """
        data = json.dumps({'reason': StateReasons.BENIGN.value})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        self.linked_account_obj.refresh_from_db()
        self.detection_1.refresh_from_db()

        closed_account = CloseHistory.objects.filter(linked_account_id=self.linked_account_obj.id).exclude(detection_id=self.detection_1.id)
        closed_detection = CloseHistory.objects.get(detection_id=self.detection_1.id)
        resp_json = resp.json()

        self.assertEqual(resp.status_code, 200)
        self.assertEqual(closed_account.count(), 1)
        self.assertEqual(self.linked_account_obj.state, 'inactive')
        self.assertEqual(closed_account[0].reason, StateReasons.BENIGN.value)
        self.assertEqual(closed_detection.linked_account_id, self.linked_account_obj.id)
        self.assertEqual(closed_detection.reason, StateReasons.BENIGN.value)
        self.assertTrue(self.detection_1.is_filtered_by_user)
        self.assertEqual(resp_json['detections_closed'], [self.detection_1.id])

    def test_account_does_not_exist(self):
        """
        Test Exception is raised when account does not exist for given account id
        """
        url = reverse('api-v3.4:api-account-close-v3.4', kwargs={'pk': **********})
        data = json.dumps({'reason': StateReasons.REMEDIATED.value})
        resp = self.client.patch(url, data=data, content_type='application/json')

        resp_json = resp.json()
        self.assertEqual(resp.status_code, 404)
        self.assertEqual(resp_json['detail'], 'Not found.')

    def test_account_close_invalid_reason(self):
        """
        Test Exception is raised when an invalid reason is provided
        """
        data = json.dumps({'reason': 'invalid'})
        resp = self.client.patch(self.url, data=data, content_type='application/json')
        resp_json = resp.json()

        self.assertEqual(resp.status_code, 422)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Invalid reason provided. Valid values are remediated or benign.')

    def test_account_close_missing_reason(self):
        """
        Test Exception is raised when reason is missing
        """
        data = json.dumps({})
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = resp.json()
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message']['reason'], 'Reason is required')

    def test_account_close_invalid_json(self):
        """
        Test Exception is raised when invalid JSON is provided
        """
        data = '{'
        resp = self.client.patch(self.url, data=data, content_type='application/json')

        resp_json = resp.json()
        self.assertEqual(resp.status_code, 400)
        self.assertEqual(resp_json['_meta']['message'], 'Payload must be valid JSON')
