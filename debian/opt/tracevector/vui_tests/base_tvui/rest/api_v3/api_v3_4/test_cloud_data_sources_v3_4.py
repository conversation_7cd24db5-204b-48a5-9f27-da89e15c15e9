# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import json
from unittest.mock import patch, MagicMock
from django.test import override_settings
from django.urls import reverse
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile, CloudSensor
from vui_tests.vui_testcase import VuiTestCase


class BasicCloudDataSourcesV3_4Tests(VuiTestCase):
    """Base test class for CloudDataSourcesV3_4 tests"""
    
    def setUp(self):
        super().setUp()
        
        # Create test user
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        
        # Mock permission check
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True
        
        # Create API client user
        self.client_user = User.objects.create_user(
            username='api_client_test',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='test_client', name='Test Client', description='Test API Client', created_by=self.user
            ),
        )
        
        # Setup API client
        self.client = APIClient(REMOTE_ADDR='************')
        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)
        self.client.force_authenticate(user=self.client_user, token=self.token)
        
        # Test data
        self.sample_sensor_data = [
            {
                'source_id': 'aws-123',
                'sensor_name': 'AWS Connector 1',
                'name': 'AWS Connector 1',
                'sensor_type': 'AWS',
                'serial_number': 'aws-serial-123',
                'status': 'LOGS_FLOWING',
                'region': 'us-east-1',
                'last_seen': '2024-01-01T12:00:00Z',
                'forwardingState': {
                    'icon': 'success',
                    'statusText': 'Logs Flowing',
                    'message': 'Connector is actively ingesting data',
                    'showLastSeen': True
                }
            },
            {
                'source_id': 'o365-456',
                'sensor_name': 'Office 365 Connector',
                'name': 'Office 365 Connector',
                'sensor_type': 'O365',
                'serial_number': 'o365-serial-456',
                'status': 'CREATED',
                'region': 'global',
                'last_seen': '2024-01-01T11:00:00Z',
                'forwardingState': {
                    'icon': 'warning',
                    'statusText': 'Pairing',
                    'message': 'Connector requires authorization',
                    'showLastSeen': False
                }
            },
            {
                'source_id': 'azure-789',
                'sensor_name': 'Azure Connector',
                'name': 'Azure Connector',
                'sensor_type': 'AZURE',
                'serial_number': 'azure-serial-789',
                'status': 'ERROR',
                'region': 'eastus',
                'last_seen': '2024-01-01T10:00:00Z',
                'error': {'message': 'Authentication failed'},
                'forwardingState': {
                    'icon': 'alert',
                    'statusText': 'Error',
                    'message': 'Authentication failed',
                    'showLastSeen': True
                }
            }
        ]


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class CloudDataSourcesV3_4Tests(BasicCloudDataSourcesV3_4Tests):
    """Test CloudDataSourcesV3_4 endpoint functionality"""
    
    def setUp(self):
        super().setUp()
        self.url = reverse('api-v3.4:api-cloud-data-sources-v3.4')
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_get_cloud_data_sources_success(self, mock_annotate, mock_get_sensors):
        """Test successful retrieval of cloud data sources"""
        # Setup mocks
        mock_get_sensors.return_value = self.sample_sensor_data.copy()
        mock_annotate.side_effect = lambda x: x  # Pass through without modification
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Verify structure
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertEqual(len(data['results']), 3)
        
        # Verify first result structure
        first_result = data['results'][0]
        expected_fields = ['id', 'name', 'type', 'region', 'status', 'health', 'created_at', 'last_seen']
        for field in expected_fields:
            self.assertIn(field, first_result)
        
        # Verify health mapping
        self.assertEqual(first_result['health']['status'], 'connected')
        
        # Verify mocks were called
        mock_get_sensors.assert_called_once()
        self.assertEqual(mock_annotate.call_count, 3)
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_empty_list(self, mock_get_sensors):
        """Test endpoint returns empty list when no sensors exist"""
        # Setup mock
        mock_get_sensors.return_value = []
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 0)
        self.assertEqual(data['count'], 0)
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_filters_deleted(self, mock_get_sensors):
        """Test endpoint filters out deleted connectors"""
        # Add deleted sensor to test data
        test_data = self.sample_sensor_data.copy()
        test_data.append({
            'source_id': 'deleted-123',
            'sensor_name': 'Deleted Connector',
            'sensor_type': 'AWS',
            'status': 'DELETED'
        })
        
        mock_get_sensors.return_value = test_data
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Should only return 3 active sensors, not the deleted one
        self.assertEqual(len(data['results']), 3)
        
        # Verify no deleted sensors in results
        for result in data['results']:
            self.assertNotEqual(result['id'], 'deleted-123')
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_database_error(self, mock_get_sensors):
        """Test endpoint handles database errors gracefully"""
        # Setup mock to raise exception
        mock_get_sensors.side_effect = Exception("Database connection failed")
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn('Unable to retrieve cloud data sources', str(data))
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_get_cloud_data_sources_annotation_error(self, mock_annotate, mock_get_sensors):
        """Test endpoint handles status annotation errors gracefully"""
        # Setup mocks
        mock_get_sensors.return_value = self.sample_sensor_data[:1]  # Just one sensor
        mock_annotate.side_effect = Exception("Status annotation failed")
        
        # Make request
        response = self.client.get(self.url)
        
        # Should still succeed even if annotation fails
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data['results']), 1)
    
    def test_get_cloud_data_sources_unauthorized(self):
        """Test endpoint requires authentication"""
        # Remove authentication
        self.client.credentials()
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify unauthorized response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_pagination(self, mock_get_sensors):
        """Test endpoint pagination works correctly"""
        # Create larger dataset
        large_dataset = []
        for i in range(25):  # More than default page size
            large_dataset.append({
                'source_id': f'sensor-{i}',
                'sensor_name': f'Sensor {i}',
                'sensor_type': 'AWS',
                'status': 'LOGS_FLOWING'
            })
        
        mock_get_sensors.return_value = large_dataset
        
        # Make request with page size
        response = self.client.get(self.url, {'page_size': 10})
        
        # Verify pagination
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(len(data['results']), 10)
        self.assertEqual(data['count'], 25)
        self.assertIn('next', data)
        self.assertIsNone(data['previous'])
    
    def test_post_method_not_allowed(self):
        """Test POST method is not allowed"""
        response = self.client.post(self.url, {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
    
    def test_put_method_not_allowed(self):
        """Test PUT method is not allowed"""
        response = self.client.put(self.url, {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
    
    def test_delete_method_not_allowed(self):
        """Test DELETE method is not allowed"""
        response = self.client.delete(self.url)
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)


class CloudDataSourceSerializerV3_4Tests(BasicCloudDataSourcesV3_4Tests):
    """Test CloudDataSourceSerializerV3_4 serializer functionality"""
    
    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
        self.request.user = self.client_user
    
    def test_serializer_basic_fields(self):
        """Test serializer maps basic fields correctly"""
        sensor_data = self.sample_sensor_data[0]
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})
        
        result = serializer.data
        
        self.assertEqual(result['id'], 'aws-123')
        self.assertEqual(result['name'], 'AWS Connector 1')
        self.assertEqual(result['type'], 'AWS')
        self.assertEqual(result['region'], 'us-east-1')
        self.assertEqual(result['status'], 'LOGS_FLOWING')
    
    def test_serializer_health_mapping_connected(self):
        """Test serializer maps connected health status correctly"""
        sensor_data = self.sample_sensor_data[0]  # LOGS_FLOWING with success icon
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})
        
        result = serializer.data
        health = result['health']
        
        self.assertEqual(health['status'], 'connected')
        self.assertIn('actively ingesting', health['message'])
    
    def test_serializer_health_mapping_connecting(self):
        """Test serializer maps connecting health status correctly"""
        sensor_data = self.sample_sensor_data[1]  # CREATED with warning icon
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})
        
        result = serializer.data
        health = result['health']
        
        self.assertEqual(health['status'], 'connecting')
        self.assertIn('authorization', health['message'])
    
    def test_serializer_health_mapping_error(self):
        """Test serializer maps error health status correctly"""
        sensor_data = self.sample_sensor_data[2]  # ERROR with alert icon
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})
        
        result = serializer.data
        health = result['health']
        
        self.assertEqual(health['status'], 'error')
        self.assertIn('Authentication failed', health['message'])
    
    def test_serializer_fallback_health_mapping(self):
        """Test serializer fallback health mapping without forwardingState"""
        sensor_data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Sensor',
            'sensor_type': 'AWS',
            'status': 'LOGS_FLOWING'
            # No forwardingState
        }
        
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})
        result = serializer.data
        health = result['health']
        
        self.assertEqual(health['status'], 'connected')
        self.assertIn('actively ingesting', health['message'])
    
    def test_serializer_handles_missing_fields(self):
        """Test serializer handles missing optional fields gracefully"""
        minimal_sensor_data = {
            'source_id': 'minimal-123',
            'status': 'UNKNOWN'
        }
        
        serializer = CloudDataSourceSerializerV3_4(minimal_sensor_data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['id'], 'minimal-123')
        self.assertIsNone(result['name'])
        self.assertIsNone(result['type'])
        self.assertIsNone(result['region'])
        self.assertEqual(result['health']['status'], 'unknown')
