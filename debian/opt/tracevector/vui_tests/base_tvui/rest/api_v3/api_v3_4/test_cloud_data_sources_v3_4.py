# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest.mock import patch
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status
from django.urls import reverse
from django.test.utils import override_settings

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile
from vui_tests.vui_testcase import VuiTestCase


class CloudDataSourcesV3_4Tests(VuiTestCase):
    """Test CloudDataSourcesV3_4 view functionality"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

        # Simple test data
        self.sample_data = [
            {'source_id': 'aws-123', 'sensor_name': 'AWS Connector', 'sensor_type': 'AWS', 'status': 'INGESTING'},
            {'source_id': 'o365-456', 'sensor_name': 'O365 Connector', 'sensor_type': 'O365', 'status': 'CREATED'},
            {'source_id': 'deleted-789', 'sensor_name': 'Deleted', 'sensor_type': 'AWS', 'status': 'DELETED'}
        ]
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_get_queryset_success(self, mock_annotate, mock_get_sensors):
        """Test get_queryset filters and processes data correctly"""
        mock_get_sensors.return_value = self.sample_data.copy()
        mock_annotate.side_effect = lambda x: x

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        # Should filter out DELETED status
        self.assertEqual(len(result), 2)

        # Verify correct sensors are returned
        sensor_ids = [sensor['source_id'] for sensor in result]
        self.assertIn('aws-123', sensor_ids)
        self.assertIn('o365-456', sensor_ids)
        self.assertNotIn('deleted-789', sensor_ids)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_empty(self, mock_get_sensors):
        """Test get_queryset with no data"""
        mock_get_sensors.return_value = []

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        self.assertEqual(len(result), 0)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_error_handling(self, mock_get_sensors):
        """Test get_queryset handles errors"""
        mock_get_sensors.side_effect = Exception("Database error")

        view = CloudDataSourcesV3_4()

        with self.assertRaises(Exception):
            view.get_queryset()

    def test_view_configuration(self):
        """Test view class configuration"""
        view = CloudDataSourcesV3_4()

        self.assertEqual(view.permission, 'data_sources')
        self.assertEqual(view.serializer_class, CloudDataSourceSerializerV3_4)
        self.assertEqual(view.allowed_methods, ('GET',))


class CloudDataSourceSerializerV3_4Tests(VuiTestCase):
    """Test CloudDataSourceSerializerV3_4 serializer"""
    
    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
    
    def test_basic_serialization(self):
        """Test basic field mapping to Option 3 schema"""
        data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Connector',
            'sensor_type': 'AWS',
            'status': 'INGESTING',
            'luid': 'test-luid',
            'region': 'us-east-1',
            'sensor_data': {'setup_link': 'https://test.com', 'audit_logs_enabled': True}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        # Verify Option 3 schema fields
        self.assertEqual(result['connector_id'], 'test-123')
        self.assertEqual(result['connector_name'], 'Test Connector')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'INGESTING')
        self.assertEqual(result['luid'], 'test-luid')
        
        # Verify properties structure
        self.assertIsInstance(result['properties'], dict)
        self.assertEqual(result['properties']['region'], 'us-east-1')
        self.assertEqual(result['properties']['setup_link'], 'https://test.com')
        self.assertTrue(result['properties']['audit_logs_enabled'])
    
    def test_minimal_data(self):
        """Test serializer handles minimal data"""
        data = {'source_id': 'minimal-123', 'status': 'UNKNOWN'}
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['connector_id'], 'minimal-123')
        self.assertEqual(result['connector_state'], 'UNKNOWN')
        self.assertIsNone(result['connector_name'])
        self.assertEqual(result['properties'], {})
    
    def test_error_handling(self):
        """Test error field mapping"""
        data = {
            'source_id': 'error-123',
            'status': 'ERROR',
            'error': {'message': 'Auth failed'}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['error'], 'Auth failed')
        self.assertEqual(result['connector_state'], 'ERROR')


class CloudDataSourcesPaginationTests(VuiTestCase):
    """
    Better approach for testing pagination functionality
    This demonstrates proper Django testing vs the manual approach
    """

    def setUp(self):
        super().setUp()

        # Create test user with proper authentication
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        self.client_user = User.objects.create_user(
            username='api_client_test',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='test_client', name='Test Client', description='Test API Client', created_by=self.user
            ),
        )

        # Setup API client with proper authentication
        self.client = APIClient(REMOTE_ADDR='************')
        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)
        self.client.force_authenticate(user=self.client_user, token=self.token)

        # Mock permission check
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True

        self.url = reverse('api-v3.4:api-cloud-data-sources-v3.4')

    def _create_test_dataset(self, size):
        """Helper to create test dataset of specified size"""
        return [
            {
                'source_id': f'connector-{i}',
                'sensor_name': f'Connector {i}',
                'sensor_type': 'AWS',
                'status': 'INGESTING',
                'region': 'us-east-1',
                'luid': f'luid-{i}',
                'sensor_data': {'setup_link': f'https://test-{i}.com'}
            }
            for i in range(size)
        ]

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_pagination_default_behavior(self, mock_annotate, mock_get_sensors):
        """Test default pagination (no params)"""
        mock_get_sensors.return_value = self._create_test_dataset(25)
        mock_annotate.side_effect = lambda x: x

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Verify pagination structure
        self.assertIn('count', data)
        self.assertIn('next', data)
        self.assertIn('previous', data)
        self.assertIn('results', data)

        # Verify pagination values
        self.assertEqual(data['count'], 25)
        self.assertEqual(len(data['results']), 20)  # Default page size
        self.assertIsNotNone(data['next'])
        self.assertIsNone(data['previous'])

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_pagination_custom_page_size(self, mock_annotate, mock_get_sensors):
        """Test custom page size parameter"""
        mock_get_sensors.return_value = self._create_test_dataset(15)
        mock_annotate.side_effect = lambda x: x

        response = self.client.get(self.url, {'page_size': '5'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data['count'], 15)
        self.assertEqual(len(data['results']), 5)
        self.assertIsNotNone(data['next'])

        # Verify first result has correct schema
        first_result = data['results'][0]
        self.assertEqual(first_result['connector_id'], 'connector-0')
        self.assertEqual(first_result['connector_name'], 'Connector 0')

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_pagination_second_page(self, mock_annotate, mock_get_sensors):
        """Test accessing second page"""
        mock_get_sensors.return_value = self._create_test_dataset(15)
        mock_annotate.side_effect = lambda x: x

        response = self.client.get(self.url, {'page': '2', 'page_size': '10'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data['count'], 15)
        self.assertEqual(len(data['results']), 5)  # Remaining items
        self.assertIsNone(data['next'])
        self.assertIsNotNone(data['previous'])

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_pagination_invalid_page_number(self, mock_get_sensors):
        """Test invalid page number returns 404"""
        mock_get_sensors.return_value = self._create_test_dataset(5)

        response = self.client.get(self.url, {'page': '999'})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_pagination_invalid_page_size_fallback(self, mock_annotate, mock_get_sensors):
        """Test invalid page size falls back gracefully"""
        mock_get_sensors.return_value = self._create_test_dataset(3)
        mock_annotate.side_effect = lambda x: x

        response = self.client.get(self.url, {'page_size': 'invalid'})

        # Should still work with default page size
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data['results']), 3)  # All data fits in default page
