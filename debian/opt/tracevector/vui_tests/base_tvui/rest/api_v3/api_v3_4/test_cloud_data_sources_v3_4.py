# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest.mock import patch
from django.test import override_settings
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile
from vui_tests.vui_testcase import VuiTestCase


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class CloudDataSourcesV3_4Tests(VuiTestCase):
    """Test CloudDataSourcesV3_4 endpoint functionality"""
    
    def setUp(self):
        super().setUp()
        
        # Create test user and API client
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        self.client_user = User.objects.create_user(
            username='api_client_test',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='test_client', name='Test Client', description='Test API Client', created_by=self.user
            ),
        )
        
        # Setup API client
        self.client = APIClient()
        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)
        
        # Mock permission check
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True
        
        self.url = '/api/v3.4/data-sources'
        
        # Simple test data
        self.sample_data = [
            {'source_id': 'aws-123', 'sensor_name': 'AWS Connector', 'sensor_type': 'AWS', 'status': 'INGESTING'},
            {'source_id': 'o365-456', 'sensor_name': 'O365 Connector', 'sensor_type': 'O365', 'status': 'CREATED'},
            {'source_id': 'deleted-789', 'sensor_name': 'Deleted', 'sensor_type': 'AWS', 'status': 'DELETED'}
        ]
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_get_cloud_data_sources_success(self, mock_annotate, mock_get_sensors):
        """Test successful retrieval of cloud data sources"""
        mock_get_sensors.return_value = self.sample_data.copy()
        mock_annotate.side_effect = lambda x: x
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Should filter out DELETED status
        self.assertEqual(len(data['results']), 2)
        self.assertEqual(data['count'], 2)
        
        # Verify Option 3 schema structure
        result = data['results'][0]
        required_fields = ['connector_id', 'connector_name', 'connector_type', 'properties']
        for field in required_fields:
            self.assertIn(field, result)
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_empty(self, mock_get_sensors):
        """Test endpoint with no data"""
        mock_get_sensors.return_value = []
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data['results']), 0)
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_error(self, mock_get_sensors):
        """Test endpoint handles errors"""
        mock_get_sensors.side_effect = Exception("Database error")
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_authentication_required(self):
        """Test endpoint requires authentication"""
        self.client.credentials()  # Remove auth
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_only_get_allowed(self):
        """Test only GET method is allowed"""
        self.assertEqual(self.client.post(self.url, {}).status_code, status.HTTP_405_METHOD_NOT_ALLOWED)


class CloudDataSourceSerializerV3_4Tests(VuiTestCase):
    """Test CloudDataSourceSerializerV3_4 serializer"""
    
    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
    
    def test_basic_serialization(self):
        """Test basic field mapping to Option 3 schema"""
        data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Connector',
            'sensor_type': 'AWS',
            'status': 'INGESTING',
            'luid': 'test-luid',
            'region': 'us-east-1',
            'sensor_data': {'setup_link': 'https://test.com', 'audit_logs_enabled': True}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        # Verify Option 3 schema fields
        self.assertEqual(result['connector_id'], 'test-123')
        self.assertEqual(result['connector_name'], 'Test Connector')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'INGESTING')
        self.assertEqual(result['luid'], 'test-luid')
        
        # Verify properties structure
        self.assertIsInstance(result['properties'], dict)
        self.assertEqual(result['properties']['region'], 'us-east-1')
        self.assertEqual(result['properties']['setup_link'], 'https://test.com')
        self.assertTrue(result['properties']['audit_logs_enabled'])
    
    def test_minimal_data(self):
        """Test serializer handles minimal data"""
        data = {'source_id': 'minimal-123', 'status': 'UNKNOWN'}
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['connector_id'], 'minimal-123')
        self.assertEqual(result['connector_state'], 'UNKNOWN')
        self.assertIsNone(result['connector_name'])
        self.assertEqual(result['properties'], {})
    
    def test_error_handling(self):
        """Test error field mapping"""
        data = {
            'source_id': 'error-123',
            'status': 'ERROR',
            'error': {'message': 'Auth failed'}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['error'], 'Auth failed')
        self.assertEqual(result['connector_state'], 'ERROR')
