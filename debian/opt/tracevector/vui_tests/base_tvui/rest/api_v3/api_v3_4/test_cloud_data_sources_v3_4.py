# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest.mock import patch
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status
from rest_framework.exceptions import ValidationError
from django.urls import reverse
from django.test.utils import override_settings

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile
from vui_tests.vui_testcase import VuiTestCase


class CloudDataSourcesV3_4Tests(VuiTestCase):
    """Test CloudDataSourcesV3_4 view functionality"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

        # Simple test data
        self.sample_data = [
            {'source_id': 'aws-123', 'sensor_name': 'AWS Connector', 'sensor_type': 'AWS', 'status': 'INGESTING'},
            {'source_id': 'o365-456', 'sensor_name': 'O365 Connector', 'sensor_type': 'O365', 'status': 'CREATED'},
            {'source_id': 'deleted-789', 'sensor_name': 'Deleted', 'sensor_type': 'AWS', 'status': 'DELETED'}
        ]
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_get_queryset_success(self, mock_annotate, mock_get_sensors):
        """Test get_queryset filters and processes data correctly"""
        mock_get_sensors.return_value = self.sample_data.copy()
        mock_annotate.side_effect = lambda x: x

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        # Should filter out DELETED status
        self.assertEqual(len(result), 2)

        # Verify correct sensors are returned
        sensor_ids = [sensor['source_id'] for sensor in result]
        self.assertIn('aws-123', sensor_ids)
        self.assertIn('o365-456', sensor_ids)
        self.assertNotIn('deleted-789', sensor_ids)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_empty(self, mock_get_sensors):
        """Test get_queryset with no data"""
        mock_get_sensors.return_value = []

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        self.assertEqual(len(result), 0)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_error_handling(self, mock_get_sensors):
        """Test get_queryset handles errors"""
        mock_get_sensors.side_effect = Exception("Database error")

        view = CloudDataSourcesV3_4()

        with self.assertRaises(ValidationError):
            view.get_queryset()

    def test_view_configuration(self):
        """Test view class configuration"""
        view = CloudDataSourcesV3_4()

        self.assertEqual(view.permission, 'data_sources')
        self.assertEqual(view.serializer_class, CloudDataSourceSerializerV3_4)
        self.assertEqual(view.allowed_methods, ('GET',))

    def test_pagination_support(self):
        """Test that view supports pagination"""
        view = CloudDataSourcesV3_4()

        # Verify pagination is configured
        self.assertIsNotNone(view.pagination_class)
        self.assertEqual(view.pagination_class.__name__, 'StandardResultSetPagination')


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class CloudDataSourcesV3_4IntegrationTests(VuiTestCase):
    """Integration tests for the CloudDataSourcesV3_4 endpoint"""

    def setUp(self):
        super().setUp()

        # Create test user
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')

        # Mock permission check
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True

        # Create API client user
        self.client_user = User.objects.create_user(
            username='api_client_test',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='test_client', name='Test Client', description='Test API Client', created_by=self.user
            ),
        )

        # Setup API client
        self.client = APIClient(REMOTE_ADDR='************')
        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)
        self.client.force_authenticate(user=self.client_user, token=self.token)

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_endpoint_url_and_response_format(self, mock_get_sensors):
        """Test the new URL works and returns paginated results"""
        # Setup test data
        mock_get_sensors.return_value = [
            {
                'source_id': 'aws-123',
                'sensor_name': 'AWS Connector',
                'sensor_type': 'AWS',
                'status': 'LOGS_FLOWING',
                'created_at': '2025-06-01T10:30:00Z',
                'last_log_received': '2025-07-01T17:35:00Z',
                'updated_at': '2025-07-01T17:35:00Z',
                'sensor_data': {
                    'region': 'us-east-1',
                    'setup_link': 'https://fake-ms-consent.com/link',
                    'license_type': 'standard'
                }
            }
        ]

        # Test the new URL
        url = reverse('api-v3.4:api-cloud-data-sources-v3.4')
        self.assertTrue(url.endswith('/api/v3.4/data-source/connectors/'))

        response = self.client.get(url)

        # Verify response structure (paginated)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Should have pagination structure
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertEqual(len(data['results']), 1)

        # Verify connector data structure matches contract
        connector = data['results'][0]
        expected_fields = [
            'connector_id', 'connector_name', 'connector_type', 'connector_state',
            'created_at', 'error', 'last_log_received', 'updated_at', 'properties'
        ]
        for field in expected_fields:
            self.assertIn(field, connector)

        # Verify specific values match contract
        self.assertEqual(connector['connector_id'], 'aws-123')
        self.assertEqual(connector['connector_name'], 'AWS Connector')
        self.assertEqual(connector['connector_type'], 'AWS')
        self.assertEqual(connector['connector_state'], 'LOGS_FLOWING')

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_pagination_parameters(self, mock_get_sensors):
        """Test that page and page_size parameters work"""
        # Create larger dataset
        large_dataset = []
        for i in range(25):
            large_dataset.append({
                'source_id': f'connector-{i}',
                'sensor_name': f'Connector {i}',
                'sensor_type': 'AWS',
                'status': 'LOGS_FLOWING'
            })

        mock_get_sensors.return_value = large_dataset

        url = reverse('api-v3.4:api-cloud-data-sources-v3.4')

        # Test with page_size parameter
        response = self.client.get(url, {'page_size': 10})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Should respect page_size
        self.assertEqual(len(data['results']), 10)
        self.assertEqual(data['count'], 25)
        self.assertIn('next', data)
        self.assertIsNone(data['previous'])


class CloudDataSourceSerializerV3_4Tests(VuiTestCase):
    """Test CloudDataSourceSerializerV3_4 serializer"""
    
    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
    
    def test_basic_serialization(self):
        """Test basic field mapping to contract schema"""
        data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Connector',
            'sensor_type': 'AWS',
            'status': 'INGESTING',
            'region': 'us-east-1',
            'sensor_data': {'setup_link': 'https://test.com', 'audit_logs_enabled': True}
        }

        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data

        # Verify contract schema fields
        self.assertEqual(result['connector_id'], 'test-123')
        self.assertEqual(result['connector_name'], 'Test Connector')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'INGESTING')

        # Verify properties structure
        self.assertIsInstance(result['properties'], dict)
        self.assertEqual(result['properties']['region'], 'us-east-1')
        self.assertEqual(result['properties']['setup_link'], 'https://test.com')
        self.assertTrue(result['properties']['audit_logs_enabled'])
    
    def test_minimal_data(self):
        """Test serializer handles minimal data"""
        data = {'source_id': 'minimal-123', 'status': 'UNKNOWN'}
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['connector_id'], 'minimal-123')
        self.assertEqual(result['connector_state'], 'UNKNOWN')
        self.assertIsNone(result['connector_name'])
        self.assertEqual(result['properties'], {})
    
    def test_error_handling(self):
        """Test error field mapping"""
        data = {
            'source_id': 'error-123',
            'status': 'ERROR',
            'error': {'message': 'Auth failed'}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['error'], 'Auth failed')
        self.assertEqual(result['connector_state'], 'ERROR')


class CloudDataSourcesPaginationTests(VuiTestCase):
    """
    Test pagination functionality using a simpler approach
    This demonstrates proper testing patterns without complex setup
    """

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

        # Create larger test dataset for pagination testing
        self.large_dataset = [
            {
                'source_id': f'connector-{i}',
                'sensor_name': f'Connector {i}',
                'sensor_type': 'AWS',
                'status': 'INGESTING',
                'region': 'us-east-1',
                'luid': f'luid-{i}',
                'sensor_data': {'setup_link': f'https://test-{i}.com'}
            }
            for i in range(25)  # Create 25 items for pagination testing
        ]

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_view_handles_large_dataset(self, mock_annotate, mock_get_sensors):
        """Test view can handle large datasets (pagination logic)"""
        mock_get_sensors.return_value = self.large_dataset
        mock_annotate.side_effect = lambda x: x

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        # Should return all non-deleted items
        self.assertEqual(len(result), 25)

        # Verify data structure is correct
        first_item = result[0]
        self.assertEqual(first_item['source_id'], 'connector-0')
        self.assertEqual(first_item['sensor_name'], 'Connector 0')

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_serializer_with_large_dataset(self, mock_annotate, mock_get_sensors):
        """Test serializer performance with larger dataset"""
        mock_get_sensors.return_value = self.large_dataset
        mock_annotate.side_effect = lambda x: x

        # Test serialization of multiple items
        request = self.factory.get('/')
        serializer = CloudDataSourceSerializerV3_4(self.large_dataset[:5], many=True, context={'request': request})
        result = serializer.data

        # Verify all items serialized correctly
        self.assertEqual(len(result), 5)

        # Verify Option 3 schema structure for each item
        for i, item in enumerate(result):
            self.assertEqual(item['connector_id'], f'connector-{i}')
            self.assertEqual(item['connector_name'], f'Connector {i}')
            self.assertEqual(item['connector_type'], 'AWS')
            self.assertEqual(item['connector_state'], 'INGESTING')
            self.assertIsInstance(item['properties'], dict)
            self.assertEqual(item['properties']['region'], 'us-east-1')

    def test_view_configuration_for_pagination(self):
        """Test view is properly configured for pagination"""
        view = CloudDataSourcesV3_4()

        # Verify pagination class is set
        self.assertIsNotNone(view.pagination_class)

        # Verify it's a list view (supports pagination)
        self.assertTrue(hasattr(view, 'get_queryset'))
        self.assertTrue(hasattr(view, 'list'))
