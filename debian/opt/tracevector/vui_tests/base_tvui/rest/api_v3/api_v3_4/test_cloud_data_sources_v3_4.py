# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest.mock import patch
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status
from django.urls import reverse
from django.test.utils import override_settings

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile
from vui_tests.vui_testcase import VuiTestCase


class CloudDataSourcesV3_4Tests(VuiTestCase):
    """Test CloudDataSourcesV3_4 view functionality"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

        # Simple test data
        self.sample_data = [
            {'source_id': 'aws-123', 'sensor_name': 'AWS Connector', 'sensor_type': 'AWS', 'status': 'INGESTING'},
            {'source_id': 'o365-456', 'sensor_name': 'O365 Connector', 'sensor_type': 'O365', 'status': 'CREATED'},
            {'source_id': 'deleted-789', 'sensor_name': 'Deleted', 'sensor_type': 'AWS', 'status': 'DELETED'}
        ]
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_get_queryset_success(self, mock_annotate, mock_get_sensors):
        """Test get_queryset filters and processes data correctly"""
        mock_get_sensors.return_value = self.sample_data.copy()
        mock_annotate.side_effect = lambda x: x

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        # Should filter out DELETED status
        self.assertEqual(len(result), 2)

        # Verify correct sensors are returned
        sensor_ids = [sensor['source_id'] for sensor in result]
        self.assertIn('aws-123', sensor_ids)
        self.assertIn('o365-456', sensor_ids)
        self.assertNotIn('deleted-789', sensor_ids)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_empty(self, mock_get_sensors):
        """Test get_queryset with no data"""
        mock_get_sensors.return_value = []

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        self.assertEqual(len(result), 0)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_error_handling(self, mock_get_sensors):
        """Test get_queryset handles errors"""
        mock_get_sensors.side_effect = Exception("Database error")

        view = CloudDataSourcesV3_4()

        with self.assertRaises(Exception):
            view.get_queryset()

    def test_view_configuration(self):
        """Test view class configuration"""
        view = CloudDataSourcesV3_4()

        self.assertEqual(view.permission, 'data_sources')
        self.assertEqual(view.serializer_class, CloudDataSourceSerializerV3_4)
        self.assertEqual(view.allowed_methods, ('GET',))


class CloudDataSourceSerializerV3_4Tests(VuiTestCase):
    """Test CloudDataSourceSerializerV3_4 serializer"""
    
    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
    
    def test_basic_serialization(self):
        """Test basic field mapping to Option 3 schema"""
        data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Connector',
            'sensor_type': 'AWS',
            'status': 'INGESTING',
            'luid': 'test-luid',
            'region': 'us-east-1',
            'sensor_data': {'setup_link': 'https://test.com', 'audit_logs_enabled': True}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        # Verify Option 3 schema fields
        self.assertEqual(result['connector_id'], 'test-123')
        self.assertEqual(result['connector_name'], 'Test Connector')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'INGESTING')
        self.assertEqual(result['luid'], 'test-luid')
        
        # Verify properties structure
        self.assertIsInstance(result['properties'], dict)
        self.assertEqual(result['properties']['region'], 'us-east-1')
        self.assertEqual(result['properties']['setup_link'], 'https://test.com')
        self.assertTrue(result['properties']['audit_logs_enabled'])
    
    def test_minimal_data(self):
        """Test serializer handles minimal data"""
        data = {'source_id': 'minimal-123', 'status': 'UNKNOWN'}
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['connector_id'], 'minimal-123')
        self.assertEqual(result['connector_state'], 'UNKNOWN')
        self.assertIsNone(result['connector_name'])
        self.assertEqual(result['properties'], {})
    
    def test_error_handling(self):
        """Test error field mapping"""
        data = {
            'source_id': 'error-123',
            'status': 'ERROR',
            'error': {'message': 'Auth failed'}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['error'], 'Auth failed')
        self.assertEqual(result['connector_state'], 'ERROR')


class CloudDataSourcesPaginationTests(VuiTestCase):
    """
    Test pagination functionality using a simpler approach
    This demonstrates proper testing patterns without complex setup
    """

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

        # Create larger test dataset for pagination testing
        self.large_dataset = [
            {
                'source_id': f'connector-{i}',
                'sensor_name': f'Connector {i}',
                'sensor_type': 'AWS',
                'status': 'INGESTING',
                'region': 'us-east-1',
                'luid': f'luid-{i}',
                'sensor_data': {'setup_link': f'https://test-{i}.com'}
            }
            for i in range(25)  # Create 25 items for pagination testing
        ]

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_view_handles_large_dataset(self, mock_annotate, mock_get_sensors):
        """Test view can handle large datasets (pagination logic)"""
        mock_get_sensors.return_value = self.large_dataset
        mock_annotate.side_effect = lambda x: x

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        # Should return all non-deleted items
        self.assertEqual(len(result), 25)

        # Verify data structure is correct
        first_item = result[0]
        self.assertEqual(first_item['source_id'], 'connector-0')
        self.assertEqual(first_item['sensor_name'], 'Connector 0')

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_serializer_with_large_dataset(self, mock_annotate, mock_get_sensors):
        """Test serializer performance with larger dataset"""
        mock_get_sensors.return_value = self.large_dataset
        mock_annotate.side_effect = lambda x: x

        # Test serialization of multiple items
        request = self.factory.get('/')
        serializer = CloudDataSourceSerializerV3_4(self.large_dataset[:5], many=True, context={'request': request})
        result = serializer.data

        # Verify all items serialized correctly
        self.assertEqual(len(result), 5)

        # Verify Option 3 schema structure for each item
        for i, item in enumerate(result):
            self.assertEqual(item['connector_id'], f'connector-{i}')
            self.assertEqual(item['connector_name'], f'Connector {i}')
            self.assertEqual(item['connector_type'], 'AWS')
            self.assertEqual(item['connector_state'], 'INGESTING')
            self.assertIsInstance(item['properties'], dict)
            self.assertEqual(item['properties']['region'], 'us-east-1')

    def test_view_configuration_for_pagination(self):
        """Test view is properly configured for pagination"""
        view = CloudDataSourcesV3_4()

        # Verify pagination class is set
        self.assertIsNotNone(view.pagination_class)

        # Verify it's a list view (supports pagination)
        self.assertTrue(hasattr(view, 'get_queryset'))
        self.assertTrue(hasattr(view, 'list'))


class CloudDataSourcesManualTestingExample:
    """
    Example of how to do manual testing (for reference only)
    This shows the difference between manual testing and proper unit tests
    """

    @staticmethod
    def manual_test_example():
        """
        This is what the user's original manual testing approach looked like.
        It's useful for development but not for automated testing.
        """
        # This would be the manual approach:
        # view = CloudDataSourcesV3_4()
        # request = APIRequestFactory().get('/api/v3.4/data-sources/?page_size=5')
        # request.user = Mock()  # ← Too simple, causes issues
        # response = view.list(request)  # ← Would fail without proper mocking

        # Problems:
        # 1. No proper authentication setup
        # 2. No database mocking
        # 3. No assertions - just prints
        # 4. Hits real dependencies
        # 5. Not repeatable/reliable

        pass
