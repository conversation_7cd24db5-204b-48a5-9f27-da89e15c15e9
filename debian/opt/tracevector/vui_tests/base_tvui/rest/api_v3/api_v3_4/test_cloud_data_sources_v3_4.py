# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest.mock import patch
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status
from rest_framework.exceptions import ValidationError
from django.urls import reverse
from django.test.utils import override_settings

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile
from vui_tests.vui_testcase import VuiTestCase


class CloudDataSourcesV3_4Tests(VuiTestCase):
    """Test CloudDataSourcesV3_4 view functionality"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

        # Simple test data
        self.sample_data = [
            {'source_id': 'aws-123', 'sensor_name': 'AWS Connector', 'sensor_type': 'AWS', 'status': 'INGESTING'},
            {'source_id': 'o365-456', 'sensor_name': 'O365 Connector', 'sensor_type': 'O365', 'status': 'CREATED'},
            {'source_id': 'deleted-789', 'sensor_name': 'Deleted', 'sensor_type': 'AWS', 'status': 'DELETED'}
        ]
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_get_queryset_success(self, mock_annotate, mock_get_sensors):
        """Test get_queryset filters and processes data correctly"""
        mock_get_sensors.return_value = self.sample_data.copy()
        mock_annotate.side_effect = lambda x: x

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        # Should filter out DELETED status
        self.assertEqual(len(result), 2)

        # Verify correct sensors are returned
        sensor_ids = [sensor['source_id'] for sensor in result]
        self.assertIn('aws-123', sensor_ids)
        self.assertIn('o365-456', sensor_ids)
        self.assertNotIn('deleted-789', sensor_ids)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_empty(self, mock_get_sensors):
        """Test get_queryset with no data"""
        mock_get_sensors.return_value = []

        view = CloudDataSourcesV3_4()
        result = view.get_queryset()

        self.assertEqual(len(result), 0)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_queryset_error_handling(self, mock_get_sensors):
        """Test get_queryset handles errors"""
        mock_get_sensors.side_effect = Exception("Database error")

        view = CloudDataSourcesV3_4()

        with self.assertRaises(ValidationError):
            view.get_queryset()

    def test_view_configuration(self):
        """Test view class configuration"""
        view = CloudDataSourcesV3_4()

        self.assertEqual(view.permission, 'data_sources')
        self.assertEqual(view.serializer_class, CloudDataSourceSerializerV3_4)
        self.assertEqual(view.allowed_methods, ('GET',))

    def test_pagination_support(self):
        """Test that view supports pagination"""
        view = CloudDataSourcesV3_4()

        # Verify pagination is configured
        self.assertIsNotNone(view.pagination_class)
        self.assertEqual(view.pagination_class.__name__, 'StandardResultSetPagination')


class CloudDataSourcesV3_4SerializerTests(VuiTestCase):
    """Test CloudDataSourceSerializerV3_4 contract compliance"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')

    def test_contract_field_compliance(self):
        """Test serializer produces exact contract fields"""
        data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Connector',
            'sensor_type': 'AWS',
            'status': 'LOGS_FLOWING'
        }

        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data

        # Verify all contract fields exist
        expected_fields = [
            'connector_id', 'connector_name', 'connector_type', 'connector_state',
            'created_at', 'error', 'last_log_received', 'updated_at', 'properties'
        ]
        for field in expected_fields:
            self.assertIn(field, result)

        # Verify field mapping
        self.assertEqual(result['connector_id'], 'test-123')
        self.assertEqual(result['connector_name'], 'Test Connector')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'LOGS_FLOWING')

    def test_properties_extraction(self):
        """Test properties field extracts sensor_data correctly"""
        data = {
            'source_id': 'test-123',
            'status': 'LOGS_FLOWING',
            'region': 'us-east-1',
            'sensor_data': {
                'setup_link': 'https://test.com',
                'audit_logs_enabled': True,
                'license_type': 'standard'
            }
        }

        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        properties = serializer.data['properties']

        self.assertEqual(properties['region'], 'us-east-1')
        self.assertEqual(properties['setup_link'], 'https://test.com')
        self.assertTrue(properties['audit_logs_enabled'])
        self.assertEqual(properties['license_type'], 'standard')


    def test_error_handling(self):
        """Test error field mapping"""
        data = {
            'source_id': 'error-123',
            'status': 'ERROR',
            'error': {'message': 'Auth failed'}
        }

        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data

        self.assertEqual(result['error'], 'Auth failed')
        self.assertEqual(result['connector_state'], 'ERROR')


class CloudDataSourcesV3_4IntegrationTests(VuiTestCase):
    """Integration tests for full API response contract compliance"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()

    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_full_api_response_contract(self, mock_annotate, mock_get_sensors):
        """Test complete API response matches data contract exactly"""
        # Setup realistic test data
        mock_get_sensors.return_value = [
            {
                'source_id': 'aws-connector-123',
                'sensor_name': 'AWS Production',
                'sensor_type': 'AWS',
                'status': 'LOGS_FLOWING',
                'created_at': '2025-06-01T10:30:00Z',
                'last_log_received': '2025-07-01T17:35:00Z',
                'updated_at': '2025-07-01T17:35:00Z',
                'region': 'us-east-1',
                'sensor_data': {
                    'setup_link': 'https://aws-setup.com/auth',
                    'license_type': 'standard',
                    'audit_logs_enabled': True
                }
            },
            {
                'source_id': 'o365-connector-456',
                'sensor_name': 'Office 365 Tenant',
                'sensor_type': 'O365',
                'status': 'ERROR',
                'created_at': '2025-05-15T08:20:00Z',
                'error': {'message': 'Authentication failed'},
                'sensor_data': {
                    'ms_tenant_id': 'tenant-abc-123',
                    'api_type': 'graph'
                }
            },
            {
                'source_id': 'deleted-connector',
                'sensor_name': 'Deleted Connector',
                'sensor_type': 'AWS',
                'status': 'DELETED'  # Should be filtered out
            }
        ]
        mock_annotate.side_effect = lambda x: x

        # Create view and simulate full request cycle
        view = CloudDataSourcesV3_4()
        request = self.factory.get('/api/v3.4/data-source/connectors')

        # Get queryset (this tests the filtering logic)
        queryset = view.get_queryset()

        # Serialize the data (this tests the contract mapping)
        serializer = view.serializer_class(queryset, many=True, context={'request': request})

        # Simulate pagination response structure
        paginated_response = {
            'count': len(serializer.data),
            'next': None,
            'previous': None,
            'results': serializer.data
        }

        # Verify top-level response structure (DRF pagination)
        self.assertIn('count', paginated_response)
        self.assertIn('next', paginated_response)
        self.assertIn('previous', paginated_response)
        self.assertIn('results', paginated_response)

        # Should have 2 connectors (DELETED filtered out)
        self.assertEqual(paginated_response['count'], 2)
        self.assertEqual(len(paginated_response['results']), 2)

        # Verify each connector in results matches exact data contract
        connectors = paginated_response['results']

        # Test AWS connector (complete data)
        aws_connector = next(c for c in connectors if c['connector_id'] == 'aws-connector-123')
        self.assertEqual(aws_connector['connector_name'], 'AWS Production')
        self.assertEqual(aws_connector['connector_type'], 'AWS')
        self.assertEqual(aws_connector['connector_state'], 'LOGS_FLOWING')
        self.assertEqual(aws_connector['created_at'], '2025-06-01T10:30:00Z')
        self.assertEqual(aws_connector['last_log_received'], '2025-07-01T17:35:00Z')
        self.assertEqual(aws_connector['updated_at'], '2025-07-01T17:35:00Z')
        self.assertIsNone(aws_connector['error'])

        # Verify properties structure
        aws_properties = aws_connector['properties']
        self.assertEqual(aws_properties['region'], 'us-east-1')
        self.assertEqual(aws_properties['setup_link'], 'https://aws-setup.com/auth')
        self.assertEqual(aws_properties['license_type'], 'standard')
        self.assertTrue(aws_properties['audit_logs_enabled'])

        # Test O365 connector (error case)
        o365_connector = next(c for c in connectors if c['connector_id'] == 'o365-connector-456')
        self.assertEqual(o365_connector['connector_name'], 'Office 365 Tenant')
        self.assertEqual(o365_connector['connector_type'], 'O365')
        self.assertEqual(o365_connector['connector_state'], 'ERROR')
        self.assertEqual(o365_connector['created_at'], '2025-05-15T08:20:00Z')
        self.assertEqual(o365_connector['error'], 'Authentication failed')
        self.assertIsNone(o365_connector['last_log_received'])
        self.assertIsNone(o365_connector['updated_at'])

        # Verify O365 properties
        o365_properties = o365_connector['properties']
        self.assertEqual(o365_properties['ms_tenant_id'], 'tenant-abc-123')
        self.assertEqual(o365_properties['api_type'], 'graph')

        # Verify all connectors have exact contract fields (no extra, no missing)
        expected_fields = {
            'connector_id', 'connector_name', 'connector_type', 'connector_state',
            'created_at', 'error', 'last_log_received', 'updated_at', 'properties'
        }
        for connector in connectors:
            self.assertEqual(set(connector.keys()), expected_fields,
                           f"Connector {connector['connector_id']} has incorrect fields")