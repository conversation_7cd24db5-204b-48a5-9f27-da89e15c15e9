# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

from unittest.mock import patch
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile
from vui_tests.vui_testcase import VuiTestCase


class CloudDataSourcesV3_4Tests(VuiTestCase):
    """Test CloudDataSourcesV3_4 endpoint functionality"""
    
    def setUp(self):
        super().setUp()
        
        # Create test user and API client
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        self.client_user = User.objects.create_user(
            username='api_client_test',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='test_client', name='Test Client', description='Test API Client', created_by=self.user
            ),
        )
        
        # Setup API client and factory
        self.client = APIClient()
        self.factory = APIRequestFactory()
        self.token = Token.objects.create(user=self.client_user)

        # Mock permission check
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True

        # Simple test data
        self.sample_data = [
            {'source_id': 'aws-123', 'sensor_name': 'AWS Connector', 'sensor_type': 'AWS', 'status': 'INGESTING'},
            {'source_id': 'o365-456', 'sensor_name': 'O365 Connector', 'sensor_type': 'O365', 'status': 'CREATED'},
            {'source_id': 'deleted-789', 'sensor_name': 'Deleted', 'sensor_type': 'AWS', 'status': 'DELETED'}
        ]
    
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.annotate_sensor_status')
    def test_get_cloud_data_sources_success(self, mock_annotate, mock_get_sensors):
        """Test successful retrieval of cloud data sources"""
        mock_get_sensors.return_value = self.sample_data.copy()
        mock_annotate.side_effect = lambda x: x

        # Create request and test view directly
        request = self.factory.get('/api/v3.4/data-sources')
        request.user = self.client_user

        view = CloudDataSourcesV3_4.as_view()
        response = view(request)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should filter out DELETED status
        self.assertEqual(len(response.data['results']), 2)
        self.assertEqual(response.data['count'], 2)

        # Verify Option 3 schema structure
        result = response.data['results'][0]
        required_fields = ['connector_id', 'connector_name', 'connector_type', 'properties']
        for field in required_fields:
            self.assertIn(field, result)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_empty(self, mock_get_sensors):
        """Test endpoint with no data"""
        mock_get_sensors.return_value = []

        request = self.factory.get('/api/v3.4/data-sources')
        request.user = self.client_user

        view = CloudDataSourcesV3_4.as_view()
        response = view(request)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)

    @patch('base_tvui.rest.api_v3.api_v3_4.api_v3_4_views.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_error(self, mock_get_sensors):
        """Test endpoint handles errors"""
        mock_get_sensors.side_effect = Exception("Database error")

        request = self.factory.get('/api/v3.4/data-sources')
        request.user = self.client_user

        view = CloudDataSourcesV3_4.as_view()
        response = view(request)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_view_class_configuration(self):
        """Test view class is configured correctly"""
        view = CloudDataSourcesV3_4()

        self.assertEqual(view.permission, 'data_sources')
        self.assertEqual(view.serializer_class, CloudDataSourceSerializerV3_4)
        self.assertEqual(view.allowed_methods, ('GET',))


class CloudDataSourceSerializerV3_4Tests(VuiTestCase):
    """Test CloudDataSourceSerializerV3_4 serializer"""
    
    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
    
    def test_basic_serialization(self):
        """Test basic field mapping to Option 3 schema"""
        data = {
            'source_id': 'test-123',
            'sensor_name': 'Test Connector',
            'sensor_type': 'AWS',
            'status': 'INGESTING',
            'luid': 'test-luid',
            'region': 'us-east-1',
            'sensor_data': {'setup_link': 'https://test.com', 'audit_logs_enabled': True}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        # Verify Option 3 schema fields
        self.assertEqual(result['connector_id'], 'test-123')
        self.assertEqual(result['connector_name'], 'Test Connector')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'INGESTING')
        self.assertEqual(result['luid'], 'test-luid')
        
        # Verify properties structure
        self.assertIsInstance(result['properties'], dict)
        self.assertEqual(result['properties']['region'], 'us-east-1')
        self.assertEqual(result['properties']['setup_link'], 'https://test.com')
        self.assertTrue(result['properties']['audit_logs_enabled'])
    
    def test_minimal_data(self):
        """Test serializer handles minimal data"""
        data = {'source_id': 'minimal-123', 'status': 'UNKNOWN'}
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['connector_id'], 'minimal-123')
        self.assertEqual(result['connector_state'], 'UNKNOWN')
        self.assertIsNone(result['connector_name'])
        self.assertEqual(result['properties'], {})
    
    def test_error_handling(self):
        """Test error field mapping"""
        data = {
            'source_id': 'error-123',
            'status': 'ERROR',
            'error': {'message': 'Auth failed'}
        }
        
        serializer = CloudDataSourceSerializerV3_4(data, context={'request': self.request})
        result = serializer.data
        
        self.assertEqual(result['error'], 'Auth failed')
        self.assertEqual(result['connector_state'], 'ERROR')
