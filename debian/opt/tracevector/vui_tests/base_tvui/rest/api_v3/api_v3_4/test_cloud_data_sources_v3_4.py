# Copyright (c) 2024 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential

import json
from unittest.mock import patch, MagicMock
from django.test import override_settings
from django.urls import reverse
from rest_framework.test import APIClient, APIRequestFactory
from rest_framework.authtoken.models import Token
from rest_framework import status

from base_tvui.rest.api_v3.api_v3_4.api_v3_4_views import CloudDataSourcesV3_4
from base_tvui.rest.api_v3.api_v3_4.api_v3_4_serializers import CloudDataSourceSerializerV3_4
from tvui.models import User, ApiClientProfile, CloudSensor
from vui_tests.vui_testcase import VuiTestCase


class BasicCloudDataSourcesV3_4Tests(VuiTestCase):
    """Base test class for CloudDataSourcesV3_4 tests"""
    
    def setUp(self):
        super().setUp()
        
        # Create test user
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='cognito')
        
        # Mock permission check
        self.mock_perm_check = self.add_patch(
            'has_permission', patch('base_tvui.rest.custom_permissions.RoleBasedPermission.has_permission')
        )
        self.mock_perm_check.return_value = True
        
        # Create API client user
        self.client_user = User.objects.create_user(
            username='api_client_test',
            account_type=User.API_CLIENT,
            api_client_profile=ApiClientProfile.objects.create(
                client_id='test_client', name='Test Client', description='Test API Client', created_by=self.user
            ),
        )
        
        # Setup API client
        self.client = APIClient(REMOTE_ADDR='************')
        self.token = Token.objects.create(user=self.client_user)
        self.client.credentials(HTTP_AUTHORIZATION='Token ' + self.token.key)
        self.client.force_authenticate(user=self.client_user, token=self.token)
        
        # Test data with Option 3 schema fields
        self.sample_sensor_data = [
            {
                'source_id': 'aws-123',
                'sensor_name': 'AWS Connector 1',
                'name': 'AWS Connector 1',
                'sensor_type': 'AWS',
                'serial_number': 'aws-serial-123',
                'status': 'INGESTING',
                'region': 'us-east-1',
                'last_seen': '2024-01-01T12:00:00Z',
                'last_log_received': '2024-01-01T12:00:00Z',
                'luid': 'aws-luid-123',
                'sensor_data': {
                    'setup_link': 'https://aws.amazon.com/setup',
                    'license_type': 'enterprise',
                    'api_type': 'cloudtrail',
                    'audit_logs_enabled': True
                },
                'forwardingState': {
                    'icon': 'success',
                    'statusText': 'Logs Flowing',
                    'message': 'Connector is actively ingesting data',
                    'showLastSeen': True
                }
            },
            {
                'source_id': 'o365-456',
                'sensor_name': 'Office 365 Connector',
                'name': 'Office 365 Connector',
                'sensor_type': 'O365',
                'serial_number': 'o365-serial-456',
                'status': 'CREATED',
                'region': 'global',
                'last_seen': '2024-01-01T11:00:00Z',
                'luid': 'o365-luid-456',
                'sensor_data': {
                    'ms_tenant_id': 'tenant-123-456',
                    'setup_link': 'https://portal.office.com/setup',
                    'audit_logs_enabled': False
                },
                'forwardingState': {
                    'icon': 'warning',
                    'statusText': 'Pairing',
                    'message': 'Connector requires authorization',
                    'showLastSeen': False
                }
            },
            {
                'source_id': 'azure-789',
                'sensor_name': 'Azure Connector',
                'name': 'Azure Connector',
                'sensor_type': 'AZURE',
                'serial_number': 'azure-serial-789',
                'status': 'ERROR',
                'region': 'eastus',
                'last_seen': '2024-01-01T10:00:00Z',
                'error': {'message': 'Authentication failed'},
                'luid': 'azure-luid-789',
                'sensor_data': {
                    'tenant_id': 'azure-tenant-789',
                    'license_type': 'standard'
                },
                'forwardingState': {
                    'icon': 'alert',
                    'statusText': 'Error',
                    'message': 'Authentication failed',
                    'showLastSeen': True
                }
            }
        ]


@override_settings(ROOT_URLCONF='base_tvui.urls_cloud')
class CloudDataSourcesV3_4Tests(BasicCloudDataSourcesV3_4Tests):
    """Test CloudDataSourcesV3_4 endpoint functionality"""
    
    def setUp(self):
        super().setUp()
        self.url = reverse('api-v3.4:api-cloud-data-sources-v3.4')
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    @patch('base_tvui.providers.lib_sensors.annotate_sensor_status')
    def test_get_cloud_data_sources_success(self, mock_annotate, mock_get_sensors):
        """Test successful retrieval of cloud data sources with Option 3 schema"""
        # Setup mocks
        mock_get_sensors.return_value = self.sample_sensor_data.copy()
        mock_annotate.side_effect = lambda x: x  # Pass through without modification
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Verify structure
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertEqual(len(data['results']), 3)
        
        # Verify first result structure (Option 3 schema)
        first_result = data['results'][0]
        expected_fields = [
            'connector_id', 'connector_name', 'connector_type', 'luid', 
            'connector_state', 'created_at', 'error', 'last_log_received', 
            'updated_at', 'properties'
        ]
        for field in expected_fields:
            self.assertIn(field, first_result)
        
        # Verify properties structure
        self.assertIn('properties', first_result)
        self.assertIsInstance(first_result['properties'], dict)
        
        # Verify connector data mapping
        self.assertEqual(first_result['connector_id'], 'aws-123')
        self.assertEqual(first_result['connector_name'], 'AWS Connector 1')
        self.assertEqual(first_result['connector_type'], 'AWS')
        self.assertEqual(first_result['connector_state'], 'INGESTING')
        
        # Verify mocks were called
        mock_get_sensors.assert_called_once()
        self.assertEqual(mock_annotate.call_count, 3)
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_empty_list(self, mock_get_sensors):
        """Test endpoint returns empty list when no sensors exist"""
        # Setup mock
        mock_get_sensors.return_value = []
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 0)
        self.assertEqual(data['count'], 0)
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_filters_deleted(self, mock_get_sensors):
        """Test endpoint filters out deleted connectors"""
        # Add deleted sensor to test data
        test_data = self.sample_sensor_data.copy()
        test_data.append({
            'source_id': 'deleted-123',
            'sensor_name': 'Deleted Connector',
            'sensor_type': 'AWS',
            'status': 'DELETED'
        })
        
        mock_get_sensors.return_value = test_data
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Should only return 3 active sensors, not the deleted one
        self.assertEqual(len(data['results']), 3)
        
        # Verify no deleted sensors in results
        for result in data['results']:
            self.assertNotEqual(result['connector_id'], 'deleted-123')
    
    @patch('base_tvui.providers.lib_cloud_sensor.CloudSensorManagement.get_all_sensors')
    def test_get_cloud_data_sources_database_error(self, mock_get_sensors):
        """Test endpoint handles database errors gracefully"""
        # Setup mock to raise exception
        mock_get_sensors.side_effect = Exception("Database connection failed")
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify error response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn('Unable to retrieve cloud data sources', str(data))
    
    def test_get_cloud_data_sources_unauthorized(self):
        """Test endpoint requires authentication"""
        # Remove authentication
        self.client.credentials()
        
        # Make request
        response = self.client.get(self.url)
        
        # Verify unauthorized response
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_only_get_method_allowed(self):
        """Test only GET method is allowed"""
        self.assertEqual(self.client.post(self.url, {}).status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        self.assertEqual(self.client.put(self.url, {}).status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        self.assertEqual(self.client.delete(self.url).status_code, status.HTTP_405_METHOD_NOT_ALLOWED)


class CloudDataSourceSerializerV3_4Tests(BasicCloudDataSourcesV3_4Tests):
    """Test CloudDataSourceSerializerV3_4 serializer functionality with Option 3 schema"""

    def setUp(self):
        super().setUp()
        self.factory = APIRequestFactory()
        self.request = self.factory.get('/')
        self.request.user = self.client_user

    def test_serializer_basic_fields(self):
        """Test serializer maps basic fields correctly to Option 3 schema"""
        sensor_data = self.sample_sensor_data[0]
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})

        result = serializer.data

        self.assertEqual(result['connector_id'], 'aws-123')
        self.assertEqual(result['connector_name'], 'AWS Connector 1')
        self.assertEqual(result['connector_type'], 'AWS')
        self.assertEqual(result['connector_state'], 'INGESTING')
        self.assertEqual(result['luid'], 'aws-luid-123')
        self.assertEqual(result['last_log_received'], '2024-01-01T12:00:00Z')

    def test_serializer_properties_mapping(self):
        """Test serializer maps properties correctly"""
        sensor_data = self.sample_sensor_data[0]  # AWS connector with full sensor_data
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})

        result = serializer.data
        properties = result['properties']

        self.assertEqual(properties['region'], 'us-east-1')
        self.assertEqual(properties['setup_link'], 'https://aws.amazon.com/setup')
        self.assertEqual(properties['license_type'], 'enterprise')
        self.assertEqual(properties['api_type'], 'cloudtrail')
        self.assertTrue(properties['audit_logs_enabled'])

    def test_serializer_o365_properties(self):
        """Test serializer maps O365-specific properties correctly"""
        sensor_data = self.sample_sensor_data[1]  # O365 connector
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})

        result = serializer.data
        properties = result['properties']

        self.assertEqual(properties['region'], 'global')
        self.assertEqual(properties['ms_tenant_id'], 'tenant-123-456')
        self.assertEqual(properties['setup_link'], 'https://portal.office.com/setup')
        self.assertFalse(properties['audit_logs_enabled'])

    def test_serializer_azure_properties(self):
        """Test serializer maps Azure-specific properties correctly"""
        sensor_data = self.sample_sensor_data[2]  # Azure connector
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})

        result = serializer.data
        properties = result['properties']

        self.assertEqual(properties['region'], 'eastus')
        self.assertEqual(properties['ms_tenant_id'], 'azure-tenant-789')
        self.assertEqual(properties['license_type'], 'standard')

    def test_serializer_error_mapping(self):
        """Test serializer maps error information correctly"""
        sensor_data = self.sample_sensor_data[2]  # Azure connector with error
        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})

        result = serializer.data

        self.assertEqual(result['error'], 'Authentication failed')
        self.assertEqual(result['connector_state'], 'ERROR')

    def test_serializer_handles_missing_fields(self):
        """Test serializer handles missing optional fields gracefully"""
        minimal_sensor_data = {
            'source_id': 'minimal-123',
            'status': 'UNKNOWN'
        }

        serializer = CloudDataSourceSerializerV3_4(minimal_sensor_data, context={'request': self.request})
        result = serializer.data

        self.assertEqual(result['connector_id'], 'minimal-123')
        self.assertIsNone(result['connector_name'])
        self.assertIsNone(result['connector_type'])
        self.assertEqual(result['connector_state'], 'UNKNOWN')
        self.assertIsNone(result['luid'])
        self.assertIsNone(result['created_at'])
        self.assertIsNone(result['updated_at'])
        self.assertIsNone(result['error'])
        self.assertEqual(result['properties'], {})

    def test_serializer_handles_string_error(self):
        """Test serializer handles string error format"""
        sensor_data = {
            'source_id': 'error-test',
            'status': 'ERROR',
            'error': 'Simple error message'
        }

        serializer = CloudDataSourceSerializerV3_4(sensor_data, context={'request': self.request})
        result = serializer.data

        self.assertEqual(result['error'], 'Simple error message')
