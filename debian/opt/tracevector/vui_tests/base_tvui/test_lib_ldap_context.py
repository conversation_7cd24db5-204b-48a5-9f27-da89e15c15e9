import json
from unittest import mock

from tvui.models import EntityWorkQueue, setting, LdapContext
from entity_receiver.entity_helpers import EntityIdentifier
from django.utils import timezone

from vui_tests.vui_testcase import VuiTestCase

from base_tvui.lib_ldap_context import process_ldap_context


USER_AD_CONTEXT_FULL_UPDATE = {
    "event_action": "FULL_UPDATE",
    "DN": "CN=El Well,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "user",
    "context": json.dumps(
        {
            "dn": "CN=El Well,CN=Users,DC=qe-ad,DC=test",
            "data_gathered_at": "Wed Jun 12 08:33:09 2025",
            "accountExpires": ["9999-12-31 23:59:59.999999+00:00"],
            "cn": ["El Well"],
            "dNSHostName": [],
            "department": ["Sales"],
            "description": [" "],
            "displayName": ["El Well"],
            "distinguishedName": ["CN=El Well,CN=Users,DC=qe-ad,DC=test"],
            "info": [],
            "l": [],
            "location": [],
            "macAddress": [],
            "machineRole": [],
            "mail": ["<EMAIL>"],
            "managedBy": [],
            "manager": ["CN=Albert Einstein,CN=Users,DC=qe-ad,DC=test"],
            "memberOf": ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test"],
            "nETBIOSName": [],
            "networkAddress": [],
            "objectClass": ["top", "person", "organizationalPerson", "user"],
            "objectSid": ["S-1-5-21-**********-**********-**********-1108"],
            "operatingSystem": [],
            "physicalLocationObject": [],
            "pwdLastSet": ["2023-11-02 17:52:12.532808+00:00"],
            "sAMAccountName": ["elwell"],
            "servicePrincipalName": [],
            "telephoneNumber": ["**********"],
            "title": ["VP"],
            "userPrincipalName": ["<EMAIL>"],
            "account_disabled": False,
            "account_lockedout": False,
            "password_expired": False,
        }
    ),
}


GROUP_AD_CONTEXT_FULL_UPDATE = {
    "event_action": "FULL_UPDATE",
    "DN": "CN=DnsUpdateProxy,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "group",
    "context": json.dumps(
        {
            "dn": "CN=DnsUpdateProxy,CN=Users,DC=qe-ad,DC=test",
            "data_gathered_at": "Wed Jun 12 08:33:08 2025",
            "cn": ["DnsUpdateProxy"],
            "description": ["DNS clients"],
            "displayName": [],
            "distinguishedName": ["CN=DnsUpdateProxy,CN=Users,DC=qe-ad,DC=test"],
            "groupType": [-**********],
            "managedBy": [],
            "member": [],
            "objectClass": ["top", "group"],
        }
    ),
}


class TestLdapContextProcessingNonDirSync(VuiTestCase):

    def setUp(self):
        setting.objects.create(group="feature", key="ad_context_receiver", value="on")
        self.now = timezone.now()

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_processes_full_update_inserts(self, mock_run_task):
        ldap_contexts = [
            USER_AD_CONTEXT_FULL_UPDATE,
            GROUP_AD_CONTEXT_FULL_UPDATE,
        ]
        eis = [
            EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now),
            EntityIdentifier(batch_uid='abcdef', batch_index=1, forwarder_date=self.now),
        ]

        resp = process_ldap_context(ldap_contexts, eis)
        self.assertEqual(LdapContext.objects.count(), 2)

        user_ldap_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(user_ldap_context.ad_profile_id, USER_AD_CONTEXT_FULL_UPDATE['ad_profile_id'])
        self.assertEqual(user_ldap_context.distinguished_name, USER_AD_CONTEXT_FULL_UPDATE['DN'])
        self.assertEqual(user_ldap_context.ad_context, json.loads(USER_AD_CONTEXT_FULL_UPDATE['context']))
        self.assertEqual(user_ldap_context.ad_context['displayName'], ["El Well"])

        group_ldap_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(group_ldap_context.ad_profile_id, GROUP_AD_CONTEXT_FULL_UPDATE['ad_profile_id'])
        self.assertEqual(group_ldap_context.distinguished_name, GROUP_AD_CONTEXT_FULL_UPDATE['DN'])
        self.assertEqual(group_ldap_context.ad_context, json.loads(GROUP_AD_CONTEXT_FULL_UPDATE['context']))

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_processes_full_update_upsert(self, mock_run_task):
        ldap_contexts = [USER_AD_CONTEXT_FULL_UPDATE, GROUP_AD_CONTEXT_FULL_UPDATE]
        eis = [
            EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now),
            EntityIdentifier(batch_uid='abcdef', batch_index=1, forwarder_date=self.now),
        ]
        resp = process_ldap_context(ldap_contexts, eis)
        self.assertEqual(LdapContext.objects.count(), 2)

        user_ldap_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(user_ldap_context.ad_context['displayName'], ["El Well"])

        group_ldap_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(group_ldap_context.ad_context['description'], ["DNS clients"])

        update_user_context = USER_AD_CONTEXT_FULL_UPDATE.copy()
        update_user_context['context'] = json.loads(update_user_context['context'])
        update_user_context['context']["displayName"] = ["El Well 2"]
        update_user_context['context'] = json.dumps(update_user_context['context'])

        update_group_context = GROUP_AD_CONTEXT_FULL_UPDATE.copy()
        update_group_context['context'] = json.loads(update_group_context['context'])
        update_group_context['context']["description"] = ["DNS clients 2"]
        update_group_context['context'] = json.dumps(update_group_context['context'])

        ldap_contexts = [update_user_context, update_group_context]
        eis = [
            EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now),
            EntityIdentifier(batch_uid='abcdef', batch_index=1, forwarder_date=self.now),
        ]
        resp = process_ldap_context(ldap_contexts, eis)

        user_ldap_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(user_ldap_context.ad_context['displayName'], ["El Well 2"])

        group_ldap_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(group_ldap_context.ad_context['description'], ["DNS clients 2"])


GROUP_ADD = {
    "event_action": "GROUP_ADD",
    "DN": "CN=TestGroup,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "group",
    "context": json.dumps(
        {
            "dn": "CN=TestGroup,CN=Users,DC=qe-ad,DC=test",
            "data_gathered_at": "Thu Jun 14 21:42:45 2025",
            "groupType": [-**********],
            "objectClass": ["top", "group"],
            "dNSHostName": [],
            "machineRole": [],
            "mail": [],
            "info": [],
            "nETBIOSName": [],
            "objectSid": [],
            "manager": [],
            "l": [],
            "displayName": [],
            "location": [],
            "managedBy": [],
            "networkAddress": [],
            "sAMAccountName": [],
            "accountExpires": [],
            "member": [],
            "telephoneNumber": [],
            "memberOf": [],
            "servicePrincipalName": [],
            "userPrincipalName": [],
            "title": [],
            "physicalLocationObject": [],
            "macAddress": [],
            "cn": [],
            "department": [],
            "operatingSystem": [],
            "pwdLastSet": [],
            "description": [],
            "distinguishedName": ["CN=TestGroup,CN=Users,DC=qe-ad,DC=test"],
        }
    ),
}

GROUP_DELETE = {
    "event_action": "GROUP_DELETE",
    "DN": "CN=TestGroup,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "group",
    "context": json.dumps({}),
}

MEMBEROF_ADD = {
    "event_action": "MEMBEROF_ADD",
    "DN": "CN=test gx,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "user",
    "context": json.dumps(["CN=TestGroup,CN=Users,DC=qe-ad,DC=test"]),
}

MEMBEROF_DELETE = {
    "event_action": "MEMBEROF_DELETE",
    "DN": "CN=test gx,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "user",
    "context": json.dumps(["CN=TestGroup,CN=Users,DC=qe-ad,DC=test"]),
}

MEMBER_ADD = {
    "event_action": "MEMBER_ADD",
    "DN": "CN=TestGroup,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "group",
    "context": json.dumps(["CN=test gx,CN=Users,DC=qe-ad,DC=test"]),
}

MEMBER_DELETE = {
    "event_action": "MEMBER_DELETE",
    "DN": "CN=TestGroup,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "group",
    "context": json.dumps(["CN=test gx,CN=Users,DC=qe-ad,DC=test"]),
}

USER_ADD = {
    "event_action": "USER_ADD",
    "DN": "CN=test gx,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "user",
    "context": json.dumps(
        {
            "dn": "CN=test gx,CN=Users,DC=qe-ad,DC=test",
            "data_gathered_at": "Wed Jun 12 08:33:09 2025",
            "accountExpires": ["9999-12-31 23:59:59.999999+00:00"],
            "cn": ["Test GX"],
            "dNSHostName": [],
            "department": ["Sales"],
            "description": [" "],
            "displayName": ["Test GX"],
            "distinguishedName": ["CN=test gx,CN=Users,DC=qe-ad,DC=test"],
            "info": [],
            "l": [],
            "location": [],
            "macAddress": [],
            "machineRole": [],
            "mail": ["<EMAIL>"],
            "managedBy": [],
            "manager": ["CN=Albert Einstein,CN=Users,DC=qe-ad,DC=test"],
            "memberOf": ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test"],
            "nETBIOSName": [],
            "networkAddress": [],
            "objectClass": ["top", "person", "organizationalPerson", "user"],
            "objectSid": ["S-1-5-21-**********-**********-**********-1108"],
            "operatingSystem": [],
            "physicalLocationObject": [],
            "pwdLastSet": ["2023-11-02 17:52:12.532808+00:00"],
            "sAMAccountName": ["testgx"],
            "servicePrincipalName": [],
            "telephoneNumber": ["**********"],
            "title": ["VP"],
            "userPrincipalName": ["<EMAIL>"],
            "account_disabled": False,
            "account_lockedout": False,
            "password_expired": False,
        }
    ),
}

USER_DELETE = {
    "event_action": "USER_DELETE",
    "DN": "CN=test gx,CN=Users,DC=qe-ad,DC=test",
    "ad_profile_id": "71db5354",
    "object_class": "user",
    "context": json.dumps({}),
}


class TestLdapContextProcessingDirSync(VuiTestCase):

    def setUp(self):
        setting.objects.create(group="feature", key="ad_context_receiver", value="on")
        self.now = timezone.now()

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_group_add(self, mock_run_task):
        resp = process_ldap_context([GROUP_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], [])

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_member_add_to_existing_group(self, mock_run_task):
        resp = process_ldap_context([GROUP_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], [])

        resp = process_ldap_context([MEMBER_ADD], [EntityIdentifier(batch_uid='bcdefg', batch_index=0, forwarder_date=self.now)])

        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], ["CN=test gx,CN=Users,DC=qe-ad,DC=test"])

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_member_remove_from_existing_group(self, mock_run_task):
        resp = process_ldap_context([GROUP_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], [])

        resp = process_ldap_context([MEMBER_ADD], [EntityIdentifier(batch_uid='bcdefg', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)
        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], ["CN=test gx,CN=Users,DC=qe-ad,DC=test"])

        resp = process_ldap_context([MEMBER_DELETE], [EntityIdentifier(batch_uid='cdefgh', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)
        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], [])

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_group_delete(self, mock_run_task):
        resp = process_ldap_context([GROUP_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context['member'], [])

        resp = process_ldap_context([GROUP_DELETE], [EntityIdentifier(batch_uid='bcdefg', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_group_context = LdapContext.objects.get(object_class="group")
        self.assertEqual(test_group_context.ad_context, {})
        self.assertEqual(test_group_context.state, LdapContext.DELETED_STATE)

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_user_add(self, mock_run_task):
        resp = process_ldap_context([USER_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_user_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(test_user_context.distinguished_name, "CN=test gx,CN=Users,DC=qe-ad,DC=test")
        self.assertEqual(test_user_context.ad_context['memberOf'], ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test"])

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_user_add_to_group(self, mock_run_task):
        resp = process_ldap_context([USER_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_user_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(test_user_context.ad_context['memberOf'], ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test"])

        resp = process_ldap_context([MEMBEROF_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])
        test_user_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(
            test_user_context.ad_context['memberOf'],
            ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test", "CN=TestGroup,CN=Users,DC=qe-ad,DC=test"],
        )

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_user_removed_from_group(self, mock_run_task):
        resp = process_ldap_context([USER_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_user_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(test_user_context.ad_context['memberOf'], ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test"])

        resp = process_ldap_context([MEMBEROF_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])
        test_user_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(
            test_user_context.ad_context['memberOf'],
            ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test", "CN=TestGroup,CN=Users,DC=qe-ad,DC=test"],
        )

        resp = process_ldap_context([MEMBEROF_DELETE], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])
        test_user_context = LdapContext.objects.get(object_class="user")
        self.assertEqual(test_user_context.ad_context['memberOf'], ["CN=Domain Users,CN=Users,DC=qe-ad,DC=test"])

    @mock.patch('base_tvui.lib_ldap_context.run_task')
    def test_user_delete(self, mock_run_task):
        resp = process_ldap_context([USER_ADD], [EntityIdentifier(batch_uid='abcdef', batch_index=0, forwarder_date=self.now)])

        self.assertEqual(LdapContext.objects.count(), 1)

        test_user_context = LdapContext.objects.get(object_class="user")

        resp = process_ldap_context([USER_DELETE], [EntityIdentifier(batch_uid='bcdefg', batch_index=0, forwarder_date=self.now)])
