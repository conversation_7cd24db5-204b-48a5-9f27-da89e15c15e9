# Copyright (c) 2016-2018 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
"""
Tests notification framework

Created on Mar 30, 2016

@author: sedwards
"""

import json
import dateutil.parser
from datetime import timedelta
from django.utils import timezone
from unittest.mock import patch

from lockdown import lockdown_utils
from base_tvui.lib_notifications import still_targets_key_asset
from base_tvui.notification_render import (
    AlarmedAccountNotification,
    AlarmedHostNotification,
    AssignmentNotificationRenderer,
    CampaignNotification,
    HostLockdownNotificationRenderer,
    SystemHealthNotification,
    SystemNotification,
    TestRenderer,
    AzureADAutoLockdownNotificationRenderer,
    ADLockdownNotificationRenderer,
)

from tvui.models import (
    Account,
    AccountGroup,
    Campaign,
    CampaignEvent,
    CampaignMembership,
    DetectionSchema,
    GroupCollection,
    HostGroup,
    LinkedAccount,
    User,
    detection,
    detection_detail,
    host,
    host_session,
    setting,
    HostRole,
)
from vui_tests.vui_testcase import VuiTestCase
from vui_tests.base_tvui.test_lib_account import create_accounts


class TestBasicNotificationRender(VuiTestCase):
    """
    Tests the dummy TestRenderer Class
    """

    def test_test_render_email(self):
        test_data = {'one': 'button', 'two': ['my', 'shoes']}
        r = TestRenderer()
        ret_val = r.render_for_email(test_data)

        # two elements in the ret val?
        self.assertEqual(len(ret_val), 2)

        # test subject line
        self.assertIn('subject', ret_val)
        self.assertEqual(ret_val['subject'], 'Test Render Email')

        # test Content
        self.assertIn('contents', ret_val)
        self.assertIn('plaintext', ret_val['contents'])
        self.assertIn('html', ret_val['contents'])

        msg_body = ret_val['contents']['plaintext']

        message = msg_body[: msg_body.index('{')]
        expected_message = 'Test Text Email content. Notification data: '
        self.assertEqual(message, expected_message)

        decoded_data = json.loads(msg_body[msg_body.index('{') :])
        expected_data = {'one': 'button', 'two': ['my', 'shoes']}
        self.assertEqual(decoded_data, expected_data)


class TestAlarmedAccountNotificationRenderer(VuiTestCase):
    """
    Tests Alarmed Host Notification Renderer
    """

    def setUp(self):
        self.now = timezone.now()
        self.scoring_schema = '{"scoring": {"account": {"archetypeBucket": "cncHC", "activeTime": 14}}}'
        setting.objects.create(group='priority_entities', key='score', value=80)

        # Creating an account will auto create the linked account and attach it to the account
        self.account = Account.objects.create(
            uid='<EMAIL>',
            urgency_score=50,
            attack_rating=5,
            account_type=Account.TYPE_KERBEROS,
        )
        self.detection_test_1 = detection.objects.create(
            type='aws_s3_discovery',
            type_vname='AWS S3 Enumeration',
            category='RECONNAISSANCE',
            last_timestamp=self.now,
            state='active',
            t_score=90,
            c_score=90,
            account=self.account,
        )
        self.detection_test_2 = detection.objects.create(
            type='aws_logging_disabled',
            type_vname='AWS Logging Disabled',
            category='LATERAL MOVEMENT',
            last_timestamp=self.now - timedelta(days=4),
            state='active',
            t_score=60,
            c_score=60,
            account=self.account,
        )
        self.detection_test_3 = detection.objects.create(
            type='aws_cloudtrail_logging_modified',
            type_vname='AWS CloudTrail Logging Modified',
            category='LATERAL MOVEMENT',
            last_timestamp=self.now - timedelta(days=24),
            state='active',
            account=self.account,
            t_score=40,
            c_score=40,
        )
        DetectionSchema.objects.create(type='aws_s3_discovery', schema=self.scoring_schema)
        DetectionSchema.objects.create(type='aws_logging_disabled', schema=self.scoring_schema)
        DetectionSchema.objects.create(type='aws_cloudtrail_logging_modified', schema=self.scoring_schema)

    def test_alarmed_account_notification(self):
        # TODO: offset the linked_account.id to a higher number so that account id does not match linked account id
        notification_data = {
            'reason': 'account',
            'account': {'id': self.account.linked_account.id, 'account_t_score': 30, 'account_c_score': 30},
            'detections': [
                self.detection_test_1.id,
                self.detection_test_2.id,
                self.detection_test_3.id,
            ],
        }

        account_renderer_test = AlarmedAccountNotification()
        account_renderer_test.render_for_email(notification_data)
        self.assertEqual(account_renderer_test.alert_data['notification_subject'], 'Account alert - <EMAIL>')
        self.assertEqual(account_renderer_test.alert_data['reason'], 'Account Alert - <EMAIL>')
        self.assertEqual(account_renderer_test.alert_data['orig_reason'], 'account')
        self.assertEqual(account_renderer_test.alert_data['brief'], 'Account Alert')
        self.assertEqual(account_renderer_test.alert_data['total_account_active_detection_count'], 3)
        self.assertEqual(account_renderer_test.alert_data['url'], 'settings/notifications/')
        self.assertEqual(account_renderer_test.alert_data['account_attack_profile'], 'AWS Threat Actor')
        self.assertEqual(account_renderer_test.alert_data['account_groups'], '-')
        self.assertEqual(account_renderer_test.alert_data['account_importance'], 'Medium (Default)')
        self.assertEqual(account_renderer_test.alert_data['most_recent_detection'], self.detection_test_1)
        # Refresh strips miliseconds off last_timestamp, which is needed for this compare to work
        self.detection_test_1.refresh_from_db()
        self.assertEqual(account_renderer_test.alert_data['most_recent_detection'].last_timestamp, self.detection_test_1.last_timestamp)

    def test_papi_host_detail_not_present(self):
        self.detection_papi = detection.objects.create(
            type='papi_breach',
            type_vname='Privilege Anomaly: Unusual Trio',
            category='LATERAL MOVEMENT',
            last_timestamp=self.now,
            state='active',
            t_score=90,
            c_score=90,
            account=self.account,
        )
        DetectionSchema.objects.create(type='papi_breach', schema=self.scoring_schema)
        notification_data = {
            'reason': 'account',
            'account': {'id': self.account.linked_account.id, 'account_t_score': 30, 'account_c_score': 30},
            'detections': [
                self.detection_papi.id,
            ],
        }

        account_renderer_test = AlarmedAccountNotification()
        account_renderer_test.render_for_email(notification_data)
        self.assertEqual(account_renderer_test.alert_data['papi_host'], None)
        self.assertEqual(account_renderer_test.alert_data['papi_host_session_id'], None)
        self.assertEqual(account_renderer_test.alert_data['papi_host_source'], None)
        self.assertEqual(account_renderer_test.alert_data['reason'], 'Account Alert - <EMAIL>')
        self.assertEqual(account_renderer_test.alert_data['most_recent_detection'], self.detection_papi)

    def test_alarm_on_linked_account_group_membership(self):
        setting.objects.create(group='feature', key='linked_account_groups', value='on')

        notification_data = {
            'reason': 'account',
            'account': {'id': self.account.linked_account.id, 'account_t_score': 30, 'account_c_score': 30},
            'detections': [
                self.detection_test_1.id,
                self.detection_test_2.id,
                self.detection_test_3.id,
            ],
        }

        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='passw0rd')
        account_group1 = AccountGroup.objects.create(name='group1', type='Account', last_modified_by=self.user)
        account_group1.linked_accounts.add(self.account.linked_account)

        # Alert should indicate alarmed account is member of this group

        account_renderer_test = AlarmedAccountNotification()
        account_renderer_test.render_for_email(notification_data)

        self.assertEqual(account_renderer_test.alert_data['account_groups'], 'group1')

        # Multiple groups
        account_group2 = AccountGroup.objects.create(name='sample_group', type='Account', last_modified_by=self.user)
        account_group2.linked_accounts.add(self.account.linked_account)

        account_group3 = AccountGroup.objects.create(name='my test group', type='Account', last_modified_by=self.user)
        account_group3.linked_accounts.add(self.account.linked_account)

        account_renderer_test = AlarmedAccountNotification()
        account_renderer_test.render_for_email(notification_data)

        account_groups = sorted([g.strip() for g in account_renderer_test.alert_data['account_groups'].split(",")])
        self.assertEqual(account_groups, ['group1', 'my test group', 'sample_group'])


class TestAlarmedHostNotificationRenderer(VuiTestCase):
    """
    Tests Alarmed Host Notification Renderer
    """

    def setUp(self):
        now = timezone.now()
        self.scoring_schema = '{"scoring": {"host": {"archetypeBucket": "cncHC", "activeTime": 14}}}'

        self.host_test_1 = host.objects.create(name='host_alert_true', t_score=60, c_score=60, state='active')
        self.host_session_1 = host_session.objects.create(
            host=self.host_test_1, session_luid='luid_1', ip_address='10.0.0.0', start=now - timedelta(days=3), end=timezone.now()
        )
        self.host_test_2 = host.objects.create(name='key_asset_host', t_score=0, c_score=0, state='inactive', key_asset=True)
        self.host_session_2 = host_session.objects.create(
            host=self.host_test_2, session_luid='luid_2', ip_address='********', start=now - timedelta(days=9), end=None
        )
        self.detection_test_1 = detection.objects.create(
            type='smb_ransomware',
            type_vname='Ransomware File Activity',
            category='RECONNAISSANCE',
            last_timestamp=now,
            state='active',
            t_score=90,
            c_score=90,
            src_ip='********',
            host_session_id=self.host_session_1.id,
        )
        self.detection_test_2 = detection.objects.create(
            type='bitcoin',
            type_vname='Cryptocurrency Mining',
            category='BOTNET ACTIVITY',
            last_timestamp=now - timedelta(days=4),
            state='active',
            t_score=70,
            c_score=40,
            src_ip='********',
            host_session_id=self.host_session_1.id,
        )
        self.detection_test_3 = detection.objects.create(
            type='kerberos_client',
            type_vname='Kerberos Client Activity',
            category='LATERAL MOVEMENT',
            last_timestamp=now - timedelta(days=24),
            state='active',
            t_score=50,
            c_score=50,
            src_ip='********',
            host_session_id=self.host_session_1.id,
        )
        # targets a key asset
        self.dd_test_3 = detection_detail.objects.create(
            type='kerberos_client',
            dst_host_session=self.host_session_2,
            dst_ip=self.host_session_2.ip_address,
            first_timestamp=now - timedelta(days=24),
            last_timestamp=now - timedelta(days=24),
            host_detection=self.detection_test_3,
            src_session_luid=self.host_session_1.session_luid,
            src_ip=self.host_session_1.ip_address,
        )
        self.detection_test_4 = detection.objects.create(
            type='hidden_dns_tunnel_exfil',
            type_vname='Hidden DNS Tunnel',
            category='EXFILTRATION',
            last_timestamp=now - timedelta(days=44),
            state='active',
            t_score=40,
            c_score=80,
            src_ip='********',
            host_session_id=self.host_session_1.id,
        )

        self.notification_data = {
            'host': {'host_t_score': self.host_test_1.t_score, 'id': self.host_test_1.id, 'host_c_score': self.host_test_1.c_score},
            'reason': 'host',
            'detections': [self.detection_test_1.id, self.detection_test_2.id, self.detection_test_3.id, self.detection_test_4.id],
        }

        self.add_patch(
            'get_hostname_and_default_ip',
            patch('base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('***************', '***************')),
        )

    def test_alarmed_host_notification(self):
        host_renderer_test = AlarmedHostNotification()
        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['reason'], 'Host Alert - {}'.format(self.host_test_1.name))
        self.assertEqual(host_renderer_test.alert_data['host'], self.host_test_1)
        self.assertEqual(host_renderer_test.alert_data['host_tscore'], 60)
        self.assertEqual(host_renderer_test.alert_data['host_cscore'], 60)
        self.assertEqual(host_renderer_test.alert_data['host_severity'], 'Critical')
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'], self.detection_test_1)
        self.assertEqual(host_renderer_test.alert_data['other_recent_detections_list'][0], self.detection_test_2)
        self.assertEqual(host_renderer_test.alert_data['other_recent_detections_list'][1], self.detection_test_3)
        self.assertEqual(host_renderer_test.alert_data['other_recent_detections_list'][2], self.detection_test_4)
        self.assertEqual(host_renderer_test.alert_data['brief'], 'Host Alert')
        self.assertEqual(host_renderer_test.alert_data['attack_name'], '-')
        self.assertEqual(host_renderer_test.alert_data['attack_profile'], '-')
        self.assertEqual(len(host_renderer_test.alert_data['profile_indicators']), 0)
        self.assertEqual(host_renderer_test.alert_data['groups'], '-')
        self.assertEqual(host_renderer_test.alert_data['importance'], 'Medium (Default)')

        # Refresh strips miliseconds off last_timestamp, which is needed for this compare to work
        self.detection_test_1.refresh_from_db()
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'].last_timestamp, self.detection_test_1.last_timestamp)

    @patch('base_tvui.lib_host.HostAttributeAPI.get_attributes')
    def test_alarmed_host_notification_role_using_HAPI(self, mock_get_attributes):
        host_renderer_test = AlarmedHostNotification()
        host.objects.update_or_create(id=self.host_test_1.id, defaults={"host_luid": "abcDEF"})
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "off"})
        mock_get_attributes.return_value = [
            {
                "id": 1,
                "first_seen": "2020-10-22T01:22:10Z",
                "value": "dhcp",
                "host_luid": "abcDEF",
                "role_display_name": "DHCP Server",
                "last_seen": "2020-10-22T01:22:10Z",
            }
        ]

        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['reason'], 'Host Alert - {}'.format(self.host_test_1.name))
        self.assertEqual(host_renderer_test.alert_data['host'], self.host_test_1)
        self.assertEqual(host_renderer_test.alert_data['host_tscore'], 60)
        self.assertEqual(host_renderer_test.alert_data['host_cscore'], 60)
        self.assertEqual(host_renderer_test.alert_data['host_severity'], 'Critical')
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'], self.detection_test_1)
        self.assertEqual(host_renderer_test.alert_data['other_recent_detections_list'][0], self.detection_test_2)
        self.assertEqual(host_renderer_test.alert_data['other_recent_detections_list'][1], self.detection_test_3)
        self.assertEqual(host_renderer_test.alert_data['other_recent_detections_list'][2], self.detection_test_4)
        self.assertEqual(host_renderer_test.alert_data['brief'], 'Host Alert')
        self.assertEqual(host_renderer_test.alert_data['attack_name'], '-')
        self.assertEqual(host_renderer_test.alert_data['attack_profile'], '-')
        self.assertEqual(len(host_renderer_test.alert_data['profile_indicators']), 0)
        self.assertEqual(host_renderer_test.alert_data['groups'], '-')
        self.assertEqual(host_renderer_test.alert_data['importance'], 'Medium (Default)')
        self.assertEqual(host_renderer_test.alert_data['host_roles'], 'DHCP Server')

        # Refresh strips miliseconds off last_timestamp, which is needed for this compare to work
        self.detection_test_1.refresh_from_db()
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'].last_timestamp, self.detection_test_1.last_timestamp)

    @patch('base_tvui.lib_host.HostAttributeAPI.get_attributes')
    def test_alarmed_host_notification_no_role_using_HAPI(self, mock_get_attributes):
        host_renderer_test = AlarmedHostNotification()
        host.objects.update_or_create(id=self.host_test_1.id, defaults={"host_luid": "abcDEF"})
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "off"})
        mock_get_attributes.return_value = []

        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['host_roles'], '')

        host.objects.update_or_create(id=self.host_test_1.id, defaults={"host_luid": None})
        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['host_roles'], None)

    def test_alarmed_host_notification_role_using_host_role_table(self):
        host_renderer_test = AlarmedHostNotification()
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "on"})
        HostRole.objects.create(host_id=self.host_test_1.id, role_name='Web Server', source='HAPI')
        HostRole.objects.create(host_id=self.host_test_1.id, role_name='DNS Server', source='HAPI')
        host.objects.update_or_create(id=self.host_test_1.id, defaults={"host_luid": "abcDEF"})

        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['host_roles'], 'DNS Server, Web Server')

    def test_alarmed_host_notification_no_role_using_host_role_table(self):
        host_renderer_test = AlarmedHostNotification()
        setting.objects.update_or_create(group="feature", key="use_host_role_table", defaults={"value": "on"})
        host.objects.update_or_create(id=self.host_test_1.id, defaults={"host_luid": "abcDEF"})

        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['host_roles'], '')

        host.objects.update_or_create(id=self.host_test_1.id, defaults={"host_luid": None})

        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['host_roles'], None)

    def test_alarmed_host_notification_with_group(self):
        self.user = User.objects.create_user('vadmin', email='<EMAIL>', password='passw0rd')
        hg = HostGroup.objects.create(name='HostGroup 0', type=GroupCollection.HOST, description='test desc', last_modified_by=self.user)
        hg.hosts.add(self.host_test_1)
        hg.hosts.add(self.host_test_2)

        host_renderer_test = AlarmedHostNotification()
        host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['groups'], 'HostGroup 0')
        self.assertEqual(host_renderer_test.alert_data['importance'], 'Medium')

    def test_alarmed_host_notification_with_attack_profile(self):
        DetectionSchema.objects.create(type='smb_ransomware', schema=self.scoring_schema)
        DetectionSchema.objects.create(type='bitcoin', schema=self.scoring_schema)
        DetectionSchema.objects.create(type='kerberos_client', schema=self.scoring_schema)
        DetectionSchema.objects.create(type='hidden_dns_tunnel_exfil', schema=self.scoring_schema)

        host_renderer_test = AlarmedHostNotification()
        host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['attack_name'], 'Active detections are behaviors associated with ransomware.')
        self.assertEqual(host_renderer_test.alert_data['attack_profile'], 'Ransomware')
        self.assertEqual(getattr(host_renderer_test.alert_data['profile_indicators'][0], 'last_det_id'), self.detection_test_1.id)

    def test_stll_targets_key_asset(self):
        self.detection_test_3.targets_key_asset = True
        self.detection_test_3.save()
        host_renderer_test = AlarmedHostNotification()
        # detections 3, 5 are targeting a key asset
        dets = detection.objects.all()
        result = still_targets_key_asset(dets)
        self.assertTrue(result)
        # detections 1, 2, 4 are not targeting a key asset
        dets = detection.objects.filter(targets_key_asset=False)
        result = still_targets_key_asset(dets)
        self.assertFalse(result)
        # detection 1 is not targeting a key asset
        self.detection_test_1.targets_key_asset = True
        self.detection_test_1.save()
        dets = detection.objects.filter(id=self.detection_test_1.id)
        result = still_targets_key_asset(dets)
        self.assertFalse(result)

    def test_highlighted_detection(self):
        d_cats = setting.objects.create(group='alert', key='detection_categories', value='')
        d_types = setting.objects.create(group='alert', key='detection_types', value='')
        host_renderer_test = AlarmedHostNotification()
        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'], self.detection_test_1)

        # test detection types configured result in configured type as most recent
        d_types.value = 'frontwatch,bitcoin'
        d_types.save()
        host_renderer_test = AlarmedHostNotification()
        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'], self.detection_test_2)  # bitcoin

        # test detection categories
        d_cats.value = 'command_and_control,exfiltration'
        d_cats.save()
        d_types.value = 'frontwatch,cnc_dga'
        d_types.save()
        host_renderer_test = AlarmedHostNotification()
        test = host_renderer_test.render_for_email(self.notification_data)
        self.assertEqual(host_renderer_test.alert_data['most_recent_detection'], self.detection_test_4)  # hidden dns exfil


class TestCampaignNotificationRenderer(VuiTestCase):
    """
    Tests Campaign Notification Renderer
    """

    def setUp(self):
        self.add_patch(
            'get_hostname_and_default_ip',
            patch('base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('***************', '***************')),
        )
        ext_ip = '*******'
        self.now = timezone.now()
        self.campaign = Campaign.objects.create(
            external_ip=ext_ip,
            external_domain='bad.foobar.com',
            name='bad.foobar.com-1',
            state=Campaign.ACTIVE,
            couch_note_id='FpIwlw1',
            last_updated=self.now - timedelta(hours=1),
            reason_closed=None,
        )

        for x in range(0, 3):
            src_host_ip = '127.0.0.{}'.format(x)
            src_host_name = 'campaign_host_{}'.format(x)
            det_note_id = 'DpIwlw{}'.format(x)
            host_x = host.objects.create(
                last_source=src_host_ip,
                name=src_host_name,
                t_score=75,
                c_score=80,
                last_detection_timestamp=self.now,
                host_luid='nh1{}'.format(x),
            )
            hs_x = host_session.objects.create(
                ip_address=src_host_ip, start=self.now - timedelta(days=1), end=self.now, session_luid=str(x), host=host_x
            )
            det_x = detection.objects.create(
                type='http_cnc',
                type_vname="Suspicious HTTP",
                category="COMMAND & CONTROL",
                src_ip=src_host_ip,
                host_session=hs_x,
                t_score=55,
                c_score=60,
                last_timestamp=self.now,
            )
            dd_x1 = detection_detail.objects.create(
                type='http_cnc',
                src_ip='127.0.0.1',
                dst_ip=ext_ip,
                dst_dns='bad.foobar.com',
                last_timestamp=self.now,
                host_detection=det_x,
                couch_note_id=det_note_id,
            )
            dd_x2 = detection_detail.objects.create(
                type='http_cnc',
                src_ip='127.0.0.1',
                dst_ip=ext_ip,
                dst_dns='bad.foobar.com',
                last_timestamp=self.now,
                host_detection=det_x,
                couch_note_id=det_note_id,
            )

            campaign_mem_x = CampaignMembership.objects.create(
                campaign=self.campaign, host_session_luid=hs_x.session_luid, host_session=hs_x, latest_conn_ts=self.now - timedelta(hours=x)
            )

            campaign_event_x1 = CampaignEvent.objects.create(
                campaign=self.campaign,
                event_type='detection',
                detail_type='detection_detail',
                event_name='Suspicious HTTP',
                campaign_member=campaign_mem_x,
                recorded_hostname=src_host_name,
                recorded_dest_name='Dr. Evil',
                recorded_src_ip='*********',
                recorded_dest_ip=ext_ip,
                timestamp=self.now - timedelta(hours=x),
                detection_note_id=det_note_id,
                det_detail=dd_x1,
                is_original_det=(x == 0),
            )

            campaign_event_x2 = CampaignEvent.objects.create(
                campaign=self.campaign,
                event_type='connection',
                event_name='Connection',
                campaign_member=campaign_mem_x,
                recorded_hostname=src_host_name,
                recorded_dest_name='Dr. Evil',
                recorded_src_ip='*********',
                recorded_dest_ip=ext_ip,
                is_original_det=False,
                timestamp=self.now - timedelta(hours=x),
                detection_note_id=det_note_id,
            )

        self.reason_event = self.campaign.events.filter(is_original_det=True, partial=False).order_by('-timestamp').first()

    def test_campaign_added_notification(self):
        notification_data = {'campaign_id': self.campaign.id, 'reason': 'added'}

        # test the rendering function
        campaign_notification_renderer = CampaignNotification()
        test_render = campaign_notification_renderer.render_for_email(notification_data)

        # validate the rendered data for template
        rendered_notification_data = campaign_notification_renderer._get_campaign_notification_data(notification_data)
        self.assertEqual(rendered_notification_data['campaign']['id'], self.campaign.id)
        self.assertEqual(rendered_notification_data['campaign']['name'], self.campaign.name)
        self.assertEqual(rendered_notification_data['campaign']['status'], self.campaign.state)
        self.assertEqual(rendered_notification_data['campaign']['internalHosts'], 3)
        self.assertEqual(rendered_notification_data['campaign']['detectionCount'], 3)
        self.assertEqual(rendered_notification_data['campaign']['reason']['recordedSource'], self.reason_event.recorded_hostname)
        self.assertEqual(
            rendered_notification_data['campaign']['reason']['srcHostId'], self.reason_event.campaign_member.host_session.host.id
        )
        self.assertEqual(
            rendered_notification_data['campaign']['reason']['recordedDestination'],
            self.reason_event.recorded_dest_name or self.reason_event.recorded_dest_ip,
        )
        self.assertEqual(rendered_notification_data['campaign']['reason']['recordedDetectionName'], self.reason_event.event_name)
        self.assertEqual(rendered_notification_data['campaign']['reason']['detectionId'], self.reason_event.detail.host_detection.id)
        self.assertEqual(
            rendered_notification_data['campaign']['firstActivity'], self.campaign.events.all().earliest('timestamp').timestamp
        )
        self.assertEqual(rendered_notification_data['campaign']['lastActivity'], self.campaign.events.all().latest('timestamp').timestamp)
        self.assertEqual(rendered_notification_data['notification_reason'], 'added')
        self.assertEqual(
            rendered_notification_data['notification_subject'],
            "New Attack Campaign: {}".format(rendered_notification_data['campaign']['name']),
        )

    def test_campaign_closed_notification(self):
        self.campaign.state = Campaign.CLOSED
        self.campaign.reason_closed = 'idled'
        self.campaign.save()
        notification_data = {'campaign_id': self.campaign.id, 'reason': 'closed'}

        # test the rendering function
        campaign_notification_renderer = CampaignNotification()
        test_render = campaign_notification_renderer.render_for_email(notification_data)

        # validate the rendered data for template
        rendered_notification_data = campaign_notification_renderer._get_campaign_notification_data(notification_data)
        self.assertEqual(rendered_notification_data['campaign']['id'], self.campaign.id)
        self.assertEqual(rendered_notification_data['campaign']['name'], self.campaign.name)
        self.assertEqual(rendered_notification_data['campaign']['status'], self.campaign.state)
        self.assertEqual(rendered_notification_data['campaign']['reasonClosed'], self.campaign.reason_closed)
        self.assertEqual(rendered_notification_data['campaign']['internalHosts'], 3)
        self.assertEqual(rendered_notification_data['campaign']['detectionCount'], 3)
        self.assertEqual(
            rendered_notification_data['campaign']['firstActivity'], self.campaign.events.all().earliest('timestamp').timestamp
        )
        self.assertEqual(rendered_notification_data['campaign']['lastActivity'], self.campaign.events.all().latest('timestamp').timestamp)
        self.assertEqual(rendered_notification_data['notification_reason'], 'closed')
        self.assertEqual(
            rendered_notification_data['notification_subject'],
            "Attack Campaign: {} Closed".format(rendered_notification_data['campaign']['name']),
        )


class TestSystemNotification(VuiTestCase):
    def setUp(self):
        self.add_patch(
            'get_hostname_and_default_ip',
            patch('base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('***************', '***************')),
        )

        self.system_notification_renderer = SystemNotification()
        self.system_health_notification_renderer = SystemHealthNotification()

    def test_render_packets_dropped(self):
        notification_data = {
            "message_parameters": {
                "silenced": False,
                "timestamp": **********,
                "last_state_change": **********,
                "silenced_by": [],
                "id": "ab7eeaa6-9a0a-45f6-b977-0cfca685653c",
                "occurrences_watermark": 1,
                "client": {
                    "socket": {"bind": "127.0.0.1", "port": 3030},
                    "subscriptions": ["common", "client:A21000000000161"],
                    "timestamp": **********,
                    "version": "1.9.0",
                    "address": "127.0.0.1",
                    "name": "A21000000000161",
                },
                "occurrences": 1,
                "action": "resolve",
                "last_ok": **********,
                "check": {
                    "status": 0,
                    "executed": **********,
                    "total_state_change": 11,
                    "handlers": ["syslog", "email"],
                    "issued": **********,
                    "interval": 3600,
                    "command": "sys_check -o sensu --functions colossus_packet_drop_rate",
                    "subscribers": ["common"],
                    "contact_customer_support": True,
                    "duration": 4.048,
                    "output": {
                        "retcode": 0,
                        "truncate": True,
                        "title": "Packet Processing Engine",
                        "tags": ["customer"],
                        "module": "colossus",
                        "error": "",
                        "message": "Packet processing is healthy on A21000000000161",
                        "runtime": 0.006849631999999994,
                        "name": "colossus_packet_drop_rate",
                    },
                    "history": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "2", "0"],
                    "type": "standard",
                    "name": "colossus_packet_drop_rate",
                },
            },
            "message_key": "system_health",
        }
        resp = self.system_health_notification_renderer.render_for_email(notification_data)
        self.assertTrue(isinstance(resp, dict))
        self.assertEqual(resp.get('subject'), 'Vectra system alert')

    def test_render_for_email_missing_params(self):
        notification_data = {'message_key': 'vm_not_covered', 'message_parameters': {'vms': 'test,abc'}}
        with self.assertRaises(KeyError):
            self.system_notification_renderer.render_for_email(notification_data)


class TestAssignmentNotificationRenderer(VuiTestCase):
    def setUp(self):
        self.now = timezone.now()
        self.NEW = AssignmentNotificationRenderer.NEW
        self.REMOVED = AssignmentNotificationRenderer.REMOVED
        self.assignment_notification_renderer = AssignmentNotificationRenderer()
        self.user1 = User.objects.create_user('user1', email='<EMAIL>', password='passw0rd')
        self.user2 = User.objects.create_user('user2', email='<EMAIL>', password='passw0rd')
        self.cognito_user = User.objects.create_user('cognito', email='<EMAIL>', password='passw0rd')

        self.src_host = host.objects.create(
            last_source='*********', name='cmp_host', t_score=75, c_score=80, host_luid='nh1', targets_key_asset=1
        )
        self.src_host_session = host_session.objects.create(
            ip_address='127.0.0.1', start=self.now - timedelta(days=1), end=self.now, session_luid='rhs1', host=self.src_host
        )

        self.det = detection.objects.create(
            type='http_cnc',
            type_vname="Suspicious HTTP",
            category="COMMAND & CONTROL",
            src_ip='*********',
            host_session=self.src_host_session,
            t_score=55,
            c_score=60,
            last_timestamp=self.now,
            targets_key_asset=1,
        )

        base_account = Account.objects.create(uid='<EMAIL>', account_type='kerberos', state='active')
        self.src_account = base_account.linked_account

        self.add_patch(
            'get_hostname_and_default_ip',
            patch('base_tvui.lib_tv.get_hostname_and_default_ip', return_value=('***************', '***************')),
        )

    def shortDescription(self):
        return None

    def test_assignment_for_host_notification_removed(self):
        """
        removed assignment notification email for a host
        """
        self.assignment = self.src_host.assign(self.user1, self.user2)

        notification_data = {
            'message_parameters': {'check': {'status': '', 'contact_customer_support': False}},
            'assignment_id': self.assignment.id,
            'assigned_to_id': self.assignment.user.id,
            'assigned_to': self.assignment.user.username,
            'assigned_on': self.assignment.date_assigned,
            'action': self.REMOVED,
            'assigned_by': self.assignment.assigned_by.username if self.assignment.assigned_by else 'Inactive User',
            'assignment_type_id': self.assignment.type_id,
            'assignment_obj_type': self.assignment.obj_type,
        }

        notification_render_data = self.assignment_notification_renderer.get_assignment_data(notification_data)
        self.assertTrue(notification_render_data['is_host_assignment'])
        self.assertEqual(notification_render_data['host_id'], self.src_host.id)
        self.assertEqual(notification_render_data['hostname'], self.src_host.name)
        self.assertEqual(notification_render_data['assigned_by'], self.assignment.assigned_by.username)
        self.assertEqual(notification_render_data['assigned_to'], self.assignment.user.username)

        return_value = self.assignment_notification_renderer.render_for_email(notification_data)

        self.assertIn('contents', return_value)
        self.assertEqual('Host unassigned from you', return_value['subject'])

    def test_assignment_for_account_notification(self):
        """Assignment notification for account"""
        assignment = self.src_account.assign(self.user1, self.user2)

        notification_data = {
            'assignment_id': assignment.id,
            'assigned_to_id': assignment.user.id,
            'assigned_to': assignment.user.username,
            'assigned_on': assignment.date_assigned,
            'action': self.NEW,
            'assigned_by': assignment.assigned_by.username if assignment.assigned_by else 'Inactive User',
            'assignment_type_id': assignment.type_id,
            'assignment_obj_type': assignment.obj_type,
        }

        notification_render_data = self.assignment_notification_renderer.get_assignment_data(notification_data)
        self.assertTrue(notification_render_data['is_account_assignment'])
        self.assertEqual(notification_render_data['account_id'], self.src_account.id)
        self.assertEqual(notification_render_data['account_uid'], self.src_account.uid)
        self.assertEqual(notification_render_data['assigned_by'], assignment.assigned_by.username)
        self.assertEqual(notification_render_data['assigned_to'], assignment.user.username)

        return_value = self.assignment_notification_renderer.render_for_email(notification_data)

        self.assertIn('contents', return_value)
        self.assertEqual('Account assigned to you', return_value['subject'])

    def test_assignment_for_host_notifications_customer_support(self):
        """
        new assignment notification email for a host
        """
        self.assignment = self.src_host.assign(self.user1, self.user2)

        notification_data = {
            'message_parameters': {'check': {'status': '', 'contact_customer_support': True}},
            'assignment_id': self.assignment.id,
            'assigned_to_id': self.assignment.user.id,
            'assigned_to': self.assignment.user.username,
            'assigned_on': self.assignment.date_assigned,
            'action': self.NEW,
            'assigned_by': self.assignment.assigned_by.username if self.assignment.assigned_by else 'Inactive User',
            'assignment_type_id': self.assignment.type_id,
            'assignment_obj_type': self.assignment.obj_type,
        }

        notification_render_data = self.assignment_notification_renderer.get_assignment_data(notification_data)
        self.assertTrue(notification_render_data['is_host_assignment'])
        self.assertEqual(notification_render_data['host_id'], self.src_host.id)
        self.assertEqual(notification_render_data['hostname'], self.src_host.name)
        self.assertEqual(notification_render_data['assigned_by'], self.assignment.assigned_by.username)
        self.assertEqual(notification_render_data['assigned_to'], self.assignment.user.username)

        return_value = self.assignment_notification_renderer.render_for_email(notification_data)

        self.assertIn('contents', return_value)
        self.assertEqual('Host assigned to you', return_value['subject'])


class TestHostLockdownNotificationRenderer(VuiTestCase):
    def setUp(self):
        self.maxDiff = None

        render_patch = patch('base_tvui.notification_render.HostLockdownNotificationRenderer.render_email_template')
        self.render_mock = render_patch.start()
        self.addCleanup(render_patch.stop)

        self.user = User.objects.create(username='<EMAIL>', account_type=User.SPECIAL)
        self.host = host.objects.create(name='test.host')

        self.default_auto_settings = {  # these don't matter for rendering logic, but should still be passed through
            'lockdown_auto_certainty': 75,
            'lockdown_auto_enabled': False,
            'lockdown_auto_privilege': 5,
            'lockdown_auto_threat': 75,
            'lockdown_auto_timeout_minutes': 60,
            'lockdown_enabled': False,
        }

        self.renderer = HostLockdownNotificationRenderer()

    def test_host_manual_lock(self):
        self.renderer.render_for_email(
            {
                'success': True,
                'action': 'locked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': self.user.id,
                'actor_id': self.user.id,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': '2020-06-01T01:00:00Z',
                'locked_duration_min': 60,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Microsoft Defender ATP Host Manually Isolated',
            'host': self.host,
            'edr_name': 'Microsoft Defender ATP',
            'action': {'inverse_verb': 'unisolated', 'type': 'locked', 'verb': 'isolated', 'verb_infinitive': 'isolate'},
            'actor': self.user,
            'user': self.user,
            'is_auto': False,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': dateutil.parser.parse('2020-06-01T01:00:00Z'),
            'auto_settings': self.default_auto_settings,
            'lock_duration': '1 hour',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)

    def test_host_manual_unlock(self):
        self.renderer.render_for_email(
            {
                'success': True,
                'action': 'unlocked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': self.user.id,
                'actor_id': self.user.id,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': '2020-06-01T01:00:00Z',
                'locked_duration_min': 60,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Microsoft Defender ATP Host Manually Unisolated',
            'edr_name': 'Microsoft Defender ATP',
            'host': self.host,
            'action': {'inverse_verb': 'isolated', 'type': 'unlocked', 'verb': 'unisolated', 'verb_infinitive': 'unisolate'},
            'actor': self.user,
            'user': self.user,
            'is_auto': False,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': dateutil.parser.parse('2020-06-01T01:00:00Z'),
            'auto_settings': self.default_auto_settings,
            'lock_duration': '1 hour',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)

    def test_host_auto_unlock_manually_locked(self):
        self.renderer.render_for_email(
            {
                'success': True,
                'action': 'unlocked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': self.user.id,
                'actor_id': None,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': '2020-06-01T01:00:00Z',
                'locked_duration_min': 60,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Microsoft Defender ATP Host Automatically Unisolated',
            'host': self.host,
            'edr_name': 'Microsoft Defender ATP',
            'action': {'inverse_verb': 'isolated', 'type': 'unlocked', 'verb': 'unisolated', 'verb_infinitive': 'unisolate'},
            'actor': None,
            'user': self.user,
            'is_auto': True,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': dateutil.parser.parse('2020-06-01T01:00:00Z'),
            'auto_settings': self.default_auto_settings,
            'lock_duration': '1 hour',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)

    def test_host_auto_unlock_auto_locked(self):
        self.renderer.render_for_email(
            {
                'success': True,
                'action': 'unlocked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': None,
                'actor_id': None,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': '2020-06-01T01:00:00Z',
                'locked_duration_min': 60,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Microsoft Defender ATP Host Automatically Unisolated',
            'host': self.host,
            'edr_name': 'Microsoft Defender ATP',
            'action': {'inverse_verb': 'isolated', 'type': 'unlocked', 'verb': 'unisolated', 'verb_infinitive': 'unisolate'},
            'actor': None,
            'user': None,
            'is_auto': True,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': dateutil.parser.parse('2020-06-01T01:00:00Z'),
            'auto_settings': self.default_auto_settings,
            'lock_duration': '1 hour',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)

    def test_host_auto_lock(self):
        self.renderer.render_for_email(
            {
                'success': True,
                'action': 'locked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': None,
                'actor_id': None,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': '2020-06-01T01:00:00Z',
                'locked_duration_min': 60,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Microsoft Defender ATP Host Automatically Isolated',
            'host': self.host,
            'edr_name': 'Microsoft Defender ATP',
            'action': {'inverse_verb': 'unisolated', 'type': 'locked', 'verb': 'isolated', 'verb_infinitive': 'isolate'},
            'actor': None,
            'user': None,
            'is_auto': True,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': dateutil.parser.parse('2020-06-01T01:00:00Z'),
            'auto_settings': self.default_auto_settings,
            'lock_duration': '1 hour',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)

    def test_fail_auto_lock(self):
        self.renderer.render_for_email(
            {
                'success': False,
                'action': 'locked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': None,
                'actor_id': None,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': None,
                'locked_duration_min': 0,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Failed to Isolate Host',
            'host': self.host,
            'edr_name': 'Microsoft Defender ATP',
            'action': {'inverse_verb': 'unisolated', 'type': 'locked', 'verb': 'isolated', 'verb_infinitive': 'isolate'},
            'actor': None,
            'user': None,
            'is_auto': True,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': None,
            'auto_settings': self.default_auto_settings,
            'lock_duration': '',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)

    def test_fail_auto_unlock_auto_locked(self):
        self.renderer.render_for_email(
            {
                'success': False,
                'action': 'unlocked',
                'edr_type': 'windows_defender',
                'host_id': self.host.id,
                'user_id': self.user.id,
                'actor_id': None,
                'timestamp': '2020-06-01T00:00:00Z',
                'now_timestamp': '2020-06-01T00:00:00Z',
                'timeout_timestamp': '2020-06-01T01:00:00Z',
                'locked_duration_min': 60,
            }
        )

        expected_render_data = {
            'notification_subject': 'Lockdown: Failed to Unisolate Host',
            'host': self.host,
            'edr_name': 'Microsoft Defender ATP',
            'action': {'inverse_verb': 'isolated', 'type': 'unlocked', 'verb': 'unisolated', 'verb_infinitive': 'unisolate'},
            'actor': None,
            'user': self.user,
            'is_auto': True,
            'timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'now_timestamp': dateutil.parser.parse('2020-06-01T00:00:00Z'),
            'timeout_timestamp': dateutil.parser.parse('2020-06-01T01:00:00Z'),
            'auto_settings': self.default_auto_settings,
            'lock_duration': '1 hour',
        }

        actual_render_data = self.render_mock.call_args[0][1]

        self.assertEqual(expected_render_data, actual_render_data)


class TestAzureADAutoLockdownNotificationRenderer(VuiTestCase):
    """
    Azure AD is only an RUX feature.
    """

    def setUp(self):
        self.user = User.objects.create(username='<EMAIL>', account_type=User.SPECIAL)
        self.renderer = AzureADAutoLockdownNotificationRenderer()

    @patch('base_tvui.notification_render.conditions.is_cloud')
    def test_account_lockdown_rux_success_notification_render(self, mock_is_cloud):
        mock_is_cloud.return_value = True
        now = timezone.now()
        acc_uids = ['<EMAIL>']
        acc_ids = create_accounts(acc_uids, account_type=Account.TYPE_KERBEROS)  # This should generate a LinkedAccount as well
        test_account_obj = Account.objects.get(uid="<EMAIL>")
        test_account_obj.urgency_score = 80
        test_account_obj.entity_importance = 5
        test_account_obj.linked_account.urgency_score = 80
        test_account_obj.linked_account.entity_importance = 5
        test_account_obj.save()
        test_account_obj.linked_account.save()

        lockdown_auto_settings = {
            'lockdown_enabled': True,
            'lockdown_auto_enabled': True,
            'lockdown_auto_urgency': 75,
            'lockdown_auto_importance': 5,
            'lockdown_auto_timeout_minutes': 60,
        }
        ld_settings = lockdown_utils.get_account_lockdown_settings()
        ld_settings.update(lockdown_auto_settings)
        lockdown_utils.set_account_lockdown_settings(ld_settings)

        test_acct = Account.objects.get(uid="<EMAIL>")
        test_alert_data = {
            "action": "disabled",
            "account_id": test_acct.id,
            "linked_account_id": test_acct.linked_account.id,
            "user_id": 999,
            "timestamp": now.isoformat(),
            "timeout_timestamp": (now + timedelta(minutes=60)).isoformat(),
            "locked_duration_min": 60,
            "success": True,
            "scores": {"urgency": 80, "entity_importance": 5},
        }

        rendered_template = self.renderer.render_for_email(test_alert_data)
        self.assertEqual(rendered_template['subject'], "AD Auto Lockdown: Account Automatically Disabled")
        self.assertIn("Urgency Score: 80", rendered_template['contents']['plaintext'])

    @patch('base_tvui.notification_render.conditions.is_cloud')
    def test_account_lockdown_rux_failure_notification_render(self, mock_is_cloud):
        mock_is_cloud.return_value = True
        now = timezone.now()
        acc_uids = ['<EMAIL>']
        acc_ids = create_accounts(acc_uids, account_type=Account.TYPE_KERBEROS)  # This should generate a LinkedAccount as well
        test_account_obj = Account.objects.get(uid="<EMAIL>")
        test_account_obj.urgency_score = 80
        test_account_obj.entity_importance = 5
        test_account_obj.linked_account.urgency_score = 80
        test_account_obj.linked_account.entity_importance = 5
        test_account_obj.save()
        test_account_obj.linked_account.save()

        lockdown_auto_settings = {
            'lockdown_enabled': True,
            'lockdown_auto_enabled': True,
            'lockdown_auto_urgency': 75,
            'lockdown_auto_importance': 5,
            'lockdown_auto_timeout_minutes': 60,
        }
        ld_settings = lockdown_utils.get_account_lockdown_settings()
        ld_settings.update(lockdown_auto_settings)
        lockdown_utils.set_account_lockdown_settings(ld_settings)

        test_acct = Account.objects.get(uid="<EMAIL>")
        test_alert_data = {
            "action": "disabled",
            "account_id": test_acct.id,
            "linked_account_id": test_acct.linked_account.id,
            "user_id": 999,
            "timestamp": now.isoformat(),
            "timeout_timestamp": (now + timedelta(minutes=60)).isoformat(),
            "locked_duration_min": 60,
            "success": False,
            "scores": {"urgency": 80, "entity_importance": 5},
        }

        rendered_template = self.renderer.render_for_email(test_alert_data)
        self.assertEqual(rendered_template['subject'], "AD Auto Lockdown: Account Failed to Automatically disable")
        self.assertIn("Urgency Score: 80", rendered_template['contents']['plaintext'])


class TestADLockdownNotificationRenderer(VuiTestCase):
    def setUp(self):
        self.maxDiff = None

        self.user = User.objects.create(username='<EMAIL>', account_type=User.SPECIAL)
        self.renderer = ADLockdownNotificationRenderer()

    def test_account_lockdown_qux_success_notification_render(self):
        now = timezone.now()
        acc_uids = ['<EMAIL>']
        acc_ids = create_accounts(acc_uids, account_type=Account.TYPE_KERBEROS)  # This should generate a LinkedAccount as well
        test_account_obj = Account.objects.get(uid="<EMAIL>")
        test_account_obj.t_score = 80
        test_account_obj.c_score = 80
        test_account_obj.priv_level = 5
        test_account_obj.linked_account.t_score = 80
        test_account_obj.linked_account.c_score = 80
        test_account_obj.save()
        test_account_obj.linked_account.save()

        lockdown_auto_settings = {
            'lockdown_enabled': True,
            'lockdown_auto_enabled': True,
            'lockdown_auto_threat': 75,
            'lockdown_auto_certainty': 75,
            'lockdown_auto_privilege': 5,
            'lockdown_auto_timeout_minutes': 60,
        }
        ld_settings = lockdown_utils.get_account_lockdown_settings()
        ld_settings.update(lockdown_auto_settings)
        lockdown_utils.set_account_lockdown_settings(ld_settings)

        test_acct = Account.objects.get(uid="<EMAIL>")
        test_alert_data = {
            "action": "disabled",
            "account_id": test_acct.id,
            "linked_account_id": test_acct.linked_account.id,
            "user_id": 999,
            "timestamp": now.isoformat(),
            "timeout_timestamp": (now + timedelta(minutes=60)).isoformat(),
            "locked_duration_min": 60,
            "success": True,
            "scores": {"threat": 80, "confidence": 80, "priv_level": 5},
        }

        rendered_template = self.renderer.render_for_email(test_alert_data)
        self.assertEqual(rendered_template['subject'], "Lockdown: Account Automatically Disabled")
        self.assertIn("Threat 80 / Certainty 80", rendered_template['contents']['plaintext'])

    def test_account_lockdown_qux_failure_notification_render(self):
        now = timezone.now()
        acc_uids = ['<EMAIL>']
        acc_ids = create_accounts(acc_uids, account_type=Account.TYPE_KERBEROS)  # This should generate a LinkedAccount as well
        test_account_obj = Account.objects.get(uid="<EMAIL>")
        test_account_obj.t_score = 80
        test_account_obj.c_score = 80
        test_account_obj.priv_level = 5
        test_account_obj.linked_account.t_score = 80
        test_account_obj.linked_account.c_score = 80
        test_account_obj.save()
        test_account_obj.linked_account.save()

        lockdown_auto_settings = {
            'lockdown_enabled': True,
            'lockdown_auto_enabled': True,
            'lockdown_auto_threat': 75,
            'lockdown_auto_certainty': 75,
            'lockdown_auto_privilege': 5,
            'lockdown_auto_timeout_minutes': 60,
        }
        ld_settings = lockdown_utils.get_account_lockdown_settings()
        ld_settings.update(lockdown_auto_settings)
        lockdown_utils.set_account_lockdown_settings(ld_settings)

        test_acct = Account.objects.get(uid="<EMAIL>")
        test_alert_data = {
            "action": "disabled",
            "account_id": test_acct.id,
            "linked_account_id": test_acct.linked_account.id,
            "user_id": 999,
            "timestamp": now.isoformat(),
            "timeout_timestamp": (now + timedelta(minutes=60)).isoformat(),
            "locked_duration_min": 60,
            "success": False,
            "scores": {"threat": 80, "confidence": 80, "priv_level": 5},
        }

        rendered_template = self.renderer.render_for_email(test_alert_data)
        self.assertEqual(rendered_template['subject'], "Lockdown: Failed to Disable Account")

    @patch('base_tvui.notification_render.conditions.is_cloud')
    def test_account_lockdown_rux_success_notification_render(self, mock_is_cloud):
        mock_is_cloud.return_value = True
        now = timezone.now()
        acc_uids = ['<EMAIL>']
        acc_ids = create_accounts(acc_uids, account_type=Account.TYPE_KERBEROS)  # This should generate a LinkedAccount as well
        test_account_obj = Account.objects.get(uid="<EMAIL>")
        test_account_obj.urgency_score = 80
        test_account_obj.entity_importance = 5
        test_account_obj.linked_account.urgency_score = 80
        test_account_obj.linked_account.entity_importance = 5
        test_account_obj.save()
        test_account_obj.linked_account.save()

        lockdown_auto_settings = {
            'lockdown_enabled': True,
            'lockdown_auto_enabled': True,
            'lockdown_auto_urgency': 75,
            'lockdown_auto_importance': 5,
            'lockdown_auto_timeout_minutes': 60,
        }
        ld_settings = lockdown_utils.get_account_lockdown_settings()
        ld_settings.update(lockdown_auto_settings)
        lockdown_utils.set_account_lockdown_settings(ld_settings)

        test_acct = Account.objects.get(uid="<EMAIL>")
        test_alert_data = {
            "action": "disabled",
            "account_id": test_acct.id,
            "linked_account_id": test_acct.linked_account.id,
            "user_id": 999,
            "timestamp": now.isoformat(),
            "timeout_timestamp": (now + timedelta(minutes=60)).isoformat(),
            "locked_duration_min": 60,
            "success": True,
            "scores": {"urgency": 80, "entity_importance": 5},
        }

        rendered_template = self.renderer.render_for_email(test_alert_data)
        self.assertEqual(rendered_template['subject'], "Lockdown: Account Automatically Disabled")
        self.assertIn("Urgency 80 / Importance 5", rendered_template['contents']['plaintext'])

    @patch('base_tvui.notification_render.conditions.is_cloud')
    def test_account_lockdown_rux_failure_notification_render(self, mock_is_cloud):
        mock_is_cloud.return_value = True
        now = timezone.now()
        acc_uids = ['<EMAIL>']
        acc_ids = create_accounts(acc_uids, account_type=Account.TYPE_KERBEROS)  # This should generate a LinkedAccount as well
        test_account_obj = Account.objects.get(uid="<EMAIL>")
        test_account_obj.urgency_score = 80
        test_account_obj.entity_importance = 5
        test_account_obj.linked_account.urgency_score = 80
        test_account_obj.linked_account.entity_importance = 5
        test_account_obj.save()
        test_account_obj.linked_account.save()

        lockdown_auto_settings = {
            'lockdown_enabled': True,
            'lockdown_auto_enabled': True,
            'lockdown_auto_urgency': 75,
            'lockdown_auto_importance': 5,
            'lockdown_auto_timeout_minutes': 60,
        }
        ld_settings = lockdown_utils.get_account_lockdown_settings()
        ld_settings.update(lockdown_auto_settings)
        lockdown_utils.set_account_lockdown_settings(ld_settings)

        test_acct = Account.objects.get(uid="<EMAIL>")
        test_alert_data = {
            "action": "disabled",
            "account_id": test_acct.id,
            "linked_account_id": test_acct.linked_account.id,
            "user_id": 999,
            "timestamp": now.isoformat(),
            "timeout_timestamp": (now + timedelta(minutes=60)).isoformat(),
            "locked_duration_min": 60,
            "success": False,
            "scores": {"urgency": 80, "entity_importance": 5},
        }

        rendered_template = self.renderer.render_for_email(test_alert_data)
        self.assertEqual(rendered_template['subject'], "Lockdown: Failed to Disable Account")
        self.assertIn("Urgency 80 / Importance 5", rendered_template['contents']['plaintext'])
