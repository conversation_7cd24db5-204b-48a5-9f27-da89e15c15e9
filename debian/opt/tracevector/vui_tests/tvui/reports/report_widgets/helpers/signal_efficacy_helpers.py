"""
set up data for testing signal efficacy module
"""

from datetime import datetime
from tvui.models import CloseHist<PERSON>, LinkedAccount, detection
from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_detections


# 6 prioritized (3R, 2B, 1N), 2 not (2B)
CLOSE_HISTORY_DATA = [
    {
        "id": 201,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 1,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-05-11 13:48:28', '%Y-%m-%d %H:%M:%S'),
        "reason": 'remediated',
        "assignment_timestamp": None,
        "is_prioritized": True,
    },
    {
        "id": 202,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 2,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-06-10 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'remediated',
        "assignment_timestamp": None,
        "is_prioritized": True,
    },
    {
        "id": 203,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 3,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-06-09 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'remediated',
        "assignment_timestamp": None,
        "is_prioritized": True,
    },
    {
        "id": 204,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 4,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-05-06 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'benign',
        "assignment_timestamp": None,
        "is_prioritized": True,
    },
    {
        "id": 205,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 5,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-06-11 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'benign',
        "assignment_timestamp": None,
        "is_prioritized": True,
    },
    {
        "id": 206,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 6,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-06-10 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'not_valuable',
        "assignment_timestamp": None,
        "is_prioritized": True,
    },
    {
        "id": 207,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 7,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-05-11 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'benign',
        "assignment_timestamp": None,
        "is_prioritized": False,
    },
    {
        "id": 208,
        "host_id": None,
        "linked_account_id": 1,
        "detection_id": 8,
        "user_id": 1,
        "closed_on": datetime.strptime('2025-06-11 11:00:00', '%Y-%m-%d %H:%M:%S'),
        "reason": 'benign',
        "assignment_timestamp": None,
        "is_prioritized": False,
    },
]


def create_linked_account_data():
    """
    Create a LinkedAccount instance for testing.
    This function is used to simulate the linked account data that would be returned
    from a database query in the signal efficacy module.
    """
    linked_acc = LinkedAccount.objects.create(display_uid="<EMAIL>", state="active", pure_uid="<EMAIL>")
    return linked_acc.id


def create_close_history_data(user_id, linked_acc_id, detections):
    """
    Create a list of dictionaries representing close history data for testing.
    This function is used to simulate the close history data that would be returned
    from a database query in the signal efficacy module.
    """
    for data, detection in zip(CLOSE_HISTORY_DATA, detections):
        data["user_id"] = user_id
        data["linked_account_id"] = linked_acc_id
        data["detection_id"] = detection.id
        CloseHistory.objects.create(**data)


def create_required_signal_efficacy_data(user_id):
    """
    Create a LinkedAccount instance required for the signal efficacy tests.
    This function ensures that the necessary linked account is present in the database.
    """
    detections_nwk = create_detections(5, data_source_type="network")
    detections_aws = create_detections(3, data_source_type="aws")
    linked_acc_id = create_linked_account_data()
    create_close_history_data(user_id, linked_acc_id, detections_aws + detections_nwk)
