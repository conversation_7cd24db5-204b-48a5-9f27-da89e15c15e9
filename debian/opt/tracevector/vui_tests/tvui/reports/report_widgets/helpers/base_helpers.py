"""Common helpers for the widget test suite """

from uuid import uuid4
from typing import Final
from datetime import datetime
from dateutil.relativedelta import relativedelta

from django.utils.timezone import now
from django.utils import timezone
from datetime import timedelta

from tvui.models import (
    DataSourceType,
    LinkedAccount,
    LinkedAccountScoreHistory,
    Account,
    AccountType,
    AccountGroup,
    host,
    host_session,
    HostGroup,
    detection,
    score,
    User,
)
from tvui.helpers import DETECTION_METADATA

from vui_tests.tvui.detections.dynamic_detections.test_dynamic_utils import generate_schema_by_type

start_test_time = timezone.now()


HOST_FIXTURES: Final = [
    {
        'host': {'name': 'box1', 'last_source': '***********'},
        'detections': [
            {
                'created_datetime': (start_test_time - timedelta(days=23)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 60,
                'c_score': 60,
            },
            {
                'created_datetime': (start_test_time - timedelta(days=29)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 60,
                'c_score': 60,
            },
            {
                'created_datetime': (start_test_time - timedelta(days=53)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 60,
                'c_score': 60,
            },
        ],
        'score_history': [
            {'created_date': (start_test_time - timedelta(days=15)), 'score': 60, 'attack_rating': 2, 'entity_importance': 3},
            {
                'created_date': (start_test_time - timedelta(days=23)),
                'score': 80,
                'is_prioritized': True,
                'attack_rating': 3,
                'entity_importance': 3,
            },
            {'created_date': (start_test_time - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
        ],
    },
    {
        'host': {'name': 'box2', 'last_source': '***********'},
        'detections': [
            {
                'created_datetime': (start_test_time - timedelta(days=58)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 10,
                'c_score': 10,
            },
            {
                'created_datetime': (start_test_time - timedelta(days=54)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 60,
                'c_score': 60,
            },
        ],
        'score_history': [
            {'created_date': (start_test_time - timedelta(days=15)), 'score': 60, 'attack_rating': 2, 'entity_importance': 3},
            {
                'created_date': (start_test_time - timedelta(days=54)),
                'score': 80,
                'is_prioritized': True,
                'attack_rating': 3,
                'entity_importance': 3,
            },
            {'created_date': (start_test_time - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
        ],
    },
    {
        'host': {'name': 'box3-out-of-range', 'last_source': '***********'},
        'detections': [
            {
                'created_datetime': (start_test_time - timedelta(days=70)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 10,
                'c_score': 10,
            },
            {
                'created_datetime': (start_test_time - timedelta(days=72)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 60,
                'c_score': 60,
            },
        ],
        'score_history': [
            {'created_date': (start_test_time - timedelta(days=65)), 'score': 60, 'attack_rating': 2, 'entity_importance': 3},
            {
                'created_date': (start_test_time - timedelta(days=70)),
                'score': 80,
                'is_prioritized': True,
                'attack_rating': 3,
                'entity_importance': 3,
            },
            {'created_date': (start_test_time - timedelta(days=72)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
        ],
    },
]

ACCOUNT_FIXTURES: Final = [
    {
        'account': {'uid': '<EMAIL>'},
        'detections': [
            {
                'created_datetime': (start_test_time - timedelta(days=43)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 75,
                'c_score': 75,
            },
            {
                'created_datetime': (start_test_time - timedelta(days=22)),
                'type': 'tor',
                'category': 'COMMAND & CONTROL',
                't_score': 75,
                'c_score': 75,
            },
            {
                'created_datetime': (start_test_time - timedelta(days=55)),
                'type': 'spa_tcp_info',
                'category': 'INFO',  # SHOULD BE IGNORED
                't_score': 60,
                'c_score': 60,
            },
        ],
        'score_history': [
            {
                'created_date': (start_test_time - timedelta(days=28)),
                'score': 75,
                'attack_rating': 3,
                'entity_importance': 3,
                'is_prioritized': True,
            },
            {'created_date': (start_test_time - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
            {'created_date': (start_test_time - timedelta(days=5)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
        ],
    }
]


def get_relative_dates(months_back=3, start=datetime.today()):
    """
    Gets the months in the format of YYYY-MM
    for months_back amount of months, including the current month.
    This is used to keep tests from breaking on relative dates.
    """
    past_months = [(start - relativedelta(months=i)).strftime('%Y-%m') for i in range(months_back, -1, -1)]
    return past_months


def create_high_hosts(now=timezone.now(), high_user=None):
    if not high_user:
        high_user = User.objects.create_user(uuid4(), email=f'{uuid4()}@vectra.ai', password='passw0rd')
    high_entity_host = [
        {
            'host': {'name': 'needy_host', 'last_source': '999.168.0.1'},
            'detections': [
                {
                    # Spread the detections across the months to cause it to appear over time.
                    'created_datetime': (now - timedelta(days=95)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 60,
                    'c_score': 60,
                },
                {
                    'created_datetime': (now - timedelta(days=65)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 61,
                    'c_score': 61,
                },
                {
                    'created_datetime': (now - timedelta(days=35)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 62,
                    'c_score': 62,
                },
                {
                    'created_datetime': (now - timedelta(days=5)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 62,
                    'c_score': 62,
                },
            ],
            'score_history': [
                {
                    'created_date': (now - timedelta(days=95)),
                    'score': 90,
                    'attack_rating': 1,
                    'entity_importance': 1,
                    'is_prioritized': True,
                },
                {
                    'created_date': (now - timedelta(days=65)),
                    'score': 91,
                    'attack_rating': 1,
                    'entity_importance': 1,
                    'is_prioritized': True,
                },
                {
                    'created_date': (now - timedelta(days=35)),
                    'score': 92,
                    'attack_rating': 1,
                    'entity_importance': 1,
                    'is_prioritized': True,
                },
                {
                    'created_date': (now - timedelta(days=5)),
                    'score': 92,
                    'attack_rating': 1,
                    'entity_importance': 1,
                    'is_prioritized': True,
                },
            ],
        },
    ]
    create_host_fixtures(host_fixtures=high_entity_host, user=high_user, session_luid=999)


def create_user():
    """
    Method to create a test user
    """
    name = uuid4()
    return User.objects.create_user(name, email=f'{name}@vectra.ai', password='passw0rd')


def create_account():
    """
    Used to create an account, as a detection needs an account linked to it.
    """
    account = Account.objects.create(
        uid=f'{uuid4()}<EMAIL>', account_type=Account.TYPE_KERBEROS, t_score=1, c_score=1, last_seen=start_test_time
    )
    return account


def create_host_session(created_datetime, host_luid=None):
    """
    Used to create an random host
    """
    host_obj = host.objects.create(name=f'{uuid4()}', t_score=1, c_score=1, state='active')
    session_luid = uuid4()
    host_session_obj = host_session.objects.create(
        host=host_obj,
        session_luid=session_luid,
        host_luid=host_luid,
        ip_address='10.0.0.0',
        start=created_datetime - timedelta(minutes=3),
        end=created_datetime,
    )

    return host_session_obj


def create_detections(
    count=10,
    days_ago=1,
    category='Lateral Movement',
    data_source_type=None,
    created_datetime=None,
    last_timestamp=None,
    entity_type='account',
):
    """
    Method to generate detections to test against.
    """

    detections = []

    if not created_datetime:
        created_datetime = now() - timedelta(days=days_ago)

    if not last_timestamp:
        last_timestamp = now() - timedelta(days=days_ago)

    account = create_account() if entity_type == 'account' else None
    host_session_obj = create_host_session(created_datetime) if entity_type == 'host' else None

    for _ in range(count):
        detections.append(
            detection.objects.create(
                type='papi_breach',
                # Create them a day ago as we don't use detections from today.
                created_datetime=created_datetime,
                type_vname=f'"{uuid4()} {count}"',
                category=category,
                data_source_type=data_source_type,
                last_timestamp=last_timestamp,
                state='active',
                t_score=1,
                c_score=1,
                account=account,
                host_session=host_session_obj,
            )
        )
    return detections


def create_old_detection(days_ago=365, created_datetime=None, last_timestamp=None, category='Lateral Movement'):
    """
    Method to generate detections that are past the created date, to test against.
    """

    if not created_datetime:
        created_datetime = now() - timedelta(days=days_ago)

    if not last_timestamp:
        last_timestamp = now() - timedelta(days=days_ago)

    account = create_account()
    detection.objects.create(
        type='old_detection',
        type_vname=f'"{uuid4()} old"',
        category=category,
        created_datetime=created_datetime,
        last_timestamp=last_timestamp,
        state='active',
        t_score=1,
        c_score=1,
        account=account,
    )


def create_host_fixtures(host_fixtures=HOST_FIXTURES, user=None, session_luid=1, datasource_type=DataSourceType.NETWORK):
    if not user:
        create_user()
    generated_detection_schemas = {}
    for data in host_fixtures:
        host_data = data['host']
        host_obj = host.objects.create(**host_data)

        if 'detections' in data:
            default_detection = {'state': 'active'}
            for det_data in data['detections']:
                session_luid += 1
                host_session_obj = host_session.objects.create(
                    host=host_obj,
                    session_luid=session_luid,
                    ip_address='10.0.0.0',
                    start=det_data['created_datetime'] - timedelta(minutes=3),
                    end=det_data['created_datetime'],
                )

                det_data.update(default_detection)

                det_data['host_session_id'] = host_session_obj.id
                det_data['last_timestamp'] = det_data['created_datetime']
                det_data['data_source_type'] = det_data.get('data_source_type', datasource_type)

                if det_data['type'] and det_data['type'] not in generated_detection_schemas:
                    generated_detection_schemas[det_data['type']] = True
                    generate_schema_by_type(det_data['type'])

                if det_data['type']:
                    det_data['type_vname'] = DETECTION_METADATA[det_data['type']]['vname']

                detection.objects.create(**det_data)

        if 'score_history' in data:
            for score_history in data['score_history']:
                score.objects.create(
                    threat_score=score_history.get('t_score', score_history.get('score')),
                    confidence_score=score_history.get('c_score', score_history.get('score')),
                    urgency_score=score_history.get('urgency_score', score_history.get('score')),
                    timestamp=score_history['created_date'],
                    end_timestamp=score_history['created_date'],
                    is_prioritized=score_history.get('is_prioritized', False),
                    attack_rating=score_history.get('attack_rating', 0),
                    entity_importance=score_history.get('entity_importance', 0),
                    detection_profile=score_history.get('detection_profile', None),
                    host=host_obj,
                )

        if 'groups' in data:
            for group_name in data['groups']:
                group_obj, created = HostGroup.objects.update_or_create(name=group_name, last_modified_by=user)
                group_obj.hosts.add(host_obj)


def create_account_fixtures(account_fixtures=ACCOUNT_FIXTURES, user=None, now_frozen=start_test_time):
    if not user:
        user = create_user()
    generated_detection_schemas = {}

    for data in account_fixtures:
        account_uid = data['account']['uid']
        linked_account = LinkedAccount.objects.create(display_uid=f'test:{account_uid}')

        account = Account.objects.create(
            uid=account_uid,
            account_type=AccountType.KERBEROS,
            first_seen=now_frozen,
            last_seen=now_frozen,
        )

        account.linked_account = linked_account
        account.save()

        if 'detections' in data:
            default_detection = {'state': 'active'}
            for det_data in data['detections']:
                det_data.update(default_detection)
                det_data['last_timestamp'] = det_data['created_datetime']
                det_data['data_source_type'] = det_data.get('data_source_type', DataSourceType.O365)

                if det_data['type'] and det_data['type'] not in generated_detection_schemas:
                    generated_detection_schemas[det_data['type']] = True
                    generate_schema_by_type(det_data['type'])

                if det_data['type']:
                    det_data['type_vname'] = DETECTION_METADATA[det_data['type']]['vname']

                det = detection.objects.create(**det_data)
                det.account = account
                det.save()

        if 'score_history' in data:
            for score_history in data['score_history']:
                LinkedAccountScoreHistory.objects.create(
                    t_score=score_history.get('t_score', score_history.get('score')),
                    c_score=score_history.get('c_score', score_history.get('score')),
                    urgency_score=score_history.get('urgency_score', score_history.get('score')),
                    is_prioritized=score_history.get('is_prioritized', False),
                    attack_rating=score_history.get('attack_rating', 0),
                    entity_importance=score_history.get('entity_importance', 0),
                    score_date=score_history['created_date'],
                    account=linked_account,
                    created_date=score_history['created_date'],
                )

        if 'groups' in data:
            for group_name in data['groups']:
                group, created = AccountGroup.objects.update_or_create(name=group_name, last_modified_by=user)
                group.accounts.add(account)
