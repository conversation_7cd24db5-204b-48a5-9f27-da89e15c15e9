from unittest import mock
from unittest.mock import patch
from datetime import timedelta

from django.utils.timezone import now

from tvui.models import detection, Account
from vui_tests.vui_testcase import VuiTestCase
from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_detections, create_old_detection
from tvui.reports.report_widgets.hours_saved_widget import HoursSavedWidgetQuery


class TestHoursSaved(VuiTestCase):
    """
    Test Class for the hours saved widget
    """

    def setUp(self):
        self.hours_saved_widget = HoursSavedWidgetQuery()
        self.detections = []

    def test_widget_result(self):
        result = self.hours_saved_widget.execute()
        assert result[0]['sum'] == 0

    def test_widget_result_with_data_is_greater_than_0(self):
        create_detections(count=6)
        result = self.hours_saved_widget.execute()
        sum_result = result[0]['sum']
        assert sum_result > 0
        # Validate we are only at 1 decimal points and no more.
        assert len(str(sum_result).rsplit('.')[-1]) == 1

    def test_widget_query_only_returns_detections_in_date_range(self):
        create_detections(count=2)
        len_original = len(self.hours_saved_widget.get_detections(num_days_ago=1))
        create_old_detection()
        create_old_detection(category='INFO')

        db_detections = self.hours_saved_widget.get_detections(num_days_ago=1)
        len_query_with_old_data = len(db_detections)
        for db_detection in db_detections:
            # Validate old detection isn't returned.
            assert db_detection.type != 'old_detection'
        # Validate the results returned before and after created_date are the same therefore it's not returned.
        assert len_original == len_query_with_old_data

    def test_widget_params(self):
        # Detections today not included
        create_detections(count=1, days_ago=0)
        hours_saved_widget_with_params = HoursSavedWidgetQuery(params={'num_days': 30})
        assert hours_saved_widget_with_params.params == {'num_days': 30}
        db_detections = hours_saved_widget_with_params.get_detections(num_days_ago=1)
        len_detections_only_today = len(db_detections)
        # Detections yesterday included
        create_detections(count=1, days_ago=1)
        create_detections(count=3, category=detection.INFO, days_ago=1)  # these should be ignored
        len_detections_yesterday = len(hours_saved_widget_with_params.get_detections(num_days_ago=1))
        # Detections before 30 days not included
        create_detections(count=1, days_ago=35)
        db_detections = hours_saved_widget_with_params.get_detections(num_days_ago=1)
        len_detections_before_30_days = len(db_detections)
        for db_detection in db_detections:
            # Validate old detection isn't returned.
            assert db_detection.type != 'old_detection'
        # Validate the results returned before and after created_date are the same therefore it's not returned.
        assert len_detections_only_today == 0
        assert len_detections_yesterday == 1
        assert len_detections_before_30_days == 1

    def test_widget_with_data_source_types(self):
        # First we create some detections of AWS
        create_detections(count=5, data_source_type='aws')
        aws_hours_saved_widget = HoursSavedWidgetQuery(params={'num_days': 1, 'attack_surface': 'aws'})
        aws_hours_saved_result = aws_hours_saved_widget.execute()[0]['sum']
        aws_detections = aws_hours_saved_widget.get_detections(num_days_ago=1, surface='aws')

        # Then we do the same for 0365, validate no overlap.
        create_detections(count=10, data_source_type='o365')
        o365_hours_saved_widget = HoursSavedWidgetQuery(params={'num_days': 1, 'attack_surface': 'o365'})
        o365_hours_saved_result = o365_hours_saved_widget.execute()[0]['sum']
        o365_detections = o365_hours_saved_widget.get_detections(num_days_ago=1, surface='o365')

        # Validate the params are as expected
        assert aws_hours_saved_widget.params == {'num_days': 1, 'attack_surface': 'aws'}
        assert o365_hours_saved_widget.params == {'num_days': 1, 'attack_surface': 'o365'}
        assert o365_detections != aws_detections
        # Validate the result
        assert aws_hours_saved_result < o365_hours_saved_result
