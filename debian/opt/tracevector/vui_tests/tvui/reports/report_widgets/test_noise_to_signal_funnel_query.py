from unittest.mock import patch

import importlib
from base_tvui.feature_flipper.flags import Flags
from base_tvui.feature_flipper.helpers import flag_enabled

from vui_tests.vui_testcase import VuiTestCase
from freezegun import freeze_time

from django.utils import timezone
from datetime import timed<PERSON><PERSON>, datetime
from collections import OrderedDict
from decimal import Decimal

from tvui.models import (
    LinkedAccount,
    LinkedAccountScoreHistory,
    Account,
    host,
    detection,
    score,
    User,
)

from tvui.reports.report_widgets.noise_to_signal_funnel_query import NoiseToSignalFunnelQuery

from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_account_fixtures, create_host_fixtures


class NoiseToSignalFunnelQueryTestBase(VuiTestCase):
    """DRY"""

    @patch('base_tvui.feature_flipper.conditions.is_cloud', return_value=True)
    def setUp(self, _, is_qux=False, datasource_type=None):
        self.maxDiff = 3000

        self.now = timezone.make_aware(datetime(year=2025, month=4, day=10, hour=0, minute=0, second=0, microsecond=0))
        self.freezer = freeze_time(self.now)
        self.freezer.start()
        self.test_user = User.objects.create_user('other', email='<EMAIL>', password='passw0rd')

        threat_score_eval = lambda x: 30 if is_qux else x
        # for QUX tests below, create detections with lower t_score so that we can validate that query returns different results to the RUX version

        self.host_fixtures = [
            {
                'host': {'name': 'box1', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=23)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=29)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=53)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=23)),
                        'score': threat_score_eval(80),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=9)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=58)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=54)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=54)),
                        'score': threat_score_eval(80),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
            {
                'host': {'name': 'box3-out-of-range', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=70)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=72)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=65)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=70)),
                        'score': threat_score_eval(80),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=72)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
        ]

        if datasource_type:
            create_host_fixtures(self.host_fixtures, self.test_user, datasource_type=datasource_type)
        else:
            create_host_fixtures(self.host_fixtures, self.test_user)

        self.account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=43)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(75),
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=22)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(75),
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=28)),
                        'score': threat_score_eval(75),
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=5)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
                ],
            }
        ]

        create_account_fixtures(self.account_fixtures, self.test_user, self.now)

    def tearDown(self) -> None:
        self.freezer.stop()
        return super().tearDown()


class TestNoiseToSignalReportWidgetQueryRUX(NoiseToSignalFunnelQueryTestBase):
    """
    RUX tests
    """

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def setUp(self, mock_flag_enabled):
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.noise_to_signal_funnel_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.noise_to_signal_funnel_query)
        from tvui.reports.report_widgets.noise_to_signal_funnel_query import NoiseToSignalFunnelQuery

        super().setUp(is_qux=False)

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    def test_get_with_zero_previous_period(self):
        query = NoiseToSignalFunnelQuery(params={'num_days': 90})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict({'column_code': 'detections', 'column_name': '# of Detections', 'cnt': Decimal(12.0)}),
                OrderedDict(
                    {
                        'column_code': 'potential_attack_progressions',
                        'column_name': '# of Potential Attack Progressions',
                        'cnt': Decimal(4.0),
                    }
                ),
                OrderedDict(
                    {
                        'column_code': 'prioritized_alerts',
                        'column_name': '# of Prioritized Alerts',
                        'cnt': Decimal(4.0),
                    }
                ),
            ],
        )

    def test_get_noise_to_signal_report(self):
        query = NoiseToSignalFunnelQuery(params={'num_days': 30})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict([('column_code', 'detections'), ('column_name', '# of Detections'), ('cnt', Decimal('3'))]),
                OrderedDict(
                    [
                        ('column_code', 'potential_attack_progressions'),
                        ('column_name', '# of Potential Attack Progressions'),
                        ('cnt', Decimal('2')),
                    ]
                ),
                OrderedDict(
                    [
                        ('column_code', 'prioritized_alerts'),
                        ('column_name', '# of Prioritized Alerts'),
                        ('cnt', Decimal('2')),
                    ]
                ),
            ],
        )

    def test_get_noise_to_signal_report_no_data(self):
        # clean database
        detection.objects.all().delete()
        host.objects.all().delete()
        LinkedAccount.objects.all().delete()
        Account.objects.all().delete()
        score.objects.all().delete()
        LinkedAccountScoreHistory.objects.all().delete()

        query = NoiseToSignalFunnelQuery(params={'num_days': 30})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict({'column_code': 'detections', 'column_name': '# of Detections', 'cnt': Decimal(0.0)}),
                OrderedDict(
                    {
                        'column_code': 'potential_attack_progressions',
                        'column_name': '# of Potential Attack Progressions',
                        'cnt': Decimal(0.0),
                    }
                ),
                OrderedDict(
                    {
                        'column_code': 'prioritized_alerts',
                        'column_name': '# of Prioritized Alerts',
                        'cnt': Decimal(0.0),
                    }
                ),
            ],
        )


class TestNoiseToSignalReportWidgetQueryQUX(NoiseToSignalFunnelQueryTestBase):
    """
    On QUX instead fo prioritised entities (is_prioritised) we filter score history by threat_score >= 50
    """

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def setUp(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: False})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.noise_to_signal_funnel_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.noise_to_signal_funnel_query)
        from tvui.reports.report_widgets.noise_to_signal_funnel_query import NoiseToSignalFunnelQuery

        super().setUp(is_qux=True)

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    def test_get_noise_to_signal_report_threat_scores_below_50_QUX(self):
        """
        Every t/threat score so far has been below 50, which is the min threshold to
        qualify as a Critical/High host account (prioritized entity in RUX) in QUX
        So we expect to get 0 Prioritized Alerts for both time periods in this test
        """
        query = NoiseToSignalFunnelQuery(params={'num_days': 30})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict([('column_code', 'detections'), ('column_name', '# of Detections'), ('cnt', Decimal('3'))]),
                OrderedDict(
                    [
                        ('column_code', 'potential_attack_progressions'),
                        ('column_name', '# of Potential Attack Progressions'),
                        ('cnt', Decimal('2')),
                    ]
                ),
                OrderedDict(
                    [
                        ('column_code', 'prioritized_alerts'),
                        ('column_name', '# of Prioritized Alerts'),
                        ('cnt', Decimal('0')),
                    ]
                ),
            ],
        )

    def test_get_noise_to_signal_report_threat_scores_over_50_QUX(self):
        """
        Following the previous test, we'll now inject 2 detections with threat scores above 50 and
        validate that we get 2 Prioritized Alerts for the current period
        """

        new_high_threat_account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=43)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=22)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=28)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=5)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
                ],
            }
        ]
        create_account_fixtures(new_high_threat_account_fixtures, self.test_user, self.now)

        new_high_threat_host_fixtures = [
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=58)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=54)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': 60,
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=54)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
        ]

        create_host_fixtures(new_high_threat_host_fixtures, self.test_user, session_luid=88)

        query = NoiseToSignalFunnelQuery(params={'num_days': 30})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict([('column_code', 'detections'), ('column_name', '# of Detections'), ('cnt', Decimal('4'))]),
                OrderedDict(
                    [
                        ('column_code', 'potential_attack_progressions'),
                        ('column_name', '# of Potential Attack Progressions'),
                        ('cnt', Decimal('3')),
                    ]
                ),
                OrderedDict(
                    [
                        ('column_code', 'prioritized_alerts'),
                        ('column_name', '# of Prioritized Alerts'),
                        ('cnt', Decimal('1')),
                    ]
                ),
            ],
        )


class TestNoiseToSignalReportWidgetRuxQueryAttackSurface(NoiseToSignalFunnelQueryTestBase):
    """
    Attack Surface tests
    """

    def setUp(self):
        self.now = timezone.make_aware(datetime(year=2025, month=4, day=10, hour=0, minute=0, second=0, microsecond=0))
        super().setUp(is_qux=False, datasource_type='aws')

    def test_get_attack_surface_with_zero_previous_period(self):
        query = NoiseToSignalFunnelQuery(params={'num_days': 90, 'attack_surface': 'aws'})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict({'column_code': 'detections', 'column_name': '# of Detections', 'cnt': Decimal(10.0)}),
                OrderedDict(
                    {
                        'column_code': 'potential_attack_progressions',
                        'column_name': '# of Potential Attack Progressions',
                        'cnt': Decimal(3.0),
                    }
                ),
                OrderedDict(
                    {
                        'column_code': 'prioritized_alerts',
                        'column_name': '# of Prioritized Alerts',
                        'cnt': Decimal(3.0),
                    }
                ),
            ],
        )

    def test_get_attack_surface_noise_to_signal_report(self):
        query = NoiseToSignalFunnelQuery(params={'num_days': 30, 'attack_surface': 'aws'})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict([('column_code', 'detections'), ('column_name', '# of Detections'), ('cnt', Decimal('2'))]),
                OrderedDict(
                    [
                        ('column_code', 'potential_attack_progressions'),
                        ('column_name', '# of Potential Attack Progressions'),
                        ('cnt', Decimal('1')),
                    ]
                ),
                OrderedDict([('column_code', 'prioritized_alerts'), ('column_name', '# of Prioritized Alerts'), ('cnt', Decimal('1'))]),
            ],
        )

    def test_get_attack_surface_noise_to_signal_report_no_data(self):
        # clean database
        detection.objects.all().delete()
        host.objects.all().delete()
        LinkedAccount.objects.all().delete()
        Account.objects.all().delete()
        score.objects.all().delete()
        LinkedAccountScoreHistory.objects.all().delete()

        query = NoiseToSignalFunnelQuery(params={'num_days': 30, 'attack_surface': 'aws'})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict({'column_code': 'detections', 'column_name': '# of Detections', 'cnt': Decimal(0.0)}),
                OrderedDict(
                    {
                        'column_code': 'potential_attack_progressions',
                        'column_name': '# of Potential Attack Progressions',
                        'cnt': Decimal(0.0),
                    }
                ),
                OrderedDict(
                    {
                        'column_code': 'prioritized_alerts',
                        'column_name': '# of Prioritized Alerts',
                        'cnt': Decimal(0.0),
                    }
                ),
            ],
        )


class TestNoiseToSignalReportWidgetPercentageChangeOff(NoiseToSignalFunnelQueryTestBase):
    """
    when executive_overview_datepicker is on, we do not the percentage changes to be included in the get results
    NOTE: it is off by default so the opposite validation is done in test_get_noise_to_signal_report above
    """

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def setUp(self, mock_flag_enabled):
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.noise_to_signal_funnel_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.noise_to_signal_funnel_query)
        from tvui.reports.report_widgets.noise_to_signal_funnel_query import NoiseToSignalFunnelQuery

        super().setUp(is_qux=True)

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    def test_get_noise_to_signal_report_no_percentage_change(self):
        """
        when executive_overview_datepicker is on, we do not the percentage changes to be included in the get results
        """
        query = NoiseToSignalFunnelQuery(params={'num_days': 30})
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict([('column_code', 'detections'), ('column_name', '# of Detections'), ('cnt', Decimal('3'))]),
                OrderedDict(
                    [
                        ('column_code', 'potential_attack_progressions'),
                        ('column_name', '# of Potential Attack Progressions'),
                        ('cnt', Decimal('2')),
                    ]
                ),
                OrderedDict(
                    [
                        ('column_code', 'prioritized_alerts'),
                        ('column_name', '# of Prioritized Alerts'),
                        ('cnt', Decimal('0')),
                    ]
                ),
            ],
        )
