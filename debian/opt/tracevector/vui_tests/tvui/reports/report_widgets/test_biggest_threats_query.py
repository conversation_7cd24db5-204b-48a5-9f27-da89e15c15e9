import importlib
from unittest.mock import patch
from base_tvui.feature_flipper.flags import Flags
from base_tvui.feature_flipper.helpers import flag_enabled
from vui_tests.vui_testcase import VuiTestCase
from freezegun import freeze_time
from django.utils import timezone
from datetime import timedelta
from collections import OrderedDict

from tvui.models import (
    DataSourceType,
    User,
)
from tvui.detections.detection_types import DetectionType

from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_account_fixtures, create_host_fixtures


class TestBiggestThreats(VuiTestCase):
    @patch('base_tvui.feature_flipper.conditions.is_cloud', return_value=True)
    def setUp(self, _):
        self.maxDiff = 6000

        self.now = timezone.now()

        self.freezer = freeze_time(self.now)
        self.freezer.start()

        self.test_user = User.objects.create_user('other', email='<EMAIL>', password='passw0rd')

        self.host_fixtures = [
            {
                'host': {'name': 'box1', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.ICMP_TUNNEL,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                        'data_source_type': DataSourceType.NETWORK,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.INTERNAL_ICMP_TUNNEL_C2S,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                        'data_source_type': 'aws',
                    },
                ],
                'score_history': [
                    {
                        # this score should be ignored because we dont check the current day
                        'created_date': (self.now - timedelta(minutes=1)),
                        'score': 100,
                        'is_prioritized': True,
                        'attack_rating': 10,
                        'entity_importance': 2,
                        'detection_profile': 'Should be ignored',
                    },
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': 60,
                        'attack_rating': 2,
                        'entity_importance': 2,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=54)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 8,
                        'entity_importance': 2,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        # Same prioritized score on the next day
                        'created_date': (self.now - timedelta(days=55)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 8,
                        'entity_importance': 2,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        # First occurrence of peak prioritized score
                        'created_date': (self.now - timedelta(days=56)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 8,
                        'entity_importance': 2,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=70)),
                        'score': 10,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': ['DC1', 'DC2'],
            },
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                        'state': 'inactive',
                        'data_source_type': DataSourceType.NETWORK,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 65,
                        'c_score': 65,
                        'state': 'inactive',
                        'data_source_type': DataSourceType.NETWORK,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=35)),
                        'score': 70,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=70)),
                        'score': 15,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
            {
                'host': {'name': 'box3', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 40,
                        'data_source_type': DataSourceType.NETWORK,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=61)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                        'data_source_type': DataSourceType.NETWORK,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=16)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=36)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=71)),
                        'score': 15,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
            {
                'host': {'name': 'VirtualBox', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=10)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 20,
                        'c_score': 20,
                        'data_source_type': DataSourceType.NETWORK,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=10)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 20,
                        'c_score': 20,
                        'state': 'inactive',
                        'data_source_type': DataSourceType.NETWORK,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=9)),
                        'score': 20,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=8)),
                        'score': 20,
                        'is_prioritized': True,
                        'attack_rating': 20,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=7)),
                        'score': 20,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
            {
                'host': {'name': 'GCPHostMachine', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=10)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONQUER',
                        't_score': 10,
                        'c_score': 10,
                        'data_source_type': DataSourceType.NETWORK,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=10)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONQUER',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                        'data_source_type': DataSourceType.NETWORK,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=16)),
                        'score': 10,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: Click here for more!',
                    },
                    {
                        'created_date': (self.now - timedelta(days=19)),
                        'score': 10,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=20)),
                        'score': 10,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
        ]

        create_host_fixtures(self.host_fixtures, self.test_user)

        self.account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.O365_SUSPECTED_COMPROMISED_ACCESS,
                        'type_vname': 'Azure AD Suspected Compromised Access',
                        'category': 'COMMAND & CONTROL',
                        't_score': 70,
                        'c_score': 70,
                        'data_source_type': DataSourceType.O365,
                    }
                ],
                'score_history': [
                    {'created_date': (self.now - timedelta(days=60)), 'score': 30, 'attack_rating': 1, 'entity_importance': 0},
                    {
                        'created_date': (self.now - timedelta(days=29)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 0,
                        'is_prioritized': True,
                    },
                    {
                        'created_date': (self.now - timedelta(days=30)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 0,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=15)), 'score': 30, 'attack_rating': 2, 'entity_importance': 0},
                ],
                'groups': ['GroupA', 'GroupB'],
            },
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.PAPI_BREACH,
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                        'data_source_type': DataSourceType.O365,
                    }
                ],
                'score_history': [
                    {'created_date': (self.now - timedelta(days=60)), 'score': 30, 'attack_rating': 1, 'entity_importance': 0},
                    {
                        # entity is still prioritized the next day
                        'created_date': (self.now - timedelta(days=29)),
                        'score': 52,
                        'attack_rating': 3,
                        'entity_importance': 2,
                        'is_prioritized': True,
                    },
                    {
                        # First occurence of peak prioritized score
                        'created_date': (self.now - timedelta(days=30)),
                        'score': 52,
                        'attack_rating': 3,
                        'entity_importance': 2,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=15)), 'score': 30, 'attack_rating': 2, 'entity_importance': 0},
                ],
                'groups': ['GroupA', 'GroupC'],
            },
        ]

        create_account_fixtures(self.account_fixtures, self.test_user, self.now)

    def tearDown(self) -> None:
        self.freezer.stop()
        return super().tearDown()

    def clean_result(self, el):
        del el['id']
        el['peaked_on'] = el['peaked_on'].strftime('%Y-%m-%d %T')
        return el

    def select_archetype(self, _dets, _entity_id, entity_type):
        if entity_type == 'account':
            return {
                'archetype': 'admin',
                'pos_assoc_detections': [
                    ('sw_o365_exfilDeleteAccount', 'M365 Exfiltration Before Termination'),
                    ('papi_admin_peer_console', 'Privilege Anomaly: Unusual Account on Host'),
                    ('papi_admin_second_console', 'Privilege Anomaly: Unusual Service from Host'),
                    ('papi_breach', 'Privilege Anomaly: Unusual Trio'),
                    ('papi_insider_attack', 'Privilege Anomaly: Unusual Service - Insider'),
                    ('papi_rogue_admin', 'Privilege Anomaly: Unusual Service'),
                    ('papi_unusual_admin_console', 'Privilege Anomaly: Unusual Host'),
                ],
            }
        else:
            return {
                'archetype': 'scanner',
                'pos_assoc_detections': [
                    ('smb_ransomware', 'Ransomware File Activity'),
                    ('smb_brute_force', 'SMB Brute-Force'),
                ],
            }

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_get_biggest_threats(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.biggest_threats_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.biggest_threats_query)
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90})
        result = query.execute()
        mock_flag_enabled.assert_called_with("unified_prioritization")

        cleaned_results = list(map(self.clean_result, result))

        self.assertEqual(
            cleaned_results,
            [
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box1',
                        'entity_importance': 'High',
                        'attack_rating': 8,
                        'urgency_score': 80,
                        't_score': 80,
                        'c_score': 80,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=56)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.AWS]}, {DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'severity_str': 'Critical',
                        'groups': 'DC1, DC2',
                        'attack_progression': 'ICMP Tunnel, ICMP Tunnel: Client',
                        'mitre_techniques': 'Fallback Channels (T1008), Exfiltration Over Alternative Protocol (T1048), Non-Application Layer Protocol (T1095), Lateral Tool Transfer (T1570)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'account',
                        'entity_name': 'test:<EMAIL>',
                        'entity_importance': 'Low',
                        'attack_rating': 3,
                        'urgency_score': 75,
                        't_score': 75,
                        'c_score': 75,
                        'severity': 5,
                        'last_seen_ip': '',
                        'peaked_on': (self.now - timedelta(days=30)).strftime('%Y-%m-%d %T'),
                        'data_source_types': DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.O365],
                        'severity_str': 'Critical',
                        'groups': 'GroupA, GroupB',
                        'attack_progression': 'Azure AD Suspected Compromised Access',
                        'mitre_techniques': 'Valid Accounts (T1078)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box2',
                        'entity_importance': 'Medium',
                        'attack_rating': 3,
                        'urgency_score': 70,
                        't_score': 70,
                        'c_score': 70,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=35)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'account',
                        'entity_name': 'test:<EMAIL>',
                        'entity_importance': 'High',
                        'attack_rating': 3,
                        'urgency_score': 52,
                        't_score': 52,
                        'c_score': 52,
                        'severity': 5,
                        'last_seen_ip': '',
                        'peaked_on': (self.now - timedelta(days=30)).strftime('%Y-%m-%d %T'),
                        'data_source_types': DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.O365],
                        'severity_str': 'Critical',
                        'groups': 'GroupA, GroupC',
                        'attack_progression': 'Privilege Anomaly: Unusual Trio',
                        'mitre_techniques': 'Valid Accounts (T1078)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box3',
                        'entity_importance': 'Medium',
                        'attack_rating': 3,
                        'urgency_score': 50,
                        't_score': 50,
                        'c_score': 50,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=36)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'VirtualBox',
                        'entity_importance': 'Medium',
                        'attack_rating': 20,
                        'urgency_score': 20,
                        't_score': 20,
                        'c_score': 20,
                        'severity': 2,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=8)).strftime('%Y-%m-%d %T'),
                        'data_source_types': 'Network',
                        'severity_str': 'Low',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'GCPHostMachine',
                        'entity_importance': 'Medium',
                        'attack_rating': 3,
                        'urgency_score': 10,
                        't_score': 10,
                        'c_score': 10,
                        'severity': 2,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=19)).strftime('%Y-%m-%d %T'),
                        'data_source_types': 'Network',
                        'severity_str': 'Low',
                        'groups': '',
                        'attack_progression': '-',
                        'mitre_techniques': '-',
                    }
                ),
            ],
        )

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_get_biggest_threats_by_attack_source(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.biggest_threats_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.biggest_threats_query)
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        selected_attack_surface = DataSourceType.O365
        query = BiggestThreatsQuery(params={'num_days': 90, 'attack_surface': selected_attack_surface})
        result = query.execute()
        mock_flag_enabled.assert_called_with("unified_prioritization")

        cleaned_results = list(map(self.clean_result, result))

        self.assertEqual(
            cleaned_results,
            [
                OrderedDict(
                    {
                        'entity_type': 'account',
                        'entity_name': 'test:<EMAIL>',
                        'entity_importance': 'Low',
                        'attack_rating': 3,
                        'urgency_score': 75,
                        't_score': 75,
                        'c_score': 75,
                        'severity': 5,
                        'last_seen_ip': '',
                        'peaked_on': (self.now - timedelta(days=30)).strftime('%Y-%m-%d %T'),
                        'data_source_types': DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.O365],
                        'severity_str': 'Critical',
                        'groups': 'GroupA, GroupB',
                        'attack_progression': 'Azure AD Suspected Compromised Access',
                        'mitre_techniques': 'Valid Accounts (T1078)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'account',
                        'entity_name': 'test:<EMAIL>',
                        'entity_importance': 'High',
                        'attack_rating': 3,
                        'urgency_score': 52,
                        't_score': 52,
                        'c_score': 52,
                        'severity': 5,
                        'last_seen_ip': '',
                        'peaked_on': (self.now - timedelta(days=30)).strftime('%Y-%m-%d %T'),
                        'data_source_types': DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.O365],
                        'severity_str': 'Critical',
                        'groups': 'GroupA, GroupC',
                        'attack_progression': 'Privilege Anomaly: Unusual Trio',
                        'mitre_techniques': 'Valid Accounts (T1078)',
                    }
                ),
            ],
        )


class TestBiggestThreatsRux(TestBiggestThreats):
    """
    Class to test RUX specific logic of biggest threats.
    """

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_get_biggest_threats_with_params_priority_score_desc(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90, 'sort_by': 'peaked_on', 'sort_direction': 'desc'})
        result = query.execute()
        cleaned_results = list(map(self.clean_result, result))
        self.assertTrue(cleaned_results[0]['peaked_on'] >= cleaned_results[1]['peaked_on'])
        self.assertTrue(cleaned_results[0]['peaked_on'] >= cleaned_results[-1]['peaked_on'])

    def test_get_biggest_threats_with_params_urgency_asc(self):
        # Default sorting is urgency score, therefore no need for params to control that.
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90, 'sort_direction': 'asc'})
        result = query.execute()

        cleaned_results = list(map(self.clean_result, result))
        self.assertTrue(cleaned_results[0]['urgency_score'] <= cleaned_results[1]['urgency_score'])

    def test_get_biggest_threats_with_params_priority_score_asc(self):
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90, 'sort_by': 'peaked_on', 'sort_direction': 'asc'})
        result = query.execute()

        cleaned_results = list(map(self.clean_result, result))
        self.assertTrue(cleaned_results[0]['peaked_on'] < cleaned_results[1]['peaked_on'])

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_get_biggest_threats_sort_by_urgency_score_and_peaked_on(self, mock_flag_enabled):

        latest_time = self.now - timedelta(minutes=1)
        # We need to create duplicate data for the same date time just a minute older
        account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (latest_time - timedelta(days=60)),
                        'type': DetectionType.O365_SUSPECTED_COMPROMISED_ACCESS,
                        'type_vname': 'Azure AD Suspected Compromised Access',
                        'category': 'COMMAND & CONTROL',
                        't_score': 70,
                        'c_score': 70,
                        'data_source_type': DataSourceType.O365,
                    }
                ],
                'score_history': [
                    {'created_date': (latest_time - timedelta(days=60)), 'score': 30, 'attack_rating': 1, 'entity_importance': 0},
                    {
                        'created_date': (latest_time - timedelta(days=29)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 0,
                        'is_prioritized': True,
                    },
                    {
                        'created_date': (latest_time - timedelta(days=30)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 0,
                        'is_prioritized': True,
                    },
                    {'created_date': (latest_time - timedelta(days=15)), 'score': 30, 'attack_rating': 2, 'entity_importance': 0},
                ],
                'groups': ['GroupA', 'GroupB'],
            }
        ]
        create_account_fixtures(account_fixtures, self.test_user, latest_time)

        # mock the query
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.biggest_threats_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.biggest_threats_query)
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90})
        result = query.execute()
        mock_flag_enabled.assert_called_with("unified_prioritization")

        cleaned_results = list(map(self.clean_result, result))
        # Assert the latest one is returned first.
        self.assertTrue(cleaned_results[1]['peaked_on'] > cleaned_results[2]['peaked_on'])
        # Assert they're the same score
        self.assertTrue(cleaned_results[1]['t_score'] == cleaned_results[2]['t_score'])
        # Assert that our entity created in this test that is older, appears later in the returned list.
        self.assertTrue(cleaned_results[2]['entity_name'] == 'test:<EMAIL>')


class TestBiggestThreatsQux(TestBiggestThreats):
    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_get_biggest_threats(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: False})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.biggest_threats_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.biggest_threats_query)
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90})
        result = query.execute()
        mock_flag_enabled.assert_called_with("unified_prioritization")

        cleaned_results = list(map(self.clean_result, result))

        self.assertEqual(
            cleaned_results,
            [
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box1',
                        'entity_importance': 'High',
                        'attack_rating': 8,
                        'urgency_score': 80,
                        't_score': 80,
                        'c_score': 80,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=56)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.AWS]}, {DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'severity_str': 'Critical',
                        'groups': 'DC1, DC2',
                        'attack_progression': 'ICMP Tunnel, ICMP Tunnel: Client',
                        'mitre_techniques': 'Fallback Channels (T1008), Exfiltration Over Alternative Protocol (T1048), Non-Application Layer Protocol (T1095), Lateral Tool Transfer (T1570)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'account',
                        'entity_name': 'test:<EMAIL>',
                        'entity_importance': 'Low',
                        'attack_rating': 3,
                        'urgency_score': 75,
                        't_score': 75,
                        'c_score': 75,
                        'severity': 5,
                        'last_seen_ip': '',
                        'peaked_on': (self.now - timedelta(days=30)).strftime('%Y-%m-%d %T'),
                        'data_source_types': DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.O365],
                        'severity_str': 'Critical',
                        'groups': 'GroupA, GroupB',
                        'attack_progression': 'Azure AD Suspected Compromised Access',
                        'mitre_techniques': 'Valid Accounts (T1078)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box2',
                        'entity_importance': 'Medium',
                        'attack_rating': 3,
                        'urgency_score': 70,
                        't_score': 70,
                        'c_score': 70,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=35)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'account',
                        'entity_name': 'test:<EMAIL>',
                        'entity_importance': 'High',
                        'attack_rating': 3,
                        'urgency_score': 52,
                        't_score': 52,
                        'c_score': 52,
                        'severity': 5,
                        'last_seen_ip': '',
                        'peaked_on': (self.now - timedelta(days=30)).strftime('%Y-%m-%d %T'),
                        'data_source_types': DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.O365],
                        'severity_str': 'Critical',
                        'groups': 'GroupA, GroupC',
                        'attack_progression': 'Privilege Anomaly: Unusual Trio',
                        'mitre_techniques': 'Valid Accounts (T1078)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box3',
                        'entity_importance': 'Medium',
                        'attack_rating': 3,
                        'urgency_score': 50,
                        't_score': 50,
                        'c_score': 50,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=36)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
            ],
        )

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_get_biggest_threats_with_params_t_score_score_asc(self, mock_flag):
        mock_flag.side_effect = self.setup_flag_mock({Flags.unified_prioritization: False})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.biggest_threats_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.biggest_threats_query)
        from tvui.reports.report_widgets.biggest_threats_query import BiggestThreatsQuery

        query = BiggestThreatsQuery(params={'num_days': 90, 'sort_by': 't_score', 'sort_direction': 'asc'})
        result = query.execute()
        cleaned_results = list(map(self.clean_result, result))
        self.assertTrue(cleaned_results[0]['t_score'] < cleaned_results[1]['t_score'])
