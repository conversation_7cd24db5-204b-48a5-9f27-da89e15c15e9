from collections import OrderedDict
from freezegun import freeze_time
from datetime import datetime
from decimal import Decimal

from django.utils import timezone
from datetime import timedelta

from unittest.mock import patch

from vui_tests.vui_testcase import VuiTestCase

from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_account_fixtures, create_host_fixtures, get_relative_dates

from tvui.reports.report_widgets.attack_surface_trends_query import AttackSurfaceTrendsQuery

import importlib
from base_tvui.feature_flipper.flags import Flags
from base_tvui.feature_flipper.helpers import flag_enabled


class TestAttackSurfaceTrendsQuery(VuiTestCase):
    """
    Test Class for the hours saved widget
    """

    def setUp(self):
        self.now = timezone.make_aware(datetime(year=2025, month=1, day=10, hour=0, minute=0, second=0, microsecond=0))

        self.freezer = freeze_time(self.now)
        self.freezer.start()

        self.host_fixtures = [
            {
                'host': {'name': 'box1', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=23)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=29)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=53)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': 60,
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {'created_date': (self.now - timedelta(days=15)), 'score': 60, 'attack_rating': 2, 'entity_importance': 3},
                    {
                        'created_date': (self.now - timedelta(days=23)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=9)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=58)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=54)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': 60,
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {'created_date': (self.now - timedelta(days=15)), 'score': 60, 'attack_rating': 2, 'entity_importance': 3},
                    {
                        'created_date': (self.now - timedelta(days=54)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
            {
                'host': {'name': 'box3-out-of-range', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=270)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=272)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': 60,
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {'created_date': (self.now - timedelta(days=265)), 'score': 60, 'attack_rating': 2, 'entity_importance': 3},
                    {
                        'created_date': (self.now - timedelta(days=270)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=272)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
        ]

        create_host_fixtures(self.host_fixtures)

        self.account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=43)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=22)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': 60,
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=28)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=5)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
                ],
            },
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=95)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        'data_source_type': 'aws',
                        't_score': 75,
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=100)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        'data_source_type': 'aws',
                        't_score': 75,
                        'c_score': 75,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=95)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=96)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=97)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
                ],
            },
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=25)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=35)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=27)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 75,
                        'c_score': 75,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=29)),
                        'score': 75,
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=5)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
                ],
            },
        ]

        create_account_fixtures(self.account_fixtures, now_frozen=self.now)

    def tearDown(self) -> None:
        self.freezer.stop()
        return super().tearDown()

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    def test_widget_result_QUX(self):
        """IS_RUX will be False by default in test container so no need to patch here"""
        query = AttackSurfaceTrendsQuery()
        result = query.execute()
        expected_result = [
            OrderedDict([('data_source_type', 'AWS Cloudtrail'), ('year_month', '2024-10'), ('cnt', Decimal('1'))]),
            OrderedDict([('data_source_type', 'Network'), ('year_month', '2024-11'), ('cnt', Decimal('1'))]),
            OrderedDict([('data_source_type', 'Network'), ('year_month', '2024-12'), ('cnt', Decimal('2'))]),
            OrderedDict([('data_source_type', 'Azure AD & M365'), ('year_month', '2024-12'), ('cnt', Decimal('2'))]),
            OrderedDict([('data_source_type', 'Network'), ('year_month', '2025-01'), ('cnt', Decimal('2'))]),
            OrderedDict([('data_source_type', 'Azure AD & M365'), ('year_month', '2025-01'), ('cnt', Decimal('2'))]),
        ]
        assert result == expected_result

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_widget_result_RUX(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})

        # have to re-import to apply patch as IS_RUX is defined at QueryBase defintion
        # (ie patching after wont apply to SQL in attack_surface_trends_query)
        import tvui.reports.report_widgets.attack_surface_trends_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.attack_surface_trends_query)
        from tvui.reports.report_widgets.attack_surface_trends_query import AttackSurfaceTrendsQuery

        query = AttackSurfaceTrendsQuery()
        result = query.execute()
        expected_result = [
            OrderedDict([('data_source_type', 'AWS Cloudtrail'), ('year_month', '2024-10'), ('cnt', Decimal('1'))]),
            OrderedDict([('data_source_type', 'Azure AD & M365'), ('year_month', '2024-12'), ('cnt', Decimal('2'))]),
        ]
        assert result == expected_result

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def test_widget_result_RUX_surface_network_only(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})

        # have to re-import to apply patch as IS_RUX is defined at QueryBase defintion
        # (ie patching after wont apply to SQL in attack_surface_trends_query)
        import tvui.reports.report_widgets.attack_surface_trends_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.attack_surface_trends_query)
        from tvui.reports.report_widgets.attack_surface_trends_query import AttackSurfaceTrendsQuery

        query = AttackSurfaceTrendsQuery(params={'attack_surface': 'network'})
        results = query.execute()
        # Validate that when a param of network is passed, we only return network.
        for month in results:
            assert month['data_source_type'] == 'network'
