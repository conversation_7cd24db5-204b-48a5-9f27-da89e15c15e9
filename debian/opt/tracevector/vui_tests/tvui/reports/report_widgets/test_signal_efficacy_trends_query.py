import unittest
from unittest.mock import patch
from datetime import timedelta, datetime
from collections import OrderedDict

from django.utils import timezone
from freezegun import freeze_time

from tvui.reports.report_widgets.signal_efficacy_trends_query import SignalEfficacyQuery
from tvui.models import CloseHistory, User, LinkedAccount, detection
from base_tvui.feature_flipper.helpers import flag_enabled
from base_tvui.feature_flipper.flags import Flags
from vui_tests.tvui.reports.report_widgets.helpers.signal_efficacy_helpers import create_required_signal_efficacy_data

FROZEN_TIME = "2025-06-30"


class TestSignalEfficacyQueryBase(unittest.TestCase):
    """tests for the signal_efficacy_trends_query module"""

    @patch('base_tvui.feature_flipper.conditions.is_cloud', return_value=True)
    def setUp(self, _):

        detection.objects.all().delete()
        LinkedAccount.objects.all().delete()
        CloseHistory.objects.all().delete()
        User.objects.all().delete()

        self.now = timezone.make_aware(datetime.strptime(FROZEN_TIME, "%Y-%m-%d"))
        self.test_user = User.objects.create_user('other', email='<EMAIL>', password='passw0rd')
        print(f"Test user created: {self.test_user.username} with ID {self.test_user.id}")

        create_required_signal_efficacy_data(self.test_user.id)

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock


class TestSignalEfficacyQueryRUX(TestSignalEfficacyQueryBase):
    """
    Noise to Signal trends RUX tests
    """

    @patch('tvui.reports.report_widgets.signal_efficacy_trends_query.QueryBase.IS_RUX', True)
    @freeze_time(FROZEN_TIME)
    def test_get_valid_query_no_attack_surface(self):
        """
        Test that the query returns a valid SQL string for RUX with no attack surface
        """

        expected_results = [
            OrderedDict([('month', '2025-05'), ('column_code', 'All Prioritized Detections'), ('cnt', 2)]),
            OrderedDict([('month', '2025-05'), ('column_code', 'Closed as Benign'), ('cnt', 1)]),
            OrderedDict([('month', '2025-05'), ('column_code', 'Closed as Remediated'), ('cnt', 1)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'All Prioritized Detections'), ('cnt', 4)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'Closed as Benign'), ('cnt', 1)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'Closed as Remediated'), ('cnt', 2)]),
        ]

        query = SignalEfficacyQuery(
            params={
                "attack_surface": None,
                "from": self.now - timedelta(days=90),
                "to": self.now,
            }
        )
        results = query.execute()
        self.assertEqual(results, expected_results)

    @patch('tvui.reports.report_widgets.signal_efficacy_trends_query.QueryBase.IS_RUX', True)
    @freeze_time(FROZEN_TIME)
    def test_get_valid_query_with_attack_surface(self):
        """
        Test that the query returns a valid SQL string for RUX with attack surface param
        tests both aws and network as they are the only ones in test data
        """

        expected_network_param_results = [
            OrderedDict([('month', '2025-05'), ('column_code', 'All Prioritized Detections'), ('cnt', 1)]),
            OrderedDict([('month', '2025-05'), ('column_code', 'Closed as Benign'), ('cnt', 1)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'All Prioritized Detections'), ('cnt', 2)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'Closed as Benign'), ('cnt', 1)]),
        ]
        expected_aws_param_results = [
            OrderedDict([('month', '2025-05'), ('column_code', 'All Prioritized Detections'), ('cnt', 1)]),
            OrderedDict([('month', '2025-05'), ('column_code', 'Closed as Remediated'), ('cnt', 1)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'All Prioritized Detections'), ('cnt', 2)]),
            OrderedDict([('month', '2025-06'), ('column_code', 'Closed as Remediated'), ('cnt', 2)]),
        ]

        network_param_query = SignalEfficacyQuery(
            params={
                "attack_surface": 'network',
                "from": self.now - timedelta(days=90),
                "to": self.now,
            }
        )
        network_param_results = network_param_query.execute()
        self.assertEqual(network_param_results, expected_network_param_results)

        aws_param_query = SignalEfficacyQuery(
            params={
                "attack_surface": 'aws',
                "from": self.now - timedelta(days=90),
                "to": self.now,
            }
        )
        aws_param_results = aws_param_query.execute()
        self.assertEqual(aws_param_results, expected_aws_param_results)

    @patch('tvui.reports.report_widgets.signal_efficacy_trends_query.QueryBase.IS_RUX', True)
    @freeze_time(FROZEN_TIME)
    def test_get_no_data_query_with_attack_surface(self):
        """
        Test that a query with no assocatied data (attack_surface = o365) returns an empty list
        """
        query = SignalEfficacyQuery(
            params={
                "attack_surface": 'o365',
                "from": self.now - timedelta(days=90),
                "to": self.now,
            }
        )
        results = query.execute()
        self.assertEqual(results, [])
