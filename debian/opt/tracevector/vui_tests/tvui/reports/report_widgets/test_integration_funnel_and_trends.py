from unittest.mock import patch

from vui_tests.vui_testcase import VuiTestCase
from freezegun import freeze_time

from django.utils import timezone
from datetime import timedelta, datetime

from tvui.models import User

from tvui.reports.report_widgets.noise_to_signal_funnel_query import NoiseToSignalFunnelQuery
from tvui.reports.report_widgets.noise_to_signal_trends_query import NoiseToSignalFunnelTrends

from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_account_fixtures, create_host_fixtures

FROZEN_TIME = "2025-03-30"


class N2STestWidgetBase(VuiTestCase):
    """
    Modified base of Funnel and Trends widget
    """

    @patch('base_tvui.feature_flipper.conditions.is_cloud', return_value=True)
    def setUp(self, _, is_qux=False, datasource_type=None):
        self.maxDiff = 3000

        self.now = timezone.make_aware(datetime(year=2025, month=4, day=10, hour=0, minute=0, second=0, microsecond=0))
        self.freezer = freeze_time(self.now)
        self.freezer.start()
        self.test_user = User.objects.create_user('other', email='<EMAIL>', password='passw0rd')

        threat_score_eval = lambda x: 30 if is_qux else x
        # for QUX tests below, create detections with lower t_score so that we can validate that query returns different results to the RUX version

        self.host_fixtures = [
            {
                'host': {'name': 'box1', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=23)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=29)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=53)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=23)),
                        'score': threat_score_eval(80),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=9)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=58)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=54)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=54)),
                        'score': threat_score_eval(80),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=7)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
            {
                'host': {'name': 'box3-out-of-range', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=70)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=72)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=65)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=70)),
                        'score': threat_score_eval(80),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                    {'created_date': (self.now - timedelta(days=72)), 'score': 10, 'attack_rating': 1, 'entity_importance': 3},
                ],
            },
        ]

        if datasource_type:
            create_host_fixtures(self.host_fixtures, self.test_user, datasource_type=datasource_type)
        else:
            create_host_fixtures(self.host_fixtures, self.test_user)

        self.account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=43)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(75),
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=22)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(75),
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=28)),
                        'score': threat_score_eval(75),
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {'created_date': (self.now - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
                    {'created_date': (self.now - timedelta(days=5)), 'score': 30, 'attack_rating': 2, 'entity_importance': 3},
                ],
            }
        ]

        create_account_fixtures(self.account_fixtures, self.test_user, self.now)

    def tearDown(self) -> None:
        self.freezer.stop()
        return super().tearDown()


class TestIntegrationFunnelAndTrends(N2STestWidgetBase):
    """
    Class to validate that the funnel and trends match correctly
    """

    def setUp(self):
        super().setUp(is_qux=False)

    @freeze_time(FROZEN_TIME)
    def test_validate_funnel_and_trends_match(self):
        funnel = NoiseToSignalFunnelQuery(params={'num_days': 30})
        trends = NoiseToSignalFunnelTrends()
        # Get the data
        funnel_result = funnel.execute()
        trends_result = trends.execute()

        # Trends defaults to 6 months, we only care for the month of march to match the funnel
        trends_march = [datapoint for datapoint in trends_result if datapoint["month_range"] == "2025-03"]

        # We have 3 categories of detections, potential, and prioritised alerts
        for category in range(0, 3):
            self.assertEqual(funnel_result[category]['cnt'], trends_march[category]['cnt'])
