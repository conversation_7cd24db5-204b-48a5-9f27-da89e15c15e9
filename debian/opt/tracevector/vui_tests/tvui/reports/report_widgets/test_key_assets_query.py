import importlib

from unittest.mock import patch
from vui_tests.vui_testcase import VuiTestCase
from freezegun import freeze_time

from django.utils import timezone
from datetime import timedelta
from collections import OrderedDict

from tvui.models import (
    DataSourceType,
    User,
)
from tvui.detections.detection_types import DetectionType
from tvui.reports.report_widgets.key_assets_query import KeyAssetsQuery

from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_host_fixtures


class TestKeyAssets(VuiTestCase):
    """Test KeyAssetsQuery Widget"""

    @patch('base_tvui.feature_flipper.conditions.is_cloud', return_value=True)
    def setUp(self, _):
        self.maxDiff = 6000

        self.now = timezone.now()

        self.freezer = freeze_time(self.now)
        self.freezer.start()

        self.test_user = User.objects.create_user('other', email='<EMAIL>', password='passw0rd')

    def tearDown(self) -> None:
        self.freezer.stop()
        return super().tearDown()

    def clean_result(self, el):
        del el['id']
        if el['peaked_on']:
            el['peaked_on'] = el['peaked_on'].strftime('%Y-%m-%d %T')
        return el

    def test_key_assets_pagination(self):
        host_fixtures_with_key_asset = [
            {
                'host': {'name': 'box3', 'last_source': '***********', 'key_asset': 1},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 40,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=61)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=16)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=36)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=71)),
                        'score': 15,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
        ]
        create_host_fixtures(host_fixtures_with_key_asset, session_luid=25, user=self.test_user)

        page_1_widget = KeyAssetsQuery(params={'page': 1, 'page_size': 1, 'num_days': 90})
        page_1_widget_result = list(map(self.clean_result, page_1_widget.execute()[0]))
        page_2_widget = KeyAssetsQuery(params={'page': 2, 'page_size': 1, 'num_days': 90})
        page_2_widget_result = list(map(self.clean_result, page_2_widget.execute()[0]))
        self.assertNotEqual(page_1_widget_result, page_2_widget_result)

    def test_get_key_assets_no_key_assets(self):
        """
        Test KeyAssetsQuery when there are no key assets in the DB
        """
        host_fixtures_no_key_assets = [
            {
                'host': {'name': 'box1', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.ICMP_TUNNEL,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.INTERNAL_ICMP_TUNNEL_C2S,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                        'data_source_type': 'aws',
                    },
                ],
                'score_history': [
                    {
                        # this score should be ignored because we dont check the current day
                        'created_date': (self.now - timedelta(minutes=1)),
                        'score': 100,
                        'is_prioritized': True,
                        'attack_rating': 10,
                        'entity_importance': 2,
                        'detection_profile': 'Should be ignored',
                    },
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': 60,
                        'attack_rating': 2,
                        'entity_importance': 2,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=54)),
                        'score': 80,
                        'is_prioritized': True,
                        'attack_rating': 8,
                        'entity_importance': 2,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=70)),
                        'score': 10,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': ['DC1', 'DC2'],
            },
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 60,
                        'state': 'inactive',
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 65,
                        'c_score': 65,
                        'state': 'inactive',
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=15)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=35)),
                        'score': 70,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=70)),
                        'score': 15,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
        ]
        create_host_fixtures(host_fixtures_no_key_assets, session_luid=20, user=self.test_user)

        query = KeyAssetsQuery(params={'num_days': 90})
        result = query.execute()
        result = result[0]
        cleaned_results = list(map(self.clean_result, result))

        # Assert there are none
        self.assertEqual(cleaned_results, [])

    def test_get_key_assets_all_with_detections(self):
        """
        Get key assets when all of them contain detections
        """
        host_fixtures_with_key_assets_all_contain_detections = [
            {
                'host': {'name': 'box3', 'last_source': '***********', 'key_asset': 1},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 40,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=61)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=16)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=35)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 4,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=36)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=91)),
                        'score': 15,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
            {
                'host': {'name': 'box4', 'last_source': '***********', 'key_asset': 1},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=70)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 40,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=71)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=17)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=36)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 4,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                ],
                'groups': [],
            },
        ]

        create_host_fixtures(host_fixtures_with_key_assets_all_contain_detections, session_luid=30, user=self.test_user)
        query = KeyAssetsQuery(params={'num_days': 90})
        result = query.execute()
        result = result[0]
        cleaned_results = list(map(self.clean_result, result))

        self.assertEqual(
            cleaned_results,
            [
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box3',
                        'entity_importance': 'Low',
                        'attack_rating': 0,
                        't_score': 50,
                        'c_score': 50,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=36)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'key_asset': 1,
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box4',
                        'entity_importance': 'Low',
                        'attack_rating': 0,
                        't_score': 50,
                        'c_score': 50,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=36)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'key_asset': 1,
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                ),
            ],
        )

    def test_get_key_assets_with_and_without_detections(self):
        """
        Get key assets when a  some contain detections and some don't
        """
        host_fixtures_with_key_asset = [
            {
                'host': {'name': 'box3', 'last_source': '***********', 'key_asset': 1},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 60,
                        'c_score': 40,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=61)),
                        'type': DetectionType.TOR,
                        'category': 'COMMAND & CONTROL',
                        't_score': 0,
                        'c_score': 0,
                        'state': 'inactive',
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=16)),
                        'score': 30,
                        'attack_rating': 2,
                        'entity_importance': 1,
                        'detection_profile': 'Insider Threat: User',
                    },
                    {
                        'created_date': (self.now - timedelta(days=35)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 4,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=36)),
                        'score': 50,
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 1,
                        'detection_profile': 'Vulnerability Discovery',
                    },
                    {
                        'created_date': (self.now - timedelta(days=91)),
                        'score': 15,
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'detection_profile': 'Potentially Unwanted Program',
                    },
                ],
                'groups': [],
            },
            {
                'host': {'name': 'box-with-no-detections', 'last_source': '***********', 'key_asset': 1},
                'detections': [],
                'score_history': [],
                'groups': [],
            },
        ]
        create_host_fixtures(host_fixtures_with_key_asset, session_luid=35, user=self.test_user)

        query = KeyAssetsQuery(params={'num_days': 90})
        result = query.execute()
        result = result[0]
        cleaned_results = list(map(self.clean_result, result))

        self.assertEqual(
            cleaned_results,
            [
                OrderedDict(
                    {
                        'entity_type': 'host',
                        'entity_name': 'box3',
                        'entity_importance': 'Low',
                        'attack_rating': 0,
                        't_score': 50,
                        'c_score': 50,
                        'severity': 5,
                        'last_seen_ip': '***********',
                        'peaked_on': (self.now - timedelta(days=36)).strftime('%Y-%m-%d %T'),
                        'data_source_types': f"{DataSourceType.DATA_SOURCE_TYPE_TO_LABEL[DataSourceType.NETWORK]}",
                        'key_asset': 1,
                        'severity_str': 'Critical',
                        'groups': '',
                        'attack_progression': 'TOR Activity',
                        'mitre_techniques': 'Proxy (T1090), Multi-hop Proxy (T1188)',
                    }
                )
            ],
        )

    def test_get_key_assets_key_all_without_detections(self):
        """
        Get key assets when all of them do not contain detections
        """
        host_fixtures_with_key_assets_all_without_detections = [
            {
                'host': {'name': 'box1-with-no-detections', 'last_source': '***********', 'key_asset': 1},
                'detections': [],
                'score_history': [],
                'groups': [],
            },
            {
                'host': {'name': 'box2-with-no-detections', 'last_source': '***********', 'key_asset': 1},
                'detections': [],
                'score_history': [],
                'groups': [],
            },
        ]

        create_host_fixtures(host_fixtures_with_key_assets_all_without_detections, session_luid=40, user=self.test_user)

        query = KeyAssetsQuery(params={'num_days': 90})
        result = query.execute()
        result = result[0]
        cleaned_results = list(map(self.clean_result, result))

        self.assertEqual(cleaned_results, [])
