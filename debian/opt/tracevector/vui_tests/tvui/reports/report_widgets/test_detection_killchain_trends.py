from collections import OrderedDict
import threading
from datetime import datetime
from freezegun import freeze_time
from parameterized import parameterized

import pytz
from django.utils.timezone import activate, get_current_timezone
from vui_tests.vui_testcase import VuiTestCase
from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_detections

from tvui.reports.report_widgets.detection_killchain_trends import DetectionKillchainTrendsQuery
from tvui.models import DataSourceType


class TestDetectionKillchainTrendsQuery(VuiTestCase):
    """
    Test Class for the hours saved widget
    """

    all_results = [
        OrderedDict(
            {
                'category': 'EXFILTRATION',
                'year_month': '2024-10',
                'cnt': 5,
            }
        ),
        OrderedDict(
            {
                'category': 'EXFILTRATION',
                'year_month': '2024-11',
                'cnt': 12,
            }
        ),
        OrderedDict(
            {
                'category': 'COMMAND & CONTROL',
                'year_month': '2024-12',
                'cnt': 6,
            }
        ),
        OrderedDict(
            {
                'category': 'EXFILTRATION',
                'year_month': '2024-12',
                'cnt': 8,
            }
        ),
        OrderedDict(
            {
                'category': 'LATERAL MOVEMENT',
                'year_month': '2024-12',
                'cnt': 3,
            }
        ),
        OrderedDict(
            {
                'category': 'COMMAND & CONTROL',
                'year_month': '2025-01',
                'cnt': 9,
            }
        ),
        OrderedDict(
            {
                'category': 'EXFILTRATION',
                'year_month': '2025-01',
                'cnt': 6,
            }
        ),
        OrderedDict(
            {
                'category': 'LATERAL MOVEMENT',
                'year_month': '2025-01',
                'cnt': 4,
            }
        ),
    ]

    network_results = [
        OrderedDict(
            {
                'category': 'EXFILTRATION',
                'year_month': '2024-10',
                'cnt': 2,
            }
        ),
        OrderedDict(
            {
                'category': 'COMMAND & CONTROL',
                'year_month': '2024-12',
                'cnt': 6,
            }
        ),
        OrderedDict(
            {
                'category': 'LATERAL MOVEMENT',
                'year_month': '2024-12',
                'cnt': 3,
            }
        ),
        OrderedDict(
            {
                'category': 'COMMAND & CONTROL',
                'year_month': '2025-01',
                'cnt': 9,
            }
        ),
    ]

    def setUp(self):
        # store the timezone
        self.original_timezone = get_current_timezone()

        self.now = datetime(year=2025, month=1, day=10, hour=0, minute=0, second=0, microsecond=0)

        self.freezer = freeze_time(self.now)
        self.freezer.start()

        # this months detections
        create_detections(count=9, days_ago=5, data_source_type=DataSourceType.NETWORK, category='COMMAND & CONTROL')
        create_detections(count=6, days_ago=5, data_source_type=DataSourceType.AWS, category='EXFILTRATION')
        create_detections(count=4, days_ago=5, data_source_type=DataSourceType.O365, category='LATERAL MOVEMENT')

        # december
        create_detections(count=6, days_ago=25, data_source_type=DataSourceType.NETWORK, category='COMMAND & CONTROL')
        create_detections(count=8, days_ago=35, data_source_type=DataSourceType.O365, category='EXFILTRATION')
        create_detections(count=3, days_ago=30, data_source_type=DataSourceType.NETWORK, category='LATERAL MOVEMENT')

        # november
        create_detections(count=7, days_ago=55, data_source_type=DataSourceType.O365, category='EXFILTRATION')
        create_detections(count=5, days_ago=65, data_source_type=DataSourceType.O365, category='EXFILTRATION')

        # october
        create_detections(count=3, days_ago=85, data_source_type=DataSourceType.O365, category='EXFILTRATION')
        create_detections(count=2, days_ago=89, data_source_type=DataSourceType.NETWORK, category='EXFILTRATION')
        create_detections(count=2, days_ago=92, data_source_type=DataSourceType.NETWORK, category='INFO')

        # created out of range
        create_detections(count=3, days_ago=200, data_source_type=DataSourceType.NETWORK, category='EXFILTRATION')

    def tearDown(self) -> None:
        self.freezer.stop()
        # restore the original timezone
        activate(self.original_timezone)
        return super().tearDown()

    @parameterized.expand(
        [
            ({}, all_results),
            ({'attack_surface': 'all'}, all_results),
            ({'attack_surface': DataSourceType.NETWORK}, network_results),
        ]
    )
    def test_widget_result(self, params, expected_result):
        query = DetectionKillchainTrendsQuery(params)
        result = query.execute()
        assert result == expected_result

    def test_sql_generation_in_non_utc_tz(self):
        # set timezone to a non-UTC timezone
        activate(pytz.timezone('America/New_York'))
        original_tz = get_current_timezone()
        assert original_tz.zone == 'America/New_York'
        # arrange
        query_instance = DetectionKillchainTrendsQuery()
        group_by_field = 'category'
        datetime_field = 'created_datetime'
        date_range = query_instance.calculate_dates_months_ago(months_ago=6)

        # act
        queryset = query_instance._trends_query(group_by_field, datetime_field, date_range, 'account')

        # change timezone to UTC for SQL generation
        # this is to simulate the behavior of the original code
        # where the timezone is set to UTC when the query set is evaluated
        activate(pytz.timezone('UTC'))
        assert get_current_timezone().zone == 'UTC'
        # generate the SQL associated with the queryset
        sql = str(queryset.query)

        # assert
        self.assertNotIn('CONVERT_TZ', sql)

    def test_sql_generation_in_utc_tz(self):
        # set timezone to UTC
        activate(pytz.timezone('UTC'))
        # arrange
        query_instance = DetectionKillchainTrendsQuery()
        group_by_field = 'category'
        datetime_field = 'created_datetime'
        date_range = query_instance.calculate_dates_months_ago(months_ago=6)

        # act
        queryset = query_instance._trends_query(group_by_field, datetime_field, date_range, 'account')

        # assert current tz is UTC
        current_tz = get_current_timezone()
        assert current_tz.zone == 'UTC'
        # generate the SQL associated with the queryset
        sql = str(queryset.query)

        # assert
        self.assertNotIn('CONVERT_TZ', sql)

    def test_decorator_does_not_alter_tz(self):
        # set timezone to a non-UTC timezone
        activate(pytz.timezone('America/New_York'))
        # decorator here will temporarily override the timezone
        result = DetectionKillchainTrendsQuery().execute()
        # assert the timezone is still America/New_York
        current_tz = get_current_timezone()
        assert current_tz.zone == 'America/New_York'


class TestDetectionKillchainTrendsQueryOverrideTimezoneThreadSafety(VuiTestCase):
    """
    Test Class for the override_timezone decorator thread safety
    """

    def setUp(self):
        self.query = DetectionKillchainTrendsQuery()
        self.results = {}  # To store results from each thread

    def thread_function(self, time_zone, thread_id):
        # Set initial timezone - activate() should set the timezone local to the current thread
        activate(pytz.timezone(time_zone))
        tz_before = get_current_timezone().zone

        # Execute the def decorated with override_timezone
        _result = self.query.execute()

        # get the tz after query execution
        tz_after = get_current_timezone().zone

        # store the before and after tz
        self.results[thread_id] = (tz_before, tz_after)

    def test_override_timezone_thread_safety(self):
        threads = []
        # limited to 64 so we don't max out connections to the database
        time_zones = (pytz.all_timezones)[:64]

        # Create and start threads
        for thread_id, tz in enumerate(time_zones):
            thread = threading.Thread(target=self.thread_function, args=(tz, thread_id))
            threads.append(thread)
            thread.start()

        # Wait for threads to finish
        for thread in threads:
            thread.join()

        # Assert that each thread's timezone did not interfere with other threads
        for thread_id, tz in enumerate(time_zones):
            before_tz, after_tz = self.results[thread_id]
            self.assertEqual(before_tz, tz)
            self.assertEqual(after_tz, tz)
