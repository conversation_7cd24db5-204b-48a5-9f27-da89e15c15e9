import importlib

from datetime import timedelta, datetime
from collections import OrderedDict
from decimal import Decimal
from unittest.mock import patch
from freezegun import freeze_time
from django.utils import timezone
from base_tvui.feature_flipper.flags import Flags
from base_tvui.feature_flipper.helpers import flag_enabled
from tvui.models import (
    LinkedAccount,
    LinkedAccountScoreHistory,
    Account,
    host,
    detection,
    score,
    User,
)
from vui_tests.vui_testcase import VuiTestCase
from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import (
    create_account_fixtures,
    get_relative_dates,
    create_host_fixtures,
    create_high_hosts,
)
from tvui.reports.report_widgets.noise_to_signal_trends_query import NoiseToSignalFunnelTrends

FROZEN_TIME = "2025-03-15"


def cleanup():
    detection.objects.all().delete()
    host.objects.all().delete()
    LinkedAccount.objects.all().delete()
    Account.objects.all().delete()
    score.objects.all().delete()
    LinkedAccountScoreHistory.objects.all().delete()


class TestNoiseToSignalTrendsBase(VuiTestCase):
    """
    Test Class for noise to signal trends
    """

    @patch('base_tvui.feature_flipper.conditions.is_cloud', return_value=True)
    def setUp(self, _, is_qux=False):
        self.now = timezone.make_aware(datetime.strptime(FROZEN_TIME, "%Y-%m-%d"))
        self.test_user = User.objects.create_user('other', email='<EMAIL>', password='passw0rd')

        threat_score_eval = lambda x: 30 if is_qux else x
        # for QUX tests below, create detections with lower t_score so that we can validate that query returns different results to the RUX version

        self.host_fixtures = [
            {
                # All score histof of this host is the past month. it's in order by month.
                'host': {'name': 'box1', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=7)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=6)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(20),
                        'c_score': 20,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=55)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=5)),
                        'score': threat_score_eval(60),
                        'attack_rating': 1,
                        'entity_importance': 1,
                        'is_prioritized': True,
                    },
                    {
                        'created_date': (self.now - timedelta(days=4)),
                        'score': threat_score_eval(20),
                        'attack_rating': 1,
                        'entity_importance': 1,
                    },
                ],
            },
            # 2 months ago and so on.
            {
                'host': {'name': 'box2', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=35)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=15)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=34)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=35)),
                        'score': threat_score_eval(60),
                        'is_prioritized': True,
                        'attack_rating': 2,
                        'entity_importance': 3,
                    },
                    {
                        'created_date': (self.now - timedelta(days=34)),
                        'score': threat_score_eval(20),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                ],
            },
            {
                'host': {'name': 'box3-out-of-range', 'last_source': '***********'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=22)),
                        'type': 'spa_tcp_info',
                        'category': 'INFO',  # SHOULD BE IGNORED
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=61)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': 10,
                        'c_score': 10,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=60)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(60),
                        'c_score': 60,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=61)),
                        'score': threat_score_eval(60),
                        'attack_rating': 2,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {
                        'created_date': (self.now - timedelta(days=60)),
                        'score': threat_score_eval(20),
                        'is_prioritized': True,
                        'attack_rating': 3,
                        'entity_importance': 3,
                    },
                ],
            },
        ]

        create_host_fixtures(self.host_fixtures, self.test_user)

        self.account_fixtures = [
            {
                'account': {'uid': '<EMAIL>'},
                'detections': [
                    {
                        'created_datetime': (self.now - timedelta(days=95)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(75),
                        'c_score': 75,
                    },
                    {
                        'created_datetime': (self.now - timedelta(days=94)),
                        'type': 'tor',
                        'category': 'COMMAND & CONTROL',
                        't_score': threat_score_eval(20),
                        'c_score': 20,
                    },
                ],
                'score_history': [
                    {
                        'created_date': (self.now - timedelta(days=95)),
                        'score': threat_score_eval(75),
                        'attack_rating': 3,
                        'entity_importance': 3,
                        'is_prioritized': True,
                    },
                    {
                        'created_date': (self.now - timedelta(days=94)),
                        'score': threat_score_eval(20),
                        'attack_rating': 1,
                        'entity_importance': 3,
                    },
                ],
            }
        ]

        create_account_fixtures(self.account_fixtures, self.test_user, self.now)

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock


class TestNoiseToSignalTrendsQueryRUX(TestNoiseToSignalTrendsBase):
    """
    Noise to Signal trends RUX tests
    """

    def setUp(self):
        super().setUp(is_qux=False)

    @freeze_time(FROZEN_TIME)
    def test_get_valid_query(self):
        """
        the difference between this and QUX is RUX (this) should include prioritised_entities
        """
        query = NoiseToSignalFunnelTrends(params={'num_days': 90})
        results = query.execute()
        expected = [
            OrderedDict([('month_range', '2024-12'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
            OrderedDict([('month_range', '2025-01'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
            OrderedDict([('month_range', '2025-02'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
            OrderedDict([('month_range', '2025-03'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
            OrderedDict([('month_range', '2024-12'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2025-01'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2025-02'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2025-03'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2024-12'), ('column_code', 'Prioritized Alerts'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2025-01'), ('column_code', 'Prioritized Alerts'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2025-02'), ('column_code', 'Prioritized Alerts'), ('cnt', Decimal('1'))]),
            OrderedDict([('month_range', '2025-03'), ('column_code', 'Prioritized Alerts'), ('cnt', Decimal('1'))]),
        ]
        self.assertEqual(results, expected)


class TestNoiseToSignalTrendsQueryQUX(TestNoiseToSignalTrendsBase):
    """
    Noise to Signal trends QUX tests
    """

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def setUp(self, mock_flag_enabled):
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: False})
        # Reload the module so flag_enabled import uses patch
        import tvui.reports.report_widgets.noise_to_signal_trends_query

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.noise_to_signal_trends_query)
        from tvui.reports.report_widgets.noise_to_signal_trends_query import NoiseToSignalFunnelTrends

        super().setUp(is_qux=True)

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    @freeze_time(FROZEN_TIME)
    def test_get_valid_query_qux(self):
        """
        the difference between this and RUX is QUX (this) should NOT include prioritised_entities
        """
        query = NoiseToSignalFunnelTrends(params={'num_days': 90})
        results = query.execute()
        self.assertEqual(
            results,
            [
                OrderedDict([('month_range', '2024-12'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
                OrderedDict([('month_range', '2025-01'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
                OrderedDict([('month_range', '2025-02'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
                OrderedDict([('month_range', '2025-03'), ('column_code', 'Detections'), ('cnt', Decimal('2'))]),
                OrderedDict([('month_range', '2024-12'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
                OrderedDict([('month_range', '2025-01'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
                OrderedDict([('month_range', '2025-02'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
                OrderedDict([('month_range', '2025-03'), ('column_code', 'Potential Attack Progressions'), ('cnt', Decimal('1'))]),
            ],
        )
