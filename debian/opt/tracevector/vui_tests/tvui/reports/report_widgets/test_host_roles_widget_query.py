import random
import string
from collections import OrderedDict

from unittest.mock import patch

from vui_tests.vui_testcase import VuiTestCase
from tvui.reports.report_widgets.host_roles_widget_query import HostRolesWidgetQuery
from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_host_session
from django.utils import timezone


class TestHostRolesWidgetQuery(VuiTestCase):

    def setUp(self):
        self.maxDiff = 3000

        def _uid_gen():
            return ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))

        self.now = timezone.now()

        roles_data = {
            'dns': 15,
            'dhcp': 7,
            'web': 6,
            'dc': 13,
            'proxy': 22,
            'database': 17,
            'printer': 3,
            'fileserver': 2,
        }
        self.host_attribute_data = {}
        for role, num_sessions in roles_data.items():
            for i in list(range(num_sessions)):
                host_luid = _uid_gen()
                create_host_session(self.now, host_luid=host_luid)

                if role not in self.host_attribute_data:
                    self.host_attribute_data[role] = []

                self.host_attribute_data[role].append({'host_luid': host_luid, 'value': role})

    @patch('tvui.reports.report_widgets.host_roles_widget_query.HostAttributeAPI')
    def test_execute(self, mock_host_attribute_api):

        def mock_get_attributes(_key, value):
            return self.host_attribute_data.get(value, [])

        mock_host_attribute_api.get_attributes.side_effect = mock_get_attributes

        query = HostRolesWidgetQuery()
        results = query.execute()

        self.assertEqual(
            results,
            [
                OrderedDict(
                    {
                        'host_role_label': 'DNS Server',
                        'cnt': 15,
                        'host_role': 'dns',
                    }
                ),
                OrderedDict({'host_role_label': 'DHCP Server', 'cnt': 7, 'host_role': 'dhcp'}),
                OrderedDict({'host_role_label': 'Web Server', 'cnt': 6, 'host_role': 'web'}),
                OrderedDict({'host_role_label': 'DC Services', 'cnt': 13, 'host_role': 'dc'}),
                OrderedDict({'host_role_label': 'Proxy Server', 'cnt': 22, 'host_role': 'proxy'}),
                OrderedDict({'host_role_label': 'Database Server', 'cnt': 17, 'host_role': 'database'}),
                OrderedDict({'host_role_label': 'Printer', 'cnt': 3, 'host_role': 'printer'}),
                OrderedDict({'host_role_label': 'File Server', 'cnt': 2, 'host_role': 'fileserver'}),
            ],
        )
