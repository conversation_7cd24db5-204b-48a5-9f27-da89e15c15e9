from decimal import Decimal, ROUND_FLOOR

from unittest.mock import patch
from uuid import uuid4
from vui_tests.vui_testcase import VuiTestCase
from tvui.reports.report_widgets.noise_reduction import NoiseReductionWidgetQuery
from vui_tests.tvui.reports.report_widgets.helpers import base_helpers
from django.utils import timezone
from datetime import timed<PERSON>ta

import importlib
from base_tvui.feature_flipper.flags import Flags
from base_tvui.feature_flipper.helpers import flag_enabled


class TestNoiseReductionEmpty(VuiTestCase):
    """
    Test Class for the Noise Reduction widget, where no data has been created.
    """

    def setUp(self):
        self.empty_noise = NoiseReductionWidgetQuery()

    # TODO Re-Enable this, but it requires a frontend change as right now it's NaN otherwise.
    # def test_execute_empty(self):
    #     results = self.empty_noise.execute()
    #     widget_dict = results[0]
    #     self.assertEqual(widget_dict['label'], 'Ratio of Detections to Prioritized Entities')
    #     # It should be empty with nothing there.
    #     self.assertEqual(widget_dict['sum'], '-')

    # def test_execute_old_detections(self):
    #     base_helpers.create_old_detection()
    #     results = self.empty_noise.execute()
    #     widget_dict = results[0]
    #     # It should not pick up older detections out of range.
    #     self.assertEqual(widget_dict['sum'], '-')


class TestNoiseReduction(VuiTestCase):
    """
    Test Class for the Noise Reduction widget
    This will be for QUX testing and general testing where we expect the same result between RUX and QUX
    """

    def setUp(self):
        base_helpers.create_account_fixtures()
        base_helpers.create_host_fixtures(datasource_type='network')
        self.noise = NoiseReductionWidgetQuery()

    def test_execute_more_detections_QUX(self):
        base_helpers.create_detections(67)
        base_helpers.create_detections(count=3, category='INFO')  # these should be ignored
        results = self.noise.execute()
        widget_dict = results[0]
        self.assertEqual(widget_dict['sum'], Decimal('0.95'))

    def test_rounding_QUX(self):
        test_number_input = 8.001
        results = self.noise._handle_rounding(test_number_input)
        self.assertEqual(results, Decimal('0.08'))

    def test_execute_never_exceeds_100_percent(self):
        # Create a lot of detections, the end result should never be above 100% noise reduction.
        base_helpers.create_detections(400)
        results = self.noise.execute()
        widget_dict = results[0]
        self.assertTrue(widget_dict['sum'] <= 100)

    def test_rounding_never_up_hundred(self):
        test_number_input = 99.999999
        results = self.noise._handle_rounding(test_number_input)
        self.assertEqual(results, Decimal('0.99'))

    def test_rounding_hundred(self):
        test_number_input = 100
        results = self.noise._handle_rounding(test_number_input)
        self.assertEqual(results, Decimal('1.0'))


class TestNoiseReductionRUX(VuiTestCase):
    """
    Test Class for the Noise Reduction widget
    This is specifically for RUX tests (which we expect to have different output to QUX)
    """

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        return flag_enabled_mock

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def setUp(self, mock_flag_enabled):
        """use patching to set IS_RUX to true"""
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})

        # have to re-import to apply patch as IS_RUX is defined at QueryBase defintion
        # (ie patching after wont apply to SQL in noise_reduction)
        import tvui.reports.report_widgets.noise_reduction

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.noise_reduction)
        from tvui.reports.report_widgets.noise_reduction import NoiseReductionWidgetQuery

        # continue as normal, NoiseReductionWidgetQuery will have IS_RUX==True now
        base_helpers.create_account_fixtures()
        base_helpers.create_host_fixtures()
        self.noise = NoiseReductionWidgetQuery()

    # NOTE add tests here that we expect to have different results for RUX
    def test_execute_more_detections_RUX(self):
        base_helpers.create_detections(67)
        results = self.noise.execute()
        widget_dict = results[0]
        self.assertEqual(widget_dict['sum'], Decimal('0.98'))

    def test_rounding_RUX(self):
        test_number_input = 8.001
        results = self.noise._handle_rounding(test_number_input)
        self.assertEqual(results, Decimal('0.08'))


class TestNoiseReductionAttackSurface(VuiTestCase):
    """
    Test Class for the Noise Reduction widget
    This is specifically for attack surface
    """

    ACCOUNT_FIXTURES = [
        {
            'account': {'uid': '<EMAIL>'},
            'detections': [
                {
                    'created_datetime': (timezone.now() - timedelta(days=5)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 75,
                    'c_score': 75,
                },
                {
                    'created_datetime': (timezone.now() - timedelta(days=5)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 75,
                    'c_score': 75,
                },
                {
                    'created_datetime': (timezone.now() - timedelta(days=3)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 30,
                    'c_score': 30,
                },
            ],
            'score_history': [
                {
                    'created_date': (timezone.now() - timedelta(days=5)),
                    'score': 75,
                    'attack_rating': 3,
                    'entity_importance': 3,
                    'is_prioritized': True,
                },
                {'created_date': (timezone.now() - timedelta(days=4)), 'score': 50, 'attack_rating': 2, 'entity_importance': 3},
                {'created_date': (timezone.now() - timedelta(days=3)), 'score': 30, 'attack_rating': 1, 'entity_importance': 1},
            ],
        }
    ]
    HIGH_HOSTS = [
        {
            'host': {'name': 'nr_host', 'last_source': '999.168.0.1'},
            'detections': [
                {
                    # Spread the detections across the months to cause it to appear over time.
                    'created_datetime': (timezone.now() - timedelta(days=10)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 60,
                    'c_score': 60,
                },
                {
                    # Spread the detections across the months to cause it to appear over time.
                    'created_datetime': (timezone.now() - timedelta(days=9)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 60,
                    'c_score': 60,
                },
                {
                    'created_datetime': (timezone.now() - timedelta(days=8)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 20,
                    'c_score': 20,
                },
            ],
            'score_history': [
                {
                    'created_date': (timezone.now() - timedelta(days=9)),
                    'score': 90,
                    'attack_rating': 1,
                    'entity_importance': 1,
                    'is_prioritized': True,
                },
                {
                    'created_date': (timezone.now() - timedelta(days=8)),
                    'score': 80,
                    'attack_rating': 1,
                    'entity_importance': 1,
                    'is_prioritized': True,
                },
                {
                    'created_date': (timezone.now() - timedelta(days=7)),
                    'score': 20,
                    'attack_rating': 1,
                    'entity_importance': 1,
                },
            ],
        },
    ]

    def setup_flag_mock(self, flag_overrides):
        def flag_enabled_mock(flag):
            if flag in flag_overrides:
                return flag_overrides.get(flag, flag_enabled(flag))

        base_helpers.create_host_fixtures(host_fixtures=self.HIGH_HOSTS, datasource_type='aws')
        base_helpers.create_account_fixtures(account_fixtures=self.ACCOUNT_FIXTURES)

        return flag_enabled_mock

    @patch('base_tvui.feature_flipper.helpers.flag_enabled')
    def setUp(self, mock_flag_enabled):
        """use patching to set IS_RUX to true"""
        mock_flag_enabled.side_effect = self.setup_flag_mock({Flags.unified_prioritization: True})

        # have to re-import to apply patch as IS_RUX is defined at QueryBase defintion
        # (ie patching after wont apply to SQL in noise_reduction)
        import tvui.reports.report_widgets.noise_reduction

        importlib.reload(tvui.reports.report_widgets.query_base)
        importlib.reload(tvui.reports.report_widgets.noise_reduction)
        from tvui.reports.report_widgets.noise_reduction import NoiseReductionWidgetQuery

    def test_execute_aws(self):
        base_helpers.create_detections(2, data_source_type='aws', created_datetime=timezone.now() - timedelta(days=10))
        self.noise = NoiseReductionWidgetQuery(params={'attack_surface': 'aws'})
        results = self.noise.execute()
        widget_dict = results[0]
        self.assertEqual(widget_dict['sum'], Decimal('0.80'))

    def test_execute_many_types(self):
        # Even if we create many different entities of different kinds
        # they shouldn't fall over each other and skew the metrics.

        # As in we should only get entities and detections of the specified datasource_type
        # The results shouldn't be lower noise to signal because of more detections of a different kind.
        # They will conflict on account ID so we need more acc fixtures.
        extra_account_fixtures = {
            'account': {'uid': '<EMAIL>'},
            'detections': [
                {
                    'created_datetime': (timezone.now() - timedelta(days=43)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 75,
                    'c_score': 75,
                },
                {
                    'created_datetime': (timezone.now() - timedelta(days=22)),
                    'type': 'tor',
                    'category': 'COMMAND & CONTROL',
                    't_score': 75,
                    'c_score': 75,
                },
            ],
            'score_history': [
                {
                    'created_date': (timezone.now() - timedelta(days=28)),
                    'score': 75,
                    'attack_rating': 3,
                    'entity_importance': 3,
                    'is_prioritized': True,
                },
                {'created_date': (timezone.now() - timedelta(days=16)), 'score': 30, 'attack_rating': 1, 'entity_importance': 3},
            ],
        }
        # Validate AWS is as expected before we create 0365.
        base_helpers.create_detections(2, data_source_type='aws', created_datetime=timezone.now() - timedelta(days=10))
        self.aws_noise = NoiseReductionWidgetQuery(params={'attack_surface': 'aws'})
        aws_results = self.aws_noise.execute()
        aws_widget_dict = aws_results[0]
        self.assertEqual(aws_widget_dict['sum'], Decimal('0.80'))

        # Valdiate 0365
        base_helpers.create_account_fixtures(account_fixtures=[extra_account_fixtures])
        base_helpers.create_host_fixtures(datasource_type='o365', session_luid=40)
        base_helpers.create_detections(30, data_source_type='o365', created_datetime=timezone.now() - timedelta(days=10))
        self.noise_365 = NoiseReductionWidgetQuery(params={'attack_surface': 'o365', 'num_days': 200})
        results = self.noise_365.execute()
        widget_dict = results[0]
        self.assertEqual(widget_dict['sum'], Decimal('0.94'))
