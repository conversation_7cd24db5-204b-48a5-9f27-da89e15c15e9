from unittest import mock
from unittest.mock import patch
from datetime import timedelta

from vui_tests.vui_testcase import VuiTestCase
from tvui.reports.report_widgets.cost_saving import CostSavingWidgetQuery
from tvui.reports.report_widgets.constants import REPORTING_CURRENCY_PRECISION
from vui_tests.tvui.reports.report_widgets.helpers.base_helpers import create_detections, create_old_detection


class TestCostSaving(VuiTestCase):
    """
    Test Class for Cost Savings
    """

    def setUp(self):
        self.cost_saving_widget = CostSavingWidgetQuery()

    def test_widget_result_empty_data(self):
        result = self.cost_saving_widget.execute()
        assert result[0]['sum'] == 0

    def test_widget_result(self):
        create_detections(count=1)
        # The math is [1] detection * (SA_RATE [60] / (60/12) TIME_INVESTIGATE[12] ) == 1 * (60 / 12.0)
        # So we validate that the answer is 5 as 1 * (60 / 12.0) == 5
        result = self.cost_saving_widget.execute()
        sum_result = result[0]['sum']
        assert sum_result == 25
        # Validate we are only at 2 decimal points and no more.
        assert len(str(sum_result).rsplit('.')[-1]) <= REPORTING_CURRENCY_PRECISION

    def test_widget_query_only_returns_detections_in_date_range(self):
        create_detections(count=2)
        len_original = len(self.cost_saving_widget.get_detections())
        create_old_detection()
        create_old_detection(category='INFO')
        db_detections = self.cost_saving_widget.get_detections()
        len_query_with_old_data = len(db_detections)
        for db_detection in db_detections:
            # Validate old detection isn't returned.
            assert db_detection.type != 'old_detection'
        # Validate the results returned before and after created_date are the same therefore it's not returned.
        assert len_original == len_query_with_old_data

    def test_widget_params(self):
        create_detections(count=1)
        cost_saving_widget_with_params = CostSavingWidgetQuery(params={'num_days': 1})
        assert cost_saving_widget_with_params.params == {'num_days': 1}
        len_original = len(cost_saving_widget_with_params.get_detections(num_days_ago=1))
        create_old_detection()
        create_old_detection(category='INFO')
        db_detections = cost_saving_widget_with_params.get_detections(num_days_ago=1)
        len_query_with_old_data = len(db_detections)
        for db_detection in db_detections:
            # Validate old detection isn't returned.
            assert db_detection.type != 'old_detection'
        # Validate the results returned before and after created_date are the same therefore it's not returned.
        assert len_original == len_query_with_old_data

    def test_widget_params_with_data_source_type(self):

        # First we create some detections of AWS
        create_detections(count=5, data_source_type='aws')
        aws_cost_saving = CostSavingWidgetQuery(params={'num_days': 2, 'attack_surface': 'aws'})
        aws_detections = aws_cost_saving.get_detections(num_days_ago=2, surface='aws')
        aws_cost_saved = aws_cost_saving.execute()

        # Then we do the same for 0365, validate no overlap.
        create_detections(count=15, data_source_type='o365')
        o365_cost_saving = CostSavingWidgetQuery(params={'num_days': 2, 'attack_surface': 'o365'})
        o365_detections = o365_cost_saving.get_detections(num_days_ago=2, surface='o365')
        o365_cost_saved = o365_cost_saving.execute()

        # Validate the params are as expected
        assert aws_cost_saving.params == {'num_days': 2, 'attack_surface': 'aws'}
        assert o365_cost_saving.params == {'num_days': 2, 'attack_surface': 'o365'}
        # Validate the detections themselves
        assert len(aws_detections) != len(o365_detections)
        assert aws_cost_saved[0]['sum'] < o365_cost_saved[0]['sum']
        assert o365_cost_saved[0]['sum'] == 375
        assert aws_cost_saved[0]['sum'] == 125
