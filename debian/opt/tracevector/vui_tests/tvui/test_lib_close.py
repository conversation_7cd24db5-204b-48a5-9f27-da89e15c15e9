from uuid import uuid4
from unittest.mock import patch

from django.contrib.contenttypes.models import ContentType
from django.utils import timezone

from tvui.models import (
    Assignment,
    Account,
    AccountType,
    CloseHistory,
    DataSourceType,
    detection,
    host,
    host_session,
    LinkedAccount,
    StateReasons,
    User,
    VUIPermission,
    VUIGroup,
)
from base_tvui.lib_close_history import CloseFacilitator
from vui_tests.vui_testcase import VuiTestCase


class TestCloseFacilitator(VuiTestCase):
    """
    Test class for testing methods of the Close Facilitator.
    """

    def _create_account(self, account_id=uuid4()):
        """
        Helper test method to generate an account that has a linked account.
        returns account: Account
        """
        linked_account = LinkedAccount.objects.create(display_uid=f'test:{account_id}')

        account = Account.objects.create(
            uid=account_id,
            account_type=AccountType.KERBEROS,
            first_seen=timezone.now(),
            last_seen=timezone.now(),
        )

        account.linked_account = linked_account
        account.save()
        return account

    def setUp(self):
        """
        Create a CloseFacilitator instance
        """
        self.user = User.objects.create_user('test_cognito', email='<EMAIL>', password='passw0rd')
        self.close_facilitator = CloseFacilitator()
        self.now = timezone.now()

        self.test_host = host.objects.create(name='test')
        self.test_host_session = host_session.objects.create(
            host_id=self.test_host.id, ip_address='**********', session_luid='srchs1', start=timezone.now(), end=timezone.now()
        )

        self.test_linked_account = LinkedAccount.objects.create(display_uid='test_account')

        self.det = detection.objects.create(
            type='port_scan',
            type_vname='Port Scan',
            src_ip='**********',
            first_timestamp=timezone.now(),
            last_timestamp=timezone.now(),
            host_session=self.test_host_session,
            t_score=11,
            c_score=77,
            category='RECONNAISSANCE',
            state='active',
            sensor_luid='snsr1',
            targets_key_asset=1,
            data_source_type=DataSourceType.NETWORK,
        )

        self.assigned_user = User.objects.create_user(username='testuser', password='pass')
        content_type_host = ContentType.objects.get_or_create(app_label='tvui', model='host')[0]
        view_permission_host, _ = VUIPermission.objects.get_or_create(
            defaults={'name': 'view_host'}, codename='view_host', content_type_id=content_type_host.id
        )
        content_type_assignment = ContentType.objects.get_or_create(app_label='tvui', model='assignment')[0]
        view_permission_assignment, _ = VUIPermission.objects.get_or_create(
            defaults={'name': 'view_assignment'}, codename='view_assignment', content_type_id=content_type_assignment.id
        )
        edit_permission_assignment, _ = VUIPermission.objects.get_or_create(
            defaults={'name': 'edit_assignment'}, codename='edit_assignment', content_type_id=content_type_assignment.id
        )
        perm_group = VUIGroup.objects.get_or_create(name='detection_test')[0]
        perm_group.permissions.add(view_permission_host, view_permission_assignment, edit_permission_assignment)
        self.assigned_user.groups.add(perm_group)

        self.add_patch(
            'assignable_users',
            patch('tvui.models.ActiveUserManager.assignable_users', return_value=User.objects.filter(id=self.assigned_user.id)),
        )
        self.assignment_host = Assignment.objects.create(
            obj_type='host', type_id=self.det.host.id, user=self.assigned_user, assigned_by=self.user
        )

        self.assignment_account = Assignment.objects.create(
            obj_type='linked_account', type_id=self.test_linked_account.id, user=self.assigned_user, assigned_by=self.user
        )

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_close_detection_record_implicit_account(self, timezone_patch):
        """
        Test to validate that when we close a detection with a linked account,
        that linked account is also stored in the closed database too implicitly.
        """

        timezone_patch.return_value = self.now
        # Create a detection with an account and linked account
        implicit_det = detection.objects.create(
            type='port_scan',
            type_vname='Port Scan',
            src_ip='**********',
            first_timestamp=timezone.now(),
            last_timestamp=timezone.now(),
            account=self._create_account(),
            t_score=55,
            c_score=77,
            category='LATERAL MOVEMENT',
            state='active',
            sensor_luid='snsr1',
            targets_key_asset=1,
            data_source_type=DataSourceType.NETWORK,
        )
        detections = detection.objects.filter(id=implicit_det.id)
        self.close_facilitator.close_detections(detections, self.user, 'remediated')
        close_history_record = CloseHistory.objects.get(detection_id=implicit_det.id)
        self.assertEqual(close_history_record.detection_id, implicit_det.id)
        self.assertEqual(close_history_record.reason, StateReasons.REMEDIATED.value)
        self.assertEqual(close_history_record.user_id, self.user.id)
        self.assertEqual(close_history_record.closed_on, self.now.replace(microsecond=0))
        # Validate that implicit linked accounts are closed.
        self.assertEqual(close_history_record.linked_account_id, implicit_det.account.linked_account.id)

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_close_detection_record_implicit_host_close(self, timezone_patch):
        """
        Test to validate that when we close a detection on a host,
        without explicitly passing the host in to the close detection method
        that the host is also stored in the closed database too implicitly.
        """

        timezone_patch.return_value = self.now
        detections = detection.objects.filter(id=self.det.id)

        self.close_facilitator.close_detections(detections, self.user, 'remediated')
        close_history_record = CloseHistory.objects.get(detection_id=self.det.id)
        self.assertEqual(close_history_record.detection_id, self.det.id)
        self.assertEqual(close_history_record.reason, StateReasons.REMEDIATED.value)
        self.assertEqual(close_history_record.user_id, self.user.id)
        self.assertEqual(close_history_record.closed_on, self.now.replace(microsecond=0))
        self.assertEqual(close_history_record.host_id, self.det.host.id)

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_close_detection_record_with_passed_host_entity(self, timezone_patch):
        """
        It successfully adds close record for a detection with an associated host
        """

        timezone_patch.return_value = self.now
        detections = detection.objects.filter(id=self.det.id)

        self.close_facilitator.close_detections(detections, self.user, 'not_valuable', entity_obj=self.test_host, entity_type='host')
        close_history_record = CloseHistory.objects.get(detection_id=self.det.id)
        self.assertEqual(close_history_record.detection_id, self.det.id)
        self.assertEqual(close_history_record.reason, StateReasons.NOT_VALUABLE.value)
        self.assertEqual(close_history_record.user_id, self.user.id)
        self.assertEqual(close_history_record.closed_on, self.now.replace(microsecond=0))
        self.assertEqual(close_history_record.host_id, self.test_host.id)

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_close_detection_record_with_passed_account(self, timezone_patch):
        """
        It successfully adds close record for a detection with an associated account
        """

        timezone_patch.return_value = self.now
        detections = detection.objects.filter(id=self.det.id)

        self.close_facilitator.close_detections(
            detections, self.user, 'benign', entity_obj=self.test_linked_account, entity_type='linked_account'
        )
        close_history_record = CloseHistory.objects.get(detection_id=self.det.id)
        self.assertEqual(close_history_record.detection_id, self.det.id)
        self.assertEqual(close_history_record.reason, StateReasons.BENIGN.value)
        self.assertEqual(close_history_record.user_id, self.user.id)
        self.assertEqual(close_history_record.closed_on, self.now.replace(microsecond=0))
        self.assertEqual(close_history_record.linked_account_id, self.test_linked_account.id)

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_close_host(self, timezone_patch):
        """
        It successfully adds close record for a host
        """

        timezone_patch.return_value = self.now

        self.close_facilitator.close_entity(self.user, 'benign', entity_obj=self.test_host, entity_type='host')
        close_history_record = CloseHistory.objects.get(host_id=self.test_host.id)
        self.assertEqual(close_history_record.detection_id, None)
        self.assertEqual(close_history_record.reason, StateReasons.BENIGN.value)
        self.assertEqual(close_history_record.user_id, self.user.id)
        self.assertEqual(close_history_record.closed_on, self.now.replace(microsecond=0))
        self.assertEqual(close_history_record.host_id, self.test_host.id)
        self.assertEqual(close_history_record.assignment_timestamp, self.assignment_host.date_assigned.replace(microsecond=0))

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_close_account(self, timezone_patch):
        """
        It successfully adds close record for a host
        """

        timezone_patch.return_value = self.now

        self.close_facilitator.close_entity(self.user, 'benign', entity_obj=self.test_linked_account, entity_type='linked_account')
        close_history_record = CloseHistory.objects.get(linked_account_id=self.test_linked_account.id)
        self.assertEqual(close_history_record.detection_id, None)
        self.assertEqual(close_history_record.reason, StateReasons.BENIGN.value)
        self.assertEqual(close_history_record.user_id, self.user.id)
        self.assertEqual(close_history_record.closed_on, self.now.replace(microsecond=0))
        self.assertEqual(close_history_record.linked_account_id, self.test_linked_account.id)
        self.assertEqual(close_history_record.assignment_timestamp, self.assignment_account.date_assigned.replace(microsecond=0))
        self.assertFalse(close_history_record.is_prioritized)

    @patch('base_tvui.lib_close_history.timezone.now')
    def test_prioritized_entity_on_qux(self, timezone_patch):
        """
        is_prioritized is True when entity threat score is greater than 50
        """

        timezone_patch.return_value = self.now
        self.test_linked_account.t_score = 60
        self.test_linked_account.save()

        self.close_facilitator.close_entity(self.user, 'benign', entity_obj=self.test_linked_account, entity_type='linked_account')
        close_history_record = CloseHistory.objects.get(linked_account_id=self.test_linked_account.id)
        self.assertTrue(close_history_record.is_prioritized)

    @patch('base_tvui.lib_close_history.timezone.now')
    @patch('base_tvui.lib_close_history.flag_enabled', return_value=True)
    def test_non_prioritized_entity_on_rux(self, _, timezone_patch):
        """
        is_prioritized is False when entity is not prioritized
        """

        timezone_patch.return_value = self.now

        self.close_facilitator.close_entity(self.user, 'benign', entity_obj=self.test_linked_account, entity_type='linked_account')
        close_history_record = CloseHistory.objects.get(linked_account_id=self.test_linked_account.id)
        self.assertFalse(close_history_record.is_prioritized)

    @patch('base_tvui.lib_close_history.timezone.now')
    @patch('base_tvui.lib_close_history.flag_enabled', return_value=True)
    def test_prioritized_entity_on_rux(self, _, timezone_patch):
        """
        is_prioritized is True when entity is prioritized
        """

        timezone_patch.return_value = self.now
        self.test_linked_account.is_prioritized = True
        self.test_linked_account.save()

        self.close_facilitator.close_entity(self.user, 'benign', entity_obj=self.test_linked_account, entity_type='linked_account')
        close_history_record = CloseHistory.objects.get(linked_account_id=self.test_linked_account.id)
        self.assertTrue(close_history_record.is_prioritized)
