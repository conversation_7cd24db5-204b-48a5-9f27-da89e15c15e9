from django.utils import timezone
from tvui.models import detection_detail, InternalDetectionTarget
from vui_tests.vui_testcase import VuiTestCase
from tvui.detections.dynamic_detections.detection_target_utils import get_details_targeting_entity


class TestGetDetailsTargetingEntity(VuiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.host_detail_1 = detection_detail.objects.create(
            src_session_luid='host1src', is_host_detail=True, dst_session_luid='host1dst', last_timestamp=timezone.now()
        )

        cls.host_detail_2 = detection_detail.objects.create(
            src_session_luid='host2src', is_host_detail=True, dst_session_luid='host2dst', last_timestamp=timezone.now()
        )

        cls.account_detail_1 = detection_detail.objects.create(
            account_uid='acct1src', is_host_detail=False, flex_json={'target_uid': 'acct1dst'}, last_timestamp=timezone.now()
        )

        cls.account_detail_2 = detection_detail.objects.create(
            account_uid='acct2src', is_host_detail=False, flex_json={'target_uid': 'acct2dst'}, last_timestamp=timezone.now()
        )

        # host_detail_1 targets multiple entities (host and account)
        InternalDetectionTarget.objects.create(
            detection_detail=cls.host_detail_1, source_host_session_luid='host1src', target_host_session_luid='shared1'
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.host_detail_1, source_host_session_luid='host1src', target_account_uid='shared2'
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.host_detail_2,
            source_host_session_luid='host2src',
            target_host_session_luid='shared1',
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.host_detail_2,
            source_host_session_luid='host2src',
            target_host_session_luid='unique2',
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.account_detail_1,
            source_account_uid='acct1src',
            target_account_uid='shared2',
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.account_detail_1, source_account_uid='acct1src', target_host_session_luid='shared1'
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.account_detail_2, source_account_uid='acct2src', target_account_uid='shared2'
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.account_detail_2, source_account_uid='acct2src', target_account_uid='shared2'
        )

        InternalDetectionTarget.objects.create(
            detection_detail=cls.account_detail_2,
            source_account_uid='acct2src',
            target_account_uid='unique',
        )

    def test_get_host_targeting_details(self):
        """Test getting details that target a specific host session"""
        result = get_details_targeting_entity('shared1', is_host=True)

        # Should return host_detail_1, host_detail_2, and account_detail_1
        self.assertEqual(len(result), 3)
        detail_ids = [detail.id for detail in result]
        self.assertIn(self.host_detail_1.id, detail_ids)
        self.assertIn(self.host_detail_2.id, detail_ids)
        self.assertIn(self.account_detail_1.id, detail_ids)

    def test_get_account_targeting_details(self):
        """Test getting details that target a specific account"""
        result = get_details_targeting_entity('shared2', is_host=False)

        # Should return host_detail_1, account_detail_1, and account_detail_2
        self.assertEqual(len(result), 3)
        detail_ids = [detail.id for detail in result]
        self.assertIn(self.host_detail_1.id, detail_ids)
        self.assertIn(self.account_detail_1.id, detail_ids)
        self.assertIn(self.account_detail_2.id, detail_ids)

    def test_empty_result_account(self):
        """Test getting details for a host that is not targeted by any detection"""
        result = get_details_targeting_entity('noexist', is_host=False)

        self.assertEqual(len(result), 0)
        self.assertEqual(result, [])

    def test_single_result_host(self):
        """Test getting details for a host targeted by only one detection"""
        result = get_details_targeting_entity('unique2', is_host=True)

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].id, self.host_detail_2.id)

    def test_unique_detection_details(self):
        """Test that duplicate InternalDetectionTarget entries don't result in duplicate detection_details"""
        result = get_details_targeting_entity('shared2', is_host=False)

        detail_ids = [detail.id for detail in result]
        self.assertEqual(len(detail_ids), len(set(detail_ids)))
