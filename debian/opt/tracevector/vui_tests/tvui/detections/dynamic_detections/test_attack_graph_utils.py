from django.utils import timezone
from unittest.mock import patch

from base_tvui.account_type import AccountType
from tvui.detections.detection_types import DetectionType
from tvui.detections.dynamic_detections.attack_graph_utils import (
    BLAST_RADIUS_DETECTION_TYPES,
    get_host_attack_graph_info,
    get_linked_account_attack_graph_info,
    get_related_blast_radius_details,
)
from tvui.models import detection, detection_detail, host, smart_rule, Account, LinkedAccount, HostGroup, IPGroup, User
from vui_tests.base_tvui.smart_rules import testing_utils
from vui_tests.vui_testcase import VuiTestCase


class TestBlastRadiusDetectionTypes(VuiTestCase):
    def test_blast_radius_detection_types(self):
        # Test that we correct fill out the blast radius list
        det_types = set(
            [
                getattr(DetectionType, field)
                for field in dir(DetectionType)
                if '__' not in field and not callable(getattr(DetectionType, field)) and not getattr(DetectionType, field).startswith('cm_')
            ]
        )
        assert len(BLAST_RADIUS_DETECTION_TYPES) > 10
        assert set(BLAST_RADIUS_DETECTION_TYPES).issubset(det_types)


class TestGetRelatedBlastRadiusDetails(VuiTestCase):
    def test_matching_ip(self):
        det = detection.objects.create(type=DetectionType.BINARYLOADER, state=detection.ACTIVE, last_timestamp=timezone.now())
        detail = detection_detail.objects.create(
            type=DetectionType.BINARYLOADER, host_detection=det, last_timestamp=timezone.now(), dst_ip='*******'
        )
        dns_details, ip_details = get_related_blast_radius_details(detection_id=0, dns_list=[], ip_list=['*******'])

        assert list(dns_details) == []
        assert list(ip_details) == [detail]

    def test_matching_dns(self):
        det = detection.objects.create(type=DetectionType.BINARYLOADER, state=detection.ACTIVE, last_timestamp=timezone.now())
        detail = detection_detail.objects.create(
            type=DetectionType.BINARYLOADER, host_detection=det, last_timestamp=timezone.now(), dst_dns='foo.bar'
        )
        dns_details, ip_details = get_related_blast_radius_details(detection_id=0, dns_list=['foo.bar'], ip_list=[])

        assert list(dns_details) == [detail]
        assert list(ip_details) == []

    def test_invalid_type(self):
        det = detection.objects.create(type=DetectionType.BITCOIN, state=detection.ACTIVE, last_timestamp=timezone.now())
        detection_detail.objects.create(type=DetectionType.BITCOIN, host_detection=det, last_timestamp=timezone.now(), dst_ip='*******')
        dns_details, ip_details = get_related_blast_radius_details(detection_id=0, dns_list=[], ip_list=['*******'])

        assert list(dns_details) == []
        assert list(ip_details) == []

    def test_invalid_state(self):
        det = detection.objects.create(type=DetectionType.BINARYLOADER, state=detection.INACTIVE, last_timestamp=timezone.now())
        detection_detail.objects.create(
            type=DetectionType.BINARYLOADER, host_detection=det, last_timestamp=timezone.now(), dst_ip='*******'
        )
        dns_details, ip_details = get_related_blast_radius_details(detection_id=0, dns_list=[], ip_list=['*******'])

        assert list(dns_details) == []
        assert list(ip_details) == []

    def test_traiged(self):
        sr = smart_rule.objects.create(
            type='typeA',
            priority=40,
            conditions=testing_utils.get_smart_rule_conditions(source_conditions=None, additional_conditions=None),
        )
        det = detection.objects.create(
            type=DetectionType.BINARYLOADER, state=detection.ACTIVE, last_timestamp=timezone.now(), smart_rule=sr
        )
        detection_detail.objects.create(
            type=DetectionType.BINARYLOADER, host_detection=det, last_timestamp=timezone.now(), dst_ip='*******'
        )
        dns_details, ip_details = get_related_blast_radius_details(detection_id=0, dns_list=[], ip_list=['*******'])

        assert list(dns_details) == []
        assert list(ip_details) == []

    def test_do_not_return_own_details(self):
        det = detection.objects.create(type=DetectionType.BINARYLOADER, state=detection.ACTIVE, last_timestamp=timezone.now())
        detection_detail.objects.create(
            type=DetectionType.BINARYLOADER, host_detection=det, last_timestamp=timezone.now(), dst_ip='*******'
        )
        dns_details, ip_details = get_related_blast_radius_details(detection_id=det.id, dns_list=[], ip_list=['*******'])

        assert list(dns_details) == []
        assert list(ip_details) == []


class TestGetHostAttackGraphInfo(VuiTestCase):
    @patch('tvui.detections.dynamic_detections.attack_graph_utils.is_cloud')
    def test_host_info_cloud(self, mock_is_cloud):
        mock_is_cloud.return_value = True

        user = User.objects.create_user(username='foo', password='bar')
        hg = HostGroup.objects.create(last_modified_by=user, name='testhostgroup', description='test')
        IPGroup.objects.create(last_modified_by=user, name='testipgroup', description='test', ips=['*******'])
        h = host.objects.create(name='testhost', is_prioritized=False, last_source='*******')
        h.tags.add('testtag')
        h.hostgroup_set.add(hg)

        host_info = get_host_attack_graph_info(h)

        assert len(host_info) == 6
        assert {'key': 'id', 'value': h.id} in host_info
        assert {'key': 'name', 'value': h.name} in host_info
        assert {'key': 'isPrioritized', 'value': h.is_prioritized} in host_info
        assert {'key': 'lastSeenIP', 'value': h.last_source} in host_info
        assert {'key': 'tags', 'value': ['testtag']} in host_info
        assert {'key': 'groups', 'value': ['testhostgroup', 'testipgroup']} in host_info

    @patch('tvui.detections.dynamic_detections.attack_graph_utils.is_cloud')
    def test_host_info_appliance(self, mock_is_cloud):
        mock_is_cloud.return_value = False

        user = User.objects.create_user(username='foo', password='bar')
        hg = HostGroup.objects.create(last_modified_by=user, name='testhostgroup', description='test')
        IPGroup.objects.create(last_modified_by=user, name='testipgroup', description='test', ips=['*******'])
        h = host.objects.create(name='testhost', is_prioritized=False, last_source='*******')
        h.tags.add('testtag')
        h.hostgroup_set.add(hg)

        host_info = get_host_attack_graph_info(h)

        assert len(host_info) == 5
        assert {'key': 'id', 'value': h.id} in host_info
        assert {'key': 'name', 'value': h.name} in host_info
        assert {'key': 'lastSeenIP', 'value': h.last_source} in host_info
        assert {'key': 'tags', 'value': ['testtag']} in host_info
        assert {'key': 'groups', 'value': ['testhostgroup', 'testipgroup']} in host_info


class TestGetLinkedAccountAttackGraphInfo(VuiTestCase):
    @patch('tvui.detections.dynamic_detections.attack_graph_utils.is_cloud')
    def test_account_info_cloud(self, mock_is_cloud):
        mock_is_cloud.return_value = True

        user = User.objects.create_user(username='foo', password='bar')
        la = LinkedAccount.objects.create(
            display_uid='testaccount', is_prioritized=False, urgency_score=33, attack_rating=3, entity_importance=0
        )

        subaccounts = []
        for idx, account_type in enumerate([AccountType.KERBEROS, AccountType.O365, AccountType.AWS, AccountType.ENTRA_PRINCIPAL]):
            subaccount = Account.objects.create(uid=f'sub{idx}', account_type=account_type, last_seen=timezone.now())
            subaccount.linked_account = la
            subaccount.save()
            subaccounts.append(subaccount)

        la.tags.add('testtag')

        account_info = get_linked_account_attack_graph_info(la)
        assert len(account_info) == 18
        assert {'key': 'id', 'value': la.id} in account_info
        assert {'key': 'displayName', 'value': la.display_uid} in account_info
        assert {'key': 'isPrioritized', 'value': la.is_prioritized} in account_info
        assert {'key': 'entityType', 'value': 'Account'} in account_info
        assert {'key': 'urgencyScore', 'value': la.urgency_score} in account_info
        assert {'key': 'lastSeen', 'value': la.last_seen} in account_info
        assert {'key': 'tags', 'value': ['testtag']} in account_info
        assert {'key': 'attackRating', 'value': la.attack_rating} in account_info
        assert {'key': 'entityImportance', 'value': 'Low'} in account_info
        assert {'key': 'networkAccount', 'value': subaccounts[0].uid} in account_info
        assert {'key': 'networkLockdownStatus', 'value': subaccounts[0].lockdown_state} in account_info
        assert {'key': 'o365Account', 'value': subaccounts[1].uid} in account_info
        assert {'key': 'o365LockdownStatus', 'value': subaccounts[1].lockdown_state} in account_info
        assert {'key': 'awsAccount', 'value': subaccounts[2].uid} in account_info
        assert {'key': 'entraAccount', 'value': subaccounts[3].clean_uid} in account_info
        assert {'key': 'entraLockdownStatus', 'value': subaccounts[3].lockdown_state} in account_info
        assert {'key': 'groups', 'value': []} in account_info
        assert {'key': 'subaccounts', 'value': [subaccount.uid for subaccount in subaccounts]} in account_info

    @patch('tvui.detections.dynamic_detections.attack_graph_utils.is_cloud')
    def test_account_info_appliance(self, mock_is_cloud):
        mock_is_cloud.return_value = False

        user = User.objects.create_user(username='foo', password='bar')
        la = LinkedAccount.objects.create(
            display_uid='testaccount', is_prioritized=False, urgency_score=33, t_score=22, attack_rating=3, entity_importance=0
        )

        subaccounts = []
        for idx, account_type in enumerate([AccountType.KERBEROS, AccountType.O365, AccountType.AWS, AccountType.ENTRA_PRINCIPAL]):
            subaccount = Account.objects.create(uid=f'sub{idx}', account_type=account_type, last_seen=timezone.now())
            subaccount.linked_account = la
            subaccount.save()
            subaccounts.append(subaccount)

        la.tags.add('testtag')

        account_info = get_linked_account_attack_graph_info(la)
        assert len(account_info) == 15
        assert {'key': 'id', 'value': la.id} in account_info
        assert {'key': 'displayName', 'value': la.display_uid} in account_info
        assert {'key': 'entityType', 'value': 'Account'} in account_info
        assert {'key': 'threatScore', 'value': la.t_score} in account_info
        assert {'key': 'lastSeen', 'value': la.last_seen} in account_info
        assert {'key': 'tags', 'value': ['testtag']} in account_info
        assert {'key': 'networkAccount', 'value': subaccounts[0].uid} in account_info
        assert {'key': 'networkLockdownStatus', 'value': subaccounts[0].lockdown_state} in account_info
        assert {'key': 'o365Account', 'value': subaccounts[1].uid} in account_info
        assert {'key': 'o365LockdownStatus', 'value': subaccounts[1].lockdown_state} in account_info
        assert {'key': 'awsAccount', 'value': subaccounts[2].uid} in account_info
        assert {'key': 'entraAccount', 'value': subaccounts[3].clean_uid} in account_info
        assert {'key': 'entraLockdownStatus', 'value': subaccounts[3].lockdown_state} in account_info
        assert {'key': 'groups', 'value': []} in account_info
        assert {'key': 'subaccounts', 'value': [subaccount.uid for subaccount in subaccounts]} in account_info
