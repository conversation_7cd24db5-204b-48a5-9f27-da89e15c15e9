# Copyright (c) 2020 Vectra Networks - All Rights Reserved
# Unauthorized copying of this file via any medium is strictly prohibited
# Proprietary and confidential
from unittest.mock import patch

from base_tvui.feature_flipper import conditions
from django.utils import timezone
from tvui.detections.dynamic_detections import dynamic_processors
from tvui.detections.dynamic_detections.attack_graph_handlers import AttackGraphHandler
from tvui.models import InternalDetectionTarget, detection_detail, host
from vui_tests.vui_testcase import VuiTestCase


class TestDynamicProcessors(VuiTestCase):
    """
    Tests for dynamic detection processors
    """

    def setUp(self):
        self.detection_data = {
            'grouped_details': [
                {'bar': 'test', 'foo': 1, 'foobar': {'bar': 'test', 'foo': 1}},
                {'bar': 'example', 'foo': 2, 'foobar': {'bar': 'example', 'foo': 2}},
                {'bar': 'fake', 'foo': 3, 'foobar': {'bar': 'fake', 'foo': 3}},
                {'bar': 'fake', 'foo': 4, 'foobar': {'bar': 'fake', 'foo': 4}},
            ],
            'summary': {'total_foo': 10, 'unique_bar': ['test', 'example', 'fake']},
        }

    def test_detail_processor(self):
        schema = {'fields': {'foo': 'foo', 'bar': 'bar', 'foobar': {'handler': 'extract_fields', 'foo': 'foo', 'bar': 'bar'}}}
        detection_details = [{'bar': 'test', 'foo': 1}, {'bar': 'example', 'foo': 2}, {'bar': 'fake', 'foo': 3}, {'bar': 'fake', 'foo': 4}]
        self.assertEqual(
            dynamic_processors.DetailProcessor(schema, detection_details).process_details(), self.detection_data['grouped_details']
        )

    def test_groupby_processor(self):
        schema = {
            'groupingField': 'foo',
            'fields': {
                'foo': {'handler': 'unique_value', 'field': 'foo'},
                'bar': {'handler': 'sum_list', 'field': 'bar'},
                'foobar': {'field': 'foobar_count', 'handler': 'min_value'},
                'barfoo': 'barfoo',
            },
        }
        detection_details = [
            {'foo': 'test', 'bar': 2, 'foobar_count': 3, 'barfoo': 'a'},
            {'foo': 'test', 'bar': 5, 'foobar_count': 6, 'barfoo': 'b'},
            {'foo': 'example', 'bar': 8, 'foobar_count': 9, 'barfoo': 'c'},
            {'foo': 'fake', 'bar': 7, 'foobar_count': 6, 'barfoo': 'd'},
            {'foo': 'fake', 'bar': 4, 'foobar_count': 3, 'barfoo': 'e'},
            {'foo': 'fake', 'bar': 1, 'foobar_count': 0, 'barfoo': 'f'},
        ]
        expected_detection_data = [
            {'foo': 'test', 'bar': 7, 'foobar': 3, 'barfoo': ['a', 'b']},
            {'foo': 'example', 'bar': 8, 'foobar': 9, 'barfoo': ['c']},
            {'foo': 'fake', 'bar': 12, 'foobar': 0, 'barfoo': ['d', 'e', 'f']},
        ]
        self.assertEqual(
            dynamic_processors.GroupbyProcessor(schema, detection_details).group_and_aggregate_details(None), expected_detection_data
        )

    def test_summary_processor(self):
        schema = {
            'fields': {'total_foo': {'handler': 'sum_list', 'field': 'foo'}, 'unique_bar': {'handler': 'unique_list', 'field': 'bar'}}
        }
        self.assertEqual(
            dynamic_processors.SummaryProcessor(schema, self.detection_data['grouped_details']).summarize_groupby(None),
            self.detection_data['summary'],
        )

    def test_api_processor(self):
        schema = {'excludeFields': ['bar', 'total_foo'], 'fields': {'foobar': {'handler': 'transform_list', 'field': 'foobar'}}}
        expected_detection_data = {
            'grouped_details': [
                {'foo': 1, 'foobar': [{'bar': 'test', 'foo': 1}]},
                {'foo': 2, 'foobar': [{'bar': 'example', 'foo': 2}]},
                {'foo': 3, 'foobar': [{'bar': 'fake', 'foo': 3}]},
                {'foo': 4, 'foobar': [{'bar': 'fake', 'foo': 4}]},
            ],
            'summary': {'unique_bar': ['test', 'example', 'fake']},
        }
        self.assertEqual(dynamic_processors.APIProcessor(schema, self.detection_data).process_detection(), expected_detection_data)

    def test_detection_event_detail_processor(self):
        schema = {
            'fields': {
                'foo': 'foo',
                'bar': 'bar',
                'foobar': {'handler': 'extract_fields', 'foo': 'foo', 'bar': 'bar'},
            }
        }
        detection_detail = {'bar': 'test', 'foo': 1, 'src_ip': 'removethis'}
        # The expected output should exclude src_ip
        expected_output = self.detection_data['grouped_details'][0]
        self.assertEqual(
            dynamic_processors.DetectionEventDetailProcessor(schema, detection_detail).process_detail(),
            expected_output,
        )

    def test_detection_event_detail_processor_empty_schema(self):
        schema = {}
        detection_detail = {'bar': 'test', 'foo': 1, 'src_ip': 'removethis'}
        expected_output = {}
        self.assertEqual(
            dynamic_processors.DetectionEventDetailProcessor(schema, detection_detail).process_detail(),
            expected_output,
        )

    def test_detection_event_detail_processor_none_schema(self):
        schema = None
        detection_detail = {'bar': 'test', 'foo': 1, 'src_ip': 'removethis'}
        expected_output = {}
        self.assertEqual(
            dynamic_processors.DetectionEventDetailProcessor(schema, detection_detail).process_detail(),
            expected_output,
        )

    def test_attack_graph_processor_nodes(self):
        schema = {'fields': {'targets': {'handler': 'generic_target', 'field': 'foo', 'icon': 'service', 'label': 'Foo'}}}
        detection_data = [{'foo': 'testval'}]
        expected_output = [
            {
                'nodeId': 'service_testval',
                'name': 'testval',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval', 'label': 'Foo'}],
            }
        ]
        self.assertEqual(
            dynamic_processors.AttackGraphProcessor(schema, detection_data, 'host').process_nodes(),
            expected_output,
        )

    def test_attack_graph_processor_nodes_dedupe(self):
        schema = {'fields': {'targets': {'handler': 'generic_target', 'field': 'foo', 'icon': 'service', 'label': 'Foo'}}}
        detection_data = [{'foo': 'testval'}, {'foo': 'testval'}, {'foo': 'testval2'}]
        expected_output = [
            {
                'nodeId': 'service_testval',
                'name': 'testval',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval', 'label': 'Foo'}],
            },
            {
                'nodeId': 'service_testval2',
                'name': 'testval2',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval2', 'label': 'Foo'}],
            },
        ]
        self.assertEqual(
            dynamic_processors.AttackGraphProcessor(schema, detection_data, 'host').process_nodes(),
            expected_output,
        )

    @patch('tvui.detections.dynamic_detections.attack_graph_utils.is_cloud')
    @patch('tvui.detections.dynamic_detections.attack_graph_handlers.is_cloud')
    def test_attack_graph_processor_remove_nulls(self, mock_is_cloud_handlers, mock_is_cloud_utils):

        self.maxDiff = None
        mock_is_cloud_handlers.return_value = True
        mock_is_cloud_utils.return_value = True
        h = host.objects.create(name='testhost', urgency_score=44, last_source='*******')
        schema = {
            'fields': {
                'targets': [
                    {"handler": "extract_value", "field": "dst_hosts"},
                    {"handler": "extract_value", "field": "dst_host"},
                    {"handler": "internal_host_target"},
                ]
            }
        }
        detection_data = [
            {
                'dst_hosts': [
                    {'dst_host': {'id': None, 'ip': '*******', 'name': 'testbox0'}},
                    {'dst_host': {'id': None, 'ip': None, 'name': None}},
                    {'dst_host': {'id': h.id, 'ip': '*******', 'name': 'testbox1'}},
                ]
            }
        ]
        expected_output_pre_null_removal = [
            {
                'nodeId': 'host_testbox0',
                'name': 'testbox0',
                'nodeType': 'host',
                'urgencyScore': '0',
                'tooltipData': [{'key': 'Internal Host', 'label': 'Internal Host', 'value': 'testbox0'}],
            },
            None,
            {
                'nodeId': f'host_testhost_{h.id}',
                'name': 'testhost',
                'nodeType': 'host',
                'urgencyScore': 44,
                'tooltipData': [
                    {'key': 'id', 'value': h.id},
                    {'key': 'name', 'value': 'testhost'},
                    {'key': 'lastSeenIP', 'value': '*******'},
                    {'key': 'tags', 'value': []},
                    {'key': 'groups', 'value': []},
                    {'key': 'isPrioritized', 'value': False},
                ],
            },
        ]
        # Null will be present when handling
        self.assertEqual(
            AttackGraphHandler().get_result(detection_data[0], schema['fields']['targets'], ''),
            expected_output_pre_null_removal,
        )

        expected_output_nulls_removed = [
            {
                'nodeId': 'host_testbox0',
                'name': 'testbox0',
                'nodeType': 'host',
                'urgencyScore': '0',
                'tooltipData': [{'key': 'Internal Host', 'label': 'Internal Host', 'value': 'testbox0'}],
            },
            {
                'nodeId': f'host_testhost_{h.id}',
                'name': 'testhost',
                'nodeType': 'host',
                'urgencyScore': 44,
                'tooltipData': [
                    {'key': 'id', 'value': h.id},
                    {'key': 'name', 'value': 'testhost'},
                    {'key': 'lastSeenIP', 'value': '*******'},
                    {'key': 'tags', 'value': []},
                    {'key': 'groups', 'value': []},
                    {'key': 'isPrioritized', 'value': False},
                ],
            },
        ]
        # Null will be gone when procesing
        self.assertEqual(
            dynamic_processors.AttackGraphProcessor(schema, detection_data, 'host').process_nodes(),
            expected_output_nulls_removed,
        )

    def test_attack_graph_processor_nodes_and_edges(self):
        schema = {'fields': {'targets': {'handler': 'generic_target', 'field': 'foo', 'icon': 'service', 'label': 'Foo'}}}
        detection_data = [{'foo': 'testval'}]
        expected_nodes = [
            {
                'nodeId': 'service_testval',
                'name': 'testval',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval', 'label': 'Foo'}],
            }
        ]
        expected_edges = [{'sourceId': 'host_node_1', 'targetId': 'service_testval'}]
        nodes, edges = dynamic_processors.AttackGraphProcessor(schema, detection_data, 'host').process_nodes_and_edges(
            attributed_node_id='host_node_1', detection_edge_data={}
        )
        self.assertEquals(expected_nodes, nodes)
        self.assertEquals(expected_edges, edges)

    def test_attack_graph_processor_nodes_and_edges_reverse(self):
        schema = {
            'fields': {'targets': {'handler': 'generic_target', 'field': 'foo', 'icon': 'service', 'label': 'Foo'}},
            'reverseDirection': True,
        }
        detection_data = [{'foo': 'testval'}]
        expected_nodes = [
            {
                'nodeId': 'service_testval',
                'name': 'testval',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval', 'label': 'Foo'}],
            }
        ]
        expected_edges = [{'sourceId': 'service_testval', 'targetId': 'host_node_1'}]
        nodes, edges = dynamic_processors.AttackGraphProcessor(schema, detection_data, 'host').process_nodes_and_edges(
            attributed_node_id='host_node_1', detection_edge_data={}
        )
        self.assertEquals(expected_nodes, nodes)
        self.assertEquals(expected_edges, edges)

    def test_attack_graph_processor_nodes_and_edges_dedupe(self):
        schema = {'fields': {'targets': {'handler': 'generic_target', 'field': 'foo', 'icon': 'service', 'label': 'Foo'}}}
        detection_data = [{'foo': 'testval'}, {'foo': 'testval'}, {'foo': 'testval2'}]
        expected_nodes = [
            {
                'nodeId': 'service_testval',
                'name': 'testval',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval', 'label': 'Foo'}],
            },
            {
                'nodeId': 'service_testval2',
                'name': 'testval2',
                'nodeType': 'service',
                'tooltipData': [{'key': 'Foo', 'value': 'testval2', 'label': 'Foo'}],
            },
        ]
        expected_edges = [
            {'sourceId': 'host_node_1', 'targetId': 'service_testval'},
            {'sourceId': 'host_node_1', 'targetId': 'service_testval2'},
        ]
        nodes, edges = dynamic_processors.AttackGraphProcessor(schema, detection_data, 'host').process_nodes_and_edges(
            attributed_node_id='host_node_1', detection_edge_data={}
        )
        self.assertEquals(expected_nodes, nodes)
        self.assertEquals(expected_edges, edges)


class TestDetectionTarget(VuiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.host_detail = detection_detail.objects.create(
            src_session_luid='test_src_luid', is_host_detail=True, dst_session_luid='test_dst_luid', last_timestamp=timezone.now()
        )

        cls.account_detail = detection_detail.objects.create(
            account_uid='test_src_uid', is_host_detail=False, flex_json={'target_uid': 'test_dst_uid'}, last_timestamp=timezone.now()
        )

        cls.kerberos_detail = detection_detail.objects.create(
            src_session_luid='test_src_luid',
            is_host_detail=True,
            dst_session_luid='test_dst_luid',
            flex_json={'kerberos_account_uid': 'test_dst_uid'},
            last_timestamp=timezone.now(),
        )

        cls.csv_host_detail = detection_detail.objects.create(
            src_session_luid='test_src_luid', is_host_detail=True, flex1='luid1,luid2,luid3', last_timestamp=timezone.now()
        )

    def test_host_normal_schema(self):
        schema = {'fields': {'targets': {'handler': 'detection_target_host_session', 'field': 'dst_session_luid'}}}

        dtp = dynamic_processors.DetectionTargetProcessor(schema, self.host_detail)
        entries = dtp.process_detail()
        InternalDetectionTarget.objects.bulk_create(entries)

        # Check database entry will fail if there is anything other than one entry in the table
        target_entry = InternalDetectionTarget.objects.get()
        self.assertEqual(target_entry.detection_detail_id, self.host_detail.id)
        self.assertEqual(target_entry.source_host_session_luid, 'test_src_luid')
        self.assertEqual(target_entry.target_host_session_luid, 'test_dst_luid')
        self.assertIsNone(target_entry.source_account_uid)
        self.assertIsNone(target_entry.target_account_uid)

    def test_account_normal_schema(self):
        schema = {'fields': {'targets': {'handler': 'detection_target_account', 'field': 'flex_json.target_uid'}}}

        dtp = dynamic_processors.DetectionTargetProcessor(schema, self.account_detail)
        entries = dtp.process_detail()
        InternalDetectionTarget.objects.bulk_create(entries)

        # Check database entry
        target_entry = InternalDetectionTarget.objects.get()
        self.assertEqual(target_entry.detection_detail_id, self.account_detail.id)
        self.assertEqual(target_entry.source_account_uid, 'test_src_uid')
        self.assertEqual(target_entry.target_account_uid, 'test_dst_uid')
        self.assertIsNone(target_entry.source_host_session_luid)
        self.assertIsNone(target_entry.target_host_session_luid)

    def test_csv_target_values_handling(self):
        """Test handling of comma-separated target values"""
        schema = {'fields': {'targets': {'handler': 'detection_target_host_session', 'field': 'flex1'}}}

        dtp = dynamic_processors.DetectionTargetProcessor(schema, self.csv_host_detail)
        entries = dtp.process_detail()
        InternalDetectionTarget.objects.bulk_create(entries)

        # Check all database entries
        target_entries = InternalDetectionTarget.objects.all().order_by('target_host_session_luid')

        self.assertEqual(target_entries.count(), 3)

        expected_targets = ['luid1', 'luid2', 'luid3']
        for i, entry in enumerate(target_entries):
            self.assertEqual(entry.detection_detail_id, self.csv_host_detail.id)
            self.assertEqual(entry.source_host_session_luid, 'test_src_luid')
            self.assertEqual(entry.target_host_session_luid, expected_targets[i])
            self.assertIsNone(entry.source_account_uid)
            self.assertIsNone(entry.target_account_uid)

    def test_empty_schema(self):
        schema = {}
        entries = dynamic_processors.DetectionTargetProcessor(schema, self.account_detail).process_detail()
        self.assertEqual(len(entries), 0)

    def test_two_field_schema(self):
        schema = {
            'fields': {
                'host_targets': {'handler': 'detection_target_host_session', 'field': 'dst_session_luid'},
                'account_targets': {'handler': 'detection_target_account', 'field': 'flex_json.kerberos_account_uid'},
            }
        }

        dtp = dynamic_processors.DetectionTargetProcessor(schema, self.kerberos_detail)
        entries = dtp.process_detail()
        InternalDetectionTarget.objects.bulk_create(entries)

        # Check database entries
        target_entries = InternalDetectionTarget.objects.all()

        self.assertEqual(target_entries.count(), 2)

        # Check host target entry
        host_entry = target_entries.filter(target_host_session_luid__isnull=False).first()
        self.assertEqual(host_entry.source_host_session_luid, 'test_src_luid')
        self.assertEqual(host_entry.target_host_session_luid, 'test_dst_luid')
        self.assertIsNone(host_entry.target_account_uid)
        self.assertIsNone(host_entry.source_account_uid)

        # Check account target entry
        account_entry = target_entries.filter(target_account_uid__isnull=False).first()
        self.assertEqual(account_entry.source_host_session_luid, 'test_src_luid')
        self.assertEqual(account_entry.target_account_uid, 'test_dst_uid')
        self.assertIsNone(account_entry.target_host_session_luid)
        self.assertIsNone(host_entry.source_account_uid)

    def test_incorrect_handler_field(self):
        schema = {'fields': {'targets': {'handler': 'detection_target_host_session', 'field': 'missing_field'}}}

        dtp = dynamic_processors.DetectionTargetProcessor(schema, self.host_detail)
        res = dtp.process_detail()

        self.assertEqual(res, [])
