from unittest import mock
from bin import build_learner_state
from distillation.common import DataSourceType


@mock.patch('distillation.distillation_framework.learner.learner')
@mock.patch('bin.build_learner_state.is_distillation_enabled', side_effect=lambda x: x == DataSourceType.O365)
def test_build_learner_state(mock_flag_enabled, mock_learner):
    build_learner_state.main()
    mock_learner.assert_called_once()


@mock.patch('distillation.distillation_framework.learner.learner')
@mock.patch('bin.build_learner_state.is_distillation_enabled', return_value=False)
def test_build_learner_state_disabled_flag(mock_flag_enabled, mock_learner):
    build_learner_state.main()
    mock_learner.assert_not_called()
