# Container-only requirements
uwsgi==2.0.18
git+https://github.com/VectraAI-Engineering/vui-logger.git@v1.7
# Requirements taken from pyvui.  Keep up to date!
# https://github.com/VectraAI-Engineering/artifacts/tree/main/deb/vectra-vui-py38-202007/requirements.txt
git+https://github.com/VectraAI-Engineering/django-axes.git@10fa255b7ffd63a393f597e5e7c918ca7f381021#egg=django-axes
git+https://github.com/VectraAI-Engineering/saas-opentelemetry-connector.git@v2.5#egg=opentelemetry_connector
git+https://github.com/VectraAI-Engineering/schwepp-client.git@2288aae876f050415e4435abbecd0bfcdc3a9267
git+https://github.com/VectraAI-Engineering/auth-gateway.git@076e0bbb1db36c7f833771b1af53d3311dcad8ad#egg=lc39_client&subdirectory=lc39_client
git+https://github.com/VectraAI-Engineering/auth-gateway.git@158b76e440959230cde5ebd786c2d82e280037c4#egg=vectra.authgateway[client]
git+https://github.com/VectraAI-Engineering/collection-scoring.git@202506.2.0
git+https://github.com/VectraAI-Engineering/schema_foundry.git@202506.4.0
git+https://github.com/VectraAI-Engineering/nexus.git@202506.3.0#egg=vectra-nexus[client]
git+https://github.com/VectraAI-Engineering/secrets-service.git@202503.2.0#egg=vectra-secrets-service-client
CouchDB==1.2
cryptography==3.2.1
antlr4-python3-runtime==4.7.2
argon2-cffi==23.1.0
boto3==1.29.2
cachetools==5.3.0
chevron==0.14.0
celery==5.3.6
celery-redbeat==2.2.0
django==3.2.25
django-celery-beat==2.6.0
django-celery-results==2.5.1
django-csp==3.7
django-radius==1.5.0
django-taggit==3.0.0
djangorestframework==3.14.0
dnspython==1.16.0
elasticsearch==1.6.0
flower==2.0.1
gevent==20.6.2
greenlet==0.4.16
markupsafe==2.0.1
jinja2==2.11.3
jwcrypto==1.4.2
# Specifying Kombu version since it contains the fix for Celery/Redis instability issue.
# At the time of writing, Celery v5.5.0b1 already sets its dependency for Kombu to this version,
# but we will wait for the stable release. After that, we can remove this line.
kombu==5.4.0
lz4==2.1.10
maec==********
marshmallow==3.7.1
markupsafe==2.0.1
mysqlclient==2.2.4
mysql-replication==0.21
netaddr==0.8.0
nine==0.3.4
numpy==1.19.5
onnxruntime==1.3.0
psutil==5.8.0
# pycapnp intentionally left out.
# Luckily, it isn't needed by any uwsgi codepath, only some standalone scripts
# that don't run in the container (yet)
py-radix==0.10.0
pycryptodome==3.19.1
pydantic==2.7.1
pymysql==1.1.1
python-dateutil==2.8.2
python-ldap==3.4.0
django-auth-ldap==2.2.0
pytz==2023.3
PyYAML==5.4.1
redis==5.0.3
requests==2.32.0
simplejson==3.8.1
sortedcontainers==2.1.0
stix==********
tacacs_plus==2.6
timeout-decorator==0.4.1
tldextract==2.2.2
ua-parser==0.8.0
urllib3==1.26.19
python3-saml==1.10.1
python-jose==3.4.0
aws-requests-auth==0.4.3
jmespath==0.9.5
jsonschema==4.17.3
sentry-sdk[django]==2.14.0
xmlsec==1.3.13
django-fast-paginator==1.0.4
Faker==28.4.1
