FROM node:20-alpine AS frontend_dependencies

# git required for pnpm to install packages from git repos, not included in alpine
RUN apk --no-cache add git

RUN git config --system credential.helper '!printf "protocol=https\nhost=github.com\nusername=x-access-token\npassword=$(cat /run/secrets/github_token)\n\n"'

COPY ./client/package.json /shared/client/
COPY ./client/pnpm-lock.yaml /shared/client/
WORKDIR /shared/client

# Use artifactory cache for default npm registry
RUN npm config set registry https://artifacts.vectra.io/artifactory/api/npm/npmjs

# Install Pnpm
RUN npm install --global pnpm

# Install packages (will fail if lockfile not correct or anyone tries to install packages using yarn)
RUN --mount=type=secret,id=github_token pnpm install --frozen-lockfile

FROM ubuntu:20.04
COPY ./docker/setup_apt.sh /tmp
COPY ./docker/bash/.bashrc /root/.bashrc
COPY ./docker/bash/.bashrc /home/<USER>/.bashrc

ARG aptly_repo=development
RUN /tmp/setup_apt.sh "20.04" $aptly_repo

# VUI specific build dependencies
# Also clean up apt data so this layer stays small
ENV DEBIAN_FRONTEND noninteractive

# HACK: some packages (e.g. vectra-common) may use this file to determine if they are in docker
# during installs. In buildkit, it seems that this does not exist at build time, so we create it here.
RUN touch /.dockerenv

# changing this version string will invalidate the layer cache for the next step, causing
# packages to re-download
ARG version="3.0.0"

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
      build-essential \
      net-tools \
      dpkg-dev \
      debhelper \
      sudo \
      devscripts \
      fakeroot \
      git \
      google-chrome-stable=120.0.6099.199-1 \
      gosu \
      libboost1.61-dev \
      libevent-dev \
      libssl-dev \
      mariadb-server \
      nodejs \
      openssh-client \
      python3-apt \
      python3-pip \
      python3.8-dev \
      python3-setuptools \
      libffi-dev \
      vim \
      jq \
      libcapnp-dev \
      libldap2-dev \
      libssl-dev \
      libsasl2-dev \
      libfontconfig \
      libmariadb-dev \
      libboost-filesystem1.71.0 \
      libboost-regex1.71.0 \
      python3-vectra-utils>=6.11-112-g817472f758 \
      python3-pure-utils>=8.4.0-1056-g147532465f \
      acl \
      vectra-mac-vendor-db>=8.3-956-g4412d6c38c \
      vectra-vui-py38-202007=0.21.87 \
      vectra-msgserial>=6.14-171 \
    # install packages without dependencies
    && apt-get download python3-vectra-lc39-data-replicator-client-library python3-algo-papi-utils python3-metastore-subscriber \
    && REPLICATOR_DEB=$(ls python3-vectra-lc39-data-replicator-client-library*.deb) \
    && PAPI_UTILS_DEB=$(ls python3-algo-papi-utils*.deb) \
    && METASTORE_SUBSCRIBER_DEB=$(ls python3-metastore-subscriber*.deb) \
    && dpkg -i --force-depends "${REPLICATOR_DEB}" "${PAPI_UTILS_DEB}" "${METASTORE_SUBSCRIBER_DEB}" \
    && rm -f "${REPLICATOR_DEB}" "${PAPI_UTILS_DEB}" "${METASTORE_SUBSCRIBER_DEB}" \
    && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Use artifactory cache for default npm registry
RUN npm config set registry https://artifacts.vectra.io/artifactory/api/npm/npmjs

# Install Pnpm
RUN npm install --global pnpm

RUN git config --system credential.helper '!printf "protocol=https\nhost=github.com\nusername=x-access-token\npassword=$(cat /run/secrets/github_token)\n\n"'

COPY ./docker/docker-python3.pth /usr/share/virtualenv/vectra-vui-py38-202007/lib/python3.8/site-packages/docker-python3.pth

RUN python3 -m pip install black==24.8.0 pre-commit

# Test-only dependencies
RUN --mount=type=secret,id=github_token pyvui -m pip install \
      coverage==7.6.1 \
      pylint==2.15.10 \
      pylint-django==2.5.3 \
      requests-mock==1.8.0 \
      git+https://github.com/VectraAI-Engineering/funcparserlib.git@vectra-0.3.6#egg=funcparserlib \
      mockldap==0.3.0.post1 \
      pyfakefs==5.3.2 \
      jsonschema==3.2.0 \
      pytest==8.3.4 \
      pytest-cov==5.0.0 \
      pytest-mock==3.14.0 \
      pytest-django==4.5.2 \
      freezegun==0.3.14 \
      parameterized==0.8.1 \
      debugpy==1.8.0 \
      moto==4.2.10 \
      markupsafe==2.0.1 \
      drf-spectacular==0.27.0 \
      Faker==28.4.1 \
      pyinstrument==5.0.0

RUN mkdir -p /opt/tracevector/tvui \
    mkdir -p /opt/vectra/keys \
    && ln -s /shared/debian/opt/tracevector/tvui/tv_templates /opt/tracevector/tvui/tv_templates \
    && ln -sf /shared/keys/cloud_encrypt.key /opt/vectra/keys/cloud_encrypt.key \
    && ln -sf /shared/debian/opt/vectra/keys/cloud_encrypt.key.pub /opt/vectra/keys/cloud_encrypt.key.pub

# Run this last since it will break apt state
RUN mkdir -p /etc/secrets && touch /etc/secrets/mysql_tvui_writer /etc/secrets/mysql_tvui_reader

# give vadmin passwordless sudo; needed for:
# - sql server used for syntax checking
# - pnpm install
RUN echo "vadmin  ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Install vectra build-cache
RUN virtualenv /opt/build-cache-env && \
    /opt/build-cache-env/bin/python -m pip install --extra-index-url https://artifacts.vectra.io/api/pypi/vectra-python/simple build-cache==2022.2 && \
    ln -rsf /opt/build-cache-env/bin/build-cache /usr/local/bin/build-cache

COPY --from=frontend_dependencies /shared/client/node_modules /tmp/client/node_modules

RUN curl -L https://github.com/stoplightio/prism/releases/download/v4.8.0/prism-cli-linux -o prism \
    && [ "e9f5f02ad5fc1b5a9df1f5b37f1fcd61bfb9eb23816718e0c339056e108e16bf  prism" = "$(sha256sum prism)" ] \
    && mv prism /usr/local/bin/prism \
    && chmod +x /usr/local/bin/prism

# Mark container git root as safe
RUN git config --global --add safe.directory /shared

# Add directories some tests rely on
RUN mkdir -p /data/vui/selective_pcap
RUN chmod -R 777 /data/vui/selective_pcap
