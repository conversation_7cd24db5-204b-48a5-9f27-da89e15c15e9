{"name": "vectra", "version": "0.0.0", "private": true, "description": "Security that thinks.", "repository": "", "author": "", "directories": {"doc": "doc", "test": "tests"}, "scripts": {"start": "ember serve --output-path ../debian/opt/public/static/media/client", "start-prod": "ember serve --output-path ../debian/opt/public/static/media/client --environment=production", "start-with-mirage": "WITH_MIRAGE=1 ember serve --output-path ../debian/opt/public/static/media/client", "start-local": "pnpm local-django-css && WITH_MIRAGE=1 ember serve --environment=development-local", "start-proxy": "pnpm local-django-css && ember serve --environment=development-local --proxy=http://localhost:8080", "watch": "ember build --watch --output-path ../debian/opt/public/static/media/client", "build": "ember build --output-path ../debian/opt/public/static/media/client --environment=production", "format": "pnpm format:js && pnpm format:hbs && pnpm format:js:fixtures", "format:check": "pnpm format:js:check && pnpm format:hbs:check", "format:js": "prettier --write ./*.js '{app,tests}/**/*.js'", "format:js:check": "prettier --check ./*.js '{app,tests}/**/*.js'", "format:js:fixtures": "prettier --write ./*.js 'mirage/{routes,fixtures}/**/*.js'", "format:hbs": "prettier **/*.hbs --write --parser=glimmer", "format:hbs:check": "prettier --check --parser=glimmer **/*.hbs", "lint": "pnpm lint:js && pnpm lint:hbs && pnpm run lint:css", "lint:check": "pnpm lint:js:check && pnpm lint:hbs:check && pnpm lint:css:check", "lint:check:quiet": "pnpm lint:js:check --quiet && pnpm lint:hbs:check --quiet && pnpm lint:css:check --quiet", "lint:js": "eslint --fix ./*.js app config tests", "lint:js:check": "eslint ./*.js app config tests", "lint:hbs": "ember-template-lint --fix app", "lint:hbs:check": "ember-template-lint app", "lint:css": "stylelint --fix 'app/**/*.scss'", "lint:css:check": "stylelint 'app/**/*.scss'", "test": "ember test --server", "test-no-auto-start": "WITHOUT_CHROME_AUTOSTART=1 ember test --server", "ci:test": "CI=1 ember exam --split=5 --parallel=1 --filter='!visual'", "ci:test-visual": "CI=1 CI_VISUAL_TESTS=1 ember exam --filter='visual'", "coverage:test": "COVERAGE=true ember test", "local-django-css": "sass vectra-base-styles/css/vectra.base.scss development-local/tmp/vectra.base.css", "visual:test:local": "WITHOUT_CHROME_AUTOSTART=1 LOCAL_VISUAL_TESTS=1  ember test --server  --filter='Visual'", "visual:test:ci": "start-server-and-test visual:test:start-remote http://localhost:3000/version ci:test-visual", "visual:test:approve": "ember backstop-approve", "visual:test:clear": "rm -rf ./ember-backstop/backstop_data/bitmaps_test/*", "visual:test:start-remote": "ember backstop-remote", "visual:test:close-remote": "ember backstop-stop"}, "dependencies": {"arrive": "^2.4.1", "autosize": "^4.0.2", "bowser": "^1.9.4", "clipboard": "^2.0.6", "ember-animated": "1.1.4", "ember-velcro": "^2.2.0", "filesize": "^6.1.0", "lodash": "^4.17.21", "miragejs": "^0.1.47"}, "devDependencies": {"@babel/core": "^7.20.12", "@cfworker/json-schema": "^4.1.0", "@ember/optional-features": "^2.0.0", "@ember/test-helpers": "^2.6.0", "@xmldom/xmldom": "^0.9.6", "@zestia/ember-select-box": "^13.0.7", "ace-builds": "^1.36.5", "ajv": "^8.17.1", "antlr4-c3": "^3.4.1", "antlr4ng": "^3.0.12", "babel-eslint": "10.1.0", "broccoli-asset-rev": "^3.0.0", "broccoli-funnel": "^3.0.0", "broccoli-merge-trees": "^3.0.0", "d3": "3", "d3-array": "^1.2.4", "d3-axis": "^1.0.12", "d3-drag": "^1.2.5", "d3-force": "^1.2.1", "d3-scale": "^2.2.2", "d3-selection": "^1.4.1", "d3-shape": "1", "d3-time-format": "2", "d3-transition": "^1.3.2", "d3-zoom": "^1.8.3", "echarts": "^5.4.3", "ember-ace": "^2.0.2", "ember-auto-import": "^2.10.0", "ember-backstop": "^1.5.2", "ember-changeset": "2.2.5", "ember-changeset-validations": "2.2.1", "ember-cli": "~3.28.6", "ember-cli-app-version": "^5.0.0", "ember-cli-babel": "^7.26.10", "ember-cli-code-coverage": "^1.0.3", "ember-cli-dependency-checker": "^3.3.3", "ember-cli-deprecation-workflow": "^2.1.0", "ember-cli-htmlbars": "^5.7.2", "ember-cli-inject-live-reload": "^2.1.0", "ember-cli-json-module": "^1.0.0", "ember-cli-markdown-resolver": "^0.1.3", "ember-cli-mirage": "3.0.4", "ember-cli-moment-shim": "3.8.0", "ember-cli-sass": "^11.0.1", "ember-cli-shims": "^1.2.0", "ember-cli-sri": "^2.1.1", "ember-cli-svgstore": "0.4.1", "ember-cli-terser": "^4.0.2", "ember-cli-update": "^2.0.1", "ember-component-css": "^0.8.1", "ember-composable-helpers": "4.0.0", "ember-concurrency": "^2.3.7", "ember-cookies": "0.5.2", "ember-copy": "^1.0.0", "ember-css-properties": "^0.2.0", "ember-data": "^3.28.6", "ember-data-factory-guy": "^3.9.11", "ember-data-has-many-query": "^0.3.1", "ember-event-helpers": "^0.1.1", "ember-exam": "^8.0.0", "ember-export-application-global": "^2.0.1", "ember-fetch": "^8.1.1", "ember-file-upload": "4.0.3", "ember-get-config": "^2.1.1", "ember-light-table": "^3.0.0-beta.0", "ember-link-action": "^2.0.4", "ember-load-initializers": "^2.1.2", "ember-local-storage": "^1.7.2", "ember-maybe-import-regenerator": "^0.1.6", "ember-moment": "^9.0.0", "ember-page-title": "^7.0.0", "ember-power-select": "^4.1.6", "ember-query-params": "^2.1.2", "ember-qunit": "^5.1.5", "ember-resolver": "^8.0.3", "ember-responsive": "^4.0.1", "ember-route-action-helper": "^2.0.8", "ember-sinon": "^5.0.0", "ember-sortable": "^4.0.3", "ember-source": "~3.28.12", "ember-spread": "^7.0.2", "ember-template-lint": "^5.13.0", "ember-test-selectors": "^6.0.0", "ember-test-waiters": "^2.1.1", "ember-tooltips": "^3.6.0", "ember-truth-helpers": "2.1.0", "ember-wormhole": "^0.6.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-ember": "^10.5.8", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prefer-let": "^1.0.2", "eslint-plugin-qunit": "^6.2.0", "glob": "^7.1.6", "ip-cidr": "^4.0.2", "liquid-fire": "^0.35.1", "loader.js": "^4.7.0", "markdown-it": "^14.1.0", "postcss": "^8.5.6", "prettier": "^3.1.0", "qunit": "^2.17.2", "qunit-dom": "^1.6.0", "sass": "^1.53.0", "start-server-and-test": "^1.12.5", "stylelint": "^14.1.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-scss": "^4.3.0", "tldts": "^6.1.30", "vectra-core-ui": "git+https://github.com/VectraAI-Engineering/vectra-core-ui.git#2.15.9", "webpack": "^5.96.1"}, "engines": {"node": ">= 18"}, "ember": {"edition": "octane"}, "resolutions": {"flatpickr": "4.6.9", "cross-spawn": ">=7.0.5", "json5": ">=1.0.2", "ws": ">=8.17.1", "braces": ">=3.0.3"}}