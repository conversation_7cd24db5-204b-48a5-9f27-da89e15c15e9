config
vendor
**/*.scss
# unconventional js
/blueprints/*/files/
/vendor/
app/utils/investigations-language-tools/language/*.js

# compiled output
/dist/
/tmp/

# dependencies
/node_modules/

# misc
/coverage/
!.*
.eslintcache

# unformated ember hbs
app/pods/components/cells/**
app/pods/components/data-sources-refactor/**
app/pods/components/data-sources/**
app/pods/components/detections/**
app/pods/components/modals/**
app/pods/components/network-stats/**
app/pods/components/v-sidebar/**

# ember-try
/.node_modules.ember-try/
/package.json.ember-try

jsconfig.json
