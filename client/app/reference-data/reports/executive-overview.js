export const ExecutiveOverviewRUX = {
  id: 'executive-overview',
  name: 'Executive Overview',
  slug: 'executive-overview',
  subTitle: 'Understand the effectiveness of the Vectra Platform',
  featureFlags: ['cloud_only'],
  group: 'Executive Reports',
  sections: [
    {
      name: 'Noise to Signal Reduction',
      description:
        'This funnel represents how Vectra AI transforms raw activity into actionable insights, ensuring your team focuses only on the most critical threats. It starts with detecting behaviors - individual actions that may indicate malicious activity. Using AI-Triage and user-created triage rules, the platform then correlates the behaviors into potential attack progressions, uncovering patterns that may suggest an evolving threat. Finally, using AI-driven Prioritization, the platform distills progressions into alerts - high-confidence security cases requiring investigation. This approach filters out the noise, enabling efficient resource allocation and ultimately reducing risk.',
      tooltip:
        'The Vectra platform ingests large volumes of raw logs to trigger detections in real-time. Vectra’s prioritization then uses AI to reduce noise and create a focused list of most critical entities (hosts or accounts).',
      widgets: [
        'noise-to-signal-funnel',
        'noise-to-signal-trends-rux',
        'initial-investigation-hours-saved',
        'initial-investigation-cost-savings',
        'noise-reduction',
      ],
    },
    {
      name: 'Top Hosts and Accounts',
      description:
        'This section highlights the accounts and hosts that posed the most significant potential threat and the Key Assets triggered in your environment based on the severity observed in the reporting timeframe.',
      tooltip:
        'Top Hosts and Accounts are informed by the Urgency Score associated with the observed behaviors of the hosts or accounts. The Urgency Score is a combination of the Attack Rating and the Entity Importance.',
      widgets: ['biggest-threats', 'signal-efficacy'],
    },
    {
      name: 'Trends',
      description:
        'Gain visibility into prioritized alerts, their trends, and potential adversarial behaviors across your deployment to identify anomalies, detect emerging risks, and uncover potential security gaps. By analyzing prioritized alerts by attack surface and categorizing detections by threat type, you can proactively strengthen defenses, optimize resource allocation, and enhance your overall security posture.',
      tooltip:
        'These trends can be used to understand your coverage and breaches across your attack surfaces and the kind of attacks across the killchain that have been triggered in your environment.',
      widgets: ['attack-surface-trends', 'detection-killchain-trends'],
    },
  ],
};
