export const ExecutiveOverviewQUX = {
  id: 'executive-overview-qux',
  name: 'Executive Overview',
  slug: 'executive-overview-qux',
  subTitle: 'Understand the effectiveness of the Vectra Platform',
  featureFlags: ['appliance_only'],
  group: 'Executive Reports',
  sections: [
    {
      name: 'Noise to Signal',
      description:
        'This funnel represents how Vectra AI transforms raw activity into actionable insights, ensuring your team focuses only on the most critical threats. It starts with detecting behaviors - individual actions that may indicate malicious activity. Using AI-Triage and user-created triage rules, the platform then correlates the behaviors into potential attack progressions, uncovering patterns that may suggest an evolving threat. Finally, using AI-driven Prioritization, the platform distills progressions into alerts - high-confidence security cases requiring investigation. This approach filters out the noise, enabling efficient resource allocation and ultimately reducing risk.',
      tooltip:
        "The Vectra platform ingests large volumes of network traffic and/or raw logs to trigger detections in real-time. Vectra's prioritization then uses AI to reduce noise and create a focused list of most critical hosts or accounts.",
      widgets: [
        'noise-to-signal-funnel-qux',
        'noise-to-signal-trends-qux',
        'initial-investigation-hours-saved',
        'initial-investigation-cost-savings',
        'noise-reduction-qux',
      ],
    },
    {
      name: 'Threats',
      description:
        'This section highlights the accounts and hosts that posed the most significant potential threat and the Key Assets triggered in your environment based on the severity observed in the reporting timeframe.',
      tooltip:
        'Top Hosts and Accounts are informed by the Severity (Low, Medium, High, Critical) associated with the observed behaviors of the hosts or accounts and the list of hosts and accounts designated as Key Assets.',
      widgets: ['biggest-threats-qux', 'signal-efficacy-qux', 'key-assets'],
    },
    {
      name: 'Trends',
      description:
        'Gain visibility into prioritized alerts, their trends, and potential adversarial behaviors across your deployment to identify anomalies, detect emerging risks, and uncover potential security gaps. By analyzing prioritized alerts by attack surface and categorizing detections by threat type, you can proactively strengthen defenses, optimize resource allocation, and enhance your overall security posture.',
      tooltip:
        'These trends can be used to understand your coverage and breaches across your attack surfaces and the kind of attacks across the killchain that have been triggered in your environment.',
      widgets: ['attack-surface-trends-qux', 'detection-killchain-trends'],
    },
  ],
};
