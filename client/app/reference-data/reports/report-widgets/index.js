const noiseToSignalFunnel = {
  id: 'noise-to-signal-funnel',
  type: 'funnel',
  height: 'large',
  widgetName: 'Noise to Signal Funnel',
  tooltipText:
    'The Vectra platform ingests large volumes of raw logs to trigger detections in real-time. Vectra’s prioritization then uses AI to reduce noise and create a focused list of most critical entities (hosts or accounts).',
  extra: {
    funnelRows: [
      [
        {
          title: 'Detections',
          tooltip:
            'This is the number of suspicious behaviors in your environment that may indicate potential security threats. By monitoring these behaviors, Vectra provides deeper visibility into attacker tactics.',
          iconName: 'detections',
          columnCode: 'detections',
        },
        {
          title: 'Potential Attack Progressions',
          tooltip:
            'These are the number of hosts or accounts with suspicious behavior and patterns of activity that may indicate potential an attack in progress. Instead of focusing solely on individual behaviors, Vectra identifies sequences of events that can signal an attack progression.',
          iconName: 'hosts-and-accounts',
          columnCode: 'potential_attack_progressions',
        },
        {
          title: 'Prioritized Alerts',
          tooltip:
            'Vectra prioritizes the accounts and hosts that have been identified through AI-driven analysis as suspicious behaviors requiring attention. These prioritized alerts correspond to the hosts and accounts with prioritized Urgency Scores. Instead of reacting to thousands of disconnected alerts, the Vectra platform distills potential attack progressions into a prioritized set of alerts.',
          iconName: 'prioritised',
          columnCode: 'prioritized_alerts',
        },
      ],
    ],
  },
};

const noiseToSignalFunnelQux = {
  ...noiseToSignalFunnel,
  id: 'noise-to-signal-funnel-qux',
  tooltipText:
    'The Vectra platform ingests large volumes of network traffic and/or raw logs to trigger detections in real-time. Vectra’s prioritization then uses AI to reduce noise and create a focused list of most critical hosts or accounts.',
  extra: {
    funnelRows: [
      [
        {
          title: 'Detections',
          tooltip:
            'This is the number of suspicious behaviors in your environment that may indicate potential security threats. By monitoring these behaviors, Vectra provides deeper visibility into attacker tactics.',
          iconName: 'detections',
          columnCode: 'detections',
        },
        {
          title: 'Potential Attack Progressions',
          tooltip:
            'These are the number of hosts or accounts with suspicious behavior and patterns of activity that may indicate potential an attack in progress. Instead of focusing solely on individual behaviors, Vectra identifies sequences of events that can signal an attack progression.',
          iconName: 'hosts-and-accounts',
          columnCode: 'potential_attack_progressions',
        },
        {
          title: 'Prioritized Alerts',
          tooltip:
            'Vectra prioritizes the accounts and hosts that have been identified through AI-driven analysis as suspicious behaviors requiring attention. These prioritized alerts correspond to the hosts and accounts within the critical and high quadrants. Instead of reacting to thousands of disconnected alerts, the Vectra platform distills potential attack progressions into a prioritized set of alerts.',
          iconName: 'prioritised',
          columnCode: 'prioritized_alerts',
        },
      ],
    ],
  },
};

const noiseToSignalTrendsRux = {
  id: 'noise-to-signal-trends-rux',
  widgetName: 'Noise to Signal Trends',
  tooltipText:
    'Changes in detections, potential attack progressions and prioritized alerts can vary based on many factors including changes to your environment or coverage, different level of activity in that domain (malicious or otherwise), changes in architecture, or onboarding of new systems or services.',
  type: 'line',
  xAxisKey: 'month_range',
  xAxisName: 'Last 6 Months',
  yAxisKey: 'cnt',
  yAxisName: 'Count',
  yAxisIntervalsOnBothSides: true,
  xAxisLabelStyle: 'centered',
  key: 'column_code',
  height: 'large',
  tooltipHeader: 'Noise to Signal trends',
  colorMap: {
    Detections: '#2F5F67',
    'Potential Attack Progressions': '#FFA900',
    'Prioritized Alerts': '#D82141',
  },
};

const noiseToSignalTrendsQux = {
  id: 'noise-to-signal-trends-qux',
  widgetName: 'Noise to Signal Trends',
  tooltipText:
    'Changes in detections, potential attack progressions and prioritized alerts can vary based on many factors including changes to your environment or coverage, different level of activity in that domain (malicious or otherwise), changes in architecture, or onboarding of new systems or services.',
  type: 'line',
  xAxisKey: 'month_range',
  xAxisName: 'Last 6 Months',
  yAxisKey: 'cnt',
  yAxisName: 'Count',
  yAxisIntervalsOnBothSides: true,
  xAxisLabelStyle: 'centered',
  key: 'column_code',
  height: 'large',
  tooltipHeader: 'Noise to signal trends',
  colorMap: {
    Detections: '#2F5F67',
    'Potential Attack Progressions': '#FFA900',
    'Prioritized Alerts': '#D82141',
  },
};

const initialInvestigationHoursSaved = {
  id: 'initial-investigation-hours-saved',
  widgetName: 'Estimated Time Savings',
  featureFlags: ['executive_overview_extra_widgets'],
  label: 'Estimated Hours Saved in Initial Investigation of Detections',
  tooltipText:
    'This is an estimate which assumes an average time of 5 minutes for an analyst to conduct an initial investigation into a detection. See below for the calculation used.<br/><br/> Estimated Hours Saved in Initial Investigation = Avg. Time (min.) it Takes to Analyze An Initial Detection * Number of Detections / 60',
  type: 'count',
  displayFormat: 'roundBigNumber',
  height: 'small',
  isDownloadable: false,
  width: '33.33%',
};

const initialInvestigationCostSavings = {
  id: 'initial-investigation-cost-savings',
  widgetName: 'Estimated Cost Savings',
  featureFlags: ['executive_overview_extra_widgets'],
  label: 'Estimated Cost Savings from Investigating Initial Detections',
  tooltipText:
    'This is an estimate which assumes 1.) an average time of 5 minutes for an analyst to conduct an initial investigation into a detection and 2.) an average hourly rate of $60 for a security analyst. Estimated cost savings are reported in USD. See below for the calculation used.<br/><br/> Estimated Cost Savings in Initial Investigation (USD): Estimated Hours Saved in Initial Investigations * Avg. Hourly Rate of Security Analysts',
  type: 'count',
  displayFormat: 'currency/usd',
  isDownloadable: false,
  height: 'small',
  width: '33.33%',
};

const noiseReduction = {
  id: 'noise-reduction',
  widgetName: 'Noise Reduction',
  featureFlags: ['executive_overview_extra_widgets'],
  label: 'Reduction in Detections to Prioritised Alerts',
  tooltipText:
    "This is calculated from the reduction of detections triggered to prioritized entities done by Vectra's AI triage, custom triage rules, and user-created triage rules.",
  type: 'count',
  displayFormat: 'percent',
  isDownloadable: false,
  height: 'small',
  width: '33.33%',
};

const noiseReductionQux = {
  ...noiseReduction,
  id: 'noise-reduction-qux',
  tooltipText:
    'This is calculated from the reduction of detections triggered to critical and high hosts and accounts done by Vectra’s AI triage and user-created triage rules.',
};

const biggestThreats = {
  id: 'biggest-threats',
  widgetName: 'Top Prioritized Alerts',
  tooltipText:
    'The top 10 prioritized alerts in your environment are drawn from the highest scored hosts and accounts during this reporting timeframe. This report highlights the top prioritized alerts which which were observed as the potential most urgent threats.',
  type: 'table-expandable',
  pageSize: 10,
  height: 'extra-extra-large',
  columnAttributes: {
    entity: {
      label: 'Host or Account',
      cellType: 'entity',
    },
    urgencyScore: {
      label: 'Highest Score',
      cellType: 'number',
      sortable: true,
      extra: {
        tooltipText:
          'The Highest Score is the highest Urgency Score observed for the host or account within the reporting timeframe. The Urgency Score is a combination of the Attack Rating and the Entity Importance.',
      },
    },
    groups: { label: 'Groups', cellType: 'string' },
    dataSourceTypes: { label: 'Attack Surfaces', cellType: 'string' },
    lastSeenIp: { label: 'Last Seen IP', cellType: 'string' },
    peakedOn: {
      label: 'Peaked On',
      cellType: 'timestamp',
      extra: {
        tooltipText:
          'The Peaked On value is the date and time in which the host or account was observed to have the reported Highest Score.',
      },
    },
    entityImportance: {
      label: 'Entity Importance',
      extra: {
        tooltipText:
          'Entity Importance describes the relative value of this host or account to your organization. It is determined by the host or account’s Observed Privilege, and whether it is in a group or role with high importance.',
      },
      isExpandoField: true,
    },
    attackRating: {
      label: 'Attack Rating',
      cellType: 'attack-rating',
      extra: {
        tooltipText:
          'Attack Rating describes the level of threatening behavior observed on this host or account. A high attack rating indicates malicious behavior. It is based on the attack profile, number and type of detections, and timing between triggered detections.',
      },
      isExpandoField: true,
      format: value => `${value}/10`,
    },
    attackProgression: {
      label: 'Attack Progression',
      cellType: 'progression',
      isExpandoField: true,
    },
    mitreTechniques: {
      label: 'MITRE Techniques',
      isExpandoField: true,
    },
  },
};

// We dont need these columns for QUX
const { urgencyScore, attackRating, entityImportance, ...otherColumnAttributes } = biggestThreats.columnAttributes;

const biggestThreatsQux = {
  ...biggestThreats,
  id: 'biggest-threats-qux',
  pageSize: 10,
  tooltipText:
    'The top 10 prioritized alerts in your environment are drawn from the highest severity hosts and accounts during this reporting timeframe. This report highlights the top prioritized alerts which which were observed as the potential most critical or high severity threats.',
  columnAttributes: {
    severityStr: {
      label: 'Severity',
      cellType: 'string',
      sortable: true,
      extra: {
        tooltipText:
          'The Severity (Low, Medium, High, Critical) indicates the threat and certainty of the observed behaviors of the hosts or accounts. The Severity is a combination of the Threat and Certainty Scores.',
      },
    },
    tScore: {
      label: 'Threat Score',
      cellType: 'number',
      isExpandoField: true,
      extra: {
        tooltipText:
          'Threat Score expresses the potential for harm if the security event is true (e.g. if spamming behavior or data exfiltration was occurring). Because threat is a measure of the potential for harm, it reflects worst-case scenarios.',
      },
    },
    cScore: {
      label: 'Certainty Score',
      cellType: 'number',
      isExpandoField: true,
      extra: {
        tooltipText:
          'Certainty Score reflects the probability that a given security event occurred (e.g. the probability of spamming behavior occurring, or the probability of data exfiltration occurring), given all the evidence observed so far. Certainty is based on the degree of difference between the network behavior that caused the detection and normal network behavior. As such, the certainty score of an individual detection changes over time.',
      },
    },
    ...otherColumnAttributes,
    peakedOn: {
      label: 'Peaked On',
      cellType: 'timestamp',
      extra: {
        tooltipText:
          'The Peaked On value is the date and time in which the host or account was observed to have the highest reported Severity.',
      },
    },
  },
};

const signalEfficacy = {
  id: 'signal-efficacy',
  widgetName: 'Signal Efficacy',
  featureFlags: ['executive_overview_signal_efficacy'],
  tooltipText:
    'These trends can be used to understand the outcomes of the detections associated with the prioritized alerts (or accounts and hosts) that were triggered in your environment.<br/><br/>Remediated detections signify that actions were taken to prevent the behavior from continuing. Benign detections signify expected or authorized behavior. Not Valuable detections signify behaviors which were inaccurately triggered and should not be alerted on in the future.',
  type: 'line',
  xAxisKey: 'month',
  xAxisName: 'Last 6 Months',
  yAxisKey: 'cnt',
  yAxisName: 'Number of Prioritized Detections',
  yAxisIntervalsOnBothSides: true,
  xAxisLabelStyle: 'centered',
  key: 'column_code',
  height: 'large',
  tooltipHeader: 'DETECTION OUTCOMES TRENDS',
};

const signalEfficacyQux = {
  id: 'signal-efficacy-qux',
  widgetName: 'Signal Efficacy',
  featureFlags: ['executive_overview_signal_efficacy'],
  tooltipText:
    'These trends can be used to understand the outcomes of the detections associated with the prioritized alerts (or accounts and hosts) that were triggered in your environment.<br/><br/>Remediated detections signify that actions were taken to prevent the behavior from continuing. Benign detections signify expected or authorized behavior. Not Valuable detections signify behaviors which were inaccurately triggered and should not be alerted on in the future. Uncategorized detections were detections that were not resolved within the reporting timeframe.',
  type: 'line',
  xAxisKey: 'month',
  xAxisName: 'Last 6 Months',
  yAxisKey: 'cnt',
  yAxisName: 'Number of Prioritized Alert',
  yAxisIntervalsOnBothSides: true,
  xAxisLabelStyle: 'centered',
  key: 'column_code',
  height: 'large',
  tooltipHeader: 'PRIORITIZED ALERTS',
};

const keyAssets = {
  ...biggestThreatsQux,
  id: 'key-assets',
  widgetName: 'Key Assets',
  tooltipText:
    'Hosts or accounts which have been identified as key assets will be reported on each month to review their peak Severity and their associated, detected behaviors.',
};

const attackSurfaceTrends = {
  id: 'attack-surface-trends',
  widgetName: 'Attack Surface Trends',
  tooltipText:
    'These trends show how the threat landscape is shifting and evolving across your different environments and attack surfaces over time.',
  type: 'line',
  xAxisKey: 'year_month',
  xAxisName: 'Last 6 Months',
  yAxisKey: 'cnt',
  yAxisName: 'Number of Prioritized Alerts',
  yAxisIntervalsOnBothSides: true,
  xAxisLabelStyle: 'centered',
  key: 'data_source_type',
  height: 'large',
  tooltipHeader: 'PRIORITIZED ALERTS',
  colorMap: {
    Network: '#F3009A',
    M365: '#00A8D8',
    'AWS Cloudtrail': '#FF9A79',
    Azure: '#45E688',
    'Azure AD & M365': '#8342F3',
  },
};

const attackSurfaceTrendsQux = {
  ...attackSurfaceTrends,
  id: 'attack-surface-trends-qux',
  yAxisName: 'Number of Prioritized Alerts',
  tooltipHeader: 'PRIORITIZED ALERTS',
  query: {
    name: 'attack-surface-trends',
  },
};

const detectionKillchainTrends = {
  id: 'detection-killchain-trends',
  widgetName: 'Detection Killchain Trends',
  tooltipText:
    'These trends track how the different kinds of behaviors being observed in your environment are shifting over time.',
  type: 'line',
  xAxisKey: 'year_month',
  xAxisName: 'Last 6 Months',
  yAxisKey: 'cnt',
  yAxisName: 'Number of Detections',
  yAxisIntervalsOnBothSides: true,
  xAxisLabelStyle: 'centered',
  key: 'category',
  height: 'large',
  tooltipHeader: 'DETECTIONS',
  colorMap: {
    RECONNAISSANCE: '#008E00',
    'COMMAND & CONTROL': '#FFA900',
    'LATERAL MOVEMENT': '#D82141',
    EXFILTRATION: '#8352EB',
    INFO: '#415C65',
    'BOTNET ACTIVITY': '#00A9D6',
  },
};

const ReportWidgets = {};

[
  noiseToSignalFunnel,
  noiseToSignalTrendsRux,
  noiseToSignalTrendsQux,
  noiseToSignalFunnelQux,
  initialInvestigationHoursSaved,
  initialInvestigationCostSavings,
  noiseReduction,
  noiseReductionQux,
  biggestThreats,
  biggestThreatsQux,
  signalEfficacy,
  signalEfficacyQux,
  keyAssets,
  attackSurfaceTrends,
  attackSurfaceTrendsQux,
  detectionKillchainTrends,
].forEach(widget => {
  ReportWidgets[widget.id] = widget;
});

export { ReportWidgets as default };
