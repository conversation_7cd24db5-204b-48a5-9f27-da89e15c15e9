import investigationsOperators from 'vectra/reference-data/advanced-investigations/investigations-operators';
import investigationsDefaultColumns from 'vectra/reference-data/advanced-investigations/investigations-default-columns';
import { encodeCriteriaUrlParam } from 'vectra/utils/investigations-deeplink-helpers';
import { copy } from 'ember-copy';

function formatValuesForSearchOptions(values) {
  return values?.map?.(item => {
    let formattedObject = { ...item };

    if (!item.label) {
      formattedObject.label = item.value;
    }

    if (!item.search) {
      formattedObject.search = item.label ? `${item.value} ${item.label}` : `${item.value}`;
    }

    return formattedObject;
  });
}

function operatorLabelMapping() {
  let operators = [];
  Object.values(investigationsOperators).forEach(operator => {
    operators[operator.value] = operator.label;
  });
  return operators;
}

function apiTypeToSimpleType(apiType) {
  let type = apiType;
  if (type?.startsWith('decimal')) {
    type = 'decimal';
  }
  switch (type) {
    case 'string':
      return 'string';
    case 'timestamp':
      return 'timestamp';
    case 'bool':
    case 'boolean':
      return 'boolean';
    case 'int':
    case 'bigint':
    case 'decimal':
    case 'smallint':
    case 'tinyint':
    case 'double':
      return 'number';
    case 'json':
      return 'json';
    case 'host':
      return 'host';
    case 'azure_account':
      return 'account';
    default:
      return apiType;
  }
}

function getOperatorLabelFromValue(operatorName) {
  return Object.values(investigationsOperators).filter(op => op.value === operatorName)[0].label;
}

function getOperatorFieldTypeFromValue(operatorName) {
  return Object.values(investigationsOperators).find(op => op.value === operatorName)?.valueFieldType;
}

function getColumnSchemaFromColumnName(columnName, schema) {
  return schema?.find(column => column.name === columnName);
}

function getSortColumnMapping(sortColumn, schema, defaultSortColumn) {
  let columnSchema = getColumnSchemaFromColumnName(sortColumn, schema);
  if (!columnSchema) {
    return defaultSortColumn;
  }

  let sortColumnUnderscored = sortColumn.replaceAll('.', '_');

  // Sort entity type columns by entity.name
  if (columnSchema?.type === 'host' || columnSchema?.type === 'account') {
    return `${sortColumnUnderscored}.name`;
  }
  return sortColumnUnderscored;
}

function mapColumnsToDataApiColumnRequest(columns, schema) {
  return columns
    ?.map(column => {
      let columnSchema = getColumnSchemaFromColumnName(column, schema);
      return columnSchema ? { name: columnSchema.name, as: columnSchema.underscoredId } : null;
    })
    .filter(Boolean);
}

function getFlattenedColumns(schema = {}, path = []) {
  let searchOptions = [];

  if (schema == null || typeof schema !== 'object') {
    return [];
  }

  // Is a simple Array of Strings
  if (typeof schema.type === 'string') {
    let name = [...path].join('.');
    searchOptions.push({
      name,
      type: schema.type,
      isDisplayable: false,
      isFilterable: true,
    });
  }

  // Is A Nested Array Parent
  if (Array.isArray(schema)) {
    let name = [...path].join('.');
    if (!name.includes('[]')) {
      searchOptions.push({
        name,
        type: 'json',
        isDisplayable: true,
        isFilterable: false,
      });
    }

    // Mark path segment as array and get then children
    let [value] = schema;

    path[path.length - 1] += '[]';
    let nextLevel = getFlattenedColumns(value, path);
    searchOptions = [...searchOptions, ...nextLevel];
    return searchOptions;
  }

  Object.entries(schema).forEach(([key, value]) => {
    // Handle host description mapping
    if (value?.usage?.includes('host')) {
      let name = [...path, key].join('.');
      searchOptions.push({
        ...value,
        name,
        type: 'host',
        description: value?.name?.description || '',
        isDisplayable: true,
        isFilterable: true,
      });
      return;
    }

    if (value?.usage?.includes('azure_account')) {
      let name = [...path, key].join('.');
      searchOptions.push({
        ...value,
        name,
        type: 'account',
        description: value?.name?.description || '',
        isDisplayable: true,
        isFilterable: true,
      });
      return;
    }
    // Valid Column found
    if (typeof value.type === 'string') {
      let name = [...path, key].join('.');
      searchOptions.push({
        ...value,
        name,
        isDisplayable: !name.includes('[]'),
        isFilterable: true,
      });
      return;
    }

    // Go another level deep (recursive)
    let nextLevel = getFlattenedColumns(value, [...path, key]);
    searchOptions = [...searchOptions, ...nextLevel];
  });
  return searchOptions;
}

function getOperatorsFromType(type, usage, isForDirectDataInteraction = false, cellValue) {
  switch (apiTypeToSimpleType(type)) {
    default:
    case 'timestamp':
      return [];
    case 'boolean':
      return [investigationsOperators.boolIs, investigationsOperators.boolIsNot];
    case 'number': {
      let operators = [
        investigationsOperators.intIs,
        investigationsOperators.intIsNot,
        investigationsOperators.greaterThan,
        investigationsOperators.greaterThanOrEquals,
        investigationsOperators.lessThan,
        investigationsOperators.lessThanOrEquals,
        investigationsOperators.intIsAnyOf,
        investigationsOperators.intIsNotAnyOf,
      ];
      if (!isForDirectDataInteraction) {
        operators.push(investigationsOperators.intIsBetween);
        operators.push(investigationsOperators.intIsNotBetween);
      }
      return operators;
    }
    case 'host': {
      return [
        investigationsOperators.hostIs,
        investigationsOperators.hostIsNot,
        investigationsOperators.entityStartsWith,
        investigationsOperators.entityDoesNotStartWith,
        investigationsOperators.entityContains,
        investigationsOperators.entityDoesNotContain,
        investigationsOperators.entityEndsWith,
        investigationsOperators.entityDoesNotEndWith,
        investigationsOperators.entityIsAnyOf,
        investigationsOperators.entityIsNotAnyOf,
        investigationsOperators.entityIsEmpty,
        investigationsOperators.entityIsNotEmpty,
      ];
    }
    case 'account': {
      return [
        investigationsOperators.accountIs,
        investigationsOperators.accountIsNot,
        investigationsOperators.entityStartsWith,
        investigationsOperators.entityDoesNotStartWith,
        investigationsOperators.entityContains,
        investigationsOperators.entityDoesNotContain,
        investigationsOperators.entityEndsWith,
        investigationsOperators.entityDoesNotEndWith,
        investigationsOperators.entityIsAnyOf,
        investigationsOperators.entityIsNotAnyOf,
        investigationsOperators.entityIsEmpty,
        investigationsOperators.entityIsNotEmpty,
      ];
    }
    case 'string': {
      if ((isForDirectDataInteraction && cellValue === '') || cellValue === null) {
        return [investigationsOperators.isEmpty, investigationsOperators.isNotEmpty];
      }
      if (usage?.includes('ip_address')) {
        let operators = [
          investigationsOperators.ipIs,
          investigationsOperators.ipIsNot,
          investigationsOperators.ipIsAnyOf,
          investigationsOperators.ipIsNotAnyOf,
        ];
        if (!isForDirectDataInteraction) {
          operators.push(investigationsOperators.ipIsBetween);
          operators.push(investigationsOperators.ipIsNotBetween);
          operators.push(investigationsOperators.ipIsIn);
          operators.push(investigationsOperators.ipIsNotIn);
        }
        return operators;
      }
      return [
        investigationsOperators.is,
        investigationsOperators.isNot,
        investigationsOperators.startsWith,
        investigationsOperators.doesNotStartWith,
        investigationsOperators.contains,
        investigationsOperators.doesNotContain,
        investigationsOperators.endsWith,
        investigationsOperators.doesNotEndWith,
        investigationsOperators.isAnyOf,
        investigationsOperators.isNotAnyOf,
        investigationsOperators.isEmpty,
        investigationsOperators.isNotEmpty,
      ];
    }
  }
}

function filterPillsNotInSchema(query, schema) {
  let validPills = [];
  query?.forEach(queryItem => {
    if (
      schema.some(column => {
        if (column.name === queryItem.key) {
          return getOperatorsFromType(column.type, column.usage)
            .map(operator => operator.value)
            .includes(queryItem.operator);
        }
        return false;
      })
    ) {
      validPills.push(queryItem);
    }
  });
  return validPills;
}

function getSuggestedGroupByActivity(suggested = {}, activity = '') {
  return suggested[activity] ? suggested[activity] : [];
}

function getDefaultColumnsFromSchemaAndActivity(schema = [], activity = '') {
  let columns = [];
  if (investigationsDefaultColumns[activity]?.length) {
    columns = investigationsDefaultColumns[activity].filter(column =>
      schema.find(schemaItem => schemaItem.name === column),
    );
  }

  return columns.length
    ? columns
    : schema
        .filter(field => field.isDisplayable !== false)
        .splice(0, 7)
        .map(field => field.name);
}

function orderByName(first, second) {
  return first.name.replaceAll('_', '').localeCompare(second.name.replaceAll('_', ''), 'en');
}

function _getDropdownItemFromSchema(columnSchema) {
  return {
    label: columnSchema.name,
    value: columnSchema.name,
    tooltip: columnSchema.description ?? null,
    operators: columnSchema.operators,
  };
}

function getGroupedSearchDropdownOptions(schema, dataStream, suggestedItems = {}) {
  let supportedSchemas = schema?.filter(columnSchema => columnSchema.operators?.length);

  let suggestedOptions = getSuggestedGroupByActivity(suggestedItems, dataStream);
  let suggestedSchemas = supportedSchemas
    .filter(columnSchema => suggestedOptions.find(label => label === columnSchema.name))
    .sort(orderByName)
    .map(columnSchema => _getDropdownItemFromSchema(columnSchema));
  let availableSchemas = supportedSchemas
    .filter(columnSchema => !suggestedOptions.find(label => label === columnSchema.name))
    .sort(orderByName)
    .map(columnSchema => _getDropdownItemFromSchema(columnSchema));

  if (suggestedSchemas.length) {
    return [
      {
        groupName: 'Suggested',
        options: suggestedSchemas,
      },
      {
        groupName: 'Available',
        options: availableSchemas,
      },
    ];
  } else {
    return availableSchemas;
  }
}

function applyUnderscoreIds(column) {
  // Required while Data API cannot accept '.' in column name of a query (https://jira.vectra.io/browse/DPAPI-359)
  return {
    ...column,
    underscoredId: column.name.replaceAll('.', '_'),
  };
}

function isColumnDeprecated(column) {
  return column.restrictions && column.restrictions.includes('deprecated');
}

function parseSchemaResponse(schema) {
  return getFlattenedColumns(schema, [])
    .map(column => ({
      ...column,
      operators: getOperatorsFromType(column.type, column.usage, false),
    }))
    .filter(column => !isColumnDeprecated(column))
    .map(column => applyUnderscoreIds(column));
}

function standardQuery(currentQuery, key, type, usage, operator, cellValue) {
  let parsedValue = cellValue;
  if (operator.valueFieldType === 'multivalue') {
    parsedValue = [{ value: String(cellValue) }];
  }
  if (operator.valueFieldType === 'caseOptionText') {
    parsedValue = { value: cellValue, caseSensitive: false };
  }
  // Ensure parity with dropdowns where booleans are added to url params as a string of either 'true' or 'false'.
  if (apiTypeToSimpleType(type) === 'boolean') {
    parsedValue = cellValue ? 'true' : 'false';
  }

  if (operator.valueFieldType === 'noValue') {
    parsedValue = null;
  }

  return encodeCriteriaUrlParam([
    ...currentQuery,
    {
      key,
      operator: operator.value,
      value: parsedValue,
    },
  ]);
}

function tryCombineMultivalueQuery(currentQuery, key, operator, cellValue) {
  if (operator.valueFieldType === 'multivalue') {
    let updatedFilters = copy(currentQuery, true);
    let filterMatch = false;
    updatedFilters.forEach(filter => {
      if (filter.key === key && filter.operator === operator.value) {
        filter.value.push({ value: cellValue });
        filterMatch = true;
      }
    });
    if (filterMatch) {
      return encodeCriteriaUrlParam(updatedFilters);
    }
  }
  return null;
}

function createSubmenuForQuery(searchParams, title, key, type, usage, cellValue) {
  if (!getOperatorsFromType(type, usage)) {
    return {};
  }
  return {
    title,
    items: getOperatorsFromType(type, usage, true, cellValue).map(operator => {
      let query =
        tryCombineMultivalueQuery(searchParams.query, key, operator, cellValue) ||
        standardQuery(searchParams.query, key, type, usage, operator, cellValue);
      return {
        title: operator.label,
        link: {
          route: 'investigate',
          query: {
            pageNumber: 1,
            ...searchParams,
            query,
          },
        },
      };
    }),
  };
}

function clipboardFormatter(value, type) {
  switch (type) {
    case 'json':
      if (typeof value === 'object') {
        if (Array.isArray(value) && value.length === 1) {
          if (typeof value[0] === 'object') {
            return JSON.stringify(value[0]);
          } else {
            return value[0];
          }
        } else {
          return JSON.stringify(value);
        }
      } else {
        return value;
      }
    case 'timestamp':
      return new Date(value).toISOString();
    default:
      return value;
  }
}

export {
  formatValuesForSearchOptions,
  operatorLabelMapping,
  getColumnSchemaFromColumnName,
  getSortColumnMapping,
  getDefaultColumnsFromSchemaAndActivity,
  mapColumnsToDataApiColumnRequest,
  parseSchemaResponse,
  getFlattenedColumns,
  applyUnderscoreIds,
  getOperatorsFromType,
  isColumnDeprecated,
  createSubmenuForQuery,
  getOperatorLabelFromValue,
  getOperatorFieldTypeFromValue,
  getGroupedSearchDropdownOptions,
  apiTypeToSimpleType,
  orderByName,
  filterPillsNotInSchema,
  clipboardFormatter,
};
