<form
  class={{concat "traditional " this.styleNamespace}}
  {{action (perform this.submitFormTask) on="submit"}}
>
  <VAutocompleteInput
    @onInputChange={{fn (mut this.inputValue)}}
    @placeholder={{t "Add to Group"}}
    @inputValue={{this.inputValue}}
    @getSuggestions={{this.getSuggestions}}
    @closeOnSelect={{true}}
    @afterOptionsComponent={{component
      "widgets/widget-groups/widget-group-create"
      onCreateGroup=@onCreateGroup
    }}
    @onSelect={{action "selectedGroupChanged"}}
    @subtextKey="description"
  />
  <p class="actions p-0">
    <Vc::Button::Action
      @type="secondary"
      @onClick={{action "cancel"}}
      data-test--cancel-add-to-group
      as |ba|
    >
      <ba.label>
        {{t "Cancel"}}
      </ba.label>
    </Vc::Button::Action>
    {{#if (eq @type "host")}}
      <Vc::Button::Action
        @type="secondary"
        @onClick={{perform @previewTriageChangesTask this.selectedGroup}}
        @isDisabled={{not this.selectedGroup}}
        data-test--bulk-add-to-group--preview-triage-changes
        as |ba|
      >
        <ba.label>
          {{t "Preview Triage Changes"}}
        </ba.label>
      </Vc::Button::Action>
    {{/if}}
    <Vc::Button::Action
      @type="primary"
      @onClick={{perform this.submitFormTask}}
      @isDisabled={{not this.selectedGroup}}
      data-test-modal-submit
      as |ba|
    >
      <ba.label>
        {{t "Add"}}
      </ba.label>
    </Vc::Button::Action>
  </p>
</form>