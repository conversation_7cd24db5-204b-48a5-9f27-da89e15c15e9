<ul>
  <VListButton
    class={{if (not this.canFilterJustDetections) "disabled"}}
    @disabled={{not this.canFilterJustDetections}}
    data-test--widget-bulk-triage--detection-filter-button
    {{on "click" @onFilterJustDetections}}
  >
    <strong>
      {{t "Filter just these detections"}}
    </strong>
    <p>
      {{t
        "Recommended when a single instance of the behavior is determined to be benign and should no longer be scored."
      }}
    </p>
    {{#if
      (and this.canFilterJustDetections this.anyDetectionTriagedNonSingleton)
    }}
      <label class="warning">
        {{t "Will only apply to active detections"}}
      </label>
    {{/if}}
  </VListButton>
  <VListButton
    class={{if (not this.canMarkAsFixed) "disabled"}}
    @disabled={{not this.canMarkAsFixed}}
    data-test--widget-bulk-triage--mark-as-fixed
    {{on "click" @onMarkAsFixed}}
  >
    <strong>
      {{t "Mark as Fixed"}}
    </strong>
    <p>
      {{t
        "Recommended when action has been taken to rectify the issue that caused the detection and the detection should no longer contribute to the host score."
      }}
    </p>
    {{#if
      (and this.canFilterJustDetections this.anyDetectionTriagedNonSingleton)
    }}
      <label class="warning">
        {{t "Will only apply to active detections"}}
      </label>
    {{/if}}
  </VListButton>
  <VListButton
    class={{if (not this.canUnfilter) "disabled"}}
    @disabled={{not this.canUnfilter}}
    data-test--widget-bulk-triage--unfilter
    {{on "click" @onUnfilter}}
  >
    <strong>
      {{t "Unfilter"}}
    </strong>
    {{#if (and this.canUnfilter this.anyDetectionTriagedNonSingleton)}}
      <label class="warning">
        {{t "Will only apply to detections that have been filtered by a user"}}
      </label>
    {{/if}}
  </VListButton>
  <VListButton
    class={{if (not this.canUnmarkAsFixed) "disabled"}}
    @disabled={{not this.canUnmarkAsFixed}}
    data-test--widget-bulk-triage--unmark-as-fixed
    {{on "click" @onUnmarkAsFixed}}
  >
    <strong>
      {{t "Unmark as Fixed"}}
    </strong>
    {{#if (and this.canUnmarkAsFixed this.canMarkAsFixed)}}
      <label class="warning">
        {{t "Will only apply to detections that have been marked as fixed"}}
      </label>
    {{/if}}
  </VListButton>
</ul>