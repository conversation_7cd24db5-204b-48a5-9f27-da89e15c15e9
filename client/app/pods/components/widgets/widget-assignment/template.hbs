<div class="widget-assignment_container">
  {{#if
    (or
      this.saveAssignmentTask.isRunning
      this.deleteAssignmentTask.isRunning
      this.getEntityAssignmentDetails.isRunning
    )
  }}
    <VLoading />
  {{else if this.isDetectionPage}}
    {{#if (or this.entityDetails @model.assignment)}}
      {{#let (or this.entityDetails @model.assignment) as |assignment|}}
        {{#if assignment}}
          <figure class="assignment-widget__detections">
            <p data-test-widget-assignment-details>
              <LinkTo
                @route="entities.entity"
                @models={{array (pluralize this.entityType) this.entityId}}
              >
                {{or
                  this.name
                  @model.account.name
                  @model.account.uid
                  @model.host.name
                }}
              </LinkTo>
              {{t "is assigned to"}}
              {{#if assignment.currentUser}}
                <span class="bold">
                  {{t "you"}}
                </span>
                ({{assignment.assignedTo}})
              {{else}}
                {{assignment.assignedTo}}
              {{/if}}
            </p>
            {{#if @isSingleEntityPage}}
              <figcaption>
                <p data-test-widget-assignment--assigned-by>
                  {{t "Assigned by"}}
                  {{assignment.assignedBy}},
                  {{moment-format assignment.assignedOn "MMM Do YYYY HH:mm"}}
                </p>
              </figcaption>
            {{/if}}
          </figure>
        {{/if}}
      {{/let}}
    {{else}}
      <p data-test-widget-assignment-details>
        <LinkTo
          @route="entities.entity"
          @models={{array (pluralize this.entityType) this.entityId}}
        >
          {{or
            this.name
            @model.account.name
            @model.account.uid
            @model.host.name
          }}
        </LinkTo>
        {{t "is not assigned"}}
      </p>
    {{/if}}
  {{else if (not this.editing)}}
    {{#if @showLabel}}
      <p class="widget-label" data-test-widget-assignment--label>{{t
          "Entity assignment"
        }}</p>
    {{/if}}
    <figure>
      <p
        {{did-update
          (if
            @model.assignment.currentUser
            (end-metric "lcp_entity_action_assign")
            (clear-metric "lcp_entity_action_assign")
          )
        }}
        data-test-widget-assignment--assigned-to
      >
        {{@model.assignment.assignedTo}}
      </p>
      <figcaption>
        <p data-test-widget-assignment--assigned-by>
          {{t "Assigned by"}}
          {{@model.assignment.assignedBy}},
          {{moment-format @model.assignment.assignedOn "MMM Do YYYY HH:mm"}}
        </p>
        {{#if (can "edit_assignment")}}
          <span class="actions">
            <button
              data-test-widget-assignment--edit
              id={{concat @model.modelName "-assignment-widget-edit"}}
              onclick={{action "trackEvent" "edit"}}
              class="btn subtle icon"
              {{action (mut this.editing) true}}
            >
              <Vc::Icon @iconName="actions/edit-mini" @class="icon" />
            </button>
            <button
              data-test-widget-assignment--delete
              id={{concat @model.modelName "-assignment-widget-delete"}}
              onclick={{action "trackEvent" "delete"}}
              class="btn subtle icon"
              {{action (mut this.showDeleteAssignmentModal) true}}
            >
              <Vc::Icon @iconName="actions/delete-mini" @class="icon" />
            </button>
            {{#if (not (feature-enabled "signal_efficacy_closed_as"))}}
              <button
                data-test-widget-assignment--resolve
                id={{concat @model.modelName "-assignment-widget-resolve"}}
                onclick={{action "trackEvent" "resolve"}}
                class="btn subtle icon"
                onClick={{action (mut this.showResolveModal)}}
              >
                <Vc::Icon @iconName="actions/resolve" @class="icon" />
              </button>
            {{/if}}
          </span>
        {{/if}}
      </figcaption>
    </figure>
  {{else if (can "edit_assignment")}}
    {{#if @showLabel}}
      <p class="widget-label" data-test-widget-assignment--label>
        {{t "Assign this entity"}}
      </p>
    {{/if}}
    <form>
      {{#if this.loadUsers.isIdle}}
        <Vc::Dropdown::Select
          @label="Assign User"
          @focusOnRender={{true}}
          @hasNoVisibleLabel={{true}}
          @options={{this.formattedUsers}}
          @selectedItem={{this.editAssignedUserId}}
          @onItemSelected={{action (mut this.editAssignedUserId)}}
          data-test-assignment-dropdown
        />
      {{else}}
        {{v-loading class="input"}}
      {{/if}}
      <p class="actions">
        <button
          data-test-widget-assignment--cancel
          onclick={{action "trackEvent" "cancel"}}
          class="btn secondary icon"
          id="cancel-assign-button"
          {{action "cancel"}}
        >
          <Vc::Icon @iconName="misc-nope" @class="icon" />
          {{t "Cancel"}}
        </button>
        <button
          data-test-widget-assignment--save
          onclick={{action "trackEvent" "save"}}
          class="btn primary icon"
          id="save-assign-button"
          {{action "save" this.editAssignedUser}}
        >
          <Vc::Icon @iconName="misc-yep" @class="icon" />
          {{t "Save"}}
        </button>
      </p>
    </form>
    {{#if @autoAssignments}}
      <label class="de-em">
        {{t "All active and future detections will be assigned to this user"}}
      </label>
    {{/if}}
    {{#if (and this.editAssignedUser (not this.editAssignedUser.currentUser))}}
      {{#if (not this.editAssignedUser.hasEmail)}}
        <label class="warning" data-test-widget-assignment--warning-no-email>
          {{t
            "This user won’t receive assignment notifications until they set up an email address on their profile"
          }}
        </label>
      {{else if (not this.editAssignedUser.notificationsEnabled)}}
        <label
          class="warning"
          data-test-widget-assignment--warning-notifications-disabled
        >
          {{t "This user has disabled assignment notifications"}}
        </label>
      {{/if}}
    {{/if}}
  {{else}}
    <p data-test-widget-assignment--read-only>
      {{t "No user assigned to this "}}
      {{#if (feature-enabled "unified_prioritization")}}
        {{singularize @model.entityType}}
      {{else}}
        {{if @model.modelName (singularize @model.modelName) "detection"}}
      {{/if}}
    </p>
  {{/if}}
</div>