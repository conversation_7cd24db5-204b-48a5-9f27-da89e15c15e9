<Vc::WidgetContainer
  class="single-group-page__regex-container"
  data-test-regex-widget-container
  @showBorder={{true}}
  as |wc|
>
  <wc.header>{{t "Regex"}}</wc.header>
  <wc.body>
    <div data-test-regex-body class="d-flex align-items-center">
      <Vc::Overflow @value={{@regex}} data-test-regex-rule />
      <Vc::Button::Action
        @id="single-group-page__edit-regex"
        @onClick={{@onClick}}
        @type="secondary"
        @isDisabled={{this.isButtonDisabled}}
        @accessibilityText="Edit Regex"
        @tooltip={{if this.isButtonDisabled this.tooltipText}}
        @tooltipPosition="right"
        data-test-regex-widget-button
        as |ba|
      >
        <ba.label>
          <Vc::Icon @class="subtle icon" @iconName="actions/edit" />
        </ba.label>
      </Vc::Button::Action>
    </div>
  </wc.body>
</Vc::WidgetContainer>