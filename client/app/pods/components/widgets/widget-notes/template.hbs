<VSidebar
  @title="Notes"
  @icon="actions/note"
  @canView={{and (can this.viewPerm) (gt @notes.length 0)}}
  @actionIcon="misc-plus"
  @actionText="Add"
  @onAction={{fn (mut this.showNote) true}}
  data-test--sidebar-widget-notes
>
  {{#each (take 3 (sort-by "lastEdit:desc" @notes)) as |note|}}
    {{#if (eq this.deleteId note.id)}}
      <figure class="note delete">
        <p>{{t "Are you sure you want to delete this note?"}}</p>
        <div>
          <Button @label="Cancel" @onClick={{this.onClose}} @type="secondary" />
          <Button
            @label="Delete"
            @onClick={{fn this.onDelete note}}
            @type="secondary"
          />
        </div>
      </figure>
    {{else}}
      <figure class="note">
        <div class="markdown-note">
          <Common::OverflowMultiline
            @label={{render-markdown note.note disable="image"}}
            @maxLines={{3}}
            @hasTooltip={{false}}
          />
        </div>
        <figcaption>
          <div>
            {{#if note.dateModified}}
              {{t "edited by"}}
              <strong>{{note.modifiedBy}}</strong>,
              {{moment-format note.dateModified "MMM Do YYYY HH:mm"}}<br />
            {{/if}}
            {{t "created by"}}
            <strong>{{note.createdBy}}</strong>,
            {{moment-format note.dateCreated "MMM Do YYYY HH:mm"}}
          </div>
          <div class="actions">
            {{#if (and (can this.editPerm) note.canEdit this.canEdit)}}
              <Vc::Button::IconOnly
                class="actions__button"
                @onClick={{fn (mut this.deleteId) note.id}}
                @accessibilityText={{t "delete"}}
                @iconName="actions/delete-mini"
                data-test-action-delete-note
              />
              <Vc::Button::IconOnly
                class="actions__button"
                @onClick={{fn this.onEdit note.id}}
                @accessibilityText={{t "edit"}}
                @iconName="actions/edit-mini"
                data-test-action-edit-note
              />
            {{/if}}
            <Vc::Button::IconOnly
              class="actions__button"
              @onClick={{fn this.onView note.id}}
              @accessibilityText={{t "view"}}
              @iconName="actions/view"
              data-test-action-view-note
            />
          </div>
        </figcaption>
      </figure>
    {{/if}}
  {{/each}}
  {{#if (gt @notes.length 3)}}
    <div class="footer">
      <span>
        {{t "3 of "}}{{@notes.length}}
      </span>
      <Vc::Button::Action
        class="widget-notes__secondary-button"
        @type="secondary"
        @onClick={{this.onViewAll}}
        as |ba|
      >
        <ba.label>
          {{t "View All"}}
        </ba.label>
      </Vc::Button::Action>
    </div>
  {{/if}}
</VSidebar>
{{#if this.showNotes}}
  <EmberWormhole @to="modal">
    <Modals::Modal-Notes
      @closeAction={{this.onClose}}
      @deleteAction={{this.onDelete}}
      @saveTask={{this.saveNote}}
      @type={{this.modalType}}
      @noteId={{this.notesId}}
      @notes={{@notes}}
    />
  </EmberWormhole>
{{else if (or this.showNote @showNotesAnyway this.editNote)}}
  <EmberWormhole @to="modal">
    <Modals::Modal-Note
      @changeset={{changeset this.noteToEdit}}
      @onClose={{this.onClose}}
      @saveTask={{this.saveNote}}
    />
  </EmberWormhole>
{{/if}}