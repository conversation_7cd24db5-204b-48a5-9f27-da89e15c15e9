{{#if (can "edit_groups")}}
  <VAutocompleteInput
    data-test-group-widget-autocomplete
    @onInputChange={{fn (mut this.inputValue)}}
    @inputValue={{this.inputValue}}
    @placeholder={{t "Join a group"}}
    @getSuggestions={{this.getGroupSuggestions}}
    @closeOnSelect={{true}}
    @afterOptionsComponent={{component
      "widgets/widget-groups/widget-group-create"
      onCreateGroup=(action @widgetActions.createGroup @model)
    }}
    @onSelect={{action "selectGroup"}}
  />
{{/if}}
{{#if @model.groups.length}}
  <ul class="list-values">
    {{#each (sort-by "name" this.mappedModel) as |group|}}
      <li data-test-list-item>
        <LinkTo
          @route="manage.single-group-page"
          @model={{group.id}}
          data-test-pivot-to-single-group-page-link
        >
          <Vc::Button::Action
            data-test-view-more-button
            @type="secondary"
            @id="group-widget--link"
            @accessibilityText={{group.name}}
            @class="p-0 mt-4"
            as |ba|
          >
            <ba.label>{{group.name}}</ba.label>
          </Vc::Button::Action>
        </LinkTo>
        <span class="actions">
          <a
            {{action @widgetActions.searchForGroup group}}
            class="btn subtle icon"
            role="button"
            disabled={{@widgetActions.advancedSearchMode}}
            data-test--widget-groups--action="search"
            title={{t "Search for group"}}
          >
            <Vc::Icon
              @iconName="filtered-by-rule"
              @class="table-icon filter-icon"
            />
          </a>
          {{#if (and (can "edit_groups") group.canEdit)}}
            <div id={{concat "unlink-group-" group.id}} class="d-flex">
              <button
                {{action "removeFromGroup" group}}
                class="btn subtle icon"
                disabled={{or
                  (not group.canUnlink)
                  (not (eq group.memberType "static"))
                }}
                title={{t "Unjoin group"}}
                data-test-unlink-button
              >
                <Vc::Icon @iconName="actions/unlink-mini" @class="icon" />
              </button>
            </div>

            {{#if
              (or (not group.canUnlink) (not (eq group.memberType "static")))
            }}
              <Vc::Tooltip
                @position="right"
                @hideIcon={{true}}
                @attachedElementId="body"
                @targetId={{concat "unlink-group-" group.id}}
                data-test-groups-unlink-tooltip
                @attachedElementId={{concat "#unlink-group-" group.id}}
              >
                {{#if (eq group.memberType "dynamic")}}
                  {{t "This entity is part of a regex-based group. A regex"}}
                  <br />
                  {{t "update is needed to exclude this entity from the"}}
                  <br />
                  {{t "group."}}
                {{else if (eq group.memberType "active_directory")}}
                  {{t "This entity is part of an AD group defined by an AD"}}
                  <br />
                  {{t "rule. To exclude it, you must update the group"}}
                  <br />
                  {{t "membership in Microsoft Active Directory."}}
                {{else}}
                  {{#if group.cognitoManaged}}
                    {{t "This entity is part of a dynamic group."}}
                  {{/if}}
                  {{#if (eq group.exactMatch false)}}
                    {{t
                      (concat
                        "This entity matches a member of this wildcard group."
                      )
                    }}
                  {{/if}}
                  {{#if group.notAllowedType}}
                    {{t
                      (concat
                        "This entity matches a member of this "
                        (capitalize group.groupType)
                        " group"
                      )
                    }}
                  {{/if}}
                  <br />
                  {{t
                    "A rule change is needed to exclude this entity from the group."
                  }}
                {{/if}}
              </Vc::Tooltip>
            {{/if}}
          {{/if}}
        </span>
      </li>
      {{#if (feature-enabled "unified_prioritization")}}
        <span data-test-importance-subtext class="importance">
          <span>{{t "Importance: "}}</span>
          <b>{{group.importance}}</b>
        </span>
      {{/if}}
    {{/each}}
  </ul>
{{else if (not (can "edit_groups"))}}
  <p data-test--widget-groups--read-only>{{t "No groups on this "}}{{singularize
      @model.modelName
    }}</p>
{{/if}}
{{#if this.loading}}
  {{v-loading class="full"}}
{{/if}}