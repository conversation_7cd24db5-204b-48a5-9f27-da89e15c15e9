import Component from '@glimmer/component';
import { inject as service } from '@ember/service';
import { action } from '@ember/object';
import { clipboardFormatter } from 'vectra/utils/investigations-data-api-operators';

export default class extends Component {
  @service table;
  @service toast;
  @service clipboard;

  get columnsFormattedForTable() {
    let mappedColumns =
      this.args.columns?.map?.(({ label, name, type }) => ({
        label: label ?? name,
        valuePath: name,
        fullValuePath: name,
        cellComponent: this.getCellComponentFromColumnType(type),
        dataType: type,
        canOverflow: true,
        width: '200px',
        sorted: false,
        sortable: false,
        resizable: true,
        extra: {
          json: { formatted: true, lineClamp: 5 },
          directDataInteraction: {
            isEnabled: cellValue => cellValue !== '',
            getDirectDataMenuItems: cellValue => {
              return [
                {
                  title: 'Copy to clipboard',
                  action: () => {
                    this.copyToClipboard(cellValue, type);
                  },
                  icon: 'actions/copy',
                },
              ];
            },
          },
        },
      })) ?? [];

    if (mappedColumns.length) {
      mappedColumns = this.table.applyTimezoneToColumnHeaders(mappedColumns);
      let dateFormat = 'MMM Do YYYY HH:mm:ss.SSS';
      mappedColumns = this.table.applyDateFormatToDateColumns(mappedColumns, dateFormat);
    }
    return mappedColumns;
  }

  get isExpandableTable() {
    return this.columnsFormattedForTable.filter(item => item.dataType === 'json').length;
  }

  getCellComponentFromColumnType(type) {
    switch (type) {
      case 'jsonHostLink':
        return 'cells/cell-host-json-link';
      case 'json':
        return 'vc/table/cells/json/cell-json';
      case 'number':
        return 'vc/table/cells/number/cell-number';
      case 'timestamp':
        return 'vc/table/cells/date/cell-datetime';
      case 'boolean':
        return 'vc/table/cells/boolean/cell-boolean-string';
      default:
        return 'vc/table/cells/text/cell-text';
    }
  }

  @action
  async copyToClipboard(value, type) {
    try {
      await this.clipboard.writeText(clipboardFormatter(value, type));
      this.toast.success('Copied to clipboard');
    } catch {
      this.toast.error('Copy to clipboard failed');
    }
  }
}
