<div
  class={{concat this.styleNamespace " investigate--advanced__source-panel"}}
  data-test--investigate-source-panel
  {{did-update this.onUpdateQuery @query}}
>
  <div
    class="{{concat
        'investigate__resizeable'
        (if this.isDarkMode ' dark-mode' ' light-mode')
      }}"
  >
    <EmberAce
      id={{this.editorId}}
      class={{concat
        "ember-ace"
        (if this.isDarkMode " ace-dark-mode" " ace-light-mode")
      }}
      @value={{this.advancedInvestigationsService.textEditorValue}}
      @update={{this.onQueryChange}}
      @ready={{this.onEditorCreated}}
      @theme="ace/theme/sqlserver"
      @mode="ace/mode/sql"
      @minLines="40"
      @maxLines="200"
      @showGutter={{true}}
      @showPrintMargin={{false}}
      @highlightActiveLine={{false}}
      @overlays={{@annotations}}
      @enableDefaultAutocompletion={{false}}
      @suggestCompletions={{this.getSuggestions}}
      @enableLiveAutocompletion={{true}}
      @useWrapMode={{true}}
      {{on "keydown" (fn (mut this.isDatepickerOpen) false)}}
      {{did-insert this.onEditorInsert}}
      data-test--investigate-textbox
      as |editor|
    >
      <editor.completion-tooltip as |suggestion|>
        {{suggestion.caption}}
      </editor.completion-tooltip>
    </EmberAce>
    <div
      class={{concat
        "investigate__editor-action-buttons d-flex align-items-center"
        (if this.isDarkMode " actions-dark-mode" "")
      }}
    >
      <Vc::Button::Action
        @type="secondary"
        @onClick={{@onBasicLinkClick}}
        data-test-basic-link
        as |ba|
      >
        <ba.label>
          {{t "Basic"}}
        </ba.label>
      </Vc::Button::Action>
      <Vc::InAppHelp
        @attachedElementId=".investigate"
        @header="SQL Search Documentation"
        @content={{hash
          description="Help documentation for SQL search"
          links=(array
            (hash
              icon="links/article"
              link="https://support.vectra.ai/s/article/KB-VS-1864"
              text="Syntax & Documentation"
              type="Documentation"
            )
            (hash
              icon="links/article"
              link="https://vectranetworks.my.salesforce.com/sfc/p/#i0000000HOyr/a/6S000001tkLd/X4Dhe9vQmtmy191FL9ptS2oh_GnaOagRvqblkmuibmg"
              text="Network Metadata"
              type="Documentation"
            )
            (hash
              icon="links/article"
              link="https://vectranetworks.my.salesforce.com/sfc/p/#i0000000HOyr/a/6S000000YcId/iEiysUxKGHADVLWQVXXaB15IluMo8Po6ADhPD4_IyYo"
              text="AWS Metadata"
              type="Documentation"
            )
            (hash
              icon="links/article"
              link="https://vectranetworks.my.salesforce.com/sfc/p/#i0000000HOyr/a/6S000000WusP/JRn1jOopDUqyeIbXC4SeDHAUMeQh4hrSTkP8VVkkak4"
              text="Azure AD and M365 Metadata"
              type="Documentation"
            )
          )
        }}
        @onLinkClick={{this.onLinkClicked}}
        @onOpen={{fn (mut this.showPopout) true}}
        @onClose={{fn (mut this.showPopout) false}}
        @showPopout={{this.showPopout}}
        @position="bottom-end"
      >
        <div class="section-header ps-0 pt-0">
          {{t "Shortcuts:"}}
        </div>
        <div class="investigate__help-shortcuts ps-5">
          <ul>
            <li> {{t "Run Query: cmd/ctrl + Enter"}} </li>
            <li> {{t "Open Date-picker: cmd/ctrl + D"}} </li>
          </ul>
        </div>
      </Vc::InAppHelp>
      <Vc::Button::Action
        @type="secondary"
        @onClick={{toggle "isDarkMode" this}}
        data-test-basic-link
        as |ba|
      >
        <ba.label>
          {{! IT'S JUST A TEMPORARY PLACEHOLDER FOR ICONS THAT ARE YET TO BE DESIGNED }}
          {{#if this.isDarkMode}}
            <AdvancedInvestigations::AdvancedSearch::SourcePanel::Icons::Sun />
          {{else}}
            <AdvancedInvestigations::AdvancedSearch::SourcePanel::Icons::Luna />
          {{/if}}
        </ba.label>
      </Vc::Button::Action>
    </div>
  </div>
  <VResizer
    class="investigate__resizer"
    @target=".investigate__resizeable"
    @baseHeight={{300}}
    @minHeight={{200}}
    @offsetTop={{200}}
  />
  <div class="investigate__search-button w-100 px-6 py-3 mt-5 ms-0">
    <Vc::Button::Action
      @onClick={{this.onClick}}
      @isDisabled={{this.isSearchDisabled}}
      data-test-search-button
      as |ba|
    >
      <ba.label>
        {{t "Search"}}
      </ba.label>
    </Vc::Button::Action>
    {{#if this.isDatepickerOpen}}
      <Vc::Popover
        class="investigate--advanced__source-panel_popover"
        @popoverId={{this.popoverId}}
        @attachedElementId={{this.cursorId}}
        @position={{this.popoverPosition}}
        @isArrowDisplayed={{false}}
        @isShown={{true}}
        @onClose={{fn (mut this.isDatepickerOpen) false}}
      >
        <Vc::Datepicker
          @defaultDates={{this.defaultDates}}
          @maxDate={{this.newestSelectableDate}}
          @relativeDateRange={{this.relativeDateRange}}
          @positionLeft={{true}}
          @onApply={{queue
            this.addDateToQuery
            (fn (mut this.isDatepickerOpen) false)
          }}
          @dateFormat="DD MMM YYYY HH:mm"
          @hideAppliedDates={{true}}
          @isOpen={{this.isDatepickerOpen}}
          @timeZone={{(setting-timezone)}}
          @hasPresets={{true}}
          @presets={{this.datepickerPresets}}
          @onCancel={{fn (mut this.isDatepickerOpen) false}}
        />
      </Vc::Popover>
    {{/if}}
  </div>
</div>