<div
  class={{concat
    this.styleNamespace
    " w-100 d-flex align-items-center justify-content-between"
  }}
>
  <div class="d-flex align-items-center query-shortcuts__edit-name-container">
    {{#if this.showEditNameField}}
      <div
        class="d-flex query-shortcuts__edit-name-form"
        data-test-edit-shortcut-name-form
        {{on-click-outside this.toggleEditName}}
      >
        <Vc::Input::Text
          @value={{this.advancedInvestigationsService.pageTitle}}
          @type="text"
          @placeholder="Query name"
          @maxLength={{80}}
          @hideLabel={{true}}
          @hasError={{this.shortcutNameErrorMessage}}
          @errorMessage={{this.shortcutNameErrorMessage}}
          @onChange={{this.onChangeQueryName}}
          @onKeyUp={{this.onKeyUp}}
          @isDisabled={{this.editQueryNameTask.isRunning}}
          data-test-edit-shortcut-name-text-input
          fit-content
        />
        <Vc::Button::RoundedIcon
          class="mx-4 mt-4"
          @backgroundColor="blue"
          @iconName="misc-yep"
          @accessibilityText="Save"
          @isDisabled={{this.editQueryNameTask.isRunning}}
          @onClick={{perform this.editQueryNameTask}}
          @isDisabled={{this.isEditShortcutNameDisabled}}
          data-test-edit-shortcut-name-save-button
        />
        <Vc::Button::RoundedIcon
          class="mx-0 mt-4"
          @iconName="misc-close"
          @accessibilityText="Cancel"
          @isDisabled={{this.editQueryNameTask.isRunning}}
          @onClick={{this.toggleEditName}}
          @isDisabled={{this.isEditShortcutNameDisabled}}
          data-test-edit-shortcut-name-cancel-button
        />
      </div>
    {{else}}
      {{#if this.advancedInvestigationsService.pageTitle}}
        <div
          id="query-shortcut-name"
          class="query-shortcuts__query-name-wrapper"
        >
          <div class="query-shortcuts__query-name">
            <Vc::Overflow
              class="vc-typ-header-big"
              @value={{this.advancedInvestigationsService.pageTitle}}
              @targetId="query-shortcut-name"
              data-test-shortcut-name-page-title
            />
          </div>
        </div>
        <div
          id="investigate__edit-query-name-button"
          class="query-shortcuts__edit-name-form-button"
        >
          <Vc::Button::Action
            class="px-4 pt-3"
            @type="secondary"
            @iconName="actions/edit"
            @iconSize="md"
            class="icons"
            @accessibilityText="Edit Query Name"
            @onClick={{this.toggleEditName}}
            @isDisabled={{this.isEditShortcutNameDisabled}}
            data-test-edit-shortcut-name-button
          />
        </div>
        {{#if this.isEditShortcutNameDisabled}}
          <Vc::Tooltip
            @position="bottom"
            @hideIcon={{true}}
            @size="medium"
            @targetId="investigate__edit-query-name-button"
            @attachedElementId="body"
          >
            <div class="p-4">
              {{t "Query name can only be changed by its creator"}}
            </div>
          </Vc::Tooltip>
        {{/if}}
        {{#if this.advancedInvestigationsService.hasShortcutChanges}}
          <div
            class="d-flex align-items-center"
            data-test-edited-shortcut-badge
          >
            <Vc::Badge
              @label="EDITED"
              @theme={{hash
                background="var(--color-input-border)"
                color="var(--color-secondary-active)"
              }}
            />
          </div>
        {{/if}}
      {{/if}}
    {{/if}}
  </div>
  <div class="flex-grow-1 d-flex align-items-center justify-content-end">
    {{#if this.advancedInvestigationsService.pageTitle}}
      <div
        id="investigate__toggle-privacy-query-button"
        class="d-flex align-items-center justify-content-end query-shortcuts__update-privacy-button pe-4 me-4"
      >
        <Vc::Button::Action
          @type="secondary"
          @accessibilityText="Lock/Unlock"
          @onClick={{perform this.togglePrivacySettings}}
          @isDisabled={{this.isPrivacyToggleButtonDisabled}}
          data-test-toggle-shortcut-privacy-settings
          as |ba|
        >
          <ba.label>
            {{#if this.isPrivateQuery}}
              <Vc::Icon
                @class="icons query-shortcuts__current-color-icon"
                @iconName="actions/locked"
              />
              {{t "Only me"}}
            {{else}}
              <Vc::Icon @class="icons" @iconName="actions/unlock" />
              {{t "All users"}}
            {{/if}}
          </ba.label>
        </Vc::Button::Action>
        {{#if this.isPrivacyToggleButtonDisabled}}
          <Vc::Tooltip
            @position="bottom"
            @hideIcon={{true}}
            @size="medium"
            @targetId="investigate__toggle-privacy-query-button"
            @attachedElementId="body"
          >
            <div class="p-4">
              {{t "Privacy settings can only be changed by its creator"}}
            </div>
          </Vc::Tooltip>
        {{/if}}
      </div>
    {{/if}}
    {{#if
      (or
        (can "edit_advanced_investigations_shortcuts")
        (can "edit_all_advanced_investigations_shortcuts")
      )
    }}
      <Vc::Button::Action
        @type="secondary"
        @accessibilityText="New"
        @onClick={{this.onNewSearch}}
        data-test-new-investigation
        as |ba|
      >
        <ba.label>
          {{t "New"}}
        </ba.label>
      </Vc::Button::Action>
    {{/if}}
    {{#if (can "view_advanced_investigations_shortcuts")}}
      <Vc::Button::Action
        @type="secondary"
        @accessibilityText="Open"
        @onClick={{fn
          (mut this.advancedInvestigationsService.showQueriesShortcutList)
          true
        }}
        data-test-shortcuts-open-queries
        as |ba|
      >
        <ba.label>
          {{t "Open"}}
        </ba.label>
      </Vc::Button::Action>
    {{/if}}
    <span id="investigate__save-query-button">
      {{#if
        (and
          this.advancedInvestigationsService.originalShortcut
          (or
            (can "edit_advanced_investigations_shortcuts")
            (can "edit_all_advanced_investigations_shortcuts")
          )
        )
      }}
        <Vc::Button::Action
          class="d-flex align-items-center pe-0 query-shortcuts__save-or-update-button"
          @type="secondary"
          @accessibilityText="Save Query"
          @isDisabled={{or
            this.isSaveButtonDisabled
            this.validateAndUpdateShortcutTask.isRunning
          }}
          @onClick={{fn (mut this.showSaveMenu) (not this.showSaveMenu)}}
          data-test-save-or-update-shortcut
          as |ba|
        >
          <ba.label>
            {{t "Save Query"}}
          </ba.label>
          {{#if
            (and
              this.showSaveMenu
              (not this.validateAndUpdateShortcutTask.isRunning)
            )
          }}
            <Vc::CascadingMenu
              @items={{this.saveMenuItems}}
              @onOpen={{fn (mut this.showSaveMenu) true}}
              @onClose={{fn (mut this.showSaveMenu) false}}
              @icon="misc-norgie"
              @showMenu={{this.showSaveMenu}}
            />
          {{else}}
            <Vc::Icon
              @iconName="misc-norgie"
              @class="icons position-relative"
            />
          {{/if}}
        </Vc::Button::Action>
      {{else if
        (or
          (can "edit_advanced_investigations_shortcuts")
          (can "edit_all_advanced_investigations_shortcuts")
        )
      }}
        <Vc::Button::Action
          class="pe-0 query-shortcuts__save-button"
          @type="secondary"
          @accessibilityText="Save Query"
          @onClick={{fn
            (mut this.advancedInvestigationsService.showSaveQueryShortCutModal)
            true
          }}
          @isDisabled={{this.isSaveButtonDisabled}}
          data-test-save-shortcut
          as |ba|
        >
          <ba.label>
            {{t "Save Query"}}
          </ba.label>
        </Vc::Button::Action>
      {{/if}}
    </span>
    {{#if this.isSaveButtonDisabled}}
      <Vc::Tooltip
        @position="bottom-end"
        @hideIcon={{true}}
        @size="medium"
        @targetId="investigate__save-query-button"
        @attachedElementId="body"
      >
        <div class="p-4">
          {{t
            "A Data Source, Data Stream, and Connector must be selected in order to save a query"
          }}
        </div>
      </Vc::Tooltip>
    {{/if}}
  </div>
</div>
<AdvancedInvestigations::QueryShortcuts::List
  @dataSourceOptions={{@dataSourceOptions}}
/>
{{#if this.advancedInvestigationsService.showSaveQueryShortCutModal}}
  <AdvancedInvestigations::QueryShortcuts::SaveShortcutModal />
{{/if}}