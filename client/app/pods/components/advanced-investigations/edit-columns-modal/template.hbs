<Vc::Modal
  @isOpen={{true}}
  @onClose={{@onClose}}
  @type="workflow"
  @size="small"
  @class="investigate__modal--edit-columns pt-2 pb-2"
  @secondaryText={{if @changeset.changes.length "Cancel" "Close"}}
  @secondaryAction={{@onClose}}
  @primaryText="Save"
  @primaryAction={{this.onSaveClick}}
  @primaryIsLoading={{this.onSaveClick.isRunning}}
  {{did-insert this.onInsert}}
  data-test-investigate-edit-columns
  as |md|
>
  <md.header>
    {{t "Edit Columns"}}
  </md.header>
  <md.subheader>
    <div class="investigate__edit-columns__search-input">
      <Vc::Input::Text
        @placeholder="Search"
        @onChange={{this.onSearchChange}}
        @value={{this.searchValue}}
        fit-content
        {{did-insert (focusOnInsert) ".vc-input__input"}}
      />
    </div>
  </md.subheader>
  <md.body>
    <Vc::SubtleContainer
      class="pt-4"
      data-test-investigate-edit-columns-selected-container
      as |sc|
    >
      <sc.header>
        <Vc::Header::SubHeader
          class="investigate__modal--edit-columns__selected-columns-header"
        >
          <div>
            {{t "Selected Columns"}}
          </div>
          <Vc::Button::Action
            @onClick={{this.resetColumnsToDefault}}
            @type="secondary"
            @accessibilityText="Reset columns to default"
            data-test-reset-columns-button
          >
            {{t "Reset to default"}}
          </Vc::Button::Action>
        </Vc::Header::SubHeader>
      </sc.header>
      <sc.body>
        <div class="pt-3">
          {{#if (gte this.selectedColumns.length 1)}}
            {{#if (gte this.filteredSelectedColumns.length 1)}}
              {{#each this.filteredSelectedColumns as |column|}}
                <AdvancedInvestigations::EditColumnsModal::ColumnSelectionItem
                  @isColumnLimitReached={{this.isColumnLimitReached}}
                  @column={{column}}
                  @onChangeCheckbox={{this.onChangeCheckbox}}
                />
              {{/each}}
            {{else}}
              <Vc::Text
                data-test-investigate-edit-columns-selected-none-match
                class="investigate__modal--edit-columns__centered-text"
              >
                {{t "No results match your search"}}
              </Vc::Text>
            {{/if}}
          {{else}}
            <Vc::Text
              data-test-investigate-edit-columns-none-selected
              class="investigate__modal--edit-columns__centered-text"
            >
              {{t
                "No columns are selected. Select columns to start seeing results."
              }}
            </Vc::Text>
          {{/if}}
        </div>
      </sc.body>
    </Vc::SubtleContainer>

    {{#if this.availableColumns}}
      <Vc::SubtleContainer
        class="pt-5"
        data-test-investigate-edit-columns-available-container
        as |sc|
      >
        <sc.header>
          <Vc::Header::SubHeader>
            {{t "Available Columns"}}
          </Vc::Header::SubHeader>
        </sc.header>
        <sc.body>
          <div class="pt-3">
            {{#if (gte this.filteredAvailableColumns.length 1)}}
              {{#each this.filteredAvailableColumns as |column|}}
                <AdvancedInvestigations::EditColumnsModal::ColumnSelectionItem
                  @isColumnLimitReached={{this.isColumnLimitReached}}
                  @column={{column}}
                  @onChangeCheckbox={{this.onChangeCheckbox}}
                />
              {{/each}}
            {{else}}
              <Vc::Text
                data-test-investigate-edit-columns-available-none-match
                class="investigate__modal--edit-columns__centered-text"
              >
                {{t "No results match your search"}}
              </Vc::Text>
            {{/if}}
          </div>
        </sc.body>
      </Vc::SubtleContainer>
    {{/if}}
  </md.body>
</Vc::Modal>