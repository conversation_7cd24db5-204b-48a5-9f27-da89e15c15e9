<Vc::Modal
  @isOpen={{true}}
  @onClose={{@onClose}}
  @type="workflow"
  @size="small"
  @class="investigate__modal--search-mode-warning"
  @secondaryText={{"Cancel"}}
  @secondaryAction={{@onClose}}
  @primaryText={{"Switch to basic"}}
  @primaryAction={{@onConfirm}}
  @primaryIsLoading={{@onConfirm.isRunning}}
  data-test--investigate-search-mode-warning-modal
  as |md|
>
  <md.header>
    {{t "Return to basic query"}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      @type="warning"
      @primaryText={{"If you return to basic query we won't be able to keep your advanced search filters"}}
    />
  </md.subheader>
</Vc::Modal>