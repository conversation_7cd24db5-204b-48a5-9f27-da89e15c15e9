import Component from '@glimmer/component';
import podNames from 'ember-component-css/pod-names';
import { action } from '@ember/object';
import { createSubmenuForQuery, clipboardFormatter } from 'vectra/utils/investigations-data-api-operators';
import { inject as service } from '@ember/service';

export default class extends Component {
  @service clipboard;
  @service toast;
  @service collection;

  get styleNamespace() {
    return podNames['advanced-investigations/expandable-row'];
  }

  get complexData() {
    return (this.args.columns ?? [])
      .filter(({ dataType }) => dataType === 'json')
      .map(column => {
        const transformedColumn = {
          ...column,
          extra: {
            directDataInteraction: {
              getDirectDataMenuItems: (cellValue, columnConfiguration) => {
                let filteredSchema = (this.args.schemas ?? []).filter(
                  schema => schema.name === columnConfiguration.complexDataSettings.schemaPath,
                );
                let actionItems = [];
                if (filteredSchema.length > 0 && filteredSchema[0].isFilterable) {
                  let currentSchema = filteredSchema[0];

                  let addToQueryOptions = {
                    ...createSubmenuForQuery(
                      this.args.currentSearch,
                      'Add to query',
                      currentSchema.name,
                      currentSchema.type,
                      currentSchema.usage,
                      `${cellValue}`,
                    ),
                  };
                  if (addToQueryOptions?.items?.length > 0) actionItems.push(addToQueryOptions);

                  let newQueryOptions = {
                    ...createSubmenuForQuery(
                      {
                        ...this.args.currentSearch,
                        query: [],
                      },
                      'New query',
                      currentSchema.name,
                      currentSchema.type,
                      currentSchema.usage,
                      `${cellValue}`,
                    ),
                  };
                  if (newQueryOptions?.items?.length > 0) actionItems.push(newQueryOptions);
                }

                actionItems.push({
                  title: 'Copy to clipboard',
                  action: () => {
                    this.copyToClipboard(cellValue, column.dataType);
                  },
                  icon: 'actions/copy',
                });

                return actionItems;
              },
              telemetryAction: (_, columnConfiguration, itemClicked) => {
                this.collection.pendoTrack('investigationsDirectData', {
                  key: columnConfiguration?.complexDataSettings?.schemaPath,
                  operator: itemClicked?.title,
                  section: 'complexColumns',
                });
              },
              isEnabled: () => {
                return true;
              },
            },
          },
        };
        return {
          column: transformedColumn,
          data: { [column.schemaName || column.valuePath]: this.args.data?.content[column.valuePath] },
        };
      });
  }

  @action
  async copyToClipboard(value, type) {
    try {
      await this.clipboard.writeText(clipboardFormatter(value, type));
      this.toast.success('Copied to clipboard');
    } catch {
      this.toast.error('Copy to clipboard failed');
    }
  }
}
