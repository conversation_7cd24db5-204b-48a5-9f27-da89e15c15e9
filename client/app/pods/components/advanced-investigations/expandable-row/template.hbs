<div
  class={{concat this.styleNamespace " py-6 px-8"}}
  data-test--investigate-expandable-row
>
  <Vc::Text::Bold>{{t "Complex columns:"}}</Vc::Text::Bold>
  <div
    class="complex-data__wrapper"
    data-test--investigate-expandable-row-wrapper
  >
    {{#each this.complexData as |group|}}
      <Vc::StackView
        @data={{group.data}}
        @column={{group.column}}
        data-test--investigate-expandable-row-data
      />
    {{/each}}
  </div>
</div>