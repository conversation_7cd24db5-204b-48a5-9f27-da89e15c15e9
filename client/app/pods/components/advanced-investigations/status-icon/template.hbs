<div>
  {{#if @status}}
    <div class="investigate__status-icon" data-test-investigate-status-icon>
      <div id={{this.infoIconId}} class="investigate__status-svg">
        {{#if (and (eq @status "SUCCESS") (eq @numberOfResults "10000"))}}
          <AdvancedInvestigations::StatusIcon::WarnInfo />
          <Vc::Tooltip @position="vertical" @targetId={{this.infoIconId}}>
            {{t
              "Showing the first 10,000 results. Refine your query to narrow down the result set."
            }}
          </Vc::Tooltip>
        {{else if (eq @status "SUCCESS")}}
          <Vc::Icon @iconName="status/checkmark" />
        {{else if (eq @status "RUNNING")}}
          <Vc::Icon
            @iconName="status/loading"
            @class="investigate__status-svg__spinning"
          />
        {{else if (eq @status "FAILED")}}
          <Vc::Icon @iconName="status/alert" />
        {{/if}}
      </div>
    </div>
  {{/if}}
</div>