<div
  ...attributes
  class="investigate__empty-state d-flex flex-column align-items-center justify-content-start my-7"
  data-test--investigate-error-page
>
  {{#if (eq @imageCode "bus-not-coming")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageBusNotComing />
  {{else if (eq @imageCode "no-results-timeframe")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageNoResultsTimeframe />
  {{else if (eq @imageCode "bus-broken")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageBusBroken />
  {{else if (eq @imageCode "no-bus-coming")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageNoBusComing />
  {{else if (eq @imageCode "bus-stop-waiting")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageBusStopWaiting />
  {{else if (eq @imageCode "no-columns-selected")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageNoColumnsSelected />
  {{else if (eq @imageCode "no-results")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageNoResults />
  {{else if (eq @imageCode "no-table-found")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageNoTableFound />
  {{else if (eq @imageCode "empty-folder")}}
    <AdvancedInvestigations::ErrorInfoPage::ImageEmptyFolder />
  {{/if}}
  {{#if @title}}
    <Vc::Header class="mb-5" data-test--investigate-error-title>
      {{@title}}
    </Vc::Header>
  {{/if}}
  <div class="investigate__empty-state__message">
    {{#if @message}}
      <Vc::Text class="mb-5" data-test--investigate-error-message>
        {{@message}}
      </Vc::Text>

    {{else if @messageId}}
      <div class="mb-5" data-test--investigate-error-message-by-id>
        {{#if (eq @messageId "network-table-not-found")}}
          <p>{{t "No data has been seen or received recently."}}</p>
          <p>{{t
              "Possible reasons include: you are not producing any data of this type, the sensor is not set up correctly or we are experiencing an outage."
            }}</p>
          <br />
          <a
            href="https://support.vectra.ai/s/"
            target="_blank"
            class="mt-5"
            rel="noopener noreferrer"
            data-test--investigate-error-link
          >
            {{t "Submit a support request"}}
          </a>
          {{t "if you think this is an error."}}
        {{else if (eq @messageId "o365-table-not-found")}}
          <p>{{t
              "Possible reasons include: data may not have been received yet, we are experiencing an outage or you do not have the required Microsoft license to access this information."
            }}</p>
          <br />
          <p>{{t "Check your "}}
            <LinkTo @route="data-sources.o365">
              {{t "license"}}
            </LinkTo>
            {{t "and if it "}}
            <a
              href="https://learn.microsoft.com/en-us/graph/api/resources/azure-ad-auditlog-overview?view=graph-rest-1.0#what-licenses-do-i-need"
              target="_blank"
              class="mt-5"
              rel="noopener noreferrer"
              data-test--investigate-error-link
            >
              {{t "enables"}}
            </a>
            {{t "you to see logs or"}}
            <p>
              <a
                href="https://support.vectra.ai/s/"
                target="_blank"
                class="mt-5"
                rel="noopener noreferrer"
                data-test--investigate-error-link
              >
                {{t "submit a support request"}}
              </a>
              {{t "if you think this is an error."}}
            </p>
          </p>
        {{else}}
          <p>{{t
              "Possible reasons include: no data has been received yet or we are experiencing an outage."
            }}</p>
          <br />
          <a
            href="https://support.vectra.ai/s/"
            target="_blank"
            class="mt-5"
            rel="noopener noreferrer"
            data-test--investigate-error-link
          >
            {{t "Submit a support request"}}
          </a>
          {{t "if you think this is an error."}}
        {{/if}}
      </div>
    {{/if}}

    {{#if @errorId}}
      <Vc::Text
        class="mb-5 investigate__empty-state__error-id"
        data-test--investigate-error-id
      >
        {{concat "Error ID: " @errorId}}
      </Vc::Text>
    {{/if}}
    {{#if @linkHref}}
      <a
        href={{@linkHref}}
        target="_blank"
        class="vc-typ-body-bold mt-5"
        rel="noopener noreferrer"
        data-test--investigate-error-link
      >
        {{#if @linkIcon}}
          <Vc::Icon @iconName={{@linkIcon}} @class="investigate__link__icon" />
        {{/if}}
        {{@linkText}}
      </a>
    {{/if}}
    {{#if @onButtonClick}}
      <Vc::Button::Action
        @onClick={{@onButtonClick}}
        @accessibilityText={{@buttonLabel}}
        @class="mt-5"
        data-test--investigate-error-button
        as |ba|
      >
        <ba.label>{{@buttonLabel}}</ba.label>
      </Vc::Button::Action>
    {{/if}}
  </div>
</div>