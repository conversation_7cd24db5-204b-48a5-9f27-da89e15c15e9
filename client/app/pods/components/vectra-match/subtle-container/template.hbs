<div
  class="vectra-match__subtle-container pt-xl pe-l"
  data-test-vectra-match-subtle-container
  {{did-insert this.refresh}}
>
  {{#if this.shouldShowBanner}}
    <Vc::InlineNotificationBanner
      class="pb-m"
      @isVisible={{true}}
      @onClick={{fn (mut this.shouldShowBanner) false}}
    >
      <div class="d-inline-block" data-test-setup-guidence-banner>
        <div class="d-inline-block">
          <Vc::Text::Bold>
            {{this.sensorsMessage}}
          </Vc::Text::Bold>
        </div>
        <div class="d-inline-block">
          <Vc::Button::Action
            class="ps-0"
            @onClick={{this.setupGuidenceModelWizard}}
            @type="secondary"
            @isDisabled={{false}}
            @class="custom-class-name"
            data-test-attribute-default-button
            as |ba|
          >
            <ba.label>
              {{this.setupGuidenceButton}}
            </ba.label>
          </Vc::Button::Action>
        </div>
      </div>
    </Vc::InlineNotificationBanner>
  {{/if}}
  {{#if this.isLicenseInvalid}}
    <Vc::InlineNotificationBanner
      class="pb-m"
      @isVisible={{true}}
      @accessibilityText={{this.licenseStatusMessage}}
      @onClick={{fn (mut this.showLicenseInvalid) false}}
      @type={{this.licenseStatusType}}
    >
      <Vc::Text::Bold>
        {{this.licenseStatusMessage}}
      </Vc::Text::Bold>
    </Vc::InlineNotificationBanner>
  {{/if}}
  {{#if this.showUnhealthyBanner}}
    <Vc::InlineNotificationBanner
      class="pb-m"
      @isVisible={{true}}
      @accessibilityText={{concat
        this.unhealthySensorCount
        this.unhealthyBannerText
      }}
      @onClick={{fn (mut this.displayUnhealthyBanner) false}}
      @type="alert"
    >
      {{concat this.unhealthySensorCount this.unhealthyBannerText}}
    </Vc::InlineNotificationBanner>
  {{/if}}
  {{#if this.displayDelayedUpdatesBanner}}
    <div data-test-delayed-updates-banner>
      <Vc::InlineNotificationBanner
        class="pb-m"
        @isVisible={{true}}
        @accessibilityText={{this.config.subtleContainer.delayedUpdateBannerMessage}}
        @onClick={{fn (mut this.displayDelayedUpdatesBanner) false}}
      >
        {{this.config.subtleContainer.delayedUpdateBannerMessage}}
      </Vc::InlineNotificationBanner>
    </div>
  {{/if}}
  {{#if this.sensorEnablementErrorBanner}}
    <VectraMatch::SubtleContainer::EnablementBanner
      @onClick={{fn (mut this.sensorEnablementErrorBanner) false}}
      @errors={{this.sensorEnablementErrorData}}
    />
  {{/if}}
  <Vc::SubtleContainer as |sc|>
    <sc.header>
      <Vc::Header::SubHeader class="header">
        {{this.config.subtleContainer.heading}}
      </Vc::Header::SubHeader>
      <Vc::InAppHelp
        @header="Vectra Match"
        @content={{this.config.subtleContainer.inAppHelp}}
        @onOpen={{fn (mut this.showVectraMatchHelp) true}}
        @onClose={{fn (mut this.showVectraMatchHelp) false}}
        @showPopout={{this.showVectraMatchHelp}}
      />
    </sc.header>
    <sc.body>
      <Vc::Text
        class="text"
      >{{this.config.subtleContainer.paragraph}}</Vc::Text>
      <div class="d-flex">
        <div>
          <Vc::Text
            class="text"
            class="text"
          >{{this.config.subtleContainer.paragraphPartTwo}}</Vc::Text>
        </div>
        <div>
          <a
            class="paragraph-link"
            href="https://support.vectra.ai/s/article/KB-VS-1636"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Vc::Icon
              @iconName="misc-popup"
              @size="xs"
              @addSpacing={{true}}
              @class="mx-3"
            />
            {{t "Vectra Match Deployment Guide"}}
          </a>
        </div>
        {{#if
          (and
            (not this.isLicenseInvalid)
            (not (feature-enabled "vectra_match_ruleset_modification"))
          )
        }}
          <div>
            <div class="d-inline-block">
              <a
                class="paragraph-link"
                data-test-download-curated-ruleset-link
                href="/api/app/manage/vectra-match/download-vectra-ruleset"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Vc::Icon
                  @iconName="actions/download"
                  @size="xs"
                  @addSpacing={{true}}
                  @class="mx-3"
                />
                {{t "Download Vectra Ruleset"}}
              </a>
            </div>
            <div
              class="d-inline-block"
              data-test-download-curated-ruleset-tooltip
            >
              <Vc::Tooltip
                @position="horizontal"
                onmouseenter={{this.getRulesetLastUpdated.perform}}
              >
                {{this.rulesetLastUpdated}}
              </Vc::Tooltip>
            </div>
          </div>
        {{/if}}
      </div>
      <div class="pt-6">
        <Vc::Button::Action
          class="vectra-match-show-upload-ruleset-modal-button"
          data-test-upload-ruleset-button
          @type="primary"
          @isDisabled={{or
            this.isLicenseInvalid
            (not (can "edit_vectra_match_ruleset"))
          }}
          @onClick={{this.openWizard}}
          as |ba|
        >
          <ba.label>
            {{this.config.subtleContainer.uploadButton}}
          </ba.label>
        </Vc::Button::Action>
        <Vc::Button::Action
          class="vectra-match-show-enable-modal-button"
          data-test-enable-button
          @type="secondary"
          @isDisabled={{or
            this.isLicenseInvalid
            (not (can "edit_vectra_match_setting"))
          }}
          @onClick={{queue
            (fn (mut this.openModal) "enablement")
            (fn (mut this.mode) "enable")
          }}
          as |ba|
        >
          <ba.label>
            {{this.config.subtleContainer.enableSensors}}
          </ba.label>
        </Vc::Button::Action>
        <Vc::Button::Action
          class="vectra-match-show-disable-modal-button"
          data-test-disable-button
          @type="secondary"
          @isDisabled={{or
            (eq @isValidLicense "error")
            (not (can "edit_vectra_match_setting"))
          }}
          @onClick={{queue
            (fn (mut this.openModal) "enablement")
            (fn (mut this.mode) "disable")
          }}
          as |ba|
        >
          <ba.label>
            {{this.config.subtleContainer.disableSensors}}
          </ba.label>
        </Vc::Button::Action>
        <Vc::Button::Action
          class="vectra-match-show-disable-modal-button"
          data-test-delete-button
          @type="secondary"
          @isDisabled={{or
            (eq @isValidLicense "error")
            (not (can "edit_vectra_match_setting"))
          }}
          @onClick={{fn (mut this.openModal) "deleteRuleset"}}
          as |ba|
        >
          <ba.label>
            {{this.config.subtleContainer.deleteRulesets}}
          </ba.label>
        </Vc::Button::Action>
        {{#if this.rulesetModificationFlag}}
          <Vc::Button::Action
            data-test-unassign-button
            @type="secondary"
            @isDisabled={{or
              (eq @isValidLicense "error")
              (not (can "edit_vectra_match_setting"))
            }}
            @onClick={{fn (mut this.openModal) "unassignRulesetModal"}}
            as |ba|
          >
            <ba.label>
              {{this.config.subtleContainer.unassignRulesets}}
            </ba.label>
          </Vc::Button::Action>
        {{/if}}
        {{#if this.enabledSensorCountLabel}}
          <div class="ps-4 pt-5 pb-3">
            <Vc::KeyValue as |kv|>
              <kv.key>
                {{this.config.subtleContainer.sensorCountLabel}}
              </kv.key>
              <kv.value>
                <Vc::Text::Bold
                >{{this.enabledSensorCountLabel}}</Vc::Text::Bold>
              </kv.value>
            </Vc::KeyValue>
          </div>
        {{/if}}
      </div>
      <VectraMatch::SubtleContainer::Table
        @tableData={{this.tableData}}
        @enabledSensorCount={{this.enabledSensorCount}}
        @pageSize={{this.pageSize}}
        @pageSizeOptions={{this.pageSizeOptions}}
        @onPageSizeChange={{this.onPageSizeChange}}
        @onPageSelect={{this.onPageSelect}}
        @currentPageNumber={{if (gt this.sensorCount 0) this.pageNumber ""}}
        @totalRecordsCount={{this.sensorCount}}
        @sortBy={{this.sortBy}}
        @showDeleteModal={{this.showDeleteModal}}
        @showDeleteAssignment={{this.showDeleteAssignment}}
        @showExpandoDeleteAssignment={{this.showExpandoDeleteAssignment}}
        @showDeleteRulesetAndAssignment={{this.showDeleteRulesetAndAssignment}}
        @deleteRuleset={{this.deleteRuleset}}
        @deleteAssignment={{this.deleteAssignment}}
        @row={{this.row}}
        @deleteAssignmentRow={{this.deleteAssignmentRow}}
        @deleteOption={{this.deleteOption}}
        @getTableData={{this.getTableData}}
        @tableActions={{this.tableActions}}
        @expandoTableActions={{this.expandoTableActions}}
      />
      {{#if this.showEnablementModal}}
        <VectraMatch::SubtleContainer::EnablementModal
          @showModal={{this.showEnablementModal}}
          @mode={{this.mode}}
          @primaryAction={{this.postSensors}}
          @onCancel={{fn (mut this.openModal) null}}
        />
      {{/if}}
      {{#if this.showSelectRulesetToAssignModal}}
        <VectraMatch::SubtleContainer::SelectRulesetToAssignModal
          @flow={{this.flow}}
          @showModal={{this.showSelectRulesetToAssignModal}}
          @onNext={{this.changeToSecondStep}}
          @onCancel={{this.closeRulesetWizard}}
          @onError={{this.onError}}
          @getRulesets={{this.getRulesets}}
          @mappedRules={{this.mappedRules}}
        />
      {{/if}}
      {{#if this.showSelectSensorsModal}}
        <VectraMatch::SubtleContainer::SelectSensorsModal
          @flow={{this.flow}}
          @rulesetName={{this.rulesetName}}
          @rulesetFileUuid={{this.rulesetFileUuid}}
          @showModal={{this.showSelectSensorsModal}}
          @onPrev={{this.goBackToFirstStep}}
          @onCancel={{this.closeRulesetWizard}}
          @manageSensors={{this.manageSensors}}
        />
      {{/if}}
      {{#if this.showRulesetErrorModal}}
        <VectraMatch::SubtleContainer::RulesetErrorModal
          @showModal={{this.showRulesetErrorModal}}
          @errorDetails={{this.errorDetails}}
          @onCancel={{this.closeRulesetWizard}}
          @onPrev={{this.goBackToFirstStep}}
          @onNext={{this.onNext}}
          @deleteRuleset={{this.deleteRuleset}}
        />
      {{/if}}
      {{#if this.showDeleteRulesetModal}}
        <VectraMatch::SubtleContainer::Table::DeleteModal::DeleteRulesetModal
          @showDeleteRulesetModal={{this.showDeleteRulesetModal}}
          @deleteRuleset={{this.deleteRuleset}}
          @deleteRulesetTable={{this.deleteRulesetTable}}
          @getRulesets={{this.getRulesets}}
          @primaryAction={{this.deleteRuleset}}
          @onClose={{fn (mut this.openModal) null}}
        />
      {{/if}}
      {{#if this.showLoadingModal}}
        <VectraMatch::SubtleContainer::LoadingModal
          @onClose={{this.cancelUpload}}
          @config={{this.config.loadingModal}}
          @fileName={{this.rulesetFileName}}
        />
      {{/if}}
      {{#if this.showSelectRulesetToUnassignModal}}
        <VectraMatch::SubtleContainer::SelectRulesetToUnassignModal
          @flow={{this.flow}}
          @showModal={{this.showSelectRulesetToUnassignModal}}
          @onNext={{this.selectSensorsToUnassign}}
          @onCancel={{this.closeRulesetWizard}}
          @getRulesets={{this.getRulesets}}
          @mappedRules={{this.mappedRules}}
          @rulesetAssigned={{this.rulesetAssigned}}
        />
      {{/if}}
      {{#if this.rulesetModificationFlag}}
        <VectraMatch::SubtleContainer::RulesModification::Table
          @isLicenseInvalid={{this.isLicenseInvalid}}
        />
      {{/if}}
    </sc.body>
  </Vc::SubtleContainer>
</div>