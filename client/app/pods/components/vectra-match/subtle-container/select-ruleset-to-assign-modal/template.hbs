<Vc::Modal
  data-test-upload-ruleset-model
  class="ruleset-upload-assignment-modal"
  @size="medium"
  @type="workflow"
  @isOpen={{@showModal}}
  @onClose={{@onCancel}}
  @primaryText={{this.config.primaryText}}
  @primaryAction={{this.onNextStep}}
  @primaryIsLoading={{this.postRulesetFile.isRunning}}
  @secondaryText={{this.config.secondaryText}}
  @secondaryAction={{@onCancel}}
  @primaryIsDisabled={{this.primaryIsDisabled}}
  as |md|
>
  <md.header>
    {{this.config.header}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status @primaryText={{this.config.subheader}} />
  </md.subheader>
  <md.body>
    <Vc::RadioButtonList
      @items={{this.radioButtonList}}
      @checkedValue={{this.selectedRadio}}
      @groupName="example-4"
      @onChange={{queue
        (fn (mut this.selectedRadio))
        (fn (mut this.selectedFileOption) null)
        (fn (mut this.selectedFile) null)
        (action this.clearNote)
      }}
    >
      {{#if (eq this.selectedRadio "0")}}
        <a
          target="_blank"
          rel="noopener noreferrer"
          href={{this.config.curatedRulesetKBArticleUrl}}
          class="ruleset-upload-assignment-modal__curated-learn-more-link"
          data-test--curated-option-link
        >
          <Vc::Icon
            @iconName="misc-popup"
            @class="icon--misc-popup"
            @size="sm"
            @addSpacing={{true}}
          />
          {{this.config.curatedRulesetKBArticleLabel}}
        </a>
      {{/if}}
      {{#if (eq this.selectedRadio "1")}}
        <Vc::FileUpload
          class="vectra-match-file-select"
          @name="ruleset"
          @onChange={{this.onPickFile}}
          @onDelete={{this.onDeleteFileFromPicker}}
          @errorMessage={{this.invalidFileMessage}}
          @hasError={{this.fileHasError}}
        />
        <Vc::Text class="text pt-0">
          {{this.config.typeAndLimit}}
        </Vc::Text>
      {{/if}}
      {{#if (eq this.selectedRadio "2")}}
        <div class="dropdown-position">
          <Vc::Dropdown::Select
            data-test-dropdown
            @label="Uploaded Ruleset"
            @options={{this.mappedRules}}
            @selectedItem={{this.selectedFileOption}}
            @onItemSelected={{queue
              (fn (mut this.selectedFileOption))
              (fn (mut this.dropdownError) null)
              (fn (mut this.primaryIsDisabled) false)
            }}
            @hasError={{this.dropdownError}}
            @errorMessage={{this.dropdownError}}
          />
        </div>
      {{/if}}
    </Vc::RadioButtonList>
    <Vc::Input::Textarea
      class="pt-xxl vectra-match-rule-note"
      @value={{this.changesetServiceObject.changeset.note}}
      @label="Add Notes (optional)"
      @limit={{144}}
      @readOnly={{if (eq this.selectedRadio "1") false true}}
    />
  </md.body>
</Vc::Modal>