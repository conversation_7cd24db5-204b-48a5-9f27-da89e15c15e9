<Vc::Modal
  @size="medioum"
  @type="workflow"
  @isOpen={{@showModal}}
  @onClose={{@onCancel}}
  @primaryText={{this.primaryText}}
  @primaryAction={{this.primaryAction}}
  @secondaryText={{this.secondaryText}}
  @secondaryAction={{this.secondaryAction}}
  data-test-unassign-confirmation-modal
  as |md|
>
  <md.header>
    {{this.header}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      data-test-error-modal-subheader
      @type={{"warning"}}
      @primaryText={{this.subheader}}
    />
  </md.subheader>
</Vc::Modal>