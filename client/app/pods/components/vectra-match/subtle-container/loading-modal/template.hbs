<Vc::Modal
  data-test-loading-modal
  @isOpen={{true}}
  @secondaryText={{@config.secondaryButtonText}}
  @secondaryAction={{@onClose}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  as |md|
>
  <md.header>
    {{t @config.header}}
  </md.header>
  <md.body>
    <div class="progress-bar__container">
      <Vc::ProgressBar::Container
        {{did-insert this.startProgressBarAnimation}}
        @isLoading={{true}}
        @helperText={{concat @config.helperText @fileName " ..."}}
        @amountLoaded={{this.progressBarAmountLoaded}}
        @handleDestroy={{this.progressBarWillDestroy}}
        @onClose={{@onClose}}
      />
    </div>
  </md.body>
</Vc::Modal>