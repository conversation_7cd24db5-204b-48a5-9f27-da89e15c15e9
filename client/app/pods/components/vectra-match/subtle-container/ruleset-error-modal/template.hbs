<Vc::Modal
  @size="medioum"
  @type="workflow"
  @isOpen={{@showModal}}
  @onClose={{@onCancel}}
  @primaryText={{this.config.primaryText}}
  @primaryAction={{this.onNext}}
  @secondaryText={{this.config.backText}}
  @secondaryAction={{@onPrev}}
  @tertiaryText={{this.config.cancelText}}
  @tertiaryAction={{perform this.deleteRuleset this.rulesetInfo}}
  data-test-upload-failed-modal
  as |md|
>
  <md.header>
    {{this.header}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      data-test-error-modal-subheader
      @type={{if this.errorType209 "warning" "alert"}}
      @primaryText={{this.subheader}}
    />
  </md.subheader>
  <md.body class="ruleset-error">
    <Vc::KeyValue as |kv|>
      <kv.key>
        {{this.config.rulesetFileLabel}}
      </kv.key>
      <kv.value>
        {{@errorDetails.fileName}}
      </kv.value>
    </Vc::KeyValue>

    {{#if this.errorType209}}
      <Vc::KeyValue as |kv|>
        <kv.key>
          {{this.config.loadedRulesLabel}}
        </kv.key>
        <kv.value>
          {{this.successfulRules}}
        </kv.value>
      </Vc::KeyValue>

      <Vc::KeyValue as |kv|>
        <kv.key>
          {{this.config.failedRulesLabel}}
        </kv.key>
        <kv.value>
          {{this.failedRules}}
        </kv.value>
      </Vc::KeyValue>
    {{/if}}
    <div class="ruleset-error__link mt-xs">
      <a
        href={{this.href}}
        download={{this.errorDetailsFile}}
        data-test--ruleset-error-download-link
      >
        <span class="ruleset-error__download-icon">
          <Vc::Icon
            @iconName="actions/download"
            @size="xs"
            @addSpacing={{true}}
          />
        </span>
        {{this.config.downloadButton}}
      </a>
    </div>
    <div>
      <p>
        {{this.config.learnMoreLabel}}
      </p>
      <div class="ruleset-error__link">
        <a
          href={{this.config.externalLinkUrl}}
          target="_blank"
          rel="noopener noreferrer"
          class="ruleset-error__external-link"
          data-test--ruleset-error-external-link
        >
          <span class="ruleset-error__external-link-icon">
            <Vc::Icon
              @iconName="misc-popup"
              @class="icon"
              @size="sm"
              @addSpacing={{true}}
            />
          </span>
          {{t this.config.learnMoreLinkLabel}}
        </a>
      </div>
    </div>
  </md.body>
</Vc::Modal>