<div>
  <div class="position-relative">
    <Vc::LoadingContainer @isLoading={{@getTableData.isRunning}}>
      <Vc::Table
        @rows={{this.tableData}}
        @columns={{this.tableColumns}}
        @timeZone={{this.timezone}}
        @canExpand={{true}}
        @hideFooter={{true}}
        @sortBy={{@sortBy}}
        as |tbody|
      >
        <tbody.expanded-row as |row|>
          <VectraMatch::SubtleContainer::Table::ExpandoContent
            @row={{row.content}}
            @sensorName={{@row.sensorName}}
            @showExpandoDeleteAssignment={{@showExpandoDeleteAssignment}}
            @deleteAssignmentRow={{@deleteAssignmentRow}}
            @deleteRuleset={{@deleteRuleset}}
            @deleteAssignment={{@deleteAssignment}}
            @tableActions={{@expandoTableActions}}
          />
        </tbody.expanded-row>
      </Vc::Table>
    </Vc::LoadingContainer>
  </div>
  <Vc::Pagination
    @showPageSizeDropdown={{true}}
    @pageSize={{@pageSize}}
    @pageSizeOptions={{@pageSizeOptions}}
    @onPageSizeChange={{@onPageSizeChange}}
    @onPageSelect={{@onPageSelect}}
    @currentPageNumber={{@currentPageNumber}}
    @totalRecordsCount={{@totalRecordsCount}}
  />
</div>
{{#if this.showDeleteModal}}
  <VectraMatch::SubtleContainer::Table::DeleteModal
    @showDeleteModal={{this.showDeleteModal}}
    @showDeleteAssignment={{this.showDeleteAssignment}}
    @sensorName={{@row.sensorName}}
    @row={{@row}}
    @deleteAssignmentRow={{@deleteAssignmentRow}}
    @deleteRuleset={{@deleteRuleset}}
    @deleteAssignment={{@deleteAssignment}}
    @tableActions={{@tableActions}}
  />
{{/if}}