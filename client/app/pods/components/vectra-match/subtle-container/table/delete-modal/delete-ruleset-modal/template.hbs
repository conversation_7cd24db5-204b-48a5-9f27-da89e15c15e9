<Vc::Modal
  class="vectra_match_delete_modal"
  @size="small"
  @type="workflow"
  @isOpen={{@showDeleteRulesetModal}}
  @onClose={{@onClose}}
  @primaryText={{t "Delete"}}
  @primaryAction={{queue (perform @primaryAction this.rulesetsUuids)}}
  @secondaryText={{this.config.secondaryText}}
  @secondaryAction={{@onClose}}
  as |md|
>
  <md.header>
    {{this.config.deleteRulesetModalHeader}}
  </md.header>
  <md.subheader>{{this.config.deleteRulesetModalSubHeader}}</md.subheader>
  <md.body>
    <Vc::LoadingContainer @isLoading={{this.getRulesets.isRunning}}>
      <Vc::Table
        @rows={{@deleteRulesetTable}}
        @columns={{this.tableColumns}}
        @hideFooter={{true}}
        @tableActions={{this.tableActions}}
        @rowSelectionEnabled={{true}}
        @onSelectionChanged={{this.onSelectionChanged}}
        @emptySetMessage={{t "There are no rules files to delete"}}
        @timeZone={{(setting-timezone)}}
      />
    </Vc::LoadingContainer>
  </md.body>
</Vc::Modal>