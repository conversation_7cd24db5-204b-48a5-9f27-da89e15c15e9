<Vc::Modal
  class="vectra_match_delete_assignment_modal"
  @size="small"
  @type="workflow"
  @isOpen={{@showDeleteAssignment}}
  @onClose={{fn (mut @showDeleteAssignment) false}}
  @primaryText={{t "Remove"}}
  @primaryAction={{queue
    (perform this.deleteAssignment @deleteAssignmentRow.content @sensorSerial)
  }}
  @secondaryText={{t "Back"}}
  @secondaryAction={{fn (mut @showDeleteAssignment) false}}
  as |md|
>
  <md.header>
    {{t "Remove from this sensor"}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      data-test-delete-assignment-modal-header-status
      @type="warning"
      @primaryText={{component
        "vectra-match/subtle-container/table/delete-modal/delete-assignment-modal/custom-text"
        rulesetName=@deleteAssignmentRow.content.rulesetName
        sensorSerial=@sensorSerial
      }}
    />
  </md.subheader>
</Vc::Modal>