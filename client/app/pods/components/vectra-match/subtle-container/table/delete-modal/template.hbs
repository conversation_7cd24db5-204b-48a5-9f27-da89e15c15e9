{{#if (not this.showDeleteAssignment)}}
  <Vc::Modal
    class="vectra_match_delete_modal"
    @size="small"
    @type="workflow"
    @isOpen={{@showDeleteModal}}
    @onClose={{fn (mut @showDeleteModal) false}}
    @secondaryText={{this.config.secondaryText}}
    @secondaryAction={{fn (mut @showDeleteModal) false}}
    data-test--sensor-assigned-rulesets
    as |md|
  >
    <md.header>
      {{concat this.row.sensorName " Ruleset Files"}}
    </md.header>
    <md.subheader>
      <Vc::Form::Status>
        <Vc::KeyValueList as |list|>
          <list.kv class="pb-2" as |kv|>
            <kv.key>
              {{this.config.selectSensor}}
            </kv.key>
            <kv.value>
              {{this.row.sensorName}}
            </kv.value>
          </list.kv>
          <list.kv as |kv|>
            <kv.key>
              {{this.config.totalFilesOnSensor}}
            </kv.key>
            <kv.value>
              {{this.row.assignedRulesets.length}}
            </kv.value>
          </list.kv>
        </Vc::KeyValueList>
      </Vc::Form::Status>
    </md.subheader>
    <md.body>
      <Vc::Table
        @rows={{@row.assignedRulesets}}
        @columns={{this.tableColumns}}
        @hideFooter={{true}}
        @tableActions={{this.tableActions}}
        @timeZone={{(setting-timezone)}}
      />
    </md.body>
  </Vc::Modal>
{{/if}}
{{#if this.showDeleteAssignment}}
  <VectraMatch::SubtleContainer::Table::DeleteModal::DeleteAssignmentModal
    @showDeleteModal={{this.showDeleteModal}}
    @showDeleteAssignment={{this.showDeleteAssignment}}
    @deleteAssignment={{this.deleteAssignment}}
    @row={{@row}}
    @sensorSerial={{@row.sensorSerial}}
    @deleteAssignmentRow={{@deleteAssignmentRow}}
  />
{{/if}}