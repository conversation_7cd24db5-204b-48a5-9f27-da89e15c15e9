<div class={{this.styleNamespace}}>
  <Vc::KeyValueList class="ps-8 pt-5 pb-5" as |list|>
    {{#each this.sensorDetails as |detail|}}
      <list.kv class="pb-s" as |kv|>
        <kv.key>
          {{detail.key}}
        </kv.key>
        <kv.value>
          {{detail.value}}
          {{#if (eq detail.value "Mixed")}}
            <Vc::Tooltip @position="right" @isInline={{true}}>
              {{this.config.table.typeTooltip}}
            </Vc::Tooltip>
          {{/if}}
        </kv.value>
      </list.kv>
    {{/each}}
    {{#if this.rulesetModificationFlag}}
      <div class="w-75 mt-5">
        <Vc::Table
          @tableClass="ruleset-table"
          @rows={{@row.assignedRulesets}}
          @columns={{this.tableColumns}}
          @hideFooter={{true}}
          @tableActions={{@tableActions}}
          @timeZone={{(setting-timezone)}}
        />
      </div>
    {{/if}}
  </Vc::KeyValueList>
  {{#if @showExpandoDeleteAssignment}}
    <VectraMatch::SubtleContainer::Table::DeleteModal::DeleteAssignmentModal
      @showDeleteAssignment={{@showExpandoDeleteAssignment}}
      @deleteAssignment={{@deleteAssignment}}
      @sensorSerial={{@row.sensorSerial}}
      @deleteAssignmentRow={{@deleteAssignmentRow}}
    />
  {{/if}}
</div>