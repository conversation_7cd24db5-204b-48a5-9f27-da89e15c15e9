<Vc::Modal
  data-test-vectra-match-unassign-modal
  class="vectra-match-rule-select-modal"
  @size="medium"
  @type="workflow"
  @isOpen={{@showModal}}
  @onClose={{@onCancel}}
  @primaryText={{this.config.primaryText}}
  @primaryAction={{this.onNextStep}}
  @primaryIsLoading={{this.postRulesetFile.isRunning}}
  @secondaryText={{this.config.secondaryText}}
  @secondaryAction={{@onCancel}}
  @primaryIsDisabled={{this.isPrimaryDisabled}}
  as |md|
>
  <md.header>
    {{this.config.header}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status @primaryText={{this.config.subheader}} />
  </md.subheader>
  <md.body>
    {{#if this.rulesetAssigned}}
      <Vc::RadioButtonList
        @items={{this.radioButtonList}}
        @checkedValue={{this.selectedRadio}}
        @groupName="example-4"
        @onChange={{this.onRadioButtonSelect}}
      >
        {{#if (eq this.selectedRadio "1")}}
          <div class="dropdown-position">
            <Vc::Dropdown::Select
              data-test-dropdown
              @label="Uploaded Ruleset"
              @options={{this.mappedRules}}
              @selectedItem={{this.selectedFileOption}}
              @onItemSelected={{this.onDropdownSelect}}
            />
          </div>
        {{/if}}
      </Vc::RadioButtonList>
    {{else}}
      <Vc::Text class="text text-empty-state">
        {{this.config.noRulesToUnassignState}}
      </Vc::Text>
    {{/if}}
  </md.body>
</Vc::Modal>