<Vc::Modal
  @size="large"
  @type="workflow"
  @isOpen={{true}}
  @onClose={{@onCancel}}
  @primaryText={{this.primaryText}}
  @primaryAction={{queue (perform @primaryAction this.sensorsArray)}}
  @primaryIsLoading={{@primaryAction.isRunning}}
  @primaryIsDisabled={{or
    (not this.enablementTableData)
    (eq this.enablementTableData.length 0)
  }}
  @secondaryText={{this.config.cancel}}
  @secondaryAction={{@onCancel}}
  class="vectra-match__enablement-modal vectra_match_enablement_modal{{if
      (eq this.mode 'enable')
      '_enable'
      '_disable'
    }}"
  as |md|
>
  <md.header>
    {{#if (eq this.mode "enable")}}
      {{this.config.enablementTableModalEnableHeader}}
    {{else}}
      {{this.config.enablementTableModalDisableHeader}}
    {{/if}}
    <Vc::InAppHelp
      @header="Vectra Match"
      @content={{this.config.inAppHelp}}
      @onOpen={{fn (mut this.showVectraMatchHelp) true}}
      @onClose={{fn (mut this.showVectraMatchHelp) false}}
      @showPopout={{this.showVectraMatchHelp}}
    />
  </md.header>
  <md.body>
    <Vc::LoadingContainer @isLoading={{this.getSensorEnablement.isRunning}}>
      <Vc::Header::SubHeader class="header">
        {{#if this.showActivatingSensorWarning}}
          <Vc::InlineNotificationBanner
            @isVisible={{true}}
            @type="warning"
            @onClick={{fn (mut this.showActivatingSensorWarning) false}}
            data-test--vectra-match-enablement--activating-sensors-warning
          >
            {{#if (eq this.mode "enable")}}
              {{this.config.stillEnablingWarning}}
            {{else}}
              {{this.config.stillDisablingWarning}}
            {{/if}}
          </Vc::InlineNotificationBanner>
        {{/if}}
        {{this.config.enablementTableModalSelectSensorText}}
      </Vc::Header::SubHeader>
      <div class="table-width">
        <Vc::Table
          @rowSelectionEnabled={{true}}
          @rows={{this.enablementTableData}}
          @columns={{this.tableColumns}}
          @timeZone={{this.timezone}}
          @onSelectionChanged={{this.onSelectionChanged}}
          @hideFooter={{true}}
          @sortBy={{this.sortBy}}
          @emptySetMessage={{this.emptyMessage}}
        />
        <Vc::Pagination
          @showPageSizeDropdown={{true}}
          @pageSize={{this.pageSize}}
          @pageSizeOptions={{this.pageSizeOptions}}
          @onPageSizeChange={{this.onPageSizeChange}}
          @onPageSelect={{this.onPageSelect}}
          @currentPageNumber={{if (gt this.sensorCount 0) this.pageNumber ""}}
          @totalRecordsCount={{this.sensorCount}}
          @pageSizeOptionsPosition="above"
        />
      </div>
    </Vc::LoadingContainer>
  </md.body>
</Vc::Modal>