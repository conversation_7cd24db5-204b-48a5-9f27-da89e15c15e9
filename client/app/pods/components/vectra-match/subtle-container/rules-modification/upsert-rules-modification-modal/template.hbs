<Vc::Modal
  data-test-modification-upsert-modal
  @size="medium"
  @type="workflow"
  @isOpen={{true}}
  @primaryText="Save"
  @primaryAction={{this.save}}
  @secondaryText={{"Cancel"}}
  @secondaryAction={{this.handleCancel}}
  class="modification-upsert"
  as |md|
>
  <md.header>
    <Vc::Header class="header">
      {{#if this.isEditMode}}
        {{this.rulesModificationEditModalConfig.header}}
      {{else}}
        {{this.rulesModificationAddModalConfig.header}}
      {{/if}}
    </Vc::Header>
  </md.header>
  <md.subheader>
    <Vc::Text class="text">
      {{#if this.isEditMode}}
        <Vc::KeyValueList as |list|>
          <list.kv as |kv|>
            <kv.key>
              {{this.rulesModificationEditModalConfig.ruleName}}
            </kv.key>
            <kv.value>
              {{@row.msg}}
            </kv.value>
          </list.kv>
          <list.kv as |kv|>
            <kv.key>
              {{this.rulesModificationEditModalConfig.sid}}
            </kv.key>
            <kv.value>
              {{@row.sid}}
            </kv.value>
          </list.kv>
        </Vc::KeyValueList>
      {{else}}
        <div class="d-flex justify-content-center align-items-center gap-2">
          <div>{{this.rulesModificationAddModalConfig.subheader}}</div>
          <div class="d-inline-block">
            <a
              class="paragraph-link"
              data-test-download-curated-ruleset-link
              href="/api/app/manage/vectra-match/download-vectra-ruleset"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Vc::Icon
                @iconName="actions/download"
                @size="xs"
                @addSpacing={{true}}
                @class="mx-3"
              />
              {{t "Download Vectra Ruleset"}}
            </a>
          </div>
          <div class="ms-xxs mt-xxs" data-test-download-curated-ruleset-tooltip>
            <Vc::Tooltip @position="horizontal">
              {{t
                "Download the base Vectra Ruleset prior to making changes to this ruleset"
              }}
            </Vc::Tooltip>
          </div>
        </div>
      {{/if}}
    </Vc::Text>
  </md.subheader>
  <md.body>
    {{#if (and (not this.isEditMode) this.showInlineNotificationBanner)}}
      {{! be able hide banner even if indexing }}
      <Vc::InlineNotificationBanner
        @isVisible={{this.isIndexing}}
        @accessibilityText="This is an inline notification banner"
        @type="warning"
        @onClick={{fn (mut this.showInlineNotificationBanner) false}}
      >
        {{this.indexingStatusMessage}}
      </Vc::InlineNotificationBanner>
    {{/if}}
    {{#if (not this.isEditMode)}}
      <div class="mt-5 modification-upsert__rule-pills pb-4">
        <Vc::MultiValueInput
          @label={{this.rulesModificationAddModalConfig.multiValueInputLabel}}
          @tooltip={{this.rulesModificationAddModalConfig.tooltip}}
          @pills={{this.pills}}
          @onPillChange={{this.onPillChange}}
          @fullWidth={{true}}
          @maxPillWidth={{600}}
          @options={{this.suggestions}}
          @getOptions={{this.getSuggestions}}
          @hasError={{this.hasError}}
          @errorMessage={{this.errorMessage}}
        />
      </div>
    {{/if}}
    <div class="mt-5">
      <Vc::Dropdown::Select
        data-test-dropdown-action
        @label="Actions"
        @options={{this.actions}}
        @selectedItem={{this.selectedAction}}
        @onItemSelected={{this.manageForm}}
        @hasFixedWidth={{true}}
        @hasError={{this.hasDropdownError}}
        @errorMessage={{this.dropdownErrorMessage}}
        as |option|
      >
        {{option.label}}
        <Vc::Tooltip
          @position="right"
          @isInline={{true}}
          @attachedElementId="body"
        >
          {{option.tooltip}}
        </Vc::Tooltip>
      </Vc::Dropdown::Select>
    </div>
    {{#if this.showTrackedBy}}
      <div class="mt-5">
        <Vc::Dropdown::Select
          data-test-dropdown-trackby
          @label={{this.dropdownConfig.trackByLabel}}
          @options={{this.trackBy}}
          @selectedItem={{this.suppressTrackBy}}
          @onItemSelected={{this.selectTrackBy}}
          @hasFixedWidth={{true}}
          @tooltip={{this.dropdownConfig.trackByLabelTooltip}}
          as |option|
        >
          {{option.label}}
          <Vc::Tooltip
            @position="right"
            @isInline={{true}}
            @attachedElementId="body"
          >
            {{option.tooltip}}
          </Vc::Tooltip>
        </Vc::Dropdown::Select>
      </div>
      {{#if this.suppressTrackBy.option}}
        <div class="mt-5">
          <Vc::Input::Text
            data-test-ip-subnet-input
            @label="IP to Suppress"
            @value={{this.ipToSuppress}}
            @onChange={{this.ipSubnetCheck}}
            @hasError={{this.ipToSuppressError}}
            @errorMessage={{this.ipToSuppressErrorMessage}}
            @tooltipProps={{this.ipToSuppressTooltip}}
            @helpContent={{this.ipToSuppressHelpContent}}
          />
        </div>
      {{/if}}
    {{/if}}
    {{#if this.showType}}
      <label class="mt-5 mb-4">
        {{t "Type"}}
      </label>
      <Vc::RadioButtonList
        @groupName="type"
        @items={{this.typeRadioItems}}
        @checkedValue={{this.thresholdType}}
        @onChange={{this.selectTypeRadioItem}}
        @name="type"
      />
      <div>
        <div class="mt-4 mb-2">
          <Vc::Input::Number
            data-test-input-count
            @label="Count"
            @value={{this.count}}
            @min={{1}}
            @onChange={{this.onChangeCount}}
            @tooltipProps={{this.countTooltip}}
            @hasError={{this.hasCountError}}
            @errorMessage={{this.countErrorMessage}}
          />
        </div>
        <div class="mt-4 mb-2">
          <Vc::Input::Number
            data-test-input-seconds
            @label="Seconds"
            @value={{this.seconds}}
            @min={{1}}
            @onChange={{this.onChangeSeconds}}
            @tooltipProps={{this.rulesModificationAddModalConfig.secondsToolTip}}
            @hasError={{this.hasSecondsError}}
            @errorMessage={{this.secondsErrorMessage}}
          />
        </div>
      </div>
      <div class="mt-2">
        <div class="mt-5 mb-4 d-flex align-items-center">
          <label class="me-3">
            {{t "Track By"}}
          </label>
          <Vc::Tooltip
            @position="right"
            @attachedElementId="body"
            @size="medium"
          >
            <span>
              {{this.rulesModificationAddModalConfig.trackByRadioTooltip1}}
            </span>
            <br />
            <span>
              {{this.rulesModificationAddModalConfig.trackByRadioTooltip2}}
            </span>
            <br />
            <span>
              {{this.rulesModificationAddModalConfig.trackByRadioTooltip3}}
            </span>
            <br />
            <span>
              {{this.rulesModificationAddModalConfig.trackByRadioTooltip4}}
            </span>
            <br />
          </Vc::Tooltip>
        </div>
        <Vc::RadioButtonList
          @groupName="track-by"
          @items={{this.trackByRadioItems}}
          @onChange={{this.selectTrackBy}}
          @checkedValue={{this.thresholdTrackBy}}
          data-test-track-by-radios
        />
      </div>
    {{/if}}
    <div class="mt-5">
      <Vc::Input::Textarea
        data-test-input-notes
        @value={{this.note}}
        @label="Notes"
        @limit={{144}}
        data-test--setting--textarea
      />
    </div>
  </md.body>
</Vc::Modal>