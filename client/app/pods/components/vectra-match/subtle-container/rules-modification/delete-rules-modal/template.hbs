<Vc::Modal
  data-test-modification-delete-modal
  @size="small"
  @type="workflow"
  @isOpen="true"
  @onClose={{@onClose}}
  @primaryText={{t "Delete"}}
  @primaryAction={{queue (perform @primaryAction)}}
  @secondaryText={{"Cancel"}}
  @secondaryAction={{@onClose}}
  as |md|
>
  <md.header>
    {{@config.header}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status @type="warning" @primaryText={{@config.subheader}} />
  </md.subheader>
</Vc::Modal>