<div class="rule-modifications">
  <Vc::SubtleContainer as |sc|>
    <sc.header>
      <Vc::Header::SubHeader class="rule-modifications-header">
        {{this.config.rulesModification.header}}
      </Vc::Header::SubHeader>
      {{#if (can "edit_vectra_match_ruleset")}}
        <Vc::Button::Action
          @onClick={{fn (mut this.showRulesModificationModal) true}}
          @type="secondary"
          @accessibilityText="Add rule modification"
          @class="rule-modifications-header__add-modification_button"
          as |ba|
        >
          <ba.label>{{t "+ Add Rule Modification"}}</ba.label>
        </Vc::Button::Action>
      {{/if}}
    </sc.header>
    <sc.body>
      <div class="d-flex rule-modifications-subheader">
        <div class="rule-modifications-subheader__links-panel d-flex">
          <a
            target="_blank"
            rel="noopener noreferrer"
            href="{{this.config.rulesModification.linkUrl}}"
            class="rule-modifications-subheader__learn-more-link"
            data-test--rules-modification--link
          >
            <Vc::Icon
              @iconName="misc-popup"
              @class="icon--misc-popup"
              @size="sm"
              @addSpacing={{true}}
            />
            {{t this.config.rulesModification.linkText}}
          </a>
          {{#if (not @isLicenseInvalid)}}
            <div class="mt-xxs">
              <div class="d-inline-block">
                <a
                  class="paragraph-link"
                  data-test-download-curated-ruleset-link
                  href="/api/app/manage/vectra-match/download-vectra-ruleset"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Vc::Icon
                    @iconName="actions/download"
                    @size="xs"
                    @addSpacing={{true}}
                    @class="mx-3"
                  />
                  {{t "Download Vectra Ruleset"}}
                </a>
              </div>
              <div
                class="d-inline-block"
                data-test-download-curated-ruleset-tooltip
              >
                <Vc::Tooltip
                  @position="horizontal"
                  onmouseenter={{this.getRulesetLastUpdated.perform}}
                >
                  {{this.rulesetLastUpdated}}
                </Vc::Tooltip>
              </div>
            </div>
          {{/if}}
        </div>
        <div class="rule-modifications-subheader__filters-panel">
          <Vc::MultiSelect
            @focusOnRender={{false}}
            @dropdownClass={{"rule-modifications-subheader__actions-list-expanded"}}
            @selectedItem={{this.selectedActionsFilter}}
            @onItemSelected={{this.onActionFilterChange}}
            @options={{this.config.rulesModification.filterFields.actionOptions}}
            @prefix={{"Action"}}
            class="rule-modifications-subheader__action-filter"
            data-test--rules-modification-action-filter
          />
          <Vc::Input::Text
            @inputID="search-rules"
            @value={{this.searchTerm}}
            @type="text"
            @placeholder="Signature ID or rule name"
            class="rule-modifications-subheader__search-input"
            @onKeyUp={{this.onKeyUp}}
            @onChange={{this.onSearchTermChange}}
            @searchAndClearButton={{true}}
            data-test--rules-modification-search-input
          />
        </div>
      </div>
      <div class="position-relative">
        <Vc::LoadingContainer
          @isLoading={{this.getRulesModification.isRunning}}
        >
          <Vc::Table
            data-test-rules-modification-table
            @columns={{this.columns}}
            @rows={{this.modificationsTable}}
            @tableActions={{this.tableActions}}
            @timeZone={{this.timezone}}
            @sortBy={{this.sortBy}}
            @canExpand={{true}}
            @hideFooter={{true}}
            as |tbody|
          >
            <tbody.expanded-row as |row|>
              <VectraMatch::SubtleContainer::RulesModification::Table::ExpandoContent
                @row={{row.content}}
              />
            </tbody.expanded-row>
          </Vc::Table>
        </Vc::LoadingContainer>
      </div>
      <Vc::Pagination
        @showPageSizeDropdown={{true}}
        @pageSize={{this.pageSize}}
        @pageSizeOptions={{this.pageSizeOptions}}
        @onPageSizeChange={{this.onPageSizeChange}}
        @onPageSelect={{this.onPageSelect}}
        @currentPageNumber={{this.pageNumber}}
        @totalRecordsCount={{this.modificationsCount}}
      />
    </sc.body>
  </Vc::SubtleContainer>
</div>
{{#if this.showRulesModificationModal}}
  <VectraMatch::SubtleContainer::RulesModification::UpsertRulesModificationModal
    @onClose={{this.onCloseUpsertRulesModificationModal}}
    @refreshTable={{this.getRulesModification}}
    @row={{this.rowToModify}}
    @ruleConflictsHandler={{this.ruleConflictsHandler}}
  />
{{/if}}
{{#if this.showDeleteRulesModal}}
  <VectraMatch::SubtleContainer::RulesModification::DeleteRulesModal
    @config={{this.config.deleteModificationConfig}}
    @primaryAction={{this.deleteTask}}
    @onClose={{fn (mut this.showDeleteRulesModal) false}}
    @secondaryAction={{fn (mut this.showDeleteRulesModal) false}}
  />
{{/if}}
{{#if this.showRuleConflictsModal}}
  <VectraMatch::SubtleContainer::RulesModification::RulesModificationConflictsModal
    @rows={{this.rowConflicts}}
    @onCancel={{queue
      (fn (mut this.showRuleConflictsModal) false)
      (fn (mut this.showRulesModificationModal) false)
    }}
    @onBack={{this.onBackHandler}}
    @onKeepChanges={{perform this.onKeepChanges}}
    @onSave={{perform this.onSaveConflicts}}
  />
{{/if}}