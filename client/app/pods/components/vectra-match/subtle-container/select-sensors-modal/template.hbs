<Vc::Modal
  class="vectra_match_assignment_modal"
  @size="large"
  @type="workflow"
  @isOpen={{@showModal}}
  @onClose={{@onCancel}}
  @primaryText={{this.primaryText}}
  @primaryAction={{this.primaryAction}}
  @primaryIsLoading={{@manageSensors.isRunning}}
  @primaryIsDisabled={{not this.formIsValid}}
  @secondaryText={{this.config.backText}}
  @secondaryAction={{@onPrev}}
  @tertiaryText={{this.config.cancelText}}
  @tertiaryAction={{@onCancel}}
  class="vectra-match__assignment-modal"
  data-test-select-sensors-modal
  as |md|
>
  <md.header>
    {{this.header}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status @primaryText={{this.subheader}} />
  </md.subheader>
  <md.body>
    <Vc::LoadingContainer @isLoading={{this.getSensors.isRunning}}>
      <Vc::Header::SubHeader class="header">
        {{#if this.activatingSensorBanner}}
          <Vc::InlineNotificationBanner
            @isVisible={{true}}
            @type="warning"
            @onClick={{fn (mut this.showActivatingSensorWarning) false}}
          >
            {{this.config.stillEnablingWarning}}
          </Vc::InlineNotificationBanner>
        {{/if}}
      </Vc::Header::SubHeader>
      <div class="table-width">
        <Vc::Table
          @rowSelectionEnabled={{true}}
          @rows={{this.allSensors}}
          @columns={{this.tableColumns}}
          @timeZone={{this.timezone}}
          @onSelectionChanged={{this.onSelectionChanged}}
          @hideFooter={{true}}
          @sortBy={{this.sortBy}}
          @emptySetMessage={{if
            (not-eq this.flow "unassign")
            this.config.emptyTableMessage
            this.config.emptyUnassignTableMessage
          }}
        />
        <Vc::Pagination
          @showPageSizeDropdown={{true}}
          @pageSize={{this.pageSize}}
          @pageSizeOptions={{this.pageSizeOptions}}
          @onPageSizeChange={{this.onPageSizeChange}}
          @onPageSelect={{this.onPageSelect}}
          @currentPageNumber={{if (gt this.sensorCount 0) this.pageNumber ""}}
          @totalRecordsCount={{this.sensorCount}}
          @pageSizeOptionsPosition="above"
        />
      </div>
    </Vc::LoadingContainer>
  </md.body>
</Vc::Modal>

{{#if this.showConfirmationToUnassignModal}}
  <VectraMatch::SubtleContainer::UnassignConfirmationModal
    @primaryText={{this.primaryText}}
    @primaryAction={{this.sendSensors}}
    @showModal={{this.showConfirmationToUnassignModal}}
    @rulesetName={{this.rulesetName}}
    @sensors={{this.selectedSensors}}
    @secondaryText={{this.config.cancelText}}
    @secondaryAction={{@onCancel}}
  />
{{/if}}