<div class="respond-expando-content" data-test-respond-expando-content>
  <Vc::LoadingContainer @isLoading={{this.fetchSummary.isRunning}} />
  {{#if this.fetchSummary.lastSuccessful.completionState}}
    {{#if this.fetchSummaryError}}
      <div class="respond-expando-content__alert-status-container">
        <Vc::Form::Status
          @type="alert"
          @primaryText={{t
            "Failed to load detection summary. Reload page and try again."
          }}
        />
      </div>
    {{else}}
      <Vc::KeyValueList
        @style="columned"
        @display="multi-column"
        @unboldedValue={{true}}
        @allowSeeMore={{true}}
        @direction="ttb"
        @spacing="large"
        class="p-5"
        as |list|
      >
        {{#each this.summary as |summary|}}
          {{#if
            (or
              (not-eq summary.value undefined) (not summary.format.hideOnNull)
            )
          }}
            <list.kv as |kv|>
              <kv.key>
                <:text>
                  {{summary.format.label}}
                </:text>
                <:tooltip>
                  {{#if summary.format.tooltip}}
                    <Vc::Tooltip @position="right">
                      {{summary.format.tooltip}}
                    </Vc::Tooltip>
                  {{/if}}
                </:tooltip>
              </kv.key>
              <kv.value multiline>
                {{#if (eq (prop-type summary.value) "object")}}
                  {{#if summary.value.[0].name}}
                    {{join ", " (map-by "name" summary.value)}}
                  {{else if summary.value.[0].luid}}
                    {{join ", " (map-by "luid" summary.value)}}
                  {{else}}
                    {{join ", " summary.value}}
                  {{/if}}
                {{else}}
                  {{summary.value}}
                {{/if}}
              </kv.value>
            </list.kv>
          {{/if}}
        {{/each}}
      </Vc::KeyValueList>
    {{/if}}
  {{/if}}
</div>