<Vc::ExpandableContainer
  ...attributes
  id={{concat @id "is-expanded-" this.isExpanded}}
  @isExpanded={{this.isExpanded}}
  @onExpandToggle={{this.onExpandToggle}}
  @isLoading={{@isLoading}}
  class="filter-group__container"
  data-test-filter-widget
  as |ec|
>
  <ec.header>
    <div class="vc-typ-subheader" data-test-filter-group-header>
      {{@label}}
    </div>
  </ec.header>
  <ec.body>
    {{#unless @isLoading}}
      {{#each @filters as |filter index|}}
        {{#unless (and (eq filter.count 0) @hideZero)}}
          <div
            class="filter-item__container d-flex align-items-start"
            data-test-filter-item
          >
            <div
              class="vc-checkbox__container--overflow-hidden my-2 flex-grow-0"
            >
              <Vc::Checkbox
                id={{concat "filter-widget-label-" filter.label}}
                @onClick={{queue
                  (fn (toggle "isSelected" filter))
                  (perform @updateParams @keyName filter)
                  @onChange
                }}
                @isChecked={{or filter.isSelected false}}
                @isDisabled={{or
                  (and (not (is-anchor-mode)) (not filter.count))
                  (and (is-anchor-mode) (get-label filter.id @errors))
                }}
                data-test-filter-checkbox={{index}}
              >
                <:label>
                  <div data-test-checkbox-value>
                    <Vc::Overflow
                      @value={{filter.label}}
                      @attachedElementId="body"
                      @tooltipSize="large"
                    />
                  </div>
                </:label>
              </Vc::Checkbox>
            </div>
            {{#if (get-label filter.id @errors)}}
              {{#let (select-option filter.id @errors "id") as |error|}}
                {{#if (eq error.label 403)}}
                  <div
                    class="filter-group__container__in-app-helper"
                    {{on "mouseenter" (fn (mut error.isPopupVisible) true)}}
                    data-test-filter-widget--in-app-help
                  >
                    <Vc::InAppHelp
                      @attachedElementId="body"
                      @customPopoutCss="in-app-help__attached_body"
                      @header={{error.header}}
                      @content={{error.content}}
                      @onClose={{fn (mut error.isPopupVisible) false}}
                      @showPopout={{error.isPopupVisible}}
                      @icon="misc-alert"
                      class="ms-0"
                    />
                  </div>
                {{else}}
                  <Vc::Tooltip
                    @attachedElementId="body"
                    @customCss="tooltip__attached_body"
                    @position="right"
                    @isInline={{true}}
                    @iconName="misc-warning"
                  >
                    <div>{{t error.tooltipContent}}</div>
                  </Vc::Tooltip>
                {{/if}}
              {{/let}}
            {{/if}}
            {{#unless (is-anchor-mode)}}
              <div
                class="d-flex align-items-center justify-content-end flex-grow-1 mt-3"
              >
                <div
                  class={{concat
                    "pe-2"
                    (if (not filter.count) " filter-count--disabled")
                  }}
                  data-test-filter-item-count
                >
                  {{concat "(" (or filter.count "0") ")"}}
                </div>
              </div>
            {{/unless}}
          </div>
        {{/unless}}
      {{/each}}
    {{/unless}}
  </ec.body>
</Vc::ExpandableContainer>