<div class="d-flex pt-xl">
  <Vc::Dropdown::Select
    @label="Reporting Timeframe"
    @options={{this.datepickerPresets}}
    @selectedItem={{this.selectedPreset.label}}
    @onItemSelected={{this.onSelectPreset}}
    @searchEnabled={{false}}
    @hasFixedWidth={{true}}
  />
  <Vc::Dropdown::Select
    class="ps-m"
    @label="Attack Surface"
    @options={{this.dataSourceOptions}}
    @selectedItem={{this.selectedAttackSurfacePreset.label}}
    @onItemSelected={{this.onSelectAttackSurfacePreset}}
    @searchEnabled={{false}}
    @hasFixedWidth={{true}}
  />
</div>

<Vc::Text class="text-subtle">{{this.presetDescription}}</Vc::Text>