<Vc::WidgetContainer
  class="{{this.styleNamespace}} member-impact"
  @showBorder={{true}}
  as |wc|
>
  <wc.header>
    <div class="member-impact__header">
      <div data-test-member-impact-header>
        {{this.widgetHeader}}
      </div>
      {{#if (not this.displayPreviewButtonCenter)}}
        <div data-test-header-preview-button>
          <Manage::Groups::PreviewButton
            @isDisabled={{this.isHeaderPreviewDisabled}}
            @label={{this.config.widgetContainer.rerunButton}}
            @tooltipLabel={{this.config.widgetContainer.rerunTooltip}}
            @memberEvaluation={{this.memberEvaluation}}
          />
        </div>
      {{/if}}
    </div>
  </wc.header>
  <wc.body>
    {{#if
      (and
        (not this.displayPreviewButtonCenter) (not this.isHeaderPreviewDisabled)
      )
    }}
      <div class="mb-l">
        <Vc::InlineNotificationBanner
          @isVisible={{true}}
          @accessibilityText={{this.config.widgetContainer.inlineBanner}}
          @hideCloseIcon={{true}}
        >
          <Vc::Text::Bold>
            {{this.config.widgetContainer.inlineBanner}}
          </Vc::Text::Bold>
        </Vc::InlineNotificationBanner>
      </div>
    {{/if}}
    <div class={{this.wrapperClass}}>
      <Vc::LoadingContainer @isLoading={{this.memberEvaluation.isRunning}}>
        {{#if this.displayPreviewButtonCenter}}
          <div data-test-center-preview-button>
            <Manage::Groups::PreviewButton
              @isDisabled={{this.isCenterPreviewDisabled}}
              @label={{this.config.widgetContainer.previewButton}}
              @tooltipLabel={{this.centerPreviewTooltipText}}
              @memberEvaluation={{this.memberEvaluation}}
            />
          </div>
        {{/if}}
        {{#if (and this.emptyMemberImpact (not @transitionGroup))}}
          <Vc::Text data-test-member-impact-empty-response>
            {{this.config.widgetContainer.emptyImpactMessage}}
          </Vc::Text>
        {{else if this.memberImpact}}
          <div class="d-flex">
            <div
              class={{if
                this.groupId
                "member-impact__widget-content__add-members"
              }}
            >
              <Manage::Groups::MemberImpactList
                @totalCount={{this.memberImpact.membersAdded}}
                @members={{this.memberImpact.add}}
                @isEditState={{this.groupId}}
              />
            </div>
            {{#if this.groupId}}
              <div class="member-impact__widget-content__divider-wrapper">
                <div class="member-impact__widget-content__divider"></div>
              </div>
              <div class="member-impact__widget-content__remove-members">
                <Manage::Groups::MemberImpactList
                  @totalCount={{this.memberImpact.membersRemoved}}
                  @members={{this.memberImpact.remove}}
                  @isEditState={{this.groupId}}
                  @isRemoveList={{true}}
                  @transitionGroup={{@transitionGroup}}
                />
              </div>
            {{/if}}
          </div>
        {{/if}}
      </Vc::LoadingContainer>
    </div>
  </wc.body>
</Vc::WidgetContainer>