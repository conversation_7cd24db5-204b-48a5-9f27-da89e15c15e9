<Vc::Modal
  class="{{this.styleNamespace}} regex-modal container"
  @size="medium"
  @type="workflow"
  @isOpen={{true}}
  @onClose={{@onClose}}
  @primaryText={{this.primaryText}}
  @primaryAction={{perform this.primaryAction}}
  @primaryIsLoading={{this.isPrimaryActionLoading}}
  @secondaryText={{this.secondaryText}}
  @secondaryAction={{this.secondaryAction}}
  @tertiaryText={{this.tertiaryText}}
  @tertiaryAction={{@onClose}}
  data-test-regex-modal
  as |md|
>
  <md.header>
    {{this.modalHeader}}
    <Vc::InAppHelp
      @content={{this.inAppHelpContent}}
      @header={{this.inAppHelpHeader}}
      @onOpen={{fn (mut this.showPopout) true}}
      @onClose={{fn (mut this.showPopout) false}}
      @showPopout={{this.showPopout}}
      class="ms-4"
    />
  </md.header>
  <md.subheader>
    {{#if @transitionGroup}}
      <div class="d-flex flex-column justify-content-between">
        <div class="mb-m">
          {{this.modalSubHeader}}
        </div>
        <Vc::Form::Status
          data-test-transition-group-subheader
          @type="warning"
          @primaryText={{this.modalTransitionGroupSubHeader}}
        />
      </div>
    {{else}}
      {{this.modalSubHeader}}
    {{/if}}
  </md.subheader>
  <md.body class="vcl-row">
    <div class="col">
      <Vc::Input::Text
        class="regex-modal__regex-input mt-m mb-s"
        @label={{this.config.addRegexModal.regexInputLabel}}
        @helpContent={{this.regexInputHelperText}}
        @value={{@changesetObject.changeset.regex}}
        @onChange={{fn (mut @changesetObject.changeset.regex)}}
        @hasError={{@changesetObject.changeset.error.regex}}
        @errorMessage={{@changesetObject.changeset.error.regex.validation.firstObject}}
        fit-content
      />
      <a
        href={{this.config.addRegexModal.quickReferenceLink}}
        target="_blank"
        rel="noopener noreferrer"
        data-test-quick-reference-link
      >
        <Vc::Icon
          @iconName="misc-popup"
          @size="sm"
          @addSpacing={{true}}
          @class="regex-modal__icon--misc-popup"
        />
        {{this.config.addRegexModal.quickReferenceText}}
      </a>
      <div class="mt-l">
        <Manage::Groups::MemberImpactWidget
          @changesetObject={{@changesetObject}}
          @transitionGroup={{@transitionGroup}}
        />
      </div>
    </div>
  </md.body>
</Vc::Modal>