<div>
  {{#if this.isTransitionGroupWithNoMembersRemoved}}
    <Vc::Text::Bold class="mb-s" data-test-member-impact-count>
      <Vc::Form::Status
        @type="success"
        @primaryText="No members will be removed"
        @accessibilityText="No members will be removed"
      />
    </Vc::Text::Bold>
  {{else}}
    <Vc::Text::Bold class="mb-s" data-test-member-impact-count>
      {{this.count}}
    </Vc::Text::Bold>
    {{#if this.hasZeroMatches}}
      <Vc::Text class="text color-subtle" data-test-no-additional-members>
        {{this.zeroMatchesMessage}}
      </Vc::Text>
    {{/if}}
  {{/if}}
  {{#each @members as |member|}}
    <Vc::Text class="member-impact__member" data-test-member-impact-member>
      <Vc::Overflow @value={{member}} />
    </Vc::Text>
  {{/each}}
</div>