<span id="member-impact-preview-button" data-test-preview-button>
  <Vc::Button::Action
    @class="member-impact__preview-button"
    @onClick={{perform @memberEvaluation}}
    @type="secondary"
    @isDisabled={{@isDisabled}}
    @accessibilityText={{@label}}
    as |ba|
  >
    <ba.label>
      <div class="d-flex align-items-center">
        <Vc::Icon
          @class="member-impact__preview-button-icon{{if
            @isDisabled
            '__disabled'
          }}"
          @iconName="notable-events"
        />
        {{t @label}}
      </div>
    </ba.label>
  </Vc::Button::Action>
</span>
{{#if @isDisabled}}
  <Vc::Tooltip
    @position="bottom"
    @hideIcon={{true}}
    @attachedElementId="body"
    @targetId="member-impact-preview-button"
    data-test-preview-tooltip
  >
    {{@tooltipLabel}}
  </Vc::Tooltip>
{{/if}}