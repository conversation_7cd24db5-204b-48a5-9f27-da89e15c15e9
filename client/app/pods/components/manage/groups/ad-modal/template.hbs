<Vc::Modal
  class="{{this.styleNamespace}}"
  @size="medium"
  @type="workflow"
  @isOpen={{true}}
  @onClose={{@onClose}}
  @primaryText={{this.textByState.primaryText}}
  @primaryAction={{perform this.primaryActionByState}}
  @primaryIsLoading={{this.isPrimaryActionLoading}}
  @secondaryText={{this.secondaryText}}
  @secondaryAction={{this.secondaryActionByState}}
  @tertiaryText={{this.tertiaryText}}
  @tertiaryAction={{@onClose}}
  data-test-ad-modal
  {{did-insert (perform this.getADGroups this.adProfileId this.searchString)}}
  as |md|
>
  <md.header>
    {{this.textByState.modalHeader}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      data-test-ad-group-subheader
      @type="warning"
      @primaryText={{this.textByState.modalSubHeader}}
    />
  </md.subheader>
  <md.body>
    {{#if this.showNoGroupsSelectedError}}
      <div class="pb-3">
        <Vc::InlineNotificationBanner
          @isVisible={{true}}
          @accessibilityText={{this.noGroupsSelectedMessage}}
          @hideCloseIcon={{true}}
          @type="alert"
        >
          {{this.noGroupsSelectedMessage}}
        </Vc::InlineNotificationBanner>
      </div>
    {{/if}}
    {{#if (eq this.state "import")}}
      <Vc::LoadingContainer
        @isLoading={{this.getADGroups.isRunning}}
        data-test-ad-modal-loading
      >
        <div class="vcl-grid">
          <div class="vcl-row row-cols-2 py-4">
            <div class="col-3">
              <Vc::Dropdown::Select
                @label="AD Profile"
                @options={{this.adProfiles}}
                @onItemSelected={{this.updateSelectedADProfile}}
                @tooltip={{this.config.adImportModal.profileFilterTooltip}}
                @selectedItem={{this.selectedADProfile}}
                data-test-ad-profile-select
              />
            </div>
            <div class="col-9">
              <Vc::Input::Text
                class="ad-search-input"
                @type="text"
                @label={{this.config.adImportModal.searchLabel}}
                @placeholder={{this.config.adImportModal.searchPlaceholder}}
                @tooltipProps={{this.config.adImportModal.searchTooltip}}
                @searchAndClearButton={{true}}
                @onChange={{this.updateSearchString}}
                data-test-ad-search-input
                fit-content
              />
            </div>
          </div>

          {{#if
            (and
              (gt this.getADGroups.lastSuccessful.value.length 0)
              (not this.getADGroups.isRunning)
            )
          }}
            <div class="vcl-row">
              <div class="ad-select-all-buttons py-3">
                <Vc::Button::Action
                  @type="secondary"
                  @onClick={{this.selectAllCheckboxes}}
                  @isDisabled={{this.allGroupsSelected}}
                  data-test-ad-select-all-button
                  as |ba|
                >
                  <ba.label>
                    {{t "Select All"}}
                  </ba.label>
                </Vc::Button::Action>
                <span>{{"|"}}</span>
                <Vc::Button::Action
                  @type="secondary"
                  @onClick={{this.deselectAllCheckboxes}}
                  @isDisabled={{not this.anyGroupSelected}}
                  data-test-ad-deselect-all-button
                  as |ba|
                >
                  <ba.label>
                    {{t "Deselect All"}}
                  </ba.label>
                </Vc::Button::Action>
              </div>
            </div>
            <div class="vcl-row">
              <div class={{this.classSpacing}}>
                <Vc::Text
                  data-test-group-adGroup-column
                  class="d-flex"
                >{{this.tableImportColumnHeaders.adGroup}}
                  <Vc::Tooltip
                    @position="horizontal"
                    @isInline={{true}}
                    @size="medium"
                    data-test-ad-modal-list-tooltip
                  >
                    <div>{{this.tooltipContent}}</div>
                  </Vc::Tooltip>
                </Vc::Text>
              </div>
              <div class="col-4">
                <Vc::Text
                  data-test-group-name-column
                >{{this.tableImportColumnHeaders.name}}</Vc::Text>
              </div>
              {{#unless (feature-enabled "appliance_only")}}
                <div class="col-2">
                  <Vc::Text
                    data-test-group-importance-column
                  >{{this.tableImportColumnHeaders.importance}}</Vc::Text>
                </div>
              {{/unless}}
            </div>
          {{/if}}
          {{#if
            (and
              (eq this.getADGroups.lastSuccessful.value.length 0)
              (not this.getADGroups.isRunning)
            )
          }}
            <Vc::Text
              class="d-flex flex-row justify-content-center pt-8"
              data-test-ad-group-list-empty-response
            >
              {{this.noADGroupsMessage}}
            </Vc::Text>
          {{else}}
            {{#each
              @changesetObject.changeset.adGroups key="checked"
              as |group index|
            }}
              <Manage::Groups::AdGroupListWidget
                @adGroup={{group}}
                @index={{index}}
                @changesetObject={{@changesetObject}}
                @hasCheckboxError={{this.hasCheckboxError}}
                @updateCheckboxValue={{this.updateCheckboxValue}}
              />
            {{/each}}
          {{/if}}
        </div>
      </Vc::LoadingContainer>
    {{else}}
      <div {{did-insert (perform this.membersTask)}}>
        <Vc::Table
          @columns={{this.currentColumns}}
          @rows={{this.previewRows}}
          @hideFooter={{true}}
          @canExpand={{false}}
          @showActionsOnHover={{true}}
        />
      </div>
    {{/if}}
  </md.body>
</Vc::Modal>