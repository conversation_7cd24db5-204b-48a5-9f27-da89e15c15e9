import Component from '@glimmer/component';
import podNames from 'ember-component-css/pod-names';
import { tracked } from '@glimmer/tracking';
import { task, dropTask } from 'ember-concurrency';
import { inject as service } from '@ember/service';
import { restartableTask } from 'ember-concurrency-decorators';
import { jsonHttpRequest } from 'vectra/utils/request-common';
import config from 'vectra/reference-data/manage/groups/config';
import { action } from '@ember/object';
import {
  adGroupsPreviewColumnsRUX,
  adGroupsPreviewColumnsQUX,
  adGroupsPreviewMembersColumn,
  adGroupsPreviewLoadingColumn,
} from 'vectra/columns/manage/groups';

export default class extends Component {
  config = config;
  @service settings;

  @tracked adGroups;
  @tracked state = 'import';
  @tracked formData = [];
  @tracked taskId = null;
  @tracked hasCheckboxError = false;
  @tracked currentColumns = [
    ...(this.settings.featureEnabled('appliance_only') ? adGroupsPreviewColumnsQUX : adGroupsPreviewColumnsRUX),
    ...adGroupsPreviewLoadingColumn,
  ];

  @tracked showNoGroupsSelectedError = false;
  @tracked members = [];
  @tracked searchString = '';
  @tracked adProfileId = null;

  @service changeset;
  @service toast;
  @service router;
  @service groups;
  @service taskResult;

  get styleNamespace() {
    return podNames['manage/groups/ad-modal'];
  }

  get tooltipContent() {
    return this.config.adImportModal.nestedGroupsTooltip;
  }

  get tableImportColumnHeaders() {
    return this.config.adImportModal.tableColumnHeaders;
  }

  get noADGroupsMessage() {
    return this.config.adImportModal.noADGroupsMessage;
  }

  get noGroupsSelectedMessage() {
    return this.config.adImportModal.noGroupsSelectedMessage;
  }

  get secondaryText() {
    return this.config.adImportModal.backText;
  }

  get selectedADProfile() {
    return this.adProfileId ? this.adProfileId.label : 'All';
  }

  get classSpacing() {
    return this.settings.featureEnabled('appliance_only') ? 'col-8' : 'col-6';
  }

  get allGroupsSelected() {
    let adGroups = this.args.changesetObject.changeset.get('adGroups');
    if (Object.keys(adGroups ?? []).length === 0) {
      return false;
    }
    return adGroups.every(group => group.checked);
  }

  get anyGroupSelected() {
    let adGroups = this.args.changesetObject.changeset.get('adGroups');
    if (Object.keys(adGroups ?? []).length === 0) {
      return false;
    }
    return adGroups.some(group => group.checked);
  }

  get adProfiles() {
    let adProfiles = this.args.changesetObject.changeset.get('adProfiles');
    let options = [{ value: null, label: 'All' }];
    if (adProfiles === null || adProfiles === undefined || Object.keys(adProfiles).length === 0) {
      return options;
    }
    const profiles = Object.entries(adProfiles).map(([key, value]) => {
      return {
        value: key,
        label: value.query.ad_profile_name,
      };
    });
    return [...options, ...profiles];
  }

  get secondaryAction() {
    if (this.state === 'import') {
      return this.args.transitionToCreateModal;
    } else {
      this.state = 'import';
      this.members = [];
      return {};
    }
  }

  get tertiaryText() {
    return this.config.adImportModal.cancelText;
  }

  get textByState() {
    if (this.state === 'import') {
      return {
        modalHeader: 'AD Groups Import',
        modalSubHeader: this.config.adImportModal.subHeader,
        primaryText: this.config.adImportModal.groupsPreviewText,
      };
    } else {
      return {
        modalHeader: 'Groups Preview',
        modalSubHeader: this.config.groupsPreviewModal.subHeader,
        primaryText: this.config.groupsPreviewModal.createGroupsText,
      };
    }
  }

  get primaryActionByState() {
    return this.state === 'import' ? this.transitionToGroupsPreview : this.createGroups;
  }

  get previewRows() {
    let selectedGroups = this.args.changesetObject.changeset.get('adGroups').filter(group => group.checked);
    if (this.members && !this.membersTask.isRunning) {
      let formattedRows = selectedGroups.map(group => {
        let membersCount = this.members.find(({ dn }) => dn === group.distinguishedName)?.membersAdded;
        return {
          ...group,
          membersCount,
        };
      });
      return formattedRows;
    } else {
      let formattedRows = selectedGroups.map(group => {
        return {
          ...group,
          membersLoading: {
            type: 'loading',
            primaryText: '',
          },
        };
      });
      return formattedRows;
    }
  }

  get secondaryActionByState() {
    return this.state === 'import' ? this.args.transitionToCreateModal : this.transitionToImport;
  }

  get groupType() {
    return this.args.changesetObject.changeset.get('groupType');
  }

  @action
  transitionToImport() {
    this.state = 'import';
  }

  @action
  updateCheckboxValue(index) {
    let adGroups = this.args.changesetObject.changeset.get('adGroups');
    let adGroup = adGroups[index];
    adGroup.checked = !adGroup.checked;
    this.args.changesetObject.changeset.set('adGroups', adGroups);
    if (this.hasCheckboxError) {
      this.hasCheckboxError = false;
    }
  }

  @action
  selectAllCheckboxes() {
    let adGroups = this.args.changesetObject.changeset.get('adGroups');
    adGroups.forEach(group => {
      group.checked = true;
    });
    this.args.changesetObject.changeset.set('adGroups', adGroups);

    this.hasCheckboxError = false;
  }

  @action
  deselectAllCheckboxes() {
    let adGroups = this.args.changesetObject.changeset.get('adGroups');
    adGroups.forEach(group => {
      group.checked = false;
    });
    this.args.changesetObject.changeset.set('adGroups', adGroups);
    this.hasCheckboxError = false;
  }

  @action
  updatePreviewColumns(result) {
    let adGroupsPreviewColumns = this.settings.featureEnabled('appliance_only')
      ? adGroupsPreviewColumnsQUX
      : adGroupsPreviewColumnsRUX;
    if (result) {
      this.currentColumns = [...adGroupsPreviewColumns, ...adGroupsPreviewMembersColumn];
    }
  }

  @action
  updateSearchString(searchString) {
    this.searchString = searchString;
    this.getADGroups.perform(this.adProfileId?.value, searchString);
  }

  @action
  updateSelectedADProfile(adProfileId) {
    if (!adProfileId || adProfileId.value === null) {
      this.adProfileId = null;
      this.getADGroups.perform(null, this.searchString);
    } else {
      this.adProfileId = adProfileId;
      this.getADGroups.perform(this.adProfileId.value, this.searchString);
    }
  }

  @task
  *transitionToGroupsPreview() {
    // Validate name can't be blank
    let isValid = yield this.changeset.validate(this.args.changesetObject);
    if (!isValid) {
      this.showNoGroupsSelectedError = false;
      return;
    }

    let adGroups = this.args.changesetObject.changeset.get('adGroups');

    // Validate there is a group checked
    let hasCheckedGroup = adGroups.some(group => group.checked);
    if (!hasCheckedGroup) {
      this.hasCheckboxError = true;
      this.showNoGroupsSelectedError = true;
      return;
    }

    try {
      let result = yield this.memberEvaluation.perform();
      if (result) {
        this.state = 'preview';
        this.showNoGroupsSelectedError = false;
      }
    } catch (err) {
      console.error(err);
    }
  }

  @dropTask
  *memberEvaluation() {
    let groups = this.args.changesetObject.changeset.get('adGroups');
    let selected = groups.filter(group => group.checked);

    let payload = {
      adGroups: selected,
      groupType: this.groupType,
    };

    let url = '/api/app/adGroups/memberImpact';
    let method = 'POST';

    try {
      let response = yield jsonHttpRequest(url, payload, { method });
      this.taskId = response.previewTaskId;
      return response;
    } catch (err) {
      return null;
    }
  }

  @restartableTask
  *getADGroups(adProfileId = null, searchString = null) {
    let url = `/api/app/adGroups`;
    if (adProfileId) {
      url += `?adProfileId=${adProfileId}`;
    }
    if (searchString) {
      if (adProfileId) {
        url += `&`;
      } else {
        url += `?`;
      }
      url += `dn=${searchString}`;
    }
    try {
      let response = yield jsonHttpRequest(url);
      let groups = response.results;
      if (groups === undefined || groups.length === 0) {
        this.args.changesetObject.changeset.set('adGroups', []);
        return [];
      } else {
        let adGroups = groups.map(group => ({
          distinguishedName: group.distinguishedName,
          groupName: group.suggestedGroupName,
          importance: 'Medium', // Default to Medium
          checked: false,
        }));
        this.args.changesetObject.changeset.set('adGroups', adGroups);
        return adGroups;
      }
    } catch (err) {
      console.error(err);
      return {};
    }
  }

  @dropTask
  *membersTask() {
    try {
      let result = yield this.taskResult.pollForTaskResult.perform(this.taskId);
      this.updatePreviewColumns(result);
      this.members = result;
    } catch (err) {
      this.toast.error(this.config.groupsPreviewModal.memberImpactErrorToast);
      console.error(err);
    }
  }

  @dropTask
  *createGroups() {
    let payloadObj = this.groups.getAdGroupPayload(this.args.changesetObject);
    let url = '/api/app/adGroups';
    let method = 'POST';
    try {
      let response = yield jsonHttpRequest(url, payloadObj, { method });
      this.args.onClose();
      if (response.group.length === 1) {
        this.toast.success(this.config.groupsPreviewModal.successfulGroupToast);
        this.router.transitionTo(`/manage/group/${response.group[0].id}`);
      } else {
        this.toast.success(this.config.groupsPreviewModal.successfulGroupsToast);
        this.router.transitionTo('/manage/groups');
      }
    } catch (err) {
      this.toast.error(this.config.groupsPreviewModal.failedToast);
    }
  }
}
