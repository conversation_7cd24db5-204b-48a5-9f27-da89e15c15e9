<div
  class="{{this.styleNamespace}}
    list-widget vcl-row mb-4 d-flex align-items-start"
  data-test-ad-modal-list-widget
>
  <div class={{concat this.classSpacing " pt-3"}}>
    <Vc::Checkbox
      @isChecked={{@adGroup.checked}}
      @onClick={{action @updateCheckboxValue @index}}
      @label={{@adGroup.distinguishedName}}
      @hasError={{@hasCheckboxError}}
      data-test-ad-group-checkbox
    />
  </div>
  <div class="col-4">
    <Vc::Input::Text
      class="list-widget__name-input"
      @value={{@adGroup.groupName}}
      @type="text"
      @onChange={{fn (mut @adGroup.groupName)}}
      @hasError={{this.nameError}}
      @errorMessage={{this.nameError}}
      data-test-ad-group-name-input
      fit-content
    />
  </div>
  {{#unless (feature-enabled "appliance_only")}}
    <div class="col-2">
      <Vc::Dropdown::Select
        @options={{this.importanceOptions}}
        @selectedItem={{@adGroup.importance}}
        @onItemSelected={{fn (mut @adGroup.importance)}}
        data-test-ad-group-importance-select
      />
    </div>
  {{/unless}}
</div>