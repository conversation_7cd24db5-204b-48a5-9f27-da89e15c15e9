<Vc::Modal
  @isOpen={{true}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  @secondaryText={{if @changeset.changes.length "Cancel" "Close"}}
  @secondaryAction={{@onClose}}
  @primaryText="Generate Credentials"
  @primaryAction={{perform @submitTask}}
  @primaryIsLoading={{@submitTask.isRunning}}
  as |md|
>
  <md.header>
    {{t "Add API Client"}}
  </md.header>
  <md.body>
    <Vc::Input::Text
      class="mb-4"
      @inputID="api-client-name-input"
      @value={{@changeset.name}}
      @type="text"
      @label="Client Name"
      @hasError={{if @changeset.error.name true}}
      @errorMessage={{@changeset.error.name.validation.firstObject}}
      @onChange={{fn (mut @changeset.name)}}
    />
    <Vc::Dropdown::Select
      class="mb-6"
      @dropdownID="api-client-role-dropdown"
      @label="Role"
      @options={{@availableRoles}}
      @selectedItem={{@changeset.role}}
      @onItemSelected={{fn (mut @changeset.role)}}
      @hasFixedWidth={{true}}
      @hasError={{if @changeset.error.role true}}
      @errorMessage={{@changeset.error.role.validation.firstObject}}
      @helperContent="Roles may be edited in the Roles Tab"
    />
    <Vc::Input::Textarea
      @textareaID="api-client-description-textarea"
      @label="Description"
      @value={{@changeset.description}}
      @hasError={{if @changeset.error.description true}}
      @errorMessage={{@changeset.error.description.validation.firstObject}}
    />
  </md.body>
</Vc::Modal>