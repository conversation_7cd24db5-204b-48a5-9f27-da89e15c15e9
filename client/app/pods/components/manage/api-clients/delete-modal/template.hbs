<Vc::Modal
  @isOpen={{true}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  @secondaryText="Cancel"
  @secondaryAction={{@onClose}}
  @primaryText="Remove API Client"
  @primaryAction={{perform @submitTask @client.clientId}}
  @primaryIsLoading={{@submitTask.isRunning}}
  as |md|
>
  <md.header>
    {{t "Remove API Client"}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      @type="warning"
      @primaryText="If you remove this API Client the API connection will break"
    />
  </md.subheader>
  <md.body>
    <Vc::KeyValueList as |list|>
      <list.kv as |kv|>
        <kv.key>
          {{t "Client Name"}}
        </kv.key>
        <kv.value>
          {{@client.name}}
        </kv.value>
      </list.kv>

      <list.kv class="mt-4" as |kv|>
        <kv.key>
          {{t "Client ID"}}
        </kv.key>
        <kv.value>
          {{@client.clientId}}
        </kv.value>
      </list.kv>

      {{#if @client.description}}
        <list.kv class="mt-4" as |kv|>
          <kv.key>
            {{t "Description"}}
          </kv.key>
          <kv.value>
            {{@client.description}}
          </kv.value>
        </list.kv>
      {{/if}}
    </Vc::KeyValueList>
  </md.body>
</Vc::Modal>