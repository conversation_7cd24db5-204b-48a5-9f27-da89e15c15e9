<Vc::Modal
  class="api-client__enable-child-modal"
  @isOpen={{true}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  @secondaryText={{if @changeset.changes.length "Cancel" "Close"}}
  @secondaryAction={{@onClose}}
  @primaryText="Generate Credentials"
  @primaryAction={{perform @submitTask}}
  @primaryIsLoading={{@submitTask.isRunning}}
  data-test--enable-child-modal
  as |md|
>
  <md.header>
    {{t this.content.title}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status @primaryText={{t this.content.subtitle}}>
      <ol type="1" class="ms-3">
        {{#each this.content.subtitleContents as |content|}}
          <li>{{t content}}</li>
        {{/each}}
      </ol>
    </Vc::Form::Status>
  </md.subheader>
  <md.body>
    <Vc::KeyValue class="mt-6" as |kv|>
      <kv.key>
        {{t this.content.role.key}}
      </kv.key>
      <kv.value>
        {{t this.content.role.value}}
        <Vc::Tooltip @position="right">
          {{t this.content.role.tooltip}}
        </Vc::Tooltip>
      </kv.value>
    </Vc::KeyValue>
    <Vc::Input::Text
      class="mt-5"
      @value={{@changeset.name}}
      @type="text"
      @label={{t this.content.clientName.label}}
      @hasError={{if @changeset.error.name true}}
      @errorMessage={{@changeset.error.name.validation.firstObject}}
      @onChange={{fn (mut @changeset.name)}}
      @tooltipProps={{t this.content.clientName.tooltip}}
      data-test--text--name
    />
    <Vc::Input::Textarea
      class="mt-5"
      @label={{t this.content.description.label}}
      @value={{@changeset.description}}
      @hasError={{if @changeset.error.description true}}
      @errorMessage={{@changeset.error.description.validation.firstObject}}
      data-test--textarea--description
    />
    <Vc::Input::Text
      class="mt-5"
      @type="text"
      @label={{t this.content.instanceName.label}}
      @helpContent={{t this.content.instanceName.helpContent}}
      @tooltipProps={{t this.content.instanceName.tooltip}}
      @value={{@changeset.instanceName}}
      @hasError={{if @changeset.error.instanceName true}}
      @errorMessage={{@changeset.error.instanceName.validation.firstObject}}
      @onChange={{fn (mut @changeset.instanceName)}}
      data-test--text--instance-name
    />
  </md.body>
</Vc::Modal>