<Vc::Modal
  class="api-clients__api-secret-modal"
  @isOpen={{true}}
  @onClose={{@onClose}}
  @type="workflow"
  @size="small"
  @secondaryText="Done"
  @secondaryAction={{@onClose}}
  data-test--api-secret-modal
  as |md|
>
  <md.header>
    {{t this.currentContent.title}}
    {{#unless this.isGlobalViewSecret}}
      <Vc::InAppHelp
        @content={{hash
          description=(concat
            "Follow the documentation instructions to establish a connection between "
            (product-name)
            " API and the external tool"
          )
          links=this.articleLinks
        }}
        @header="How to create API Client connections"
        @onOpen={{@onOpenInAppHelp}}
        @onClose={{@onCloseInAppHelp}}
        @showPopout={{@showInAppHelp}}
      />
    {{/unless}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      @type="warning"
      @primaryText={{t this.currentContent.subtitle}}
    />
  </md.subheader>
  <md.body>
    <Vc::Input::Copyable
      data-test-client-id
      class="mt-4
        {{if this.isGlobalViewSecret 'api-clients__api-secret-modal--wider'}}"
      @inputID="client-id-input"
      @value={{@clientId}}
      @type="text"
      @label="Client ID"
      @isImmutable={{true}}
      @onCopySuccess={{@onCopyClipboard}}
    />
    <Vc::Input::Copyable
      data-test-client-secret
      class="mt-4
        {{if this.isGlobalViewSecret 'api-clients__api-secret-modal--wider'}}"
      @inputID="secret-key-input"
      @value={{@clientSecret}}
      @type="password"
      @label="Secret Key"
      @isImmutable={{true}}
      @helpContent={{t this.currentContent.helpContent}}
      @onCopySuccess={{@onCopyClipboard}}
    />
  </md.body>
</Vc::Modal>