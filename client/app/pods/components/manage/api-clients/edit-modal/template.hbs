<Vc::Modal
  @isOpen={{true}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  @secondaryText={{if @changeset.changes "Cancel" "Close"}}
  @secondaryAction={{@onClose}}
  @primaryText="Save"
  @primaryAction={{perform @submitTask}}
  @primaryIsLoading={{@submitTask.isRunning}}
  as |md|
>
  <md.header>
    {{t "Edit API Client"}}
  </md.header>
  <md.body>
    <Vc::Input::Text
      class="mb-4"
      @inputID="api-edit-client-name-input"
      @value={{@changeset.name}}
      @type="text"
      @label="Client Name"
      @hasError={{if @changeset.error.name true}}
      @errorMessage={{@changeset.error.name.validation.firstObject}}
      @onChange={{fn (mut @changeset.name)}}
    />
    <Vc::Dropdown::Select
      data-test-edit-modal-helper-content
      class="mb-6"
      @dropdownID="api-edit-client-role-dropdown"
      @label="Role"
      @options={{@availableRoles}}
      @selectedItem={{@changeset.role.code}}
      @hasFixedWidth={{true}}
      @isDisabled={{true}}
      @helperContent="Roles may be edited in the Roles Tab"
    />
    <Vc::Input::Textarea
      @textareaID="api-edit-client-description-textarea"
      @label="Description"
      @value={{@changeset.description}}
      @hasError={{if @changeset.error.description true}}
      @errorMessage={{@changeset.error.description.validation.firstObject}}
    />
  </md.body>
</Vc::Modal>