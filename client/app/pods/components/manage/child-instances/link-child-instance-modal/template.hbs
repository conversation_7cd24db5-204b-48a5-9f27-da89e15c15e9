<Vc::Modal
  class="child-instances__link-child-modal"
  @isOpen={{true}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  @secondaryText={{if @changeset.changes.length "Cancel" "Close"}}
  @secondaryAction={{@onClose}}
  @primaryText={{this.content.primaryText}}
  @primaryAction={{perform @submitTask}}
  @primaryIsDisabled={{this.postLinkActionsDisabled}}
  @primaryIsLoading={{@submitTask.isRunning}}
  data-test--link-child-instance-modal
  as |md|
>
  <md.header>
    {{t this.content.title}}
    <Vc::InAppHelp
      @header={{t this.content.subtitleTooltipTitle}}
      @content={{hash
        description=""
        links=(array
          (hash
            icon=this.content.subtitleTooltipIcon
            link=this.content.subtitleTooltipLink
            text=this.content.subtitleTooltipText
            type=this.content.subtitleTooltipType
          )
        )
      }}
      @onOpen={{fn (mut this.showPopout) true}}
      @onClose={{fn (mut this.showPopout) false}}
      @showPopout={{this.showPopout}}
    >
      {{t this.content.subtitleTooltipContent}}
    </Vc::InAppHelp>
  </md.header>
  <md.subheader>
    <Vc::Form::Status @primaryText={{t this.content.subtitle}}>
      <ol type="1" class="ms-3">
        {{#each this.content.subtitleContents as |content|}}
          <li>{{t content}}</li>
        {{/each}}
      </ol>
    </Vc::Form::Status>
  </md.subheader>
  <md.body>
    <Vc::Input::Text
      class="mt-5 large-width-size"
      @type="text"
      @isDisabled={{this.isLinkDisabled}}
      @label={{t this.content.url.label}}
      @helpContent={{t this.content.url.helpContent}}
      @value={{@changeset.url}}
      @hasError={{@changeset.error.url}}
      @errorMessage={{@changeset.error.url.validation.firstObject}}
      @onChange={{fn this.onLinkChange}}
      data-test--text--url
    >
      <div>
        <Vc::Button::Action
          @type="secondary"
          @iconName="actions/link"
          @class="link-child-instance-button"
          @isDisabled={{this.isLinkButtonDisabled}}
          @accessibilityText={{t this.content.url.urlLabel}}
          @onClick={{this.onLinkClick}}
          data-test--button--link-button
          as |ba|
        >
          <ba.label>
            {{t this.content.url.urlLabel}}
          </ba.label>
        </Vc::Button::Action>
      </div>
    </Vc::Input::Text>
    <Vc::Input::Text
      class="mt-5"
      @type="text"
      @isDisabled={{this.postLinkActionsDisabled}}
      @label={{t this.content.clientID.label}}
      @helpContent={{t this.content.clientID.helpContent}}
      @value={{@changeset.clientID}}
      @hasError={{@changeset.error.clientID}}
      @errorMessage={{@changeset.error.clientID.validation.firstObject}}
      @onChange={{fn (mut @changeset.clientID)}}
      data-test--text--client-id
    />
    <Vc::Input::Password
      class="mt-5"
      @value={{@changeset.secretKey}}
      @type="password"
      @isDisabled={{this.postLinkActionsDisabled}}
      @label={{t this.content.secretKey.label}}
      @helpContent={{t this.content.secretKey.helpContent}}
      @hasError={{@changeset.error.secretKey}}
      @errorMessage={{@changeset.error.secretKey.validation.firstObject}}
      @onChange={{fn (mut @changeset.secretKey)}}
      data-test--text--secret-key
    />
  </md.body>
</Vc::Modal>