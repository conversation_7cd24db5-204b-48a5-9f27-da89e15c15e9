<Vc::Modal
  @isOpen={{true}}
  @isDestructive={{true}}
  @onClose={{@onClose}}
  @size="small"
  @type="workflow"
  @secondaryText="Cancel"
  @secondaryAction={{@onClose}}
  @primaryText={{t this.content.primaryText}}
  @primaryAction={{perform @submitTask @instance.id}}
  @primaryIsLoading={{@submitTask.isRunning}}
  data-test--delete-child-instance-modal
  as |md|
>
  <md.header>
    {{t this.content.title}}
  </md.header>
  <md.subheader>
    <Vc::Form::Status
      @type="warning"
      @primaryText={{this.content.subtitleContents}}
    />
  </md.subheader>
  <md.body>
    <Vc::KeyValueList as |list|>
      <list.kv class="mt-5" as |kv|>
        <kv.key>
          {{t this.content.childInstancesNameKey}}
        </kv.key>
        <kv.value>
          {{@instance.name}}
        </kv.value>
      </list.kv>

      <list.kv class="mt-5" as |kv|>
        <kv.key>
          {{t this.content.clientIDKey}}
        </kv.key>
        <kv.value>
          {{@instance.clientId}}
        </kv.value>
      </list.kv>
    </Vc::KeyValueList>
  </md.body>
</Vc::Modal>