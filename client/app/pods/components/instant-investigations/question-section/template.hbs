{{#let (concat @section.dataSource @section.entityId) as |dataSourceId|}}
  {{#if dataSourceId}}
    <InstantInvestigations::QuestionSectionWrapper
      @multipleSections={{@multipleSections}}
      @expandedSections={{@expandedSections}}
      @expandedQuestions={{@expandedQuestions}}
      @dataSourceId={{dataSourceId}}
      @dataSource={{@section.dataSource}}
      @entityDisplayName={{@section.entityDisplayName}}
      @onExpandSectionToggle={{@onExpandSectionToggle}}
      @expandAllQuestionsInSection={{@expandAllQuestionsInSection}}
      @collapseAllQuestionsInSection={{@collapseAllQuestionsInSection}}
      @questionUids={{@section.questionUids}}
      ...attributes
    >
      <Vc::Button::Action
        @type="secondary"
        @onClick={{fn @expandAllQuestionsInSection dataSourceId}}
        @isDisabled={{eq
          @expandedQuestions.length
          @section.questionUids.length
        }}
        data-test-expand-all-button
        as |ba|
      >
        <ba.label>
          {{t "Expand All"}}
        </ba.label>
      </Vc::Button::Action>
      <span>{{"|"}}</span>
      <Vc::Button::Action
        @type="secondary"
        @onClick={{fn @collapseAllQuestionsInSection dataSourceId}}
        @isDisabled={{not @expandedQuestions.length}}
        data-test-collapse-all-button
        as |ba|
      >
        <ba.label>
          {{t "Collapse All"}}
        </ba.label>
      </Vc::Button::Action>

      {{#each @section.questionUids as |question index|}}
        <InstantInvestigations::QuestionWithSimpleMiddleware
          @question={{question}}
          @questionUid={{question.questionUid}}
          @tableExists={{question.tableExists}}
          @connectors={{question.sensorsWithAccess}}
          @dataSource={{@section.dataSource}}
          @isExpanded={{contains
            (concat dataSourceId index)
            @expandedQuestions
          }}
          @onExpandToggle={{fn @onExpandQuestionToggle dataSourceId index}}
          @dateFrom={{@dateFrom}}
          @dateTo={{@dateTo}}
          @dateRangeInHours={{@dateRangeInHours}}
          @entityId={{@entityId}}
          @subaccountId={{@section.entityId}}
          @entityUid={{@section.entityUid}}
          @entityDisplayName={{@section.entityDisplayName}}
          @entityType={{@entityType}}
          @onInsertQuestion={{fn @onInsertQuestion index}}
        />
      {{/each}}
    </InstantInvestigations::QuestionSectionWrapper>
  {{/if}}
{{/let}}