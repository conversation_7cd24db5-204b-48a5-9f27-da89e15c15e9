{{#if @multipleSections}}
  <Vc::ExpandableContainer
    @isExpanded={{contains @dataSourceId @expandedSections}}
    @onExpandToggle={{fn @onExpandSectionToggle @dataSourceId}}
    @isLoading={{false}}
    @hasUnderline={{true}}
    ...attributes
    data-test-investigation-question-section
    as |ec|
  >
    <ec.header>
      <ec.subHeader>
        {{get-data-source-label (lowercase @dataSource)}}
      </ec.subHeader>
      <ec.subContent>
        {{concat "(" @entityDisplayName ")"}}
      </ec.subContent>
    </ec.header>
    <ec.body>
      {{yield}}
    </ec.body>
  </Vc::ExpandableContainer>
{{else}}
  {{yield}}
{{/if}}