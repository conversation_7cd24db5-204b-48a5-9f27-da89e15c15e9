<Investigate::<PERSON><PERSON><PERSON><PERSON><PERSON>
  @config={{this.config}}
  @row={{@row}}
  @dateFrom={{@dateFrom}}
  @dateTo={{@dateTo}}
  @sort={{this.sort}}
  @page={{this.page}}
  @pageSize={{this.pageSize}}
  @connectors={{@connectors}}
  @onError={{this.onLoadError}}
  @metricName={{this.METRIC_NAME}}
  @getOptionalMetricLabels={{this.getOptionalMetricLabels}}
  as |request|
>
  <Vc::LoadingContainer @isLoading={{request.isLoading}}>
    {{#if request.hasError}}
      <div
        class="d-flex align-items-center justify-content-center flex-column mt-6 flex-grow-1"
        data-test-investigation-error
      >
        <Vc::Header::SubHeader class="header mb-5">
          {{t "Something went wrong"}}
        </Vc::Header::SubHeader>
        <Vc::Text class="text">
          {{t
            "There was a problem retrieving data, please try reloading the page"
          }}
        </Vc::Text>
      </div>
    {{else if request.isFulfilled}}
      <div
        class="investigation-question__table investigation-question__table--inner p-4"
        data-test-investigation-inner-question
        data-test-investigation-question-table-sort-by="{{this.sort}}"
      >
        <Vc::Table
          @tableClass="vc-table"
          @rows={{request.data}}
          @columns={{this.columns}}
          @hideExpandCollapseAll={{true}}
          @sortBy={{this.sortBy}}
          @hasStickyHeader={{true}}
          @hideFooter={{true}}
          @emptySetMessage={{"No results"}}
          @timeZone={{(setting-timezone)}}
        />
        <div class="investigate__footer">
          {{#if (gte request.data.length 1)}}
            <Vc::Pagination
              @showPageSizeDropdown={{false}}
              @pageSize={{this.pageSize}}
              @currentPageNumber={{this.page}}
              @totalRecordsCount={{request.meta.numRowsAvailable}}
              @onPageSelect={{this.onPageSelect}}
            />
          {{/if}}
        </div>
      </div>
    {{/if}}
  </Vc::LoadingContainer>
</Investigate::RequestHandler>