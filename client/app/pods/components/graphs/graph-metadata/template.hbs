{{#if this.isLoading}}<VLoading class="full" />{{/if}}
{{#if this.showTooltip}}
  <Graphs::GraphContext
    class="graph-context-metadata"
    @showTooltip={{this.showTooltip}}
    @zoomInClick={{action "zoomIn"}}
    @zoomOutClick={{action "zoomOut"}}
    @viewMetadataClick={{action "viewMetaData"}}
  />
{{/if}}
<svg data-test-svg-graph class="graph" width="100%" height="70"></svg>
{{#if this.hoveredDatum}}
  <Graphs::GraphCommon::Tooltip
    @left={{this.tooltipPositioning.left}}
    @top={{this.tooltipPositioning.top}}
    @offsetX={{this.tooltipPositioning.offsetX}}
    @parentDimensions={{this.tooltipPositioning.parentDimensions}}
  >
    <h1>
      {{moment-format @hoveredTimestamp "MMM Do YYYY HH:mm"}}
    </h1>
    {{!
      TODO: Once we hook this feature up to real endpoints, we'll want to
      refactor/augment these conditionals for real results.
    }}
    {{#if (eq @metadataType "externalDataTransfer")}}
      <dl>
        <dt>{{t "Destinations"}}</dt>
        <dd>{{or this.hoveredDatum.details.targets.length 0}}</dd>
        <dt class="invisible">{{t "Data Transferred"}}</dt>
        <dd>{{file-size this.hoveredDatum.details.y}}</dd>
        <dt class="invisible">{{t "Hosts"}}</dt>
        <dd>{{this.hoveredDatum.details.targets}}</dd>
      </dl>
    {{else if (eq @metadataType "internalDataTransfer")}}
      <dl>
        <dt>{{t "Sources"}}</dt>
        <dd>{{or this.hoveredDatum.details.destinations.length 0}}</dd>
        <dt class="invisible">{{t "Data Transferred"}}</dt>
        <dd>{{file-size this.hoveredDatum.details.y}}</dd>
        <dt class="invisible">{{t "Hosts"}}</dt>
        <dd>{{this.hoveredDatum.details.destinations}}</dd>
      </dl>
    {{else if (eq @metadataType "adminSessions")}}
      <dl>
        <dt>{{t "Targets"}}</dt>
        <dd>{{or this.hoveredDatum.details.targets 0}}</dd>
        <dt>{{t "Protocols"}}</dt>
        <dd>{{or this.hoveredDatum.details.protocols 0}}</dd>
      </dl>
    {{else if (eq @metadataType "rpcUsage")}}
      <dl>
        <dt>{{t "Targets"}}</dt>
        <dd>{{or this.hoveredDatum.details.targets 0}}</dd>
        <dt>{{t "Procedure Calls"}}</dt>
        <dd>{{or this.hoveredDatum.details.y 0}}</dd>
      </dl>
    {{else if
      (or
        (eq @metadataType "kerberosLoginsFail")
        (eq @metadataType "kerberosLoginsSuccess")
      )
    }}
      <dl>
        <dt>{{t "Attempts"}}</dt>
        <dd>{{or this.hoveredDatum.details.y 0}}</dd>
        <dt>{{t "Accounts"}}</dt>
        <dd>{{or this.hoveredDatum.details.sources 0}}</dd>
      </dl>
    {{else if
      (or
        (eq @metadataType "internalSessions")
        (eq @metadataType "externalSessions")
      )
    }}
      <dl>
        <dt>{{t "Sessions"}}</dt>
        <dd>{{or this.hoveredDatum.details.y 0}}</dd>
      </dl>
    {{else if
      (or (eq @metadataType "ntlmSuccess") (eq @metadataType "ntlmFail"))
    }}
      <dl>
        <dt>{{t "Usernames"}}</dt>
        <dd>{{or this.hoveredDatum.details.usernames 0}}</dd>
      </dl>
    {{/if}}
  </Graphs::GraphCommon::Tooltip>
{{/if}}