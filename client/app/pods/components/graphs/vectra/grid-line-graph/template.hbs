<Graphs::Private::D3::GridLineGraph
  @width={{@width}}
  @height={{@height}}
  @margins={{@margins}}
  @minTime={{@minTime}}
  @maxTime={{@maxTime}}
  @minValue={{@minValue}}
  @maxValue={{@maxValue}}
  @lines={{@lines}}
  @intersectionDetails={{@intersectionDetails}}
  @xTicks={{@xTicks}}
  @dataOutageLines={{@dataOutageLines}}
  @gridTimeType={{@gridTimeType}}
  @gridTimeInterval={{@gridTimeInterval}}
  @strongGridTimeType={{@strongGridTimeType}}
  @strongGridTimeInterval={{@strongGridTimeInterval}}
  @xLabelFormat={{@xLabelFormat}}
  @labelText={{@labelText}}
  @parentOnMouseMove={{@onMouseMove}}
  @positionLabelAtStart={{@positionLabelAtStart}}
  @addSections={{@addSections}}
>
  {{yield}}
</Graphs::Private::D3::GridLineGraph>