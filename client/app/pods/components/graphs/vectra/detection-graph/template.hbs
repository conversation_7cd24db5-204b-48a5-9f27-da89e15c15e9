<Graphs::Private::D3::VerticalBarGraph
  @width={{@width}}
  @height={{@height}}
  @margins={{this.margins}}
  @bars={{this.computedBars}}
  @onBarHover={{this.onBarHover}}
  @onBarLeave={{this.onBarLeave}}
  @hasGrid={{true}}
  @hideYAxis={{true}}
>
  {{#if this.tooltip}}
    <Graphs::GraphCommon::Tooltip
      @left={{this.tooltipX}}
      @top={{this.tooltipY}}
      @offsetX={{this.tooltipX}}
      @parentDimensions={{this.dimensions}}
    >
      <h1>{{moment-format this.tooltip.timestamp "MMM Do YYYY HH:mm"}}</h1>
      {{#each-in this.tooltip.content as |key value|}}
        <div>{{key}}:&nbsp;{{value}}</div>
      {{/each-in}}
    </Graphs::GraphCommon::Tooltip>
  {{/if}}
</Graphs::Private::D3::VerticalBarGraph>