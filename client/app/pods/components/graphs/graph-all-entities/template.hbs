<div class="graph">
  <svg id="graph"></svg>
  <div class="tooltip-point"></div>
  <div class="tooltip">
    {{#if this.entityData}}
      <LinkTo
        @route="entities.entity"
        @model={{this.entityData.id}}
        class="primary"
      >
        {{if
          (eq @entityType "host")
          this.entityData.hostname
          this.entityData.uid
        }}
      </LinkTo>
      <dl>
        {{#if (feature-enabled "observed_privilege")}}
          <dt>{{t "Observed Privilege"}}</dt>
          <dd>
            <PrivilegeCategory
              data-test-privilege-category
              @showIcon={{false}}
              @entity={{this.entityData}}
            />
          </dd>
        {{/if}}
        {{#if this.entityData.ip}}
          <dt>{{t "Last Seen IP"}}</dt>
          <dd>{{this.entityData.ip}}</dd>
        {{/if}}
        <div>
          <dt>{{t "Threat"}}</dt>
          <dd>{{this.entityData.threat}}</dd>
          <dt>/ {{t "Certainty"}}</dt>
          <dd>{{this.entityData.certainty}}</dd>
        </div>
        <div data-test--accounts-graph-icon-tooltip>
          <dt>{{t "Assignee"}}</dt>
          <dd>
            {{if
              this.entityData.assignment
              this.entityData.assignment.assignedTo
              "—"
            }}
          </dd>
        </div>
        <div>
          {{#if this.entityData.groups.length}}
            <dt>{{t "Groups"}}</dt>
            <dd>
              {{! NOTE: This conditional is inlined to avoid adding a space before the ", and X more" label. }}
              {{join ", " (map-by "name" (take 4 this.entityData.groups))}}{{#if
                (gt this.entityData.groups.length 4)
              }},
                {{t "and"}}
                {{dec 4 this.entityData.groups.length}}
                {{t "more"}}{{/if}}
            </dd>
          {{/if}}
        </div>
      </dl>
      {{#if this.entityData.detections}}
        <p class="table-legend">{{t "Latest Active Detections"}}</p>
        <table data-test--entity-graph-detection-table>
          <colgroup>
            <col style="width: 220px" />
            <col style="width: 50px" />
            <col style="width: 70px" />
          </colgroup>
          <thead>
            <tr>
              <th>{{t "Type"}}</th>
              <th class="numeric">{{t "Threat"}}</th>
              <th class="numeric">{{t "Certainty"}}</th>
            </tr>
          </thead>
          <tbody>
            {{#each (take 5 this.entityData.detections) as |detection|}}
              <tr>
                <td class={{if detection.targetsKeyAsset "targetsKeyAsset"}}>
                  <LinkTo
                    data-test--entity-graph-detection-type
                    id="type"
                    @route="detections.detection"
                    @model={{detection.id}}
                  >
                    <Vc::Overflow @value={{detection.type}} @targetId="type" />
                  </LinkTo>
                </td>
                <td class="numeric">{{detection.threat}}</td>
                <td class="numeric">{{detection.certainty}}</td>
              </tr>
            {{/each}}
          </tbody>
        </table>
      {{/if}}
    {{else}}
      <VLoading />
    {{/if}}
  </div>
  <table class="entities-label">
    <thead>
      <tr>
        <th>{{t "Severity"}}</th>
        <th>{{t (capitalize @entityType)}}</th>
      </tr>
    </thead>
    <tbody>
      <tr class="critical">
        <td class="label"><Vc::Icon
            @iconName="sev-critical-circle"
            @class="icon"
          />
          {{t "Critical"}}
        </td>
        <td class="count">
          {{this.critical}}
          <span class="meta">
            {{t (pluralize (capitalize @entityType) this.critical)}}
          </span>
        </td>
      </tr>
      <tr class="high">
        <td class="label"><Vc::Icon @iconName="sev-high-circle" @class="icon" />
          {{t "High"}}
        </td>
        <td class="count">
          {{this.high}}
          <span class="meta">
            {{t (pluralize (capitalize @entityType) this.high)}}
          </span>
        </td>
      </tr>
      <tr class="medium">
        <td class="label"><Vc::Icon
            @iconName="sev-medium-circle"
            @class="icon"
          />
          {{t "Medium"}}
        </td>
        <td class="count">
          {{this.medium}}
          <span class="meta">
            {{t (pluralize (capitalize @entityType) this.medium)}}
          </span>
        </td>
      </tr>
      <tr class="low">
        <td class="label"><Vc::Icon @iconName="sev-low-circle" @class="icon" />
          {{t "Low"}}
        </td>
        <td class="count">
          {{this.low}}
          <span class="meta">{{t (pluralize (capitalize @entityType) this.low)}}
          </span>
        </td>
      </tr>
    </tbody>
  </table>
</div>