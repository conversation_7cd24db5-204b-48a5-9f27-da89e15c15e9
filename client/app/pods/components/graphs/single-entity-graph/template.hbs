<div {{did-insert this.populateGraph}}></div>
{{#if this.getGraphDetails.isRunning}}
  <Vc::LoadingContainer @isLoading={{true}}>
    <div class="graph-skeleton">
    </div>
  </Vc::LoadingContainer>
{{else}}
  {{#if this.showLegendContainer}}
    <div
      class="graph-legend-container d-flex justify-content-end align-items-center pt-4 pe-6 ps-6"
    >
      <Vc::Button::Action
        data-test-open-legend-button
        @type="secondary"
        @accessibilityText="Open Legend"
        @onClick={{this.openLegend}}
        as |ba|
      >
        <ba.label>
          <div class="d-flex align-items-center legend-icon">
            <Vc::Icon @iconName="total-events" />
            <span>{{t "Legend"}}</span>
          </div>
        </ba.label>
      </Vc::Button::Action>
      <AttackGraph::SwitcherMenu
        class="attack-graph-switcher-menu"
        @attachedElementId="timeline-graph-switcher-menu"
        @showSaveMenu={{this.showSaveMenu}}
        @toggleShowSaveMenu={{toggle "showSaveMenu" this}}
        @icon={{this.icon}}
        @items={{this.itemsForMenu}}
      />
    </div>
  {{/if}}
  <AttackGraph::Legend @isVisible={{this.isLegendVisible}} />
  {{#if (and this.shouldShowNoActiveRemaining (not this.showScoring))}}
    <div class="empty-container" data-test-no-active-detections>
      <Common::Images::ImageNoActive />
      <Vc::Header::SubHeader
        class="image-text"
        data-test-no-active-detections-text
      >
        {{t "No active detections to visualize"}}
      </Vc::Header::SubHeader>
    </div>
  {{else if (and this.shouldShowNoneSupported (not this.showScoring))}}
    <div class="empty-container" data-test-no-supported-detections>
      <Common::Images::ImageNoSupported />
      <Vc::Header::SubHeader
        class="image-text"
        data-test-no-supported-detections-text
      >
        {{t "This entity has no detections with clear attributable targets."}}
      </Vc::Header::SubHeader>
    </div>
  {{else}}
    <div class="{{concat 'graph-wrapper ' (if this.showTree 'pb-5')}}">
      {{#if this.showTree}}
        <div id="tree-graph-id" class="graph-container mt-5">
          <Vc::Graph::Tree
            @data={{this.data}}
            @tooltipFormatter={{this.tooltipFormatter}}
          />
        </div>
      {{else if this.showScoring}}
        <div class="m-4 pt-6">
          <VTimeline
            @stride={{@stride}}
            @updateStride={{@updateStride}}
            class="scoring-timeline"
          >
            <ol class="legend me-6" data-test-entity-graph-legend>
              <li class="threat">{{t "Urgency Score"}}</li>
            </ol>
            <AttackGraph::SwitcherMenu
              class="attack-graph-switcher-menu"
              @attachedElementId="attack-graph-switcher-menu"
              @showSaveMenu={{this.showSaveMenu}}
              @toggleShowSaveMenu={{toggle "showSaveMenu" this}}
              @icon={{this.scoringIcon}}
              @items={{this.itemsForMenu}}
            />
          </VTimeline>
          <Graphs::GraphEntity
            @model={{@entity}}
            @scores={{@scores}}
            @stride={{@stride}}
            @updateStride={{@updateStride}}
            @interpolate="step-after"
          />
        </div>
      {{else}}
        <div id="network-graph-id" class="graph-container">
          <div class="network-graph">
            <Vc::Graph::Network
              @size={{"extra-large"}}
              @tooltipFormatter={{this.tooltipFormatter}}
              @onClick={{this.onClick}}
              @data={{this.nodes}}
              @links={{this.links}}
              @setGraphInstance={{this.setGraphInstance}}
              @series={{hash layout="none"}}
              data-test-single-entity-graph
            />
          </div>
        </div>
      {{/if}}
    </div>
    {{#unless this.showScoring}}
      <div class="{{concat 'resizer-spacer ' (if this.showTree 'mt-5')}}">
        {{#if (and (not this.showScoring) (not this.showTree))}}
          <Vc::Timeline
            @progress={{this.progress}}
            @play={{this.onPlay}}
            @pause={{this.onPause}}
            @previous={{this.onPrevious}}
            @next={{this.onNext}}
            @steps={{this.steps}}
            @startDate={{this.startAndEndDates.startDate}}
            @endDate={{this.startAndEndDates.endDate}}
            @currentStep={{this.currentLinkIndex}}
            @isPlaying={{this.isPlaying}}
            @firstSeenTimes={{this.firstSeenTimes}}
            data-test-single-entity-timeline
          />
        {{/if}}
        <VResizer
          class="graph__resizer"
          @target=".graph-container"
          @baseHeight={{300}}
          @minHeight={{280}}
          @offsetTop={{200}}
        />
      </div>
    {{/unless}}
  {{/if}}
  {{#if this.showModal}}
    <AttackGraph::ClusterModal
      @data={{this.modalData}}
      @onClose={{this.onCloseModal}}
    />
  {{/if}}
{{/if}}