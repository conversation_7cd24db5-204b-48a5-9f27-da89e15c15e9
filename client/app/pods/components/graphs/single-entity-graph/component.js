import Component from '@ember/component';
import { action } from '@ember/object';
import { inject as service } from '@ember/service';
import { tracked } from '@glimmer/tracking';
import { task } from 'ember-concurrency';
import { dateTimeFields, tooltipKeys } from 'vectra/reference-data/graphs/entity-graph';
import { jsonHttpRequest } from 'vectra/utils/request-common';
import transformResponseToGraphData, { transformDataForTreeGraph } from 'vectra/utils/transform-data-for-graph';

const ENTITY_GRAPH_VIEW_KEY = 'entity-graph-preferred-view';

export default class extends Component {
  @service localStorage;
  @service moment;
  @service settings;

  mainEntityName = '';
  @tracked nodes = [];
  @tracked links = [];
  @tracked currentLinkIndex = 0;
  @tracked isLegendVisible = false;
  playbackInterval = null;
  @tracked progress = 0;
  @tracked isPlaying = false;
  intervalId = null;
  @tracked showModal = false;
  @tracked modalData = [];
  @tracked targetLink = null;
  @tracked shouldShowNoActiveRemaining = false;
  @tracked shouldShowNoneSupported = false;
  @tracked highlightedNodesTracker = [];
  @tracked highlightedLinksTracker = [];
  @tracked responseData;
  @tracked data = [];
  @tracked graphInstance;
  @tracked showTree = false;
  @tracked showAttack = false;
  @tracked showScoring = false;
  @tracked showSaveMenu = false;
  @tracked showLegendContainer = true;

  get scoringIcon() {
    return this.showSaveMenu ? 'line-expanded' : 'line-collapsed';
  }

  get icon() {
    let state = this.showSaveMenu ? 'expanded' : 'collapsed';
    let graphType = this.showTree ? 'tree' : 'network';
    return `${graphType}-${state}`;
  }

  get steps() {
    return this.firstSeenTimes.length;
  }

  get firstSeenTimes() {
    let linksDates = [...this.links.map(link => link.firstSeen || link.lastSeen)];
    return linksDates.map(date => this.moment.moment(date)).sort((a, b) => a - b);
  }

  get startAndEndDates() {
    let sortedLinks = this.links.sort((a, b) => {
      let dateA = this.moment.moment(a.firstSeen || a.lastSeen);
      let dateB = this.moment.moment(b.firstSeen || b.lastSeen);
      return dateA - dateB;
    });

    let firstSeen = this.moment.moment(sortedLinks[0]?.firstSeen || sortedLinks[0]?.lastSeen);
    let lastSeen = this.moment.moment(
      sortedLinks[sortedLinks.length - 1]?.firstSeen || sortedLinks[sortedLinks.length - 1]?.lastSeen,
    );

    let startDate = firstSeen.subtract(30, 'minutes').toISOString();
    let endDate = lastSeen.add(30, 'minutes').toISOString();

    return {
      startDate,
      endDate,
    };
  }

  @action
  async populateGraph() {
    let isQUX = this.settings.featureEnabled('appliance_only');
    let attackGraphDetails = await this.getGraphDetails.perform(this.entity);
    this.responseData = attackGraphDetails;
    this.mainEntityName = attackGraphDetails.mainEntity.name;
    let { nodes, links } = await transformResponseToGraphData(attackGraphDetails, isQUX);
    this.nodes = nodes;
    this.links = links;
    this.shouldShowNoActiveRemaining = this.entity.state === 'inactive';
    this.shouldShowNoneSupported = nodes.length < 2;

    if (this.shouldShowNoActiveRemaining || this.shouldShowNoneSupported) {
      this.showScoringGraph();
    } else {
      const preferredView = this.localStorage.getItem(ENTITY_GRAPH_VIEW_KEY);
      if (preferredView === 'tree') {
        this.showTreeGraph();
      } else if (preferredView === 'scoring') {
        this.showScoringGraph();
      } else {
        // Default to attack graph
        this.showAttackGraph();
      }
    }
  }

  @task
  *getGraphDetails(entity) {
    let { id, modelName } = entity;
    try {
      let response = yield jsonHttpRequest(`/api/app/${modelName}/${id}/entity-graph`);
      return response;
    } catch (ex) {
      console.error(ex);
    }
    return [];
  }

  getKeys(key) {
    return tooltipKeys[key];
  }

  get itemsForMenu() {
    return [
      { title: 'Attack Graph', icon: 'network-graph', action: this.showAttackGraph },
      {
        title: 'Attack Flow',
        icon: 'tree-graph',
        action: this.showTreeGraph,
      },
      { title: 'Attack Timeline', icon: 'line-graph', action: this.showScoringGraph },
    ];
  }

  tooltipFormatter = params => {
    let tooltipData = params?.data?.tooltipData || [];
    if (!tooltipData.length) return '';
    if (typeof tooltipData === 'string') {
      return tooltipData;
    }

    let maxLength = 30;
    let truncateText = (text, length) => (text?.length > length ? `${text.substring(0, length)}...` : text);
    let tooltipStyle = 'width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';
    let extraInformation = '';
    if (params?.dataType === 'edge' || params?.data.isEdge) {
      extraInformation = `
        <span class='vc-typ-body-bold vc-text-bold'>${params.data.name}</span><br />
        <span class='vc-typ-body-bold vc-text-bold'>${params.data.category}</span>
      `;
    }
    return `
      <div class='vc-graph-tooltip'>
        ${params.dataType === 'edge' || params.data.isEdge ? extraInformation : ''}
        ${tooltipData
          .map(({ key, value, label }) => {
            let displayKey = label || this.getKeys(key) || key;
            let displayValue = value;

            if (dateTimeFields.includes(key) && value) {
              displayValue = this.moment.moment(value).format('DD MMM YYYY HH:mm');
            } else if (Array.isArray(value) && value.length === 0) {
              displayValue = '—';
            } else if (Array.isArray(value)) {
              displayValue = value
                .map(item => (typeof item === 'object' ? item.luid || item.name || 'Unknown' : item))
                .join(', ');
            } else if (!value) {
              displayValue = '—';
            }

            return `
              <div class='vc-graph-tooltip' style="${tooltipStyle}">
                <span class='vc-typ-body-bold vc-text-bold'>${truncateText(displayKey, maxLength)}:</span>
                <span>${truncateText(displayValue, maxLength)}</span>
              </div>
            `;
          })
          .join('')}
      </div>
    `;
  };

  @action
  onCloseModal() {
    this.showModal = false;
  }

  onClick = params => {
    if (params.data.label?.formatter) {
      this.showModal = true;
      let uniqueMembers = Array.from(
        new Map((params.data.members || []).map(member => [member.name, member])).values(),
      );
      this.modalData = (uniqueMembers || []).map(data => ({
        ...data,
        targetLink: `/${data.nodeType}s/${data.navigationId}`,
      }));
    } else {
      let { nodeType, navigationId } = params.data;
      let canNavigate = nodeType === 'account' || nodeType === 'host' || nodeType === 'detection';
      if (!canNavigate) {
        return;
      }
      if (navigationId) {
        window.open(`/${nodeType}s/${navigationId}`, '_blank');
      }
    }
  };

  @action
  showAttackGraph() {
    this.localStorage.setItem(ENTITY_GRAPH_VIEW_KEY, 'attack');
    this.showLegendContainer = true;
    this.showAttack = true;
    this.showScoring = false;
    this.showTree = false;
  }

  @action
  showScoringGraph() {
    this.localStorage.setItem(ENTITY_GRAPH_VIEW_KEY, 'scoring');
    this.showScoring = true;
    this.showTree = false;
    this.showAttack = false;
    this.showLegendContainer = false;
  }

  @action
  showTreeGraph() {
    this.localStorage.setItem(ENTITY_GRAPH_VIEW_KEY, 'tree');
    this.showLegendContainer = true;
    this.showTree = true;
    this.showAttack = false;
    this.showScoring = false;
    this.getDataForTree();
  }

  @action
  async getDataForTree() {
    let isQUX = this.settings.featureEnabled('appliance_only');
    let data = await transformDataForTreeGraph(this.responseData, isQUX);
    this.data = data;
  }

  @action
  openLegend() {
    this.isLegendVisible = true;
  }

  @action
  closeLegend() {
    this.isLegendVisible = false;
  }

  @action
  onPlay() {
    if (this.isPlaying) return;

    if (this.currentLinkIndex === this.links.length) {
      this.currentLinkIndex = 0;
      this.progress = 0;
      this.highlightedNodesTracker = [];
      this.highlightedLinksTracker = [];
    }

    this.isPlaying = true;
    this.playbackInterval = setInterval(() => {
      if (this.currentLinkIndex < this.links.length) {
        this.handleHighlight(this.currentLinkIndex);
        this.currentLinkIndex += 1;
        this.progress = (this.currentLinkIndex / this.links.length) * 100;
      } else {
        this.highlightAllNodes();
        this.onPause();
      }
    }, 1500);
  }

  highlightAllNodes() {
    this.currentLinkIndex = 0;
    this.progress = 0;
    this.graph.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
    });
    this.isPlaying = false;
  }

  @action
  onPause() {
    this.isPlaying = false;
    if (this.playbackInterval) {
      clearInterval(this.playbackInterval);
      this.playbackInterval = null;
    }
  }

  @action
  onPrevious() {
    if (this.currentLinkIndex > 0) {
      this.highlightedLinksTracker = this.highlightedLinksTracker.filter(
        linkIndex => linkIndex !== this.currentLinkIndex,
      );

      this.currentLinkIndex -= 1;
      this.handleHighlight(this.currentLinkIndex);
      this.progress = (this.currentLinkIndex / this.links.length) * 100;
    }
  }

  @action
  onNext() {
    if (this.currentLinkIndex < this.links.length - 1) {
      this.currentLinkIndex += 1;
      this.handleHighlight(this.currentLinkIndex);
      this.progress = (this.currentLinkIndex / this.links.length) * 100;
    }
  }

  handleHighlight(index) {
    if (!this.highlightedLinksTracker.includes(index)) {
      this.highlightedLinksTracker.push(index);
    }

    let currentLink = this.links[index];
    let targetNode = currentLink.target;
    if (targetNode === this.mainEntityName) {
      targetNode = currentLink.source;
    }
    this.highlightNode(targetNode);
    this.highlightLink(index);
  }

  highlightNode(nodeName) {
    if (!this.highlightedNodesTracker.includes(nodeName)) {
      this.highlightedNodesTracker.push(nodeName);
    }

    this.graph.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      name: this.highlightedNodesTracker,
      dataType: 'node',
    });
  }

  highlightLink(index) {
    if (!this.highlightedLinksTracker.includes(index)) {
      this.highlightedLinksTracker.push(index);
    }

    this.graph.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: this.highlightedLinksTracker,
      dataType: 'edge',
    });
  }

  @action
  setGraphInstance(graph) {
    this.graph = graph;
    this.updateGraphSize();
  }

  @action
  updateGraphSize() {
    let graphContainer = document.getElementById('network-graph-id');
    if (!graphContainer) return;

    let numNodes = this.nodes?.length || 1;

    let minHeight = 240;
    let maxHeight = 420;
    let heightPerNode = 105;

    let calculatedHeight = Math.min(maxHeight, Math.max(minHeight, numNodes * heightPerNode));

    graphContainer.style.height = `${calculatedHeight}px`;
    if (this.graph?.resize) {
      this.graph.resize();
    }
  }
}
