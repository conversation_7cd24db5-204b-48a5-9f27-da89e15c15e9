{{#if (eq this.status "too-many")}}
  <p class={{concat "message" this.status}}>
    {{t "Graph not shown (too many hosts)."}}
  </p>
{{/if}}
<div class={{concat "graph " this.status}} data-test-campaign-graph>
  <svg id="graph" width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <marker
        id="arrow-start"
        viewBox="0 0 12 7"
        markerWidth="6px"
        markerHeight="3.5px"
        refX="-9"
        refY="3.5"
        orient="auto"
      >
        <path d="M 12 7 0 3.5 12 0 Z" class="marker" />
      </marker>
      <marker
        id="arrow-end"
        viewBox="0 0 12 7"
        markerWidth="6px"
        markerHeight="3.5px"
        refX="21"
        refY="3.5"
        orient="auto"
      >
        <path d="M 0 0 12 3.5 0 7 Z" class="marker" />
      </marker>
      <marker
        id="arrow-start-active"
        viewBox="0 0 12 7"
        markerWidth="6px"
        markerHeight="3.5px"
        refX="-9"
        refY="3.5"
        orient="auto"
      >
        <path d="M 12 7 0 3.5 12 0 Z" class="marker active" />
      </marker>
      <marker
        id="arrow-end-active"
        viewBox="0 0 12 7"
        markerWidth="6px"
        markerHeight="3.5px"
        refX="21"
        refY="3.5"
        orient="auto"
      >
        <path d="M 0 0 12 3.5 0 7 Z" class="marker active" />
      </marker>
    </defs>
    <rect
      class="background"
      x="0"
      y="0"
      height="100%"
      width="100%"
      fill="transparent"
    />
  </svg>
  <div class="controls top">
    <button
      {{action (action "resetGraph")}}
      class="btn input icon"
      title={{t "Reset Graph"}}
    >
      <Vc::Icon @iconName="misc-reset" @class="icon" />
      {{t "Reset Graph"}}
    </button>
  </div>
  <div class="controls">
    <button
      {{action (action "zoomIn")}}
      class="btn input icon"
      title={{t "Zoom In"}}
    >
      <Vc::Icon @iconName="misc-plus" @class="icon" />
      {{t "Zoom In"}}
    </button>
    <button
      {{action (action "zoomOut")}}
      class="btn input icon"
      title={{t "Zoom Out"}}
    >
      <Vc::Icon @iconName="misc-minus" @class="icon" />
      {{t "Zoom Out"}}
    </button>
    <button
      {{action (action "resetZoom")}}
      class="btn input icon"
      title={{t "Reset Zoom"}}
    >
      <Vc::Icon @iconName="misc-zoomall" @class="icon" />
      {{t "Reset Zoom"}}
    </button>
  </div>
</div>