<Graphs::GraphHostTimeline::Svg<PERSON>ontainer
  @onSvgRender={{action "initGraph"}}
  @onMouseUp={{action "onMouseUp"}}
/>
{{#each this.points as |point index|}}
  <Graphs::GraphHostTimeline::Detection
    @index={{index}}
    @detectionIdSelected={{this.detectionIdSelected}}
    @hoveredTimestamp={{this.hoveredTimestamp}}
    @svg={{this.svg}}
    @point={{point}}
    @startTimestamp={{@startTimestamp}}
    @endTimestamp={{@endTimestamp}}
    @scaleX={{this.scaleX}}
    @scaleY={{this.scaleY}}
    @detectionRadiusPx={{this.detectionRadiusPx}}
    @onDetectionClick={{action "onDetectionClick"}}
    @onDetectionMouseOver={{action "onDetectionMouseOver"}}
    @onDetectionMouseOut={{action "onDetectionMouseOut"}}
  />
{{/each}}
{{#if this.hoveredDatum}}
  <Graphs::GraphCommon::Tooltip
    @left={{this.graphTooltipPosition.left}}
    @top={{this.graphTooltipPosition.top}}
    @offsetX={{this.graphTooltipPosition.offsetX}}
    @parentDimensions={{this.graphTooltipPosition.parentDimensions}}
  >
    <h1>{{moment-format this.hoveredTimestamp "MMM Do YYYY HH:mm"}}</h1>
    <dl>
      <dt>{{this.hoveredDatum.detectionType}}</dt>
      <dt>
        {{t "threat"}}
        {{or this.hoveredDatum.threat 0}}
        {{t "/ certainty"}}
        {{or this.hoveredDatum.certainty 0}}
      </dt>
    </dl>
  </Graphs::GraphCommon::Tooltip>
{{/if}}