<Graphs::Private::D3::V3::Common::SvgContainer
  @onSvgRender={{this.initGraph}}
  @onMouseLeave={{this.onMouseLeave}}
  @onMouseMove={{this.onMouseMove}}
  @onMouseEnter={{this.onMouseMove}}
  @scaleX={{this.scaleX}}
  @scaleY={{this.scaleY}}
/>
{{#if this.svg}}
  <Graphs::Private::D3::V3::Common::Background
    @height={{this.gridHeight}}
    @width={{this.gridWidth}}
    @margins={{@margins}}
    @svg={{this.svg}}
  />
  <Graphs::Private::D3::V3::Common::TimeGridX
    @gridHeight={{this.gridHeight}}
    @margins={{@margins}}
    @svg={{this.svg}}
    @minTime={{@minTime}}
    @maxTime={{@maxTime}}
    @gridTimeType={{@gridTimeType}}
    @gridTimeInterval={{@gridTimeInterval}}
    @gridSelector="x-grid"
    @scaleX={{this.scaleX}}
  />
  <Graphs::Private::D3::V3::Common::TimeGridX
    @gridHeight={{this.gridHeight}}
    @margins={{@margins}}
    @svg={{this.svg}}
    @minTime={{@minTime}}
    @maxTime={{@maxTime}}
    @gridTimeType={{@strongGridTimeType}}
    @gridTimeInterval={{@strongGridTimeInterval}}
    @gridSelector="strong-x-grid"
    @hasLabels={{true}}
    @xLabelFormat={{@xLabelFormat}}
    @scaleX={{this.scaleX}}
    @onRender={{this.initXAxis}}
    @positionLabelAtStart={{@positionLabelAtStart}}
  />
  <Graphs::Private::D3::V3::Common::ValueGridY
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
    @margins={{@margins}}
    @svg={{this.svg}}
    @minValue={{@minValue}}
    @maxValue={{@maxValue}}
    @gridSelector="y-grid-left"
    @showFirstLabel={{false}}
    @hasRightYLabel={{false}}
    @scaleY={{this.scaleY}}
  />
  <Graphs::Private::D3::V3::Common::ValueGridY
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
    @margins={{@margins}}
    @svg={{this.svg}}
    @minValue={{@minValue}}
    @maxValue={{@maxValue}}
    @gridSelector="y-grid-right"
    @showFirstLabel={{false}}
    @hasRightYLabel={{true}}
    @scaleY={{this.scaleY}}
  />
  <Graphs::Private::D3::V3::Common::GraphLinesContainer
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
    @svg={{this.svg}}
    @minTime={{@minTime}}
    @maxTime={{@maxTime}}
    @minValue={{@minValue}}
    @maxValue={{@maxValue}}
    @lineSelector="line"
    @margins={{@margins}}
    @lines={{@lines}}
    @scaleX={{this.scaleX}}
    @scaleY={{this.scaleY}}
  />
  {{#if this.addSections}}
    <Graphs::Private::D3::V3::Common::Section
      @scaleX={{this.scaleX}}
      @scaleY={{this.scaleY}}
      @ticks={{this.xTicks}}
      @gridHeight={{this.gridHeight}}
      @gridWidth={{this.gridWidth}}
      @svg={{this.svg}}
      @sectionSelector="section"
      @margins={{@margins}}
    />
  {{/if}}
  <Graphs::Private::D3::V3::Common::XAxisLabel
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
    @svg={{this.svg}}
    @labelSelector="x-label"
    @margins={{@margins}}
    @labelText={{@labelText}}
  />
  <Graphs::Private::D3::V3::Common::Plumbline
    @gridHeight={{this.gridHeight}}
    @svg={{this.svg}}
    @plumblineSelector="plumbline"
    @hoveredTimestamp={{this.hoveredTimestamp}}
    @scaleX={{this.scaleX}}
    @margins={{@margins}}
  />
  {{#if (and this.xTicks this.intersectionDetails)}}
    <Graphs::Private::D3::V3::Common::IntersectionLines
      @gridWidth={{this.gridWidth}}
      @svg={{this.svg}}
      @labelSelector="intersection"
      @scaleX={{this.scaleX}}
      @scaleY={{this.scaleY}}
      @margins={{@margins}}
      @ticks={{this.xTicks}}
      @intersectionDetails={{this.intersectionDetails}}
    />
  {{/if}}
  {{#if this.dataOutageLines}}
    <Graphs::Private::D3::V3::Common::DataOutageLines
      @gridHeight={{this.gridHeight}}
      @margins={{@margins}}
      @svg={{this.svg}}
      @dataOutageLines={{this.dataOutageLines}}
      @labelSelector="transition"
      @scaleX={{this.scaleX}}
      @scaleY={{this.scaleY}}
    />
  {{/if}}
  {{#if (and this.hoveredTimestamp this.hoveredValue)}}
    <Graphs::GraphCommon::Tooltip
      @left={{this.tooltipX}}
      @top={{this.tooltipY}}
      @offsetX={{this.tooltipX}}
      @parentDimensions={{this.dimensions}}
    >
      {{yield}}
    </Graphs::GraphCommon::Tooltip>
  {{/if}}
{{/if}}