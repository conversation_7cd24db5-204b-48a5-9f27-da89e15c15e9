<Graphs::Private::D3::Common::SvgContainer
  @onSvgRender={{this.initGraph}}
  @height={{@height}}
  @width={{@width}}
/>
{{#if this.svg}}
  <Graphs::Private::D3::Common::Background
    @height={{this.gridHeight}}
    @width={{this.gridWidth}}
    @margins={{@margins}}
    @svg={{this.svg}}
  />
  <Graphs::Private::D3::Common::TimeGridX
    @height={{this.gridHeight}}
    @width={{this.gridWidth}}
    @svg={{this.svg}}
    @margins={{@margins}}
    @scale={{this.scaleX}}
    @hasGrid={{@hasGrid}}
  />
  {{#unless @hideYAxis}}
    <Graphs::Private::D3::Common::ValueGridY
      @height={{this.gridHeight}}
      @width={{this.gridWidth}}
      @svg={{this.svg}}
      @margins={{@margins}}
      @scale={{this.scaleY}}
    />
  {{/unless}}
  <Graphs::Private::D3::Common::GraphBarsContainer
    @svg={{this.svg}}
    @scaleX={{this.scaleX}}
    @scaleY={{this.scaleY}}
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
    @margins={{@margins}}
    @onBarHover={{@onBarHover}}
    @onBarLeave={{@onBarLeave}}
    @bars={{@bars}}
  />
  {{yield}}
{{/if}}