<Graphs::Private::D3::V3::Common::SvgContainer
  @onSvgRender={{this.initGraph}}
/>
{{#if this.svg}}
  <Graphs::Private::D3::V3::Common::Background
    @height={{this.gridHeight}}
    @width={{this.gridWidth}}
    @margins={{@margins}}
    @svg={{this.svg}}
  />
  <Graphs::Private::D3::V3::Common::ValueGridX
    @gridHeight={{this.gridHeight}}
    @margins={{@margins}}
    @svg={{this.svg}}
    @minValue={{@minValue}}
    @maxValue={{@maxValue}}
    @gridValueType={{@gridValueType}}
    @gridValueInterval={{@gridValueInterval}}
    @gridSelector="x-grid"
    @scaleX={{this.scaleX}}
  />
  <Graphs::Private::D3::V3::Common::OrdinalAxisY
    @margins={{@margins}}
    @svg={{this.svg}}
    @selector="y-axis"
    @scale={{this.scaleY}}
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
  />
  <Graphs::Private::D3::V3::Common::GraphBarsContainer
    @margins={{@margins}}
    @bars={{@bars}}
    @svg={{this.svg}}
    @scaleY={{this.scaleY}}
    @scaleX={{this.scaleX}}
    @labelsY={{@labelsY}}
    @barAnnotations={{@barAnnotations}}
  />
  <Graphs::Private::D3::V3::Common::XAxisLabel
    @gridHeight={{this.gridHeight}}
    @gridWidth={{this.gridWidth}}
    @svg={{this.svg}}
    @labelSelector="x-label"
    @margins={{@margins}}
    @labelText={{this.labelText}}
  />
{{/if}}