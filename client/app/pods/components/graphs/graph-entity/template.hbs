<div class="graph" data-test-graph-entity-container>
  {{#unless this.attackGraphEnabled}}
    <VTimeline @stride={{@stride}} @updateStride={{@updateStride}}>
      {{#if (feature-enabled "unified_prioritization")}}
        <ol class="legend" data-test-graph-legend>
          <li class="threat">{{t "Urgency Score"}}</li>
        </ol>
      {{else}}
        <ol class="legend">
          <li class="threat">{{t "Threat"}}</li>
          <li class="certainty">{{t "Certainty"}}</li>
        </ol>
      {{/if}}
    </VTimeline>
  {{/unless}}
  <svg width="100%" height="100%"></svg>
  {{#if this.showGraphTooltip}}
    <Graphs::GraphCommon::Tooltip
      @left={{this.graphTooltipLeft}}
      @top={{this.graphTooltipTop}}
      @offsetX={{this.graphTooltipOffsetX}}
      @parentDimensions={{this.dimensions}}
    >
      <h1>{{moment-format this.hoveredDatum.timestamp "MMM Do YYYY HH:mm"}}</h1>
      {{#if (feature-enabled "unified_prioritization")}}
        <dl>
          <dt data-test-tooltip-urgency>{{t "Urgency"}}</dt>
          <dd>{{this.hoveredDatum.urgency}}</dd>
        </dl>
      {{else}}
        <dl>
          <dt>{{t "Threat"}}</dt>
          <dd>{{this.hoveredDatum.threat}}</dd>
          <dt>{{t "Certainty"}}</dt>
          <dd>{{this.hoveredDatum.certainty}}</dd>
        </dl>
      {{/if}}
    </Graphs::GraphCommon::Tooltip>
  {{/if}}
</div>