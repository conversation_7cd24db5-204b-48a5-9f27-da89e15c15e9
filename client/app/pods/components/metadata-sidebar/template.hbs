{{#if @metadataExplorerLoading}}
  <VLoading class="overlay" />
{{else}}
  <h2>{{@metadataExplorerInfo.metadataSidebarLabel}}</h2>
  {{! template-lint-disable no-invalid-link-text }}
  <a
    href="#"
    class="metadata-sidebar-close"
    {{on "click" (fn @onCloseMetadataTable)}}
  ></a>
  <VSearchbar @class="criteria-bar no-margin">
    <Vc::MultiValueInput
      class="criteria-input"
      @isDisabled={{true}}
      @pills={{@metadataExplorerInfo.criterias}}
    />
    <a
      data-test-kibana-link
      target="_blank"
      rel="noopener noreferrer"
      class="btn text"
      href={{this.kibanaSearchLink}}
      {{action
        "sendPivotToRecallMetric"
        @metadataExplorerInfo.metadataSidebarLabel
        preventDefault=false
      }}
    >
      <Vc::Icon @iconName="misc-popup" @size="sm" @addSpacing={{true}} />
      {{t "View All in Recall"}}
    </a>
  </VSearchbar>
  {{#if this.getTableDataTask.isRunning}}
    <VLoading class="overlay" />
  {{else}}
    <VTable
      class="canSelect multiSelect"
      @scrollableContainerSelector=".metadata-sidebar .lt-body-wrap"
      @columns={{this.columns}}
      @rows={{this.getTableDataTask.lastSuccessful.value}}
      @sort={{mut this.sort}}
      @canExpand={{true}}
      as |tbody|
    >
      <tbody.expanded-row as |row|>
        <MetadataSidebar::MetadataSidebarDetail @row={{row}} />
      </tbody.expanded-row>
    </VTable>
  {{/if}}
{{/if}}