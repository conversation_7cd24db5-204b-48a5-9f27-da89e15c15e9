<div
  data-test-investigation-no-data-found-container
  class="d-flex justify-content-center"
>
  <div class="investigation-no-data-found">
    <p class="investigation-no-data-found__content--header mb-m">
      {{t "Data not found"}}
    </p>
    {{#if (eq @dataSource "aws")}}
      <Vc::Text data-test-investigation-no-data-found-aws class="text aws">
        <p>
          {{t
            "Possible reasons include: no data has been received yet or we are experiencing an outage."
          }}
        </p>
        <p class="investigation-no-data-found__content--double-line">
          <a
            href={{this.supportLink}}
            target="_blank"
            rel="noopener noreferrer"
          >
            {{t "Submit a support request"}}
          </a>
          {{t "if you think this is an error."}}
        </p>
      </Vc::Text>
    {{/if}}
    {{#if (eq @dataSource "o365")}}
      <Vc::Text data-test-investigation-no-data-found-o365 class="text o365">
        <p>
          {{t
            "Possible reasons include: data may not have been received yet, we are experiencing an outage or you don't have the required Microsoft license to access this information."
          }}
        </p>
        <p class="investigation-no-data-found__content--double-line">
          {{t "Check your"}}
          <LinkTo @route="data-sources.o365">
            {{t "license"}}
          </LinkTo>
          {{t "and if it"}}
          <a
            href="https://learn.microsoft.com/en-us/graph/api/resources/azure-ad-auditlog-overview?view=graph-rest-1.0#what-licenses-do-i-need"
            target="_blank"
            rel="noopener noreferrer"
          >
            {{t "enables"}}
          </a>
          {{t "you to see logs or"}}
          <span>
            <a
              href={{this.supportLink}}
              target="_blank"
              rel="noopener noreferrer"
            >
              {{t "submit a support request"}}
            </a>
            {{t "if you think this is an error."}}
          </span>
        </p>
      </Vc::Text>
    {{/if}}

    {{#if (eq @dataSource "network")}}
      <Vc::Text
        data-test-investigation-no-data-found-network
        class="text network investigation-no-data-found__{{@display}}"
      >
        <p>
          {{t "No data has been seen or received recently."}}
        </p>
        <p>
          {{t
            "Possible reasons include: you are not producing any data of this type, the sensor is not setup correctly or we are experiencing an outage."
          }}
          <a
            href={{this.supportLink}}
            target="_blank"
            rel="noopener noreferrer"
          >
            {{t "Submit a support request"}}
          </a>
          {{t "if you think this is an error."}}
        </p>
      </Vc::Text>
    {{/if}}

    {{#if (eq @dataSource "azure-cp")}}
      <Vc::Text data-test-investigation-no-data-found-azure class="text azure">
        <p>
          {{t
            "Possible reasons include: no data has been received yet or we are experiencing an outage."
          }}
        </p>
        <p class="investigation-no-data-found__content--double-line">
          <a
            href={{this.supportLink}}
            target="_blank"
            rel="noopener noreferrer"
          >
            {{t "Submit a support request"}}
          </a>
          {{t "if you think this is an error."}}
        </p>
      </Vc::Text>
    {{/if}}
  </div>
</div>