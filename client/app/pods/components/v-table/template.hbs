{{#if (and @canExpand (not this.shouldShowBulkActions))}}
  <p class="table-actions">
    <button
      class="btn text"
      disabled={{eq this.table.expandedRows.length this.table.rows.length}}
      id={{if
        @buttonIdPrefix
        (concat @buttonIdPrefix "-button-expand-all")
        null
      }}
      {{action (action "expandAll")}}
    >
      {{t "Expand All"}}
    </button>
    <button
      class="btn text"
      disabled={{not this.table.expandedRows.length}}
      id={{if
        @buttonIdPrefix
        (concat @buttonIdPrefix "-button-collapse-all")
        null
      }}
      {{action (action "collapseAll")}}
    >
      {{t "Collapse All"}}
    </button>
  </p>
{{/if}}
{{#if this.shouldShowBulkActions}}
  <TableBulkActions
    class="isStuck"
    @canExpand={{@canExpand}}
    @showSelectAllLoaded={{this.shouldShowSelectAllLoaded}}
    @onSelectAll={{queue
      (action (action "selectAll"))
      (action (mut this.shouldShowSelectAllLoaded) false)
    }}
    @selectedRows={{this.table.selectedRows}}
    @allRows={{this.table.rows}}
    @onClose={{action (action "deselectAll")}}
    @onExpandSelected={{action (action "expandSelected")}}
    @onCollapseSelected={{action (action "collapseSelected")}}
  >
    {{#each @bulkActionComponents as |bulkActionComponent|}}
      {{component bulkActionComponent selectedRows=this.table.selectedRows}}
    {{/each}}
  </TableBulkActions>
{{/if}}
{{#light-table
  this.table
  isLegacyTable=true
  tableClassNames=(if @tableClass @tableClass "traditional")
  tableActions=(hash
    showTagsFor=(optional-action @onSelectTag)
    editRow=(optional-action @onEditRow)
    selectRow=@onSelectRow
    deleteRow=(optional-action @onDeleteRow)
    showAssignmentFor=(optional-action @onAssign)
    advancedSearchMode=@advancedSearchMode
    createGroup=(optional-action @onCreateGroup)
    editGroup=(optional-action @onEditGroup)
    searchForGroup=(optional-action @onSearchForGroup)
    searchRow=(optional-action @onSearchRow)
    removeRow=(optional-action @onRemoveRow)
    linkRow=(optional-action @onLinkRow)
    viewRow=(optional-action @onViewRow)
    downloadRow=(optional-action @onDownloadRow)
    showExternalContext=(optional-action @onShowContext)
    onNavigation=(optional-action @onNavigation)
  )
  as |lt|
}}

  {{lt.head onColumnClick=(action "onColumnClick")}}

  {{#lt.body
    classNames=(unless this.canSort "disable-sort")
    rowComponent=(component "lt-row" hoveredRowIds=@hoveredRowIds)
    canSelect=@canSelect
    onRowClick=this.onRowClick
    multiSelect=@multiSelect
    canExpand=@canExpand
    as |tb|
  }}
    {{#if this.table.isEmpty}}
      <tr class="lt-row empty">
        <td>{{t "No results."}}</td>
      </tr>
    {{/if}}
    {{yield tb this.table}}
  {{/lt.body}}

  {{#if this.showFooter}}
    {{#lt.foot}}
      {{#v-placeloader
        model=@rows pageSize=@pageSize loadMoreTask=@loadMoreTask
        as |nextPageSize|
      }}
        {{#each (range nextPageSize)}}
          <tr class="row-loading placeloader">
            {{#each this.allColumns as |c|}}
              <td
                class={{c.classNames}}
                style={{css-properties width=(or c.width "")}}
              ></td>
            {{/each}}
          </tr>
        {{/each}}
      {{/v-placeloader}}
      <tr>
        <td
          colspan={{this.allColumns.length}}
          class="v-table__footer__pagination"
        >
          {{v-pager
            scrollableContainerSelector=@scrollableContainerSelector
            model=@rows
            page=@page
            pageSize=@pageSize
            loadMoreTask=@loadMoreTask
            loadMoreAttr=@loadMoreAttr
            onLoadMore=(action (action "onLoadMore"))
          }}
        </td>
      </tr>
    {{/lt.foot}}
  {{/if}}

{{/light-table}}