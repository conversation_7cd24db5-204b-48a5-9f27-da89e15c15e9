<Vc::Modal
  id="packet-capture-modal"
  class="packet-capture-modal"
  @type="workflow"
  @isOpen={{true}}
  @onClose={{fn (mut @showPacketCaptureModal) false}}
  @primaryText={{if this.schedulingSetting "Schedule packet capture" "Run now"}}
  @primaryAction={{fn @primaryAction}}
  @secondaryText={{"Cancel"}}
  @secondaryAction={{action
    (queue (fn (mut @showPacketCaptureModal) false) (fn (mut @changeset) null))
  }}
  data-test-packet-capture__packet-capture-modal
  as |md|
>
  <md.header>
    {{#if (eq @header "Edit ")}}
      {{concat @header @changeset.name}}
    {{else}}
      {{concat @header " Packet Capture"}}
    {{/if}}
    <Vc::InAppHelp
      @header="Packet Capture"
      @content={{this.faq}}
      @onOpen={{fn (mut this.showPacketCaptureHelp) true}}
      @onClose={{fn (mut this.showPacketCaptureHelp) false}}
      @showPopout={{this.showPacketCaptureHelp}}
    />
  </md.header>
  <md.body>
    <Vc::Input::Text
      @value={{@changeset.name}}
      @onChange={{fn (mut @changeset.name)}}
      @type="text"
      @label="Name of Packet Capture"
      @hasError={{@changeset.error.name}}
      @errorMessage={{@changeset.error.name.validation.firstObject}}
      data-test-packet-capture__packet-capture-name
    />
    <Vc::Dropdown::Select
      {{did-insert this.getDefaultSelectedSensor}}
      class="mt-m"
      @label="Sensor"
      @selectedItem={{this.selectedSensorDropdownOption}}
      @onItemSelected={{this.updateSelectedSensor}}
      @options={{@sensors}}
      @tooltip="Traffic will be captured on all Sensor Capture Interfaces, Packet Capture on Management interface is not supported."
    />
    <PacketCapture::Modals::PacketCaptureModal::Scheduling
      @changeset={{@changeset}}
      @schedulingSetting={{this.schedulingSetting}}
      @timeZone={{this.timeZone}}
    />
    <PacketCapture::Modals::PacketCaptureModal::Limits
      @changeset={{@changeset}}
      @limits={{@limits}}
      @gbsTotal={{@gbsTotal}}
    />
    <PacketCapture::Modals::PacketCaptureModal::Filtering
      @changeset={{@changeset}}
    />
    <PacketCapture::Modals::PacketCaptureModal::Advanced
      @changeset={{@changeset}}
    />
  </md.body>
</Vc::Modal>