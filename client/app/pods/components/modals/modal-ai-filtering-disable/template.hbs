<Vc::Modal
  data-test--modal-ai-filtering-disable
  @isOpen={{@isOpen}}
  @size={{'small'}}
  @type={{'workflow'}}
  @primaryText="Turn off AI filtering"
  @secondaryText="Nevermind, keep AI filtering turned on"
  @onClose={{@onClose}}
  @primaryAction={{@onAccept}}
  @secondaryAction={{@onClose}}
  as |md|
>
  <md.header>
    {{t 'Turn off automatic AI filtering?'}}
  </md.header>
  <md.body>
    {{#if (gt @noOfDetections 0)}}
      <div class="modal-ai-filtering-disable__container">
        <div role="radiogroup" id="ai-filtering-disable-container">
          <Vc::RadioButton
            @value="1"
            @groupName="ai-filtering-disable"
            @isChecked={{true}}
            @onChange={{fn (mut this.keep) false}}
            aria-labelledby={{'ai-filtering-disable-container'}}
            data-test--modal-ai-filtering-disable-radio-restore
          >
            <p class="modal-ai-filtering-disable__restore">
              <span>{{t 'Restore a portion of detections that have already been filtered by AI'}}</span>
              <InAppHelpWithTracking
                class="settings-section__faq"
                @isHidden={{false}}
                @content={{this.faq}}
              />
            </p>
            <div class="de-em">
              <span>{{t 'AI filtering will be removed from '}}{{@noOfDetections}}{{t (pluralize ' detection' @noOfDetections)}}{{t '. This may take 30 minutes or more. A portion of detections may continue to be labeled as filtered and cannot be reverted.'}}</span>
            </div>
          </Vc::RadioButton>
          <Vc::RadioButton
            @value="2"
            @groupName="ai-filtering-disable"
            @isChecked={{false}}
            @onChange={{fn (mut this.keep) true}}
            aria-labelledby={{'ai-filtering-disable-container'}}
            data-test--modal-ai-filtering-disable-radio-leave
          >
            <p>{{t 'Leave existing detections as they are, but stop filtering detections moving forward'}}</p>
          </Vc::RadioButton>
        </div>
      </div>
    {{/if}}
  </md.body>
</Vc::Modal>