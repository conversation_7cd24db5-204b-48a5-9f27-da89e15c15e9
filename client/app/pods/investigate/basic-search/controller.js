import Controller from '@ember/controller';
import { tracked } from '@glimmer/tracking';
import { action } from '@ember/object';
import { inject as service } from '@ember/service';
import { run } from '@ember/runloop';
import { timeout } from 'ember-concurrency';
import { restartableTask } from 'ember-concurrency-decorators';
import { jsonHttpRequest } from 'vectra/utils/request-common';
import {
  apiTypeToSimpleType,
  createSubmenuForQuery,
  filterPillsNotInSchema,
  getColumnSchemaFromColumnName,
  getSortColumnMapping,
  mapColumnsToDataApiColumnRequest,
  clipboardFormatter,
} from 'vectra/utils/investigations-data-api-operators';
import { hasKey } from 'vectra/helpers/has-key';
import { transformQueryToDataApiCriteria } from 'vectra/utils/investigations-url-criteria-to-json-criteria';
import { decodeCriteriaUrlParam, encodeCriteriaUrlParam } from 'vectra/utils/investigations-deeplink-helpers';
import { mapErrorToErrorPage } from 'vectra/utils/investigations-error-helpers';
import constants, {
  DEFAULT_BASIC_SEARCH_QUERY_PARAMS,
  DEFAULT_SORT_COLUMN,
  PAGE_SIZE_OPTIONS,
  PILL_QUERY_TYPE,
} from 'vectra/reference-data/advanced-investigations/investigations-constants';
import {
  defaultColumnWidthByColumnName,
  defaultColumnWidthByDataSource,
} from 'vectra/reference-data/advanced-investigations/investigations-default-column-widths';
import { htmlSafe } from '@ember/template';
import { formatListToText } from 'vectra/helpers/format-list-to-text';
import { translateQueryFromPillBasedToSqlBased } from 'vectra/utils/investigations-url-criteria-to-sql';

const QUERY_URL = '/api/app/investigate/query';
const FIVE_MINUTES_IN_MS = 5 * 60 * 1000;
const ONE_SECOND_IN_MS = 1000;

export default class extends Controller {
  @service collection;
  @service clipboard;
  @service settings;
  @service toast;
  @service router;
  @service moment;
  @service localStorage;
  @service table;
  @service datepicker;

  @service('advancedInvestigations') advancedInvestigationsService;
  @service('investigationsMetrics') metricsService;

  queryParams = [
    { dataSource: 'ds' },
    { dataStream: 'a' },
    { multiConnector: 'c' },
    { query: 'q' },
    { dateFrom: 'df' },
    { dateTo: 'dt' },
    { relativeDateRange: 'rt' },
    { columns: 'cl' },
    { pageSize: 'ps' },
    { pageNumber: 'pn' },
    { sortColumn: 'sc' },
    { sortDirectionAscending: 'sda' },
    { shortcutId: 'sh' },
    { shortcutReferenceId: 'sr' },
  ];

  resultsElement = null;
  pageSizeOptions = PAGE_SIZE_OPTIONS;
  networkSid = null;
  availableConnectorsForTranslation = [];

  @tracked dataSource = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.dataSource;
  @tracked dataStream = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.dataStream;
  @tracked multiConnector = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.multiConnector;
  @tracked dateFrom = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.dateFrom;
  @tracked dateTo = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.dateTo;
  @tracked relativeDateRange = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.relativeDateRange;
  @tracked shortcutId = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.shortcutId;
  @tracked shortcutReferenceId = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.shortcutReferenceId;
  @tracked columns = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.columns;
  @tracked sortColumn = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.sortColumn;
  @tracked sortDirectionAscending = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.sortDirectionAscending;
  @tracked query = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.query;
  @tracked pageNumber = DEFAULT_BASIC_SEARCH_QUERY_PARAMS.pageNumber;
  @tracked pageSize =
    this.advancedInvestigationsService.getStoredPageSize() || DEFAULT_BASIC_SEARCH_QUERY_PARAMS.pageSize;

  @tracked model;
  @tracked errorId;
  @tracked errorPage;
  @tracked queryRowData = undefined;
  @tracked queryMetaData = {};
  @tracked requestId = '';
  @tracked showEditColumnsModal;
  @tracked showTableNotAvailableForSomeConnectorsBanner;
  @tracked connectorsWithoutData = [];
  @tracked showPartiallyExceededSearchableRangeBanner;
  @tracked showSortOtherThanTimestampBanner = false;
  @tracked showInvalidPillBanner;
  @tracked requestTrigger = 'linkOrRefresh';
  @tracked showLandingPage = true;
  @tracked isQueryCached = false;
  @tracked isDownloading = false;
  @tracked isDownloaded = false;
  @tracked lastSearchDate = null;
  @tracked hasUnchangedSearchParams = false;
  @tracked pendingSearchParams = {};
  @tracked columnsFormattedForTable = [];
  @tracked partiallyLoaded = false;
  @tracked lastColumns = [];

  get schema() {
    return this.model?.schema;
  }

  get hasAwsConnections() {
    return this.model.dataSourceOptions?.some(option => option.value === constants.AWS_DATA_SOURCE);
  }

  get hasM365Connections() {
    return this.model.dataSourceOptions?.some(option => option.value === constants.M365_DATA_SOURCE);
  }

  get hasNetworkConnections() {
    return this.model.dataSourceOptions?.some(option => option.value === constants.NETWORK_DATA_SOURCE);
  }

  get isSearchQueryValid() {
    return (
      this.dataSource &&
      this.dataStream &&
      (this.connector || this.multiConnector?.length > 0 || this.dataSource === constants.NETWORK_DATA_SOURCE) &&
      ((this.dateFrom && this.dateTo) || this.relativeDateRange)
    );
  }

  get noColumnsSelected() {
    return this.columns?.length <= 0;
  }

  get queryIsLoading() {
    return this.fetchTableRowsTask.isRunning || this.postQueryTask.isRunning;
  }

  get queryStatus() {
    return this.queryMetaData?.queryStatus;
  }

  get queryStatusRunning() {
    return this.queryMetaData?.queryStatus === 'RUNNING';
  }

  get showQueryResults() {
    return typeof this.queryRowData !== 'undefined' && this.queryMetaData?.queryStatus;
  }

  get isWaitingForResults() {
    return this.queryStatusRunning && !this.queryMetaData?.numRowsAvailable;
  }

  get isDownloadDisabled() {
    return (
      !this.schema || this.queryStatus !== 'SUCCESS' || !this.queryMetaData?.numRowsAvailable || this.noColumnsSelected
    );
  }

  get downloadResultsLocation() {
    return `api/app/investigate/download/${this.requestId}`;
  }

  get downloadResultsFilename() {
    return `investigate_results_${this.moment.moment().format('YYYYMMDDHHmmss')}.csv`;
  }

  get currentQuery() {
    if (!this.hasUnchangedSearchParams && hasKey([this.pendingSearchParams, 'query'], true)) {
      return this.pendingSearchParams.query;
    }
    return this._query;
  }

  get currentDateRange() {
    if (
      !this.hasUnchangedSearchParams &&
      hasKey([this.pendingSearchParams, 'dateFrom'], true) &&
      hasKey([this.pendingSearchParams, 'dateTo'], true) &&
      hasKey([this.pendingSearchParams, 'relativeDateRange'], true)
    ) {
      return {
        dateFrom: this.pendingSearchParams.dateFrom,
        dateTo: this.pendingSearchParams.dateTo,
        relativeDateRange: this.pendingSearchParams.relativeDateRange,
      };
    }
    return {
      dateFrom: this.dateFrom,
      dateTo: this.dateTo,
      relativeDateRange: this.relativeDateRange,
    };
  }

  get currentDataSource() {
    if (
      !this.hasUnchangedSearchParams &&
      hasKey([this.pendingSearchParams, 'dataSource'], true) &&
      hasKey([this.pendingSearchParams, 'dataStream'], true) &&
      (this.pendingSearchParams.dataSource === constants.NETWORK_DATA_SOURCE
        ? true
        : hasKey([this.pendingSearchParams, 'connector'], true) ||
          hasKey([this.pendingSearchParams, 'multiConnector'], true))
    ) {
      return this.pendingSearchParams.dataSource;
    }
    return this.dataSource;
  }

  get currentColumns() {
    if (!this.hasUnchangedSearchParams && hasKey([this.pendingSearchParams, 'columns'], true)) {
      return this.pendingSearchParams.columns;
    }
    return this.columns;
  }

  get currentDataStream() {
    if (!this.hasUnchangedSearchParams && hasKey([this.pendingSearchParams, 'dataStream'], true)) {
      return this.pendingSearchParams.dataStream;
    }
    return this.dataStream;
  }

  get currentConnector() {
    if (!this.hasUnchangedSearchParams && hasKey([this.pendingSearchParams, 'connector'], true)) {
      return this.pendingSearchParams.connector;
    }
    return this.connector;
  }

  get currentMultiConnector() {
    if (!this.hasUnchangedSearchParams && hasKey([this.pendingSearchParams, 'multiConnector'], true)) {
      return this.pendingSearchParams.multiConnector;
    }
    return this.multiConnector;
  }

  get currentSearch() {
    return {
      query: this.currentQuery,
      ...this.currentDateRange,
      columns: this.currentColumns,
      dataSource: this.currentDataSource,
      dataStream: this.currentDataStream,
      connector: this.currentConnector,
      multiConnector: this.currentMultiConnector,
    };
  }

  get searchResultCountString() {
    let resultCount =
      this.fetchTableRowsTask?.lastSuccessful?.value?.meta?.numRowsAvailable ?? this.queryMetaData?.numRowsAvailable;
    if (resultCount && !this.errorPage && !this.noColumnsSelected) {
      return resultCount.toString();
    }
    return '-';
  }

  get pageTitle() {
    if (this.queryStatus === 'RUNNING') {
      return 'Loading results...';
    }
    if (this.errorPage === 'TIMEOUT') {
      return 'Timed out query';
    }
    if (this.errorPage === 'QUERY_ON_HOLD') {
      return 'On-hold query';
    }
    if (this.queryStatus === 'FAILED') {
      return 'Issues running query';
    }
    if (this.searchResultCountString === '-' && this.queryStatus === 'SUCCESS') {
      return 'No results';
    }
    if (this.advancedInvestigationsService.pageTitle) {
      return `Investigate - ${this.advancedInvestigationsService.pageTitle}`;
    }
    if (this.model.dataSourceOptions?.findBy('value', this.dataSource)) {
      let dataSourceOption = this.model?.dataSourceOptions?.findBy('value', this.dataSource);
      let dataSourceLabel = dataSourceOption?.dataStreams?.findBy('value', this.dataStream)?.label;
      if (dataSourceLabel) {
        return `${dataSourceLabel} investigation`;
      }
    }

    return 'Start investigation';
  }

  get _query() {
    return decodeCriteriaUrlParam(this.query);
  }

  set _query(value) {
    this.query = encodeCriteriaUrlParam(value);
  }

  get textBasedQuery() {
    if (this.settings.featureEnabled('advanced_inv_query_language')) {
      if (this.dataSource === constants.NETWORK_DATA_SOURCE && !this?.networkSid) return null;

      return translateQueryFromPillBasedToSqlBased({
        dataSource: this.dataSource,
        dataStream: this.dataStream,
        multiConnector: this.dataSource === constants.NETWORK_DATA_SOURCE ? [...this.networkSid] : this.multiConnector,
        connectorOptions: this.model.connectorOptions,
        query: this._query,
        columns: this.columns,
        sortColumn: this.sortColumn,
        sortDirectionAscending: this.sortDirectionAscending,
        dateFrom: this.dateFrom,
        dateTo: this.dateTo,
        relativeDateRange: this.relativeDateRange,
        availableConnectors: this.availableConnectorsForTranslation,
      });
    }
    return '';
  }

  @action
  onPendingSearchParamsChange(searchParams, keysToRemove = []) {
    let params = { ...this.pendingSearchParams };
    keysToRemove.forEach(key => delete params[key]);
    params = {
      ...params,
      ...searchParams,
    };
    this.pendingSearchParams = params;
  }

  @action
  async copyToClipboard(value, type) {
    try {
      await this.clipboard.writeText(clipboardFormatter(value, type));
      this.toast.success('Copied to clipboard');
    } catch {
      this.toast.error('Copy to clipboard failed');
    }
  }

  @action
  getNotificationDismissed(notificationId) {
    let notificationDismissed = this.localStorage.getItem(`notificationDismissedForever-${notificationId}`);
    return notificationDismissed === 'true';
  }

  @action
  onDismissForeverClick(notificationId) {
    this.localStorage.setItem(`notificationDismissedForever-${notificationId}`, true);
  }

  @action
  reorderColumns(reorderedColumn, columns) {
    let { dataStream, columns: originalOrderOfColumns, dataSource } = this;
    let newOrderOfColumns = columns.map(column => column.schemaName);
    let columnName = reorderedColumn.schemaName;
    this.columns = newOrderOfColumns;

    let oldPosition = originalOrderOfColumns.indexOf(columnName).toString();
    let newPosition = newOrderOfColumns.indexOf(columnName).toString();
    this.requestTrigger = 'reorderColumn';
    this.collection.pendoTrack('investigationsOnColumnReorder', {
      dataSource,
      dataStream,
      columnName,
      oldPosition,
      newPosition,
      newOrderOfColumns: newOrderOfColumns.join(','),
    });
    this.advancedInvestigationsService.setStoredColumnSelection(this.dataSource, this.dataStream, this.columns);

    this.advancedInvestigationsService.updateQueryChangeset({ columns: this.columns });

    this.refreshColumns(true);
  }

  @action
  onColumnResized(column, width) {
    let { dataStream, dataSource } = this;
    let oldWidth = this._getColumnWidth(column.schemaName);
    this.collection.pendoTrack('investigationsOnColumnResized', {
      dataSource,
      dataStream,
      columnName: column.schemaName,
      newWidthInPx: width.split('px')[0],
      oldWidthInPx: oldWidth.split('px')[0],
    });
    this.advancedInvestigationsService.setStoredColumnSize(dataSource, dataStream, column.schemaName, width);
  }

  @action
  onRemoveColumnClick(column) {
    this.columns = this.columns.filter(item => item !== column.schemaName);
    this.advancedInvestigationsService.setStoredColumnSelection(this.dataSource, this.dataStream, this.columns);
    this.requestTrigger = 'removeColumn';
    this.advancedInvestigationsService.updateQueryChangeset({ columns: this.columns });
    this.refreshColumns(true);
  }

  @action
  onPageSizeChange(pageSize) {
    this._scrollToTopOfResults();
    this.pageSize = pageSize;
    this.pageNumber = 1;
    this.advancedInvestigationsService.setStoredPageSize(pageSize);
    this.requestTrigger = 'pageSizeChange';
  }

  @action
  onPageSelect(pageNumber) {
    this._scrollToTopOfResults();
    this.pageNumber = pageNumber;
    this.requestTrigger = 'pageChange';
  }

  @action
  onSortChange(_, column) {
    this.sortDirectionAscending = column.ascending;
    this.sortColumn = column.schemaName;
    this.pageNumber = 1;
    this.requestTrigger = 'sortColumn';
    this.refreshColumns(true);

    this.advancedInvestigationsService.updateQueryChangeset({
      sortColumn: this.sortColumn,
      sortDirectionAscending: this.sortDirectionAscending,
    });
  }

  @action
  refreshColumns(forceRefresh = false) {
    let hasChanges = (prevItems, newItems) => {
      if (prevItems.length !== newItems.length) {
        return true;
      }
      let sortedPrevItems = [...prevItems].sort();
      let sortedNewItems = [...newItems].sort();
      return !sortedPrevItems.every((item, index) => item === sortedNewItems[index]);
    };

    let currentTableItems = this.columnsFormattedForTable.map(item => item.schemaName);
    let hasEditModalChanges = hasChanges(this.lastColumns, this.columns);
    let hasColumnChangesFromTable = hasChanges(currentTableItems, this.columns);

    if (
      forceRefresh ||
      this.columnsFormattedForTable.length === 0 ||
      hasEditModalChanges ||
      hasColumnChangesFromTable
    ) {
      this.lastColumns = this.columns;
      this.columnsFormattedForTable = this._processColumns();
    }
    if (this.noColumnsSelected) {
      this.errorPage = 'NO_COLUMNS_SELECTED';
    }
  }

  @action
  async onSearchButtonClick(searchParams) {
    this.lastColumns = this.columns;
    this.dataSource = searchParams.dataSource;
    this.dataStream = searchParams.dataStream;
    this.connector = searchParams.connector;
    this.multiConnector = searchParams.multiConnector;
    this._query = searchParams.query;
    this.dateFrom = searchParams.dateFrom;
    this.dateTo = searchParams.dateTo;
    this.relativeDateRange = searchParams.relativeDateRange;
    this.pageNumber = 1;
    this.columns = searchParams.columns;
    this.requestTrigger = searchParams.requestTrigger;
    this.queryMetaData = {};

    // Force execute a new search despite params possibly unchanged
    await this.hardSearchRetry();
  }

  @action
  async hardSearchRetry() {
    let payload = await this.getSearchQueryPayload();
    this.advancedInvestigationsService.deleteQueryCache(payload);
    this.refreshRoute();
  }

  @action
  async executeSearch() {
    this.showTableNotAvailableForSomeConnectorsBanner = false;
    this.connectorsWithoutData = [];
    this.partiallyLoaded = false;
    this.isDownloading = false;
    this.isDownloaded = false;
    this.hasUnchangedSearchParams = true;
    this.pendingSearchParams = {};
    this.cancelCurrentQuery();
    this.queryMetaData = {};
    this.errorId = null;
    let availableConnectors = await this.advancedInvestigationsService.checkAvailableTablesForConnectors(
      this.dataSource,
      this.dataStream,
      this.multiConnector,
    );
    this.availableConnectorsForTranslation = availableConnectors;
    if (this.dataSource === constants.NETWORK_DATA_SOURCE) this.networkSid = availableConnectors.connectorsWithData;
    this.showLandingPage = false;
    this.showPartiallyExceededSearchableRangeBanner = false;
    if (availableConnectors?.status !== 'none') {
      if (availableConnectors?.status === 'partial') {
        this.showTableNotAvailableForSomeConnectorsBanner = true;
        this.connectorsWithoutData = availableConnectors.connectorsWithoutData;
      }
      let validatedQuery = filterPillsNotInSchema(this._query, this.schema);
      this.showInvalidPillBanner = this._query.length > validatedQuery.length;
      this.errorPage = null;
      this.postQueryTask.perform();
    } else {
      this.errorPage = 'TABLE_NOT_FOUND';
      this.lastSearchDate = {
        executedAt: this.moment.moment(),
        dateFrom: this.dateFrom,
        dateTo: this.dateTo,
      };
    }
  }

  @action
  refreshRoute() {
    this.router.transitionTo(this.router.currentRouteName);
  }

  @action
  cancelCurrentQuery() {
    this.postQueryTask.cancelAll();
    this.fetchTableRowsTask.cancelAll();
  }

  @action refreshLandingPageState() {
    this.cancelCurrentQuery();
    this.showTableNotAvailableForSomeConnectorsBanner = false;
    this.connectorsWithoutData = [];
    this.showLandingPage = true;
    this.errorId = null;
    this.errorPage = null;
    this.queryRowData = undefined;
    this.queryMetaData = {};
    this.requestId = '';
    this.columnsFormattedForTable = [];
    this.partiallyLoaded = false;
    this.lastSearchDate = null;
    this.advancedInvestigationsService.pageTitle = null;
  }

  @action
  trackDownload() {
    this.collection.pendoTrack('investigationsOnDownload', {
      fileSizeBytes: this.queryMetaData?.estimatedFileSizeBytes?.toString(),
      rows: this.searchResultCountString.toString(),
    });
  }

  @action
  updateIsDownloading(status) {
    this.isDownloading = status;
  }

  @action
  updateIsDownloaded(status) {
    this.isDownloaded = status;
  }

  async getSearchQueryPayload() {
    let {
      dataSource,
      dataStream,
      _query: query,
      dateFrom,
      dateTo,
      multiConnector,
      columns,
      sortColumn,
      sortDirectionAscending,
    } = this;
    let schema = await this.advancedInvestigationsService.getSchema(dataSource, dataStream);
    let validatedQuery = filterPillsNotInSchema(query, schema);
    let criteria = transformQueryToDataApiCriteria(validatedQuery);

    if (this.relativeDateRange) {
      let { startDate, endDate } = this.datepicker.getAbsoluteTimeFromRelative(this.relativeDateRange);
      dateFrom = startDate;
      dateTo = endDate;
    }

    let availableConnectors = await this.advancedInvestigationsService.checkAvailableTablesForConnectors(
      dataSource,
      dataStream,
      multiConnector,
    );

    let sort = [
      {
        column: getSortColumnMapping(sortColumn, schema, DEFAULT_SORT_COLUMN),
        direction: sortDirectionAscending ? 'Ascending' : 'Descending',
      },
    ];
    let timeRange = {
      from: new Date(dateFrom).toISOString(),
      to: new Date(dateTo).toISOString(),
    };

    let columnsForPayload = [...columns];
    // This removes the hostname's .name key if it is the sort field.
    let columnWithoutHostnameKey = sort[0].column.split('.')[0];
    if (!columnsForPayload.includes(columnWithoutHostnameKey)) {
      columnsForPayload.push(columnWithoutHostnameKey);
    }

    let subqueries = availableConnectors.connectorsWithData.map(sid => {
      return {
        source: {
          name: dataStream,
          sid,
        },
        timeRange,
        ...(criteria && { criteria }),
        columns: mapColumnsToDataApiColumnRequest(columnsForPayload, schema),
      };
    });

    return {
      query_type: 'union_all',
      subqueries,
      sort,
    };
  }

  @restartableTask
  *postQueryTask() {
    if (this.noColumnsSelected) {
      this.errorPage = 'NO_COLUMNS_SELECTED';
      return;
    }
    // Initialise query
    let payload = yield this.getSearchQueryPayload();

    let { timeRange } = payload.subqueries[0];

    this.lastSearchDate = {
      executedAt: this.moment.moment(),
      dateFrom: timeRange.from,
      dateTo: timeRange.to,
    };

    let response;
    let queryStartingTime;
    this.errorPage = null;
    try {
      // Check if request has already been executed and cached
      let cachedResponse = this.advancedInvestigationsService.getQueryCache(payload);
      if (cachedResponse) {
        response = cachedResponse;
        run(() => {
          this.isQueryCached = true;
          this.queryMetaData = { ...this.queryMetaData, queryStatus: 'RUNNING' };
        });
      } else {
        // Otherwise Make new query
        this.isQueryCached = false;
        this.showSortOtherThanTimestampBanner = false;
        this.queryMetaData = { queryStatus: 'RUNNING', numRowsAvailable: 0 };
        queryStartingTime = new Date().valueOf();
        response = yield jsonHttpRequest(QUERY_URL, payload, { method: 'POST' });
        // Write new query to cache
        if (response.requestId) {
          this.advancedInvestigationsService.setQueryCache(payload, response);
        }
      }

      let { requestId } = response;
      this.requestId = requestId;
      this._handleDateRangeBanner(response);
      this.fetchTableRowsTask.perform(queryStartingTime, payload);
    } catch (e) {
      this._handleQueryEndpointError(e);
    }
  }

  @restartableTask
  *fetchTableRowsTask(queryStartTime, payload) {
    let abortController = new AbortController();
    let { signal } = abortController;
    let currentState = 'RUNNING';
    let response;
    let statusForMetric = 'ABORT';
    let delayBetweenRetries = ONE_SECOND_IN_MS;
    let maxTime = FIVE_MINUTES_IN_MS;
    let firstResultMetricSent = false;
    let metricStartTime = this.collection.metricStartTime();
    let statusCode = 200;
    try {
      while (currentState === 'RUNNING') {
        response = yield this._getRowsHttp(signal);
        // Response handling logic
        // only sets state if data is different from previously
        if (response?.data?.length > 0 && JSON.stringify(this.queryRowData) !== JSON.stringify(response?.data)) {
          this.queryRowData = response?.data;
          this.refreshColumns();
          this.partiallyLoaded = true;
        }
        // Post after first job completes
        if (this._jobsCompleteInMetaData(response?.meta) === 1) {
          this.showSortOtherThanTimestampBanner = this.sortColumn !== 'timestamp';
          this._postAvailabilityMetric(statusCode, metricStartTime);
          if (!firstResultMetricSent) {
            firstResultMetricSent = true;
            this._postRttMetric('SUCCESS', queryStartTime, 'advanced_investigation_query_rtt', {
              query_type: PILL_QUERY_TYPE,
            });
          }
        }
        this.queryMetaData = response?.meta;
        currentState = response?.meta?.queryStatus;
        // Retry logic
        if (new Date().valueOf() > queryStartTime + maxTime) {
          let err = new Error('Max time for query reached');
          err.data = { error: { errorCode: 'TIMEOUT' } };
          throw err;
        }
        if (currentState === 'RUNNING') {
          yield timeout(delayBetweenRetries);
        }
      }
      if (response?.data !== undefined && JSON.stringify(this.queryRowData) !== JSON.stringify(response?.data)) {
        this.queryRowData = response?.data;
        this.refreshColumns();
      }
      statusForMetric = 'SUCCESS';
      return response;
    } catch (e) {
      console.error(e);
      this.queryMetaData.queryStatus = 'FAILED';
      statusCode = e.statusCode;
      statusForMetric = 'FAIL';
      return this._handleQueryEndpointError(e);
    } finally {
      this.refreshColumns();
      this.lastColumns = this.columns;
      abortController.abort();
      this.showSortOtherThanTimestampBanner = false;
      // Send Metrics
      if (!firstResultMetricSent) {
        if (statusForMetric !== 'ABORT') {
          this._postAvailabilityMetric(statusCode, metricStartTime);
        }
        this._postRttMetric(statusForMetric, queryStartTime, 'advanced_investigation_query_rtt', {
          query_type: PILL_QUERY_TYPE,
        });
      }
      this._postRttMetric(statusForMetric, queryStartTime, 'advanced_investigation_query_complete_rtt');
      this.collection.pendoTrack(
        'investigationsOnSearch',
        this._queryToPendoFormat(payload, this.queryMetaData, this.requestTrigger, this.dataSource),
      );
    }
  }

  async _getRowsHttp(signal) {
    return jsonHttpRequest(
      QUERY_URL,
      {
        requestId: this.requestId,
        page: this.pageNumber,
        pageSize: this.pageSize,
        trackingContext: this.requestTrigger || 'searchClick',
      },
      { signal },
    );
  }

  _jobsCompleteInMetaData(metaData) {
    return metaData?.jobs?.filter(job => job.status === 'SUCCESS')?.length;
  }

  _handleQueryEndpointError(e) {
    this.queryMetaData = { ...this.queryMetaData, queryStatus: 'FAILED' };
    if (e.data?.error?.errorCode === 'SYNTAX_ERROR') {
      let queryKeys = this._query?.map(query => query.key).join(',');
      this.collection.pendoTrack('investigationsOnSyntaxError', {
        keys: queryKeys || '',
      });
    }
    this.errorId = e.data?.error?.errorId ? e.data.error.errorId : null;
    this.errorPage = mapErrorToErrorPage(e);
  }

  _postAvailabilityMetric(statusForMetric, queryStartTimeMoment) {
    this.collection.postAvailabilityMetrics('adv_inv_query_initial', statusForMetric, {
      elapsedTime: queryStartTimeMoment,
      dataSourceType: this.dataSource,
    });
  }

  _getCanOverflow(type) {
    return apiTypeToSimpleType(type) !== 'json';
  }

  _postRttMetric(statusForMetric, queryStartTime, metricName, extraLabels = null) {
    if (queryStartTime) {
      const optionalLabels = {
        data_source: this.dataSource,
        ...((extraLabels && extraLabels) || {}),
      };
      this.metricsService.postRoundTripTimeMetric(metricName, statusForMetric, queryStartTime, optionalLabels);
    }
  }

  _handleDateRangeBanner(response) {
    if (response.searchableRange?.queryPartiallyExceededSearchableRange === true) {
      this.showPartiallyExceededSearchableRangeBanner = true;
    } else {
      this.showPartiallyExceededSearchableRangeBanner = false;
    }
  }

  get notAvailableSensorsMessage() {
    let { connectorsWithoutData, model, dataSource } = this;
    if (dataSource === constants.NETWORK_DATA_SOURCE) {
      return 'No data has been seen or received recently. Possible reasons include: you are not producing any data of this type, the sensor is not set up correctly, or we are experiencing an outage.';
    }
    let connectorOptions = model?.connectorOptions ?? [];
    let noDataConnectorsList = connectorOptions
      .filter(connectorOption => connectorsWithoutData.includes(connectorOption.value.replaceAll('-', '')))
      .map(connector => connector.label);

    let formattedConnectors = formatListToText([noDataConnectorsList], true);
    let reasonMessage =
      dataSource === constants.M365_DATA_SOURCE
        ? "Possible reasons include: data may not have been received yet, we are experiencing an outage, or you don't have the required Microsoft license to access this information."
        : 'Possible reasons include: no data has been received yet or we are experiencing an outage.';
    return htmlSafe(`Data not found for ${formattedConnectors}. ${reasonMessage}`);
  }

  get isExpandableTable() {
    return this.columnsFormattedForTable.filter(item => item.dataType === 'json').length;
  }

  _processColumns() {
    let columns =
      this.columns
        ?.map(column => getColumnSchemaFromColumnName(column, this.schema))
        .filter(columnSchema => Boolean(columnSchema))
        .map(columnSchema => ({
          label: columnSchema.name,
          schemaName: columnSchema.name,
          dataType: this._getDataType(columnSchema.type, columnSchema.usage),
          valuePath: columnSchema.underscoredId,
          width: this._getColumnWidth(columnSchema.name),
          cellComponent: this._getColumnCellComponent(columnSchema.type, columnSchema.usage),
          canOverflow: this._getCanOverflow(columnSchema.type),
          overflowTooltipPosition: 'auto',
          showRemoveColumnButton: true,
          sorted: this._getIsColumnSorted(columnSchema.name),
          ascending: this.sortDirectionAscending,
          draggable: true,
          sortable: true,
          resizable: true,
          minResizeWidth: 115,
          extra: {
            ...(columnSchema.description && {
              tooltipText: columnSchema.description,
            }),
            dateFormat: 'MMM Do YYYY HH:mm:ss.SSS',
            zeroAsEmdash: true,
            disableNumberFormatting:
              apiTypeToSimpleType(columnSchema.type) === 'number' && !columnSchema?.usage?.includes('quantity'),
            json: {
              formatted: true,
              lineClamp: 5,
            },
            directDataInteraction: {
              getDirectDataMenuItems: cellValue => {
                return this._createDataInteractionMenu(cellValue, columnSchema);
              },
              isEnabled: cellValue => {
                return this._showDataInteractionMenu(columnSchema, cellValue);
              },
              telemetryAction: (_cellValue, columnConfiguration, itemClicked) => {
                this.collection.pendoTrack('investigationsDirectData', {
                  key: columnConfiguration?.valuePath,
                  operator: itemClicked?.title,
                });
              },
            },
          },
        })) ?? [];
    return this.table.applyTimezoneToColumnHeaders(columns);
  }

  _getIsColumnSorted(columnName) {
    return this.sortColumn === columnName;
  }

  _getColumnWidth(name) {
    let { dataSource, dataStream, advancedInvestigationsService } = this;
    let cachedSize = advancedInvestigationsService.getStoredColumnSize(dataSource, dataStream, name);

    return (
      cachedSize ||
      defaultColumnWidthByColumnName?.[dataSource]?.[dataStream]?.[name] ||
      defaultColumnWidthByDataSource[dataSource] ||
      '320px'
    );
  }

  _getDataType(type, usage) {
    let simpleType = apiTypeToSimpleType(type);
    if (usage?.includes?.('JSONDocument') && simpleType === 'string') return 'json';
    return simpleType;
  }

  _getColumnCellComponent(type, usage) {
    let simpleType = apiTypeToSimpleType(type);
    if (usage?.includes?.('JSONDocument') && simpleType === 'string') simpleType = 'json';

    switch (simpleType) {
      default:
      case 'string':
        return 'vc/table/cells/text/cell-text';
      case 'timestamp':
        return 'vc/table/cells/date/cell-datetime';
      case 'host':
        return 'cells/cell-host-link';
      case 'account':
        return 'cells/cell-azure-account-link';
      case 'json':
        return 'vc/table/cells/json/cell-json';
      case 'number':
        return 'vc/table/cells/number/cell-number';
      case 'boolean':
        return 'vc/table/cells/boolean/cell-boolean-string';
    }
  }

  _showDataInteractionMenu(columnSchema, cellValue) {
    switch (apiTypeToSimpleType(columnSchema.type)) {
      default:
      case 'string':
        return true;
      case 'timestamp':
        return true;
      case 'json':
        return Boolean(cellValue?.length);
      case 'boolean':
        return true;
      case 'number':
        return Boolean(cellValue);
    }
  }

  _createDataInteractionMenu(value, columnSchema) {
    let copyAction = {
      title: 'Copy to clipboard',
      action: () => {
        this.copyToClipboard(value, columnSchema.type);
      },
      icon: 'actions/copy',
    };

    if (columnSchema.type === 'json') return [copyAction];

    let actionItems = [];
    let addToQueryOptions = {
      ...createSubmenuForQuery(
        this.currentSearch,
        'Add to query',
        columnSchema.name,
        columnSchema.type,
        columnSchema.usage,
        `${value}`,
      ),
    };
    if (addToQueryOptions?.items?.length > 0) actionItems.push(addToQueryOptions);

    let newQueryOptions = {
      ...createSubmenuForQuery(
        {
          ...this.currentSearch,
          query: [],
        },
        'New query',
        columnSchema.name,
        columnSchema.type,
        columnSchema.usage,
        `${value}`,
      ),
    };
    if (newQueryOptions?.items?.length > 0) actionItems.push(newQueryOptions);
    actionItems.push(copyAction);
    return actionItems;
  }

  _scrollToTopOfResults() {
    if (!this.resultsElement) return;
    let topOfResult = this.resultsElement.offsetTop;
    if (topOfResult < window.scrollY) {
      window.scrollTo(window.scrollX, topOfResult);
    }
  }

  _queryToPendoFormat(payload, queryMetaData, requestTrigger, dataSource) {
    let resultsCount = queryMetaData?.numRowsAvailable || null;

    let query = payload.subqueries[0];

    let dateFormat = 'DD MMM YYYY HH:mm';
    let timeRangeInHours = this.moment.moment(this.dateTo).diff(this.moment.moment(this.dateFrom), 'hours', true);
    let startEndTime = `${this.moment.moment(this.dateFrom).format(dateFormat)} - ${this.moment
      .moment(this.dateTo)
      .format(dateFormat)}`;

    let queryObject = {
      timeRangeInHours: Math.round(timeRangeInHours),
      timeStartEnd: startEndTime,
      dataSource,
      dataStream: query.source.name,
      numberOfColumns: query.columns?.length?.toString(),
      resultsCount,
      requestTrigger,
    };
    this.columns?.forEach((column, index) => {
      queryObject[`column${index + 1}`] = column;
    });
    this._query?.forEach((filter, index) => {
      queryObject[`key${index + 1}`] = filter.key;
      queryObject[`operator${index + 1}`] =
        filter.operator + (filter.value.caseSensitive === false ? ' (case-insensitive)' : '');
    });
    return queryObject;
  }
}
