import moment from 'moment';

import Route from '@ember/routing/route';
import { hash, task, timeout } from 'ember-concurrency';
import config from 'ember-get-config';
import { inject as service } from '@ember/service';
import { jsonHttpRequest } from 'vectra/utils/request-common';

const { refreshInterval } = config.APP.dashboard;

export default Route.extend({
  settings: service(),
  router: service(),
  advancedInvestigations: service(),

  suppressBrowserWarning: true,

  model() {
    let connectorsPromise = jsonHttpRequest('/api/app/data-sources/connectors');
    return hash({
      hasNetworkSensor:
        this.settings.featureEnabled('cloudbridge') &&
        this.advancedInvestigations
          .getDataSourceOptions()
          .then(options => options.filter(source => source.id === 'network')?.length > 0)
          .catch(() => false),
      hasO365Connector: connectorsPromise.then(({ connectors }) => (connectors?.o365 ?? []).length > 0),
      hasAzureCPConnector: connectorsPromise.then(({ connectors }) => (connectors?.['azure-cp'] ?? []).length > 0),
      o365Connectors: connectorsPromise.then(({ connectors }) => connectors?.o365 ?? []).catch(() => []),
      azureCPConnectors: connectorsPromise.then(({ connectors }) => connectors?.['azure-cp'] ?? []).catch(() => []),
    });
  },

  afterModel(model, transition) {
    if (transition.to.name === 'discover.index') {
      if (this.settings.featureEnabled('cloudbridge') && model.hasNetworkSensor) {
        this.router.replaceWith('discover.network-threat-surface');
      } else if (this.settings.featureEnabled('cloud_only') && model.hasO365Connector) {
        this.router.replaceWith('discover.azure-ad-threat-surface');
      } else if (this.settings.featureEnabled('cloud_only') && model.hasO365Connector) {
        this.router.replaceWith('discover.copilot-threat-surface');
      } else if (this.settings.featureEnabled('cloud_only') && model.hasAzureCPConnector) {
        this.router.replaceWith('discover.azure-threat-surface');
      } else {
        this.router.replaceWith('discover.threat');
      }
    }
    if (!this.settings.getSettings('auto-refresh', false)) return;
    this.refreshModel.perform();
  },

  setupController(controller, model) {
    this._super(controller, model);
    if (!this.settings.getSettings('auto-refresh', false)) return;
    controller.setProperties({
      refreshTask: this.refreshModel,
      isError: false,
      lastSuccessful: moment(),
    });
  },

  refreshModel: task(function* (retry) {
    if (this.settings.featureEnabled('cloud_only')) {
      // The dashboard refresh setting is not configurable on saas.
      return;
    }
    if (retry) {
      yield this.refresh();
    }
    if (this.controller?.get('isError')) return;
    yield timeout(refreshInterval);
    yield this.refresh();
  })
    .cancelOn('deactivate')
    .keepLatest(),

  actions: {
    error(error) {
      if (error.status === 403) {
        // Pass the error up Ember's handling chain
        return true;
      }
      if (this.controller) {
        this.controller.set('isError', true);
        return false;
      }
      return true;
    },
    loading() {
      return true;
    },
    willTransition(transition) {
      if (transition.to.name === 'discover.index') {
        if (this.context.hasNetworkSensor && this.settings.featureEnabled('cloudbridge')) {
          this.router.replaceWith('discover.network-threat-surface');
        } else if (this.context.hasO365Connector && this.settings.featureEnabled('cloud_only')) {
          this.router.replaceWith('discover.azure-ad-threat-surface');
        } else if (this.context.hasO365Connector) {
          this.router.replaceWith('discover.copilot-threat-surface');
        } else {
          this.router.replaceWith('discover.threat');
        }
      }
    },
  },
});
