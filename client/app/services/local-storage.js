import Service from '@ember/service';

export default class LocalStorageService extends Service {
  _alternativeStorage = {};

  get _localStorageIsDisabled() {
    return typeof window.localStorage !== 'object';
  }

  getItem(key) {
    if (this._localStorageIsDisabled) {
      return this._alternativeStorage[key] || null;
    }

    return localStorage.getItem(key);
  }

  setItem(key, value) {
    if (this._localStorageIsDisabled) {
      this._alternativeStorage[key] = value;
      return;
    }

    localStorage.setItem(key, value);
  }

  removeItem(key) {
    if (this._localStorageIsDisabled) {
      if (key in this._alternativeStorage) {
        delete this._alternativeStorage[key];
        return true;
      }
      return undefined;
    }

    return localStorage.removeItem(key);
  }

  clear() {
    if (this._localStorageIsDisabled) {
      this._alternativeStorage = {};
      return;
    }

    localStorage.clear();
  }
}
