/* generic user settings */
export default [
  { id: 'auto-refresh', value: false },
  { id: 'timezone', value: 'Etc/UTC' },
  { id: 'username', value: 'vadmin' },
  { id: 'auto-assignments', value: false },
  { id: 'userType', value: 'local' },
  // { id: 'hostLockdownEnabled', value: true },
  // { id: 'accountLockdownEnabled', value: true },
  // { id: 'azureADManualLockdownEnabled', value: true },
  // { id: 'userType', value: 'SAML' }, // switch to this obj in case you need
  {
    id: 'features',
    value: [
      'automatic_account_reconciliation',
      'api_tokens',
      'assignment',
      'assignments_v2',
      'aws_sensor',
      'campaigns',
      'carbon_black',
      'crowdstrike',
      'defender_lockdown',
      'external_domain_groups',
      'host_groups',
      'ldap_auth',
      'linked_account',
      'recall_hav',
      'searchability',
      'siem',
      'source_ip_groups',
      'total_recall',
      'triage_2_1',
      'kibana_via_detect',
      'ad_lockdown',
      'zpa_log_ingestion',
      'automatic_filtering',
      'ad_log_ingestion',
    ],
  },
  { id: 'passwordExpirationWarning', value: false },
  { id: 'enabledProducts', value: ['recall'] }, // comment this out if you want to test without recall
  {
    id: 'featureFlags',
    value: {
      cloud_only: true,
      account_detail_v2: false,
      account_detail_tab_v2: false,
      appliance_only: true,
      advanced_search: true,
      campaigns: true,
      celery_ui_reorder_impact_tasks: false,
      configure_rest_api: true,
      data_sources: true,
      data_sources_config: true,
      data_sources_connections: true,
      data_sources_regions: true,
      data_sources_nexus: true,
      enable_async_tasks: false,
      entv_dashboard: true,
      external_auth_ldap: true,
      external_auth_radius: true,
      external_auth_tacacs: true,
      groups: true,
      hosts: true,
      licenses: true,
      limited_time_links: true,
      observed_privilege: true,
      packet_captures: true,
      recall: true,
      saas_app_fe_metrics: true,
      saas_unified_user_management: false,
      stream: true,
      threat_feeds: true,
      settings_general: true,
      settings_sensor_flow: true,
      settings_inactive_user_logout: false,
      settings_login_caption: true,
      settings_user_password_policy: true,
      settings_remote_support: true,
      settings_account_association: true,
      setting_aws: true,
      settings_aws_cloudwatch: true,
      settings_aws_security_hub: true,
      settings_syslogs_to_kafka: true,
      notification_alert_email: true,
      settings_digest_emails: true,
      settings_smtp: true,
      settings_syslog: true,
      settings_external_connectors: true,
      settings_local_user_account_lockout: true,
      settings_edit_timezone: true,
      settings_edr: true,
      automatic_filtering: true,
      metadata_sharing: true,
      physical_hosts: true,
      customer_api_v2: true,
      user_notifications: true,
      create_azure_cp_sensor: true,
      add_remove_roles: true,
      advanced_inv_query_language: true,
      insight: true,
      azurecp_dashboard: true,
      network_discovery_dashboard: true,
      gmq_demo_2025: true,
      reports: true,
      report_host_severity: true,
      report_asset_inventory: true,
      report_operational_metrics: true,
      report_risk_migitation: true,
      contextual_links: true,
      network_traffic: true,
      saml_aws_cognito: false,
      unified_prioritization: true,
      data_sources_network: true,
      sensor_configuration: true,
      brain_setup: true,
      pendo_on_prem: true,
      account_groups: true,
      cloudbridge: true,
      azure_ad_lockdown: true,
      azure_ad_lockdown_automatic: false,
      account_lockdown_info_field: true,
      account_lockdown_account_expires: true,
      'vui/rux_ad_account_auto_lockdown': true,
      'vui/rux_host_auto_lockdown': true,
      ad_aad_accounts_lockdown_consolidation: true,
      traffic_validation: true,
      create_defender_sensor: true,
      create_sentinel_one_sensor: true,
      vsa_dashboard_naive: false,
      vsa_dashboard_cron: false,
      global_view_enabled: false,
      dynamic_groups: true,
      entity_search: false,
      resources: true,
      signal_efficacy_closed_as: false,
      signal_efficacy_public_preview: true,
      attack_graph: true,
      attack_graph_qux: false,
      dynamic_groups_member_transition: true,
      mdr_enabled: false,
      linked_account_triage_display: false,
      linked_account_groups: false,
      vectra_match_ruleset_modification: true,
      sase_remote_users: true,
      detection_resolution_setting: true,
      ad_groups: true,
    },
  },
  {
    id: 'dataSourceOnboarding',
    value: {
      enabled: true,
      hasSensorCreated: true,
    },
  },
  {
    id: 'notifications',
    value: {
      x24: {
        eolDate: 'September 30th, 2021',
      },
      saasOnlyTrial: {
        endOfTrial: *************, // timestamp or null
      },
    },
  },
  {
    id: 'inAppSupport',
    value: {
      enabled: true,
      pendoEnabledDate: moment().subtract(3, 'days').add(5, 'minutes').utc().format(),
      isUserOffline: false,
    },
  },
  {
    id: 'inactivity',
    value: 604800,
  },
  { id: 'instanceName', value: 'Global View Tenant' },
  {
    id: 'globalView',
    value: {
      globalViewAnchorUrl: 'https://www.vectra.ai',
      globalViewInstanceRole: 'anchor',
    },
  },
  { id: 'role', value: 'Global Analyst' },
  { id: 'investigateSearchableDays', value: 14 },
  { id: 'externalVui', value: '207790884667' },
  { id: 'internalBrain', value: 'saascacf3665ca4142ba8ca050b9150d35f4' },
];
