import { visit, findAll } from '@ember/test-helpers';
import { module, test } from 'qunit';
import { setupApplicationTest } from 'ember-qunit';
import { setupMirage } from 'ember-cli-mirage/test-support';
import { customConfig } from 'vectra/mirage/factories/userSetting';
import sinon from 'sinon';
import helpers from 'vectra/tests/generic/helpers';
import genericSelectors from 'vectra/mirage/selectors/genericSelectors';

let valueReportExpectedWidgetCount = 8;

let selectors = {
  timeframe: '[data-test-report-timeframe]',
  widgets: '[data-test-report-widget-container]',
  reportWrapper: '[data-test-report-container]',
};

module('Acceptance | reports | dynamic', hooks => {
  setupApplicationTest(hooks);
  setupMirage(hooks);

  let mockNow = new Date('2024-05-27T12:00:00Z');
  let dateNowStub;

  hooks.beforeEach(function () {
    dateNowStub = sinon.stub(Date, 'now').returns(mockNow);
    this.server.create(
      'userSetting',
      customConfig('featureFlags', {
        executive_overview_extra_widgets: true,
        mdr_enabled: false,
      }),
    );
  });

  hooks.afterEach(function () {
    dateNowStub.restore();
  });

  test('it shows correct timeframe on the dashboard header', async function (assert) {
    await visit('/reports/executive-overview');

    assert
      .dom(selectors.reportWrapper)
      .hasTextContaining('Reporting on the last 30 days. Apr 27th 2024 - May 26th 2024');
  });

  test('it render widgets based on configuration considering slug', async function (assert) {
    await visit('/reports/executive-overview');

    let postRequests = this.server.pretender.handledRequests.filter(
      ({ method, responseURL }) => method === 'POST' && responseURL.includes('/api/app/discover/query'),
    );
    assert.equal(postRequests.length, 0);

    assert.dom(selectors.widgets).exists({ count: valueReportExpectedWidgetCount });
  });

  test('it makes requests based on widget config', async function (assert) {
    await visit('/reports/executive-overview');

    let dataRequest = this.server.pretender.handledRequests.filter(
      ({ method, responseURL }) =>
        method === 'GET' && responseURL.includes('/api/app/report-widgets/noise-to-signal-funnel/data.json'),
    );
    assert.equal(dataRequest.length, 1);
  });

  test('it shows correct timeframe on the QUX dashboard header', async function (assert) {
    await visit('/reports/executive-overview-qux');

    assert
      .dom(selectors.reportWrapper)
      .hasTextContaining('Reporting on the last 30 days. Apr 27th 2024 - May 26th 2024');
  });

  test('it render QUX widgets based on configuration considering slug', async function (assert) {
    await visit('/reports/executive-overview-qux');

    let postRequests = this.server.pretender.handledRequests.filter(
      ({ method, responseURL }) => method === 'POST' && responseURL.includes('/api/app/discover/query'),
    );
    assert.equal(postRequests.length, 0);
    // QUX has one extra widget so we test that.
    assert.dom(selectors.widgets).exists({ count: valueReportExpectedWidgetCount + 1 });
  });

  test('it makes requests based on QUX widget config', async function (assert) {
    await visit('/reports/executive-overview-qux');
    let dataRequest = this.server.pretender.handledRequests.filter(
      ({ method, responseURL }) =>
        method === 'GET' && responseURL.includes('/api/app/report-widgets/noise-to-signal-funnel-qux/data.json'),
    );
    assert.equal(dataRequest.length, 1);
  });
});

module('Acceptance | reports | dynamicFlag', hooks => {
  setupApplicationTest(hooks);
  setupMirage(hooks);

  let mockNow = new Date('2024-05-27T12:00:00Z');
  let dateNowStub;

  hooks.beforeEach(function () {
    dateNowStub = sinon.stub(Date, 'now').returns(mockNow);
    this.server.create(
      'userSetting',
      customConfig('featureFlags', {
        executive_overview_extra_widgets: true,
      }),
    );
  });

  hooks.afterEach(function () {
    dateNowStub.restore();
  });

  test('Validate Attack Surface Dropdown doesnt have EDR', async function (assert) {
    await visit('/reports/executive-overview');
    await helpers.clickDropdown(1);
    // Get all the options in the dropdown.
    const optionsUnformatted = await findAll(genericSelectors.dropdownSelectOption);
    // Convert the items into an array/list for easier testing.
    const connectorStrings = Array.from(optionsUnformatted).map(el => el.innerText.trim());
    // Assert our EDR connectors arent there
    assert.notDeepEqual(connectorStrings, ['CrowdStrike', 'SentinelOne', 'Defender']);
  });

  test('Validate Attack Surface Dropdown has only expected surfaces', async function (assert) {
    await visit('/reports/executive-overview');
    await helpers.clickDropdown(1);
    // Get all the options in the dropdown.
    const optionsUnformatted = await findAll(genericSelectors.dropdownSelectOption);
    const connectorStrings = Array.from(optionsUnformatted).map(el => el.innerText.trim());
    assert.deepEqual(connectorStrings, ['View All', 'AWS CloudTrail', 'Azure AD & M365', 'Microsoft Azure', 'Network']);
  });
});
