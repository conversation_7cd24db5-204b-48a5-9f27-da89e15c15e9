import { module, test } from 'qunit';
import { click, currentURL, visit } from '@ember/test-helpers';
import { makeList, mock, mockFindAll, setupFactoryGuy } from 'ember-data-factory-guy';
import { setupApplicationTest } from 'ember-qunit';
import { captureBackstop, setupBackstopTest } from 'vectra/tests/helpers/visual-test';
import fixtures from 'vectra/tests/fixtures/packet-capture';

module('Visual | network-stats/packet-captures', hooks => {
  setupApplicationTest(hooks);
  setupFactoryGuy(hooks);
  setupBackstopTest(hooks);

  hooks.beforeEach(() => {
    mockFindAll('user-setting').returns({
      models: makeList('user-setting', 'with-feature-flags'),
    });

    mock({
      type: 'GET',
      url: `/api/app/network-stats/packet-captures`,
      responseText: {
        results: [
          {
            id: 123,
            name: 'Packet Capture Mirage 1',
            status: 'stopped',
            totalBytesCaptured: null,
            totalPacketsCaptured: null,
            captureStart: '2021-11-11T12:41:27.140Z',
            createdBy: '<PERSON>',
            downloadPacketURL: '/insight',
            actions: ['stop'],
            sensorLuid: 'Sensor 1',
          },
          {
            id: 124,
            name: 'Packet Capture Mirage 2',
            status: 'scheduled',
            scheduling: '2023-01-11T12:41:27.140Z',
            totalBytesCaptured: 9000,
            totalPacketsCaptured: 420,
            captureStart: '2021-11-11T12:41:27.140Z',
            createdBy: 'Jane Doe',
            downloadPacketURL: '/insight',
            actions: ['delete', 'stop'],
            sensorLuid: 'Sensor 2',
          },
          {
            id: 125,
            name: 'Packet Capture Mirage 3',
            status: 'error',
            totalBytesCaptured: 9000,
            totalPacketsCaptured: 420,
            captureStart: '2021-11-11T12:41:27.140Z',
            createdBy: 'John Doe',
            downloadPacketURL: '/insight',
            actions: ['edit', 'stop', 'view', 'copy'],
            sensorLuid: 'Sensor 3',
          },
          {
            id: 126,
            name: 'Packet Capture Mirage 4',
            status: 'starting',
            totalBytesCaptured: 9000,
            totalPacketsCaptured: 420,
            captureStart: '2021-11-10T12:41:27.140Z',
            createdBy: 'John Doe',
            downloadPacketURL: '/insight',
            actions: ['edit', 'stop', 'view', 'copy'],
            sensorLuid: 'Sensor 4',
          },
          {
            id: 128,
            name: 'Packet Capture Mirage 3',
            status: 'successful',
            totalBytesCaptured: 9000,
            totalPacketsCaptured: 420,
            captureStart: '2022-04-12T11:41:27.140Z',
            createdBy: 'John Doe',
            downloadPacketURL: '/insight',
            actions: ['edit', 'stop', 'view', 'copy'],
            sensorLuid: 'Sensor 6',
          },
        ],
        meta: {
          page: 1,
          pageSize: 25,
          totalCount: 6,
        },
        diskSpaceUsage: {
          gbsTotal: 5,
          gbs: 3,
        },
      },
    });
    mock({
      type: 'GET',
      url: `/api/app/sensorFilterOptions`,
      responseText: fixtures.sensorFilterOptions,
    });

    mock({
      type: 'GET',
      url: `/api/app/network-stats/packet-captures/limits`,
      responseText: fixtures.limits,
    });
  });

  test('network-stats.packet-captures', async function (assert) {
    await visit('/network-stats/packet-captures');
    assert.equal(currentURL(), '/network-stats/packet-captures');
    await captureBackstop(assert);
  });

  test('network-stats.packet-captures.modal', async function (assert) {
    await visit('/network-stats/packet-captures');
    await click('[data-test-packet-capture__packet-capture-button]');
    await click('[data-test-packet-capture__limit-size] input');
    await click('[data-test-packet-capture__filtering-ip-subnet] input');
    assert.equal(currentURL(), '/network-stats/packet-captures');
    await captureBackstop(assert);
  });
});
