import { module, test } from 'qunit';
import { setupFactoryGuy } from 'ember-data-factory-guy';
import { getContext, render } from '@ember/test-helpers';
import { setupRenderingTest } from 'ember-qunit';
import { hbs } from 'ember-cli-htmlbars';

module('Integration | Component | modals/modal-ai-filtering-disable', hooks => {
  setupRenderingTest(hooks);
  setupFactoryGuy(hooks);

  let selectors = {
    modalAiFilteringDisable: '[data-test--modal-ai-filtering-disable]',
    radioButtonRestore: '[data-test--modal-ai-filtering-disable-radio-restore]',
    radioButtonRestoreText: '[data-test--modal-ai-filtering-disable-radio-restore] + div',
    radioButtonLeave: '[data-test--modal-ai-filtering-disable-radio-leave]',
    radioButtonLeaveText: '[data-test--modal-ai-filtering-disable-radio-leave] + div',
  };

  let template = hbs`
    <Modals::ModalAiFilteringDisable
      @isOpen={{true}}
      @onClose={{this.onClose}}
      @noOfDetections={{this.noOfDetections}}
      @onAccept={{this.onAccept}}
    />
  `;

  function renderComponent(props = {}) {
    let defaultProps = {
      onClose: () => {},
      onAccept: () => {},
      noOfDetections: '150',
    };

    getContext().setProperties({
      ...defaultProps,
      ...props,
    });

    return render(template);
  }

  test('modal should render correctly', async function (assert) {
    await renderComponent();
    assert.dom(selectors.modalAiFilteringDisable).exists({ count: 1 });
  });

  test('modal should show an option to restore existing detections', async function (assert) {
    await renderComponent();

    assert.dom(selectors.radioButtonRestore).exists({ count: 1 });
    assert
      .dom(selectors.radioButtonRestoreText)
      .containsText('Restore a portion of detections that have already been filtered by AI');
  });

  test('Radio option to restore existing detections should be selected by default', async function (assert) {
    await renderComponent();

    assert.dom(selectors.radioButtonRestore).hasAttribute('aria-checked', 'true');
  });

  test('modal should show an option to leave existing detections as they are', async function (assert) {
    await renderComponent();

    assert.dom(selectors.radioButtonLeave).exists({ count: 1 });
    assert.dom(selectors.radioButtonLeaveText).containsText('Leave existing detections as they are');
  });

  test('modal shows no radio buttons when there are no detections', async function (assert) {
    await renderComponent({ noOfDetections: '0' });
    assert.dom(selectors.radioButtonLeave).doesNotExist();
    assert.dom(selectors.radioButtonRestore).doesNotExist();
  });
});
