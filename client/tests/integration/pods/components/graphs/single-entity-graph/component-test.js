import { click, findAll, getContext, render } from '@ember/test-helpers';
import { hbs } from 'ember-cli-htmlbars';
import { setupMirage } from 'ember-cli-mirage/test-support';
import { setupRenderingTest } from 'ember-qunit';
import { module, test } from 'qunit';
import sinon from 'sinon';
import genericSelectors from 'vectra/mirage/selectors/genericSelectors';
import Service from '@ember/service';

// Stub localStorage service
const localStorageStub = Service.extend({
  _storage: null,
  init() {
    this._super(...arguments);
    this._storage = {};
  },
  setItem(key, value) {
    this._storage[key] = value;
  },
  getItem(key) {
    return this._storage[key];
  },
});

module('Integration | Component | graphs/single-entity-graph', function (hooks) {
  setupRenderingTest(hooks);
  setupMirage(hooks);

  let windowOpenSpy;

  hooks.beforeEach(function () {
    this.owner.register('service:localStorage', localStorageStub);
    this.localStorage = this.owner.lookup('service:localStorage');
    windowOpenSpy = sinon.spy(window, 'open');
  });

  hooks.afterEach(function () {
    windowOpenSpy.restore();
  });

  let template = hbs`
    <Graphs::SingleEntityGraph
      @entity={{this.entity}}
      @detections={{this.detections}}
      @scores={{this.scores}}
      @stride={{this.stride}}
      @interpolate="step-after"
      @updateStride={{queue
        (fn (mut this.stride))
        (perform this.updateGraphTask this.stride)
      }}
    />
  `;

  let selectors = {
    graph: '[data-test-network-graph]',
    timeline: '.vc-timeline',
    label: '[data-test-axis-label]',
    roundedIcon: '[data-test-rounded-icon-button]',
    noActiveDetections: '[data-test-no-active-detections]',
    noActiveDetectionsText: '[data-test-no-active-detections-text]',
    noSupportedDetections: '[data-test-no-supported-detections]',
    noSupportedDetectionsText: '[data-test-no-supported-detections-text]',
    treeGraph: '[data-test-tree-graph]',
    scoreGraph: '[data-test-entity-graph-legend]',
    menuSelector: '[data-test--menu-switcher-button]',
    timelineGraph: '[data-test-graph-entity-container]',
  };

  let detection = {
    account: 'O365:<EMAIL>',
    category: 'COMMAND & CONTROL',
    detectionSlug: 'sw_o365_redundantAccess',
    originalDetectionType: 'O365 Redundant Access Creation',
    state: 'active',
    lastSeen: '2020-07-22T20:02:54Z',
    detectionType: 'O365 Redundant Access Creation',
    id: 44549,
    firstSeen: '2020-07-16T21:08:12Z',
  };

  let scores = [
    {
      timestamp: *************,
      u: 10,
    },
    {
      timestamp: *************,
      u: 60,
    },
  ];

  let entity = {
    modelName: 'accounts',
    id: 1,
    state: 'active',
  };

  function renderComponent(props = {}) {
    let defaultProps = {
      entity,
      detections: [{ ...detection }],
      scores: [{ ...scores }],
      stride: 7,
    };

    let context = getContext();
    context.setProperties({
      ...defaultProps,
      ...props,
    });

    return render(template);
  }

  test('when there are no active detections the scoring timeline appears and users can choose to visit another graph', async function (assert) {
    await renderComponent({
      entity: {
        ...entity,
        state: 'inactive',
      },
    });

    assert.dom(selectors.timelineGraph).exists({ count: 1 });
    await click(selectors.menuSelector);
    let menuItems = findAll(genericSelectors.menuButtonItem);
    await click(menuItems[0]);
    assert.dom(selectors.noActiveDetections).exists({ count: 1 });
    await click(selectors.menuSelector);
    await click(menuItems[1]);
    assert.dom(selectors.noActiveDetections).exists({ count: 1 });
    assert.dom(selectors.noActiveDetectionsText).hasText('No active detections to visualize');
  });

  test('when there are no supported detections the scoring timeline appears and users can choose to visit another graph', async function (assert) {
    this.server.get('/api/app/accounts/1/entity-graph', () => ({
      mainEntity: {
        name: '<EMAIL>',
        firstSeen: '2021-11-10T16:56:32Z',
        lastSeen: '2021-11-10T16:56:32Z',
        state: 'active',
        urgencyScore: 60,
      },
      edges: [
        {
          detectionType: 'Internal Stage Loader',
          firstSeen: '2020-01-07T19:10:48Z',
          lastSeen: '2020-01-07T19:15:48Z',
          state: 'active',
          category: 'LATERAL MOVEMENT',
          detectionId: 314,
          nodes: [],
        },
      ],
    }));
    await renderComponent();

    assert.dom(selectors.timelineGraph).exists({ count: 1 });
    await click(selectors.menuSelector);
    let menuItems = findAll(genericSelectors.menuButtonItem);
    await click(menuItems[0]);
    assert.dom(selectors.noSupportedDetections).exists({ count: 1 });
    await click(selectors.menuSelector);
    await click(menuItems[1]);
    assert.dom(selectors.noSupportedDetections).exists({ count: 1 });
    assert
      .dom(selectors.noSupportedDetectionsText)
      .hasText(`This entity has no detections with clear attributable targets.`);
  });

  test('graph and timeline render', async function (assert) {
    await renderComponent();

    assert.dom(selectors.graph).exists({ count: 1 });
    assert.dom(selectors.timeline).exists({ count: 1 });
  });

  test('timeline does not render when the tree graph is selected', async function (assert) {
    await renderComponent();

    await click(selectors.menuSelector);
    let menuItems = findAll(genericSelectors.menuButtonItem);
    await click(menuItems[1]);
    assert.dom(selectors.treeGraph).exists({ count: 1 });
    assert.dom(selectors.timeline).doesNotExist();
  });

  test('correct number of labels appear for timeline', async function (assert) {
    await renderComponent();

    let labels = findAll(selectors.label);
    assert.dom(selectors.label).exists({ count: 8 });
    assert.dom(labels[0]).hasText('15/01 00:33');
    assert.dom(labels[labels.length - 1]).hasText('04/02 17:43');
  });

  test('correct request is sent on render for accounts', async function (assert) {
    await renderComponent();

    let getAccountsGraphRequest = this.server.pretender.handledRequests.filter(
      ({ url, method }) => url === `/api/app/accounts/1/entity-graph` && method === 'GET',
    )?.length;
    assert.equal(getAccountsGraphRequest, 1);
  });

  test('correct request is sent on render for hosts', async function (assert) {
    await renderComponent({
      entity: { modelName: 'hosts', id: 2 },
    });

    let getHostsGraphRequest = this.server.pretender.handledRequests.filter(
      ({ url, method }) => url === `/api/app/hosts/2/entity-graph` && method === 'GET',
    )?.length;
    assert.equal(getHostsGraphRequest, 1);
  });

  test('onClick calls window.open with the correct URL when targetType is "account" and id exists', async function (assert) {
    let params = { data: { nodeType: 'account', navigationId: 123 } };
    this.owner.lookup('component:graphs/single-entity-graph').onClick(params);
    assert.ok(windowOpenSpy.calledOnceWith('/accounts/123', '_blank'), 'window.open was called with the correct URL');
  });

  test('onClick does not call window.open when targetType is not "account" or "host"', async function (assert) {
    let params = { data: { nodeType: 'generic', navigationId: 123 } };
    this.owner.lookup('component:graphs/single-entity-graph').onClick(params);
    assert.notOk(windowOpenSpy.called, 'window.open was not called');
  });

  test('onClick does not call window.open when id is missing', async function (assert) {
    let params = { data: { nodeType: 'account', navigationId: null } };
    this.owner.lookup('component:graphs/single-entity-graph').onClick(params);
    assert.notOk(windowOpenSpy.called, 'window.open was not called');
  });

  test('clicking the play button triggers onPlay and starts playback', async function (assert) {
    await renderComponent();

    let playPauseButton = findAll(selectors.roundedIcon)[1];
    let setIntervalSpy = sinon.spy(window, 'setInterval');
    await click(playPauseButton);
    assert.ok(setIntervalSpy.calledOnce, 'Playback interval is started when play is clicked');

    setIntervalSpy.restore();
  });

  test('clicking the pause button triggers the onPause and stops playback', async function (assert) {
    await renderComponent();
    let component = this.owner.lookup('component:graphs/single-entity-graph');
    component.onPlay();
    component.onPause();
    assert.false(component.isPlaying, 'Playback stopped');
    assert.strictEqual(component.playbackInterval, null, 'Playback interval is cleared');
  });

  test('clicking the Next button triggers the onNext action', async function (assert) {
    await renderComponent();
    let component = this.owner.lookup('component:graphs/single-entity-graph');
    component.links = [{ id: 1 }, { id: 2 }, { id: 3 }];
    component.currentLinkIndex = 0;
    let mockGraph = {
      dispatchAction: sinon.spy(),
    };
    component.setGraphInstance(mockGraph);
    component.onNext();

    assert.strictEqual(component.currentLinkIndex, 1, 'Moved to the next link');
    assert.ok(
      mockGraph.dispatchAction.calledWith(sinon.match({ type: 'highlight', dataIndex: [1] })),
      'Correct link is highlighted',
    );
  });

  test('clicking the Previous button triggers the onPrevious action', async function (assert) {
    await renderComponent();

    let component = this.owner.lookup('component:graphs/single-entity-graph');
    component.links = [{ id: 1 }, { id: 2 }, { id: 3 }];
    component.currentLinkIndex = 2;

    let mockGraph = {
      dispatchAction: sinon.spy(),
    };
    component.setGraphInstance(mockGraph);
    component.onPrevious();
    assert.strictEqual(component.currentLinkIndex, 1, 'Moved to the previous link');
    assert.ok(
      mockGraph.dispatchAction.calledWith(sinon.match({ type: 'highlight', dataIndex: [1] })),
      'Correct link is highlighted',
    );
  });

  test('handles empty nodes and links gracefully', async function (assert) {
    await renderComponent();
    let component = this.owner.lookup('component:graphs/single-entity-graph');

    component.nodes = [];
    component.links = [];
    let mockGraph = {
      dispatchAction: sinon.spy(),
    };
    component.setGraphInstance(mockGraph);
    assert.strictEqual(component.currentLinkIndex, 0, 'Current node index is 0 when nodes are empty');
    assert.strictEqual(component.steps, 0, 'Steps are 0 when links are empty');
  });

  test('onPlay restarts playback if currentLinkIndex is at the last link', async function (assert) {
    await renderComponent();
    let component = this.owner.lookup('component:graphs/single-entity-graph');

    component.links = [{ id: 1 }, { id: 2 }, { id: 3 }];
    component.currentLinkIndex = 3;
    let mockGraph = {
      dispatchAction: sinon.spy(),
    };
    component.setGraphInstance(mockGraph);
    component.onPlay();

    assert.strictEqual(component.currentLinkIndex, 0, 'Playback restarts at the first link');
    assert.true(component.isPlaying, 'Playback starts');
  });

  test('progress is updated correctly during playback', async function (assert) {
    await renderComponent();
    let component = this.owner.lookup('component:graphs/single-entity-graph');

    let mockGraph = {
      dispatchAction: sinon.spy(),
    };
    component.setGraphInstance(mockGraph);
    component.nodes = [{ id: 1 }, { id: 2 }, { id: 3 }];
    component.onPlay();
    component.currentNodeIndex = 1;
    component.progress = (component.currentNodeIndex / (component.nodes.length - 1)) * 100;

    assert.strictEqual(component.progress, 50, 'Progress is correctly updated to 50%');
  });

  test('recalls attack graph view from localStorage', async function (assert) {
    this.localStorage.setItem('entity-graph-preferred-view', 'attack');
    await renderComponent();
    assert.dom(selectors.graph).exists('Attack graph is shown from localStorage');
    assert.dom(selectors.treeGraph).doesNotExist('Tree graph is not shown');
    assert.dom(selectors.scoreGraph).doesNotExist('Scoring graph is not shown');
  });

  test('recalls tree graph view from localStorage', async function (assert) {
    this.localStorage.setItem('entity-graph-preferred-view', 'tree');
    await renderComponent();
    assert.dom(selectors.treeGraph).exists('Tree graph is shown from localStorage');
    assert.dom(selectors.graph).doesNotExist('Attack graph is not shown');
    assert.dom(selectors.scoreGraph).doesNotExist('Scoring graph is not shown');
  });

  test('recalls scoring graph view from localStorage', async function (assert) {
    this.localStorage.setItem('entity-graph-preferred-view', 'scoring');
    await renderComponent();
    assert.dom(selectors.scoreGraph).exists('Scoring graph is shown from localStorage');
    assert.dom(selectors.graph).doesNotExist('Attack graph is not shown');
    assert.dom(selectors.treeGraph).doesNotExist('Tree graph is not shown');
  });
});
