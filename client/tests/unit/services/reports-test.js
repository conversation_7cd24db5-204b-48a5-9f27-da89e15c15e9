import { module, test } from 'qunit';
import { setupTest } from 'ember-qunit';
import { validateReport } from 'vectra/tests/unit/reference-data/reports/report-validation-test';
import ReportWidgets from 'vectra/reference-data/reports/report-widgets';
import sinon from 'sinon';

module('Unit | Service | reports', hooks => {
  setupTest(hooks);

  let reportsService;

  hooks.beforeEach(function () {
    reportsService = this.owner.lookup('service:reports');
  });

  hooks.afterEach(function () {
    sinon.restore();
  });

  test('findBySlug fetches the RUX report when given executive-overview report slug', function (assert) {
    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'cloud_only';
    });
    let model = reportsService.findBySlug('executive-overview');
    assert.equal(model.name, 'Executive Overview');
    assert.ok(model.sections.every(section => section.name !== 'Key Assets'));
  });

  test('findBySlug fetches the QUX report when given the executive-overview-qux report slug', function (assert) {
    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'appliance_only';
    });
    let model = reportsService.findBySlug('executive-overview-qux');
    assert.equal(model.name, 'Executive Overview');
    const threatsSection = model.sections.find(section => section.name === 'Threats');
    assert.ok(threatsSection.widgets.some(widget => widget.id === 'key-assets')); // only in QUX
  });

  test('findBySlug returns undefined when given a non existing report slug', function (assert) {
    assert.equal(reportsService.findBySlug('garden-slug'), undefined);
  });

  test('findBySlug returns the report when given a report that has mdr_enabled is enabled', function (assert) {
    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return (
        feature === 'cloud_only' || feature === 'mdr_enabled' // this wont hide the report anymore
      );
    });
    assert.notEqual(reportsService.findBySlug('executive-overview'), undefined);
  });

  test('Validates the structure of the Executive Overview RUX report object', async function (assert) {
    const { noiseToSignalFunnel } = ReportWidgets;

    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'cloud_only';
    });

    let reportObj = reportsService.findBySlug('executive-overview');

    assert.deepEqual(validateReport(reportObj), [true, null]);
    assert.ok(reportObj.sections[0], noiseToSignalFunnel);
  });

  test('Validates the structure of the Executive Overview QUX report object', async function (assert) {
    const { noiseToSignalFunnel } = ReportWidgets;

    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'appliance_only';
    });

    let reportObj = reportsService.findBySlug('executive-overview-qux');

    assert.deepEqual(validateReport(reportObj), [true, null]);
    assert.ok(reportObj.sections[0], noiseToSignalFunnel);
  });
});
