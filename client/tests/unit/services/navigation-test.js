import { module, test } from 'qunit';
import { setupTest } from 'ember-qunit';
import { setupMirage } from 'ember-cli-mirage/test-support';
import sinon from 'sinon';

module('Unit | Service | navigation', hooks => {
  setupTest(hooks);
  setupMirage(hooks);

  let service;
  let handledRequests;

  hooks.beforeEach(function () {
    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'cloud_only';
    });
    service = this.owner.lookup('service:navigation');
    handledRequests = this.server.pretender.handledRequests;
  });

  module('Navigation Service Basic Functionality', () => {
    test('toggleMainNav toggles the mainNavExpanded property', function (assert) {
      const initialExpandedState = service.mainNavExpanded;
      service.toggleMainNav();
      assert.notEqual(service.mainNavExpanded, initialExpandedState, 'mainNavExpanded toggles correctly');

      service.toggleMainNav();
      assert.equal(service.mainNavExpanded, initialExpandedState, 'mainNavExpanded toggles back correctly');
    });

    test('toggleSubNav toggles the subNavExpanded property and sets subNavExpandedFor correctly', function (assert) {
      const routeName = 'discover';
      service.toggleSubNav(routeName);

      assert.ok(service.subNavExpanded, 'subNavExpanded is true after toggle');
      assert.equal(service.subNavExpandedFor, routeName, 'subNavExpandedFor is set to the route name');

      service.toggleSubNav();
      assert.notOk(service.subNavExpanded, 'subNavExpanded is false after second toggle');
      assert.equal(service.subNavExpandedFor, null, 'subNavExpandedFor is null after second toggle');
    });
  });

  module('Feature Flags and Navigation Behavior', () => {
    test('navigationConfig contains proper structure for visible items', function (assert) {
      const config = service.navigationConfig;

      assert.ok(
        config.some(item => item.type === 'item'),
        'Config contains navigation items',
      );
      assert.ok(
        config.some(item => item.type === 'group'),
        'Config contains navigation groups',
      );
    });

    test('navigationConfig supports visibility checks for items', function (assert) {
      const config = service.navigationConfig;
      const menuItem = config.find(item => item.label === 'Hunt');
      assert.false(menuItem.isVisible(), 'Hunt item is visible');
      const menuWithSubItems = config.find(item => item.label === 'Discover');
      const subitem = menuWithSubItems.items?.[0].items.find(item => item.label === 'Network');
      assert.ok(subitem.isVisible(), 'Subitem is visible based on its visibility function');
    });

    test('route settings are correctly set for navigation items', function (assert) {
      service._findAndSetRouteByName(service.navigationConfig, 'hunt');
      assert.strictEqual(
        service.currentRouteNavigationSettings.label,
        'Hunt',
        'Route settings are correctly set for the route',
      );

      service._findAndSetRouteByName(service.navigationConfig, 'discover.network-threat-surface');
      assert.strictEqual(
        service.currentRouteNavigationSettings.label,
        'Network',
        'Nested route settings are set correctly',
      );
    });

    test('navigation config can handle onClick actions', function (assert) {
      const config = service.navigationConfig;
      const logoutItem = config
        .find(item => item.type === 'group' && item.sticky === 'bottom')
        .items.find(item => item.label === 'Log Out');
      assert.ok(logoutItem.onClick, 'Log Out item has an onClick handler');
      const collapseItem = config.find(item => item.type === 'group').items.find(item => item.label === 'Collapse');
      assert.ok(collapseItem.onClick, 'Collapse item has an onClick handler');
    });
  });

  module('reports behaviour', innerHooks => {
    let reportGroups;

    innerHooks.beforeEach(function () {
      reportGroups = service.navigationConfig.find(items => items.label === 'Reports');
    });

    test('report groups are created as expected', function (assert) {
      assert.equal(reportGroups.items[0].label, 'EXECUTIVE REPORTS');
      assert.equal(reportGroups.items[1].label, 'SOC TEAM REPORTS');
    });

    test('dynamic reports get added as expected', function (assert) {
      let dynamicGroupItems = reportGroups.items[0].items;
      assert.equal(dynamicGroupItems.length, 1);
      assert.propContains(dynamicGroupItems[0], {
        label: 'Executive Overview',
        model: 'executive-overview',
        pageTitle: 'Executive Overview',
        subTitle: 'Understand the effectiveness of the Vectra Platform',
        routeName: 'reports.dynamic',
      });
    });
  });

  module('Global View', () => {
    let getEndpointCalls =
      endpoint =>
      ({ url }) =>
        url.includes(endpoint);

    test('The getChildInstancesTask task makes the correct request in anchor mode', async function (assert) {
      let globalViewService = this.owner.lookup('service:globalView');
      globalViewService.isAnchorMode = () => true;

      await service._getChildInstancesTask.perform();

      assert.equal(handledRequests.filter(getEndpointCalls(`/api/app/global/anchor/instances`)).length, 1);
    });

    test('The getChildInstancesTask task does NOT makes request in normal mode', async function (assert) {
      await service._getChildInstancesTask.perform();

      assert.equal(handledRequests.filter(getEndpointCalls(`/api/app/global/anchor/instances`)).length, 0);
    });
  });
});
