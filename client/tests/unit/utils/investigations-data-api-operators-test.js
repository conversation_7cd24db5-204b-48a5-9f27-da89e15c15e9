import { module, test } from 'qunit';
import { setupTest } from 'ember-qunit';

import {
  applyUnderscoreIds,
  createSubmenuForQuery,
  filterPillsNotInSchema,
  formatValuesForSearchOptions,
  getDefaultColumnsFromSchemaAndActivity,
  getFlattenedColumns,
  getGroupedSearchDropdownOptions,
  getOperatorLabelFromValue,
  getOperatorsFromType,
  getSortColumnMapping,
  isColumnDeprecated,
  mapColumnsToDataApiColumnRequest,
  clipboardFormatter,
} from 'vectra/utils/investigations-data-api-operators';
import investigationsDefaultColumns from 'vectra/reference-data/advanced-investigations/investigations-default-columns';

module('Unit | Utils | investigations-data-api-operators | mapColumnsToDataApiColumnRequest', hooks => {
  setupTest(hooks);
  test('mapColumnsToDataApiColumnRequest should return the correct aliased query', assert => {
    let columns = ['a.field'];
    let schema = [{ name: 'a.field', underscoredId: 'a_field' }];
    let expected = [
      {
        as: 'a_field',
        name: 'a.field',
      },
    ];
    let result = mapColumnsToDataApiColumnRequest(columns, schema);
    assert.deepEqual(result, expected);
  });

  test('mapColumnsToDataApiColumnRequest should remove columns that do not exist in the schema from its results', assert => {
    let columns = ['b.field'];
    let schema = [{ name: 'a.field', underscoredId: 'a_field' }];
    let expected = [];
    let result = mapColumnsToDataApiColumnRequest(columns, schema);
    assert.deepEqual(result, expected);
  });
});

module('Unit | Utils | investigations-data-api-operators | formatValuesForSearchOptions', hooks => {
  setupTest(hooks);

  test('if only value is provided, search and label should be populated based on it', assert => {
    let input = [
      {
        value: 'value one',
      },
      {
        value: 'value two',
      },
      {
        value: 'value three',
      },
    ];

    let result = formatValuesForSearchOptions(input);

    let expected = [
      {
        value: 'value one',
        label: 'value one',
        search: 'value one',
      },
      {
        value: 'value two',
        label: 'value two',
        search: 'value two',
      },
      {
        value: 'value three',
        label: 'value three',
        search: 'value three',
      },
    ];

    assert.deepEqual(result, expected);
  });

  test('if value and label provided, search should be both combined', assert => {
    let input = [
      {
        value: 'value one',
        label: 'label one',
      },
      {
        value: 'value two',
        label: 'label two',
      },
      {
        value: 'value three',
        label: 'label three',
      },
    ];

    let result = formatValuesForSearchOptions(input);

    let expected = [
      {
        value: 'value one',
        label: 'label one',
        search: 'value one label one',
      },
      {
        value: 'value two',
        label: 'label two',
        search: 'value two label two',
      },
      {
        value: 'value three',
        label: 'label three',
        search: 'value three label three',
      },
    ];

    assert.deepEqual(result, expected);
  });

  test('if value, label and search provided, everything should remain the same', assert => {
    let input = [
      {
        value: 'value one',
        label: 'label one',
        search: 'search one',
      },
      {
        value: 'value two',
        label: 'label two',
        search: 'search two',
      },
      {
        value: 'value three',
        label: 'label three',
        search: 'search three',
      },
    ];

    let result = formatValuesForSearchOptions(input);

    assert.deepEqual(result, input);
  });
});

module('Unit | Utils | investigations-data-api-operators | applyUnderscoreIds', hooks => {
  setupTest(hooks);

  test('function correctly returns nested search options with underscoreIds', assert => {
    let dynamicSchema = [
      {
        name: 'vectra.resolved_identity',
        type: 'string',
      },
      {
        name: 'user_name',
        type: 'string',
      },
    ];
    let expected = [
      {
        name: 'vectra.resolved_identity',
        underscoredId: 'vectra_resolved_identity',
        type: 'string',
      },
      {
        name: 'user_name',
        underscoredId: 'user_name',
        type: 'string',
      },
    ];

    let result = dynamicSchema.map(col => applyUnderscoreIds(col));

    assert.deepEqual(result, expected);
  });

  test('top level array parent is displayable but not filterable, nested array descendants are filterable but not displayable, and nested array parents are omitted', assert => {
    let dynamicSchema = {
      resources: [
        {
          arn: {
            type: 'string',
          },
        },
      ],
      role_arn: [
        {
          details: {
            names: [
              {
                firstname: { type: 'string' },
                lastname: { type: 'string' },
              },
            ],
          },
        },
      ],
    };
    let expected = [
      {
        name: 'resources',
        type: 'json',
        isFilterable: false,
        isDisplayable: true,
      },
      {
        name: 'resources[].arn',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
      {
        name: 'role_arn',
        type: 'json',
        isFilterable: false,
        isDisplayable: true,
      },
      {
        name: 'role_arn[].details.names[].firstname',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
      {
        name: 'role_arn[].details.names[].lastname',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
    ];
    let result = getFlattenedColumns(dynamicSchema, []);

    assert.deepEqual(result, expected);
  });

  test('at deeper levels of nesting, top level array parent is displayable but not filterable, nested array descendants are filterable but not displayable, and nested array parents are omitted', assert => {
    let dynamicSchema = {
      resources: [
        {
          arn: {
            type: 'string',
          },
        },
      ],
      role_arn: [
        {
          details: {
            nested_array_1: [
              {
                nested_array_2: [
                  {
                    descendant_1: { type: 'string' },
                    descendant_2: { type: 'string' },
                  },
                ],
              },
            ],
            names: [
              {
                firstname: { type: 'string' },
                lastname: { type: 'string' },
              },
            ],
          },
        },
      ],
    };
    let expected = [
      {
        name: 'resources',
        type: 'json',
        isFilterable: false,
        isDisplayable: true,
      },
      {
        name: 'resources[].arn',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
      {
        name: 'role_arn',
        type: 'json',
        isFilterable: false,
        isDisplayable: true,
      },
      {
        name: 'role_arn[].details.nested_array_1[].nested_array_2[].descendant_1',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
      {
        name: 'role_arn[].details.nested_array_1[].nested_array_2[].descendant_2',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
      {
        name: 'role_arn[].details.names[].firstname',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
      {
        name: 'role_arn[].details.names[].lastname',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
    ];
    let result = getFlattenedColumns(dynamicSchema, []);

    assert.deepEqual(result, expected);
  });

  test('function correctly allows a schema item to have the name of type', assert => {
    let dynamicSchema = {
      type: {
        arn: {
          type: 'string',
        },
      },
    };
    let expected = [
      {
        name: 'type.arn',
        type: 'string',
        isFilterable: true,
        isDisplayable: true,
      },
    ];
    let result = getFlattenedColumns(dynamicSchema);

    assert.deepEqual(result, expected);
  });

  test('function correctly maps nested array parent to a display-only column', assert => {
    let dynamicSchema = {
      resources: [
        {
          arn: { type: 'string' },
          account_id: { type: 'string' },
          type: { type: 'string' },
        },
      ],
    };
    let expected = [
      {
        name: 'resources',
        type: 'json',
        isFilterable: false,
        isDisplayable: true,
      },
      {
        isDisplayable: false,
        isFilterable: true,
        name: 'resources[].arn',
        type: 'string',
      },
      {
        isDisplayable: false,
        isFilterable: true,
        name: 'resources[].account_id',
        type: 'string',
      },
      {
        isDisplayable: false,
        isFilterable: true,
        name: 'resources[].type',
        type: 'string',
      },
    ];
    let result = getFlattenedColumns(dynamicSchema, [], true);

    assert.deepEqual(result, expected);
  });

  test('function correctly maps simple array of strings to a display and filterable column', assert => {
    let dynamicSchema = {
      resources: [{ type: 'string' }],
    };
    let expected = [
      {
        name: 'resources',
        type: 'json',
        isFilterable: false,
        isDisplayable: true,
      },
      {
        name: 'resources[]',
        type: 'string',
        isFilterable: true,
        isDisplayable: false,
      },
    ];
    let result = getFlattenedColumns(dynamicSchema, [], true);

    assert.deepEqual(result, expected);
  });

  test('function correctly sets host type and uses host.name field description', assert => {
    let dynamicSchema = {
      orig_host: {
        name: { type: 'string', description: 'originating host name' },
        usage: ['host'],
      },
    };
    let expected = [
      {
        name: 'orig_host',
        description: 'originating host name',
        type: 'host',
        isFilterable: true,
        isDisplayable: true,
        usage: ['host'],
      },
    ];
    let result = getFlattenedColumns(dynamicSchema);

    assert.deepEqual(result, expected);
  });
});

module('Unit | Utils | investigations-data-api-operators | getDefaultColumnsFromSchemaAndActivity', () => {
  test('function returns all default columns if they exist in the schema', assert => {
    let expected = investigationsDefaultColumns['audit.sharepoint'];
    let schema = expected.map(column => ({ name: column }));

    let result = getDefaultColumnsFromSchemaAndActivity(schema, 'audit.sharepoint');

    assert.deepEqual(result, expected);
  });

  test('function does not return columns which do not exist in the schema', assert => {
    let expected = investigationsDefaultColumns['audit.exchange'].splice(0, 4);
    let schema = expected.map(column => ({ name: column }));

    let result = getDefaultColumnsFromSchemaAndActivity(schema, 'audit.exchange');

    assert.deepEqual(result, expected);
  });

  test('function returns first 7 columns from the schema if none of the default columns exist in the schema', assert => {
    let schema = [
      {
        name: 'column_1',
      },
      {
        name: 'column_2',
        isDisplayable: false,
      },
      {
        name: 'column_3',
      },
      {
        name: 'column_4',
      },
      {
        name: 'column_5',
      },
      {
        name: 'column_6',
      },
      {
        name: 'column_7',
      },
      {
        name: 'column_8',
      },
      {
        name: 'column_9',
      },
    ];
    let expected = ['column_1', 'column_3', 'column_4', 'column_5', 'column_6', 'column_7', 'column_8'];
    let result = getDefaultColumnsFromSchemaAndActivity(schema, 'audit.exchange');

    assert.deepEqual(result, expected);
  });
});

module('Unit | Utils | investigations-data-api-operators | getOperatorsFromType', () => {
  test('boolean operators are returns for bool field type', assert => {
    let result = getOperatorsFromType('bool');

    assert.equal(result[0].valueFieldType, 'dropdown');
    assert.equal(result[0].dropdownValues[0].value, 'true');
    assert.equal(result[0].dropdownValues[0].label, 'true');
    assert.equal(result[0].dropdownValues[1].value, 'false');
    assert.equal(result[0].dropdownValues[1].label, 'false');
  });

  test('string operators are returns for string field type', assert => {
    let result = getOperatorsFromType('string');

    assert.deepEqual(
      result.map(i => i.value),
      ['is', 'is_not', 'sw', 'd_n_sw', 'c', 'd_n_c', 'ew', 'd_n_ew', 'iao', 'inao', 'i_e', 'i_n_e'],
    );
  });

  test('ip address operators are returns for ip address field', assert => {
    let result = getOperatorsFromType('string', ['ip_address'], false);
    assert.deepEqual(
      result.map(i => i.value),
      ['ip_is', 'ip_is_not', 'ipiao', 'ipinao', 'ip_btwn', 'ip_n_btwn', 'ip_in', 'ip_n_in'],
    );
  });

  test('ip address operators are returns for ip address field in direct data interaction', assert => {
    let result = getOperatorsFromType('string', ['ip_address'], true);
    assert.deepEqual(
      result.map(i => i.value),
      ['ip_is', 'ip_is_not', 'ipiao', 'ipinao'],
    );
  });

  test('int operators are returns for int field type', assert => {
    let result = getOperatorsFromType('int', '', false);

    assert.deepEqual(
      result.map(i => i.value),
      ['int_is', 'int_is_not', 'gt', 'gte', 'lt', 'lte', 'int_iao', 'int_inao', 'btwn', 'n_btwn'],
    );
  });

  test(' & string operators are returned where fieldName is defined [orig_name]', assert => {
    let result = getOperatorsFromType('string', '', false, 'orig_name');

    assert.deepEqual(
      result.map(i => i.value),
      ['is', 'is_not', 'sw', 'd_n_sw', 'c', 'd_n_c', 'ew', 'd_n_ew', 'iao', 'inao', 'i_e', 'i_n_e'],
    );
  });

  test('int operators are returned without direct data options if forDirectDataInteraction field is true', assert => {
    let result = getOperatorsFromType('int', null, true);

    assert.deepEqual(
      result.map(i => i.value),
      ['int_is', 'int_is_not', 'gt', 'gte', 'lt', 'lte', 'int_iao', 'int_inao'],
    );
  });

  test(' operators are returned where type is host', assert => {
    let result = getOperatorsFromType('host', 'host', false);
    assert.deepEqual(
      result.map(i => i.value),
      [
        'host_is',
        'host_is_not',
        'entity_sw',
        'entity_d_n_sw',
        'entity_c',
        'entity_d_n_c',
        'entity_ew',
        'entity_d_n_ew',
        'entity_iao',
        'entity_inao',
        'entity_i_e',
        'entity_i_n_e',
      ],
    );
  });

  test(' operators are returned where type is account', assert => {
    let result = getOperatorsFromType('account', 'account', false);
    assert.deepEqual(
      result.map(i => i.value),
      [
        'account_is',
        'account_is_not',
        'entity_sw',
        'entity_d_n_sw',
        'entity_c',
        'entity_d_n_c',
        'entity_ew',
        'entity_d_n_ew',
        'entity_iao',
        'entity_inao',
        'entity_i_e',
        'entity_i_n_e',
      ],
    );
  });
});

module('Unit | Utils | investigations-data-api-operators | getOperatorLabelFromValue', () => {
  test('returns correct label for a given value', assert => {
    let result = getOperatorLabelFromValue('d_n_sw');
    assert.equal(result, 'does not start with');
  });
});

module('Unit | Utils | investigations-data-api-operators | isColumnDeprecated', () => {
  test('isColumnDeprecated removes any values from the schema that are deprecated', function (assert) {
    let schema = [
      {
        name: 'Time',
        type: 'timestamp',
        restrictions: [],
      },
      {
        name: 'eventVersion',
        type: 'string',
        restrictions: ['deprecated'],
      },
      {
        name: 'UserIdentity.type',
        type: 'string',
      },
    ];

    let expectedSchema = [
      {
        name: 'Time',
        type: 'timestamp',
        restrictions: [],
      },
      {
        name: 'UserIdentity.type',
        type: 'string',
      },
    ];

    assert.deepEqual(
      schema.filter(col => !isColumnDeprecated(col)),
      expectedSchema,
    );
  });
});

module('Unit | Utils | investigations-data-api-operators | Direct Data', () => {
  test('String field creates correct menu items with empty current query', function (assert) {
    let expected = {
      title: 'New Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'is', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'is_not', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'sw', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'starts with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'd_n_sw', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not start with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'c', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'contains',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'd_n_c', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not contain',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'ew', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'ends with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'd_n_ew', 'value', false]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not end with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'iao', ['value']]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is any of',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'inao', ['value']]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not any of',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'i_e', null]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is empty',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'i_n_e', null]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not empty',
        },
      ],
    };

    let result = createSubmenuForQuery(
      {
        query: [],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'New Query',
      'key',
      'string',
      [],
      'value',
    );

    assert.deepEqual(result, expected);
  });

  test('Bool field creates correct menu items when false', function (assert) {
    let expected = {
      title: 'New Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'bool_is', 'false']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'bool_is_not', 'false']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
      ],
    };

    let result = createSubmenuForQuery(
      {
        query: [],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'New Query',
      'key',
      'bool',
      [],
      false,
    );

    assert.deepEqual(result, expected);
  });

  test('Bool field creates correct menu items when true', function (assert) {
    let expected = {
      title: 'New Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'bool_is', 'true']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'bool_is_not', 'true']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
      ],
    };

    let result = createSubmenuForQuery(
      {
        query: [],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'New Query',
      'key',
      'bool',
      [],
      true,
    );

    assert.deepEqual(result, expected);
  });

  test('Boolean field creates correct menu items when false', function (assert) {
    let expected = {
      title: 'New Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'bool_is', 'false']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'bool_is_not', 'false']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
      ],
    };

    let result = createSubmenuForQuery(
      {
        query: [],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'New Query',
      'key',
      'boolean',
      [],
      false,
    );

    assert.deepEqual(result, expected);
  });

  test('Int field creates correct menu items', function (assert) {
    let expected = {
      title: 'New Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'int_is', '1']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'int_is_not', '1']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'gt', '1']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is greater than',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'gte', '1']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is greater than or equals',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'lt', '1']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is less than',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'lte', '1']],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is less than or equals',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'int_iao', ['1']]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is any of',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['key', 'int_inao', ['1']]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not any of',
        },
      ],
    };

    let result = createSubmenuForQuery(
      {
        query: [],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'New Query',
      'key',
      'int',
      [],
      '1',
    );

    assert.deepEqual(result, expected);
  });

  test('String field creates correct menu items with a criteria passed already', function (assert) {
    let expected = {
      title: 'Add to Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'is', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'is_not', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'sw', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'starts with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'd_n_sw', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not start with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'c', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'contains',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'd_n_c', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not contain',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'ew', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'ends with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'd_n_ew', 'value', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not end with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'iao', ['value']],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is any of',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'inao', ['value']],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not any of',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'i_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is empty',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'i_n_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not empty',
        },
      ],
    };
    let result = createSubmenuForQuery(
      {
        query: [{ key: 'The room', operator: 'is', value: { value: 'tied together' } }],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'Add to Query',
      'key',
      'string',
      [],
      'value',
    );
    assert.deepEqual(result, expected);
  });

  test('String field with empty string value creates correct menu items with a criteria passed already', function (assert) {
    let expected = {
      title: 'Add to Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together', false],
                ['key', 'i_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is empty',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together', false],
                ['key', 'i_n_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not empty',
        },
      ],
    };
    let result = createSubmenuForQuery(
      {
        query: [{ key: 'The room', operator: 'is', value: { value: 'tied together', caseSensitive: false } }],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'Add to Query',
      'key',
      'string',
      [],
      '',
    );
    assert.deepEqual(result, expected);
  });

  test('String field with null string value creates correct menu items with a criteria passed already', function (assert) {
    let expected = {
      title: 'Add to Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'i_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is empty',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'is', 'tied together'],
                ['key', 'i_n_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not empty',
        },
      ],
    };
    let result = createSubmenuForQuery(
      {
        query: [{ key: 'The room', operator: 'is', value: { value: 'tied together' } }],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'Add to Query',
      'key',
      'string',
      [],
      null,
    );
    assert.deepEqual(result, expected);
  });

  test('Unkown field type creates no menu items', function (assert) {
    let expected = {
      title: 'New Query',
      items: [],
    };

    let result = createSubmenuForQuery(
      {
        query: [],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'New Query',
      'key',
      'unknown_type',
      [],
      '1',
    );

    assert.deepEqual(result, expected);
  });

  test('Add to query correctly collapses matching multivalue strings together', function (assert) {
    let expected = {
      title: 'Add to Query',
      items: [
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'is', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'is_not', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'sw', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'starts with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'd_n_sw', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not start with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'c', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'contains',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'd_n_c', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not contain',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'ew', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'ends with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'd_n_ew', 'a movie?', false],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'does not end with',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [['The room', 'iao', ['tied together', 'a movie?']]],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is any of',
        },
        {
          link: {
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'inao', ['a movie?']],
              ],
              relativeDateRange: 'last-24-hours',
            },
            route: 'investigate',
          },
          title: 'is not any of',
        },
        {
          link: {
            route: 'investigate',
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'i_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
          },
          title: 'is empty',
        },
        {
          link: {
            route: 'investigate',
            query: {
              connector: 'connector-name',
              dataSource: 'data-source-name',
              dataStream: 'data-stream-name',
              dateFrom: null,
              dateTo: null,
              pageNumber: 1,
              query: [
                ['The room', 'iao', ['tied together']],
                ['The room', 'i_n_e', null],
              ],
              relativeDateRange: 'last-24-hours',
            },
          },
          title: 'is not empty',
        },
      ],
    };
    let result = createSubmenuForQuery(
      {
        query: [{ key: 'The room', operator: 'iao', value: [{ value: 'tied together' }] }],
        dateFrom: null,
        dateTo: null,
        relativeDateRange: 'last-24-hours',
        dataSource: 'data-source-name',
        dataStream: 'data-stream-name',
        connector: 'connector-name',
      },
      'Add to Query',
      'The room',
      'string',
      [],
      'a movie?',
    );

    assert.deepEqual(result, expected);
  });
});

module('Unit | Utils | investigations-data-api-operators | getGroupedSearchDropdownOptions', () => {
  let schema = [
    {
      name: 'schema01',
      type: 'string',
    },
    {
      name: 'schema02',
      type: 'string',
      operators: ['is'],
    },
    {
      name: 'schema03',
      type: 'string',
      operators: ['is'],
    },
    {
      name: 'schema04',
      type: 'string',
      operators: ['is'],
    },
    {
      name: 'schema05',
      type: 'string',
    },
  ];

  test('function returns options grouped by suggested and available excluding schemas with no operators', assert => {
    let suggested = {
      cloudtrail: ['schema04', 'schema02'],
    };

    let expected = [
      {
        groupName: 'Suggested',
        options: [
          {
            label: 'schema02',
            value: 'schema02',
            operators: ['is'],

            tooltip: null,
          },
          {
            label: 'schema04',
            value: 'schema04',
            operators: ['is'],

            tooltip: null,
          },
        ],
      },
      {
        groupName: 'Available',
        options: [
          {
            label: 'schema03',
            value: 'schema03',
            operators: ['is'],

            tooltip: null,
          },
        ],
      },
    ];
    let result = getGroupedSearchDropdownOptions(schema, 'cloudtrail', suggested);
    assert.deepEqual(result, expected);
  });

  test('function does not return grouped options if no suggested schemas are returned', assert => {
    let suggested = {
      beacon: ['schema07', 'schema08'],
    };

    let expected = [
      {
        label: 'schema02',
        operators: ['is'],
        tooltip: null,
        value: 'schema02',
      },
      {
        label: 'schema03',
        operators: ['is'],
        tooltip: null,
        value: 'schema03',
      },
      {
        label: 'schema04',
        operators: ['is'],
        tooltip: null,
        value: 'schema04',
      },
    ];

    let result = getGroupedSearchDropdownOptions(schema, 'beacon', suggested);
    assert.deepEqual(result, expected);
  });

  module('Unit | Utils | investigations-data-api-operators | filterPillsNotInSchema', hooks => {
    setupTest(hooks);
    test('function returns pills if their key exists in the schema and the pill operator matches the type in the current schema', assert => {
      let testSchema = [
        {
          name: 'valid_key',
          type: 'string',
        },
        {
          name: 'invalid_type',
          type: 'string',
        },
      ];
      let query = [
        { key: 'valid_key', operator: 'is', value: 'returned' },
        { key: 'invalid_type', operator: 'boolIs', value: 'returned' },
        { key: 'invalid_key', operator: 'is', value: 'missing' },
      ];
      let expected = [{ key: 'valid_key', operator: 'is', value: 'returned' }];
      let result = filterPillsNotInSchema(query, testSchema);
      assert.deepEqual(result, expected);
    });
  });

  module('Unit | Utils | investigations-data-api-operators | getSortColumnMapping', hooks => {
    setupTest(hooks);

    test('function returns host.name if column type is host', assert => {
      let sortColumn = 'orig_host';
      let testSchema = [
        {
          name: 'orig_host',
          type: 'host',
        },
      ];
      let defaultSortColumn = 'timestamp';

      let expected = 'orig_host.name';
      let result = getSortColumnMapping(sortColumn, testSchema, defaultSortColumn);
      assert.deepEqual(result, expected);
    });

    test('function returns account.name if column type is account', assert => {
      let sortColumn = 'entra_id_account';
      let testSchema = [
        {
          name: 'entra_id_account',
          type: 'account',
        },
      ];
      let defaultSortColumn = 'timestamp';

      let expected = 'entra_id_account.name';
      let result = getSortColumnMapping(sortColumn, testSchema, defaultSortColumn);
      assert.deepEqual(result, expected);
    });

    test('sorts on underscored alias if account column is nested', assert => {
      let sortColumn = 'vectra.identity';
      let testSchema = [
        {
          name: 'vectra.identity',
          type: 'account',
        },
      ];
      let defaultSortColumn = 'timestamp';

      let expected = 'vectra_identity.name';
      let result = getSortColumnMapping(sortColumn, testSchema, defaultSortColumn);
      assert.deepEqual(result, expected);
    });

    test('function returns timestamp if column not in schema', assert => {
      let sortColumn = 'not_in_schema';
      let testSchema = [
        {
          name: 'in_schema',
          type: 'string',
        },
      ];
      let defaultSortColumn = 'timestamp';

      let expected = 'timestamp';
      let result = getSortColumnMapping(sortColumn, testSchema, defaultSortColumn);
      assert.deepEqual(result, expected);
    });

    test('function returns column if column in schema', assert => {
      let sortColumn = 'in_schema';
      let testSchema = [
        {
          name: 'in_schema',
          type: 'string',
        },
      ];
      let defaultSortColumn = 'timestamp';

      let expected = 'in_schema';
      let result = getSortColumnMapping(sortColumn, testSchema, defaultSortColumn);
      assert.deepEqual(result, expected);
    });
    test('function returns column and removes underscore', assert => {
      let sortColumn = 'in.schema';
      let testSchema = [
        {
          name: 'in.schema',
          type: 'string',
        },
      ];
      let defaultSortColumn = 'timestamp';
      let expected = 'in_schema';
      let result = getSortColumnMapping(sortColumn, testSchema, defaultSortColumn);
      assert.deepEqual(result, expected);
    });
  });

  module('Unit | Utils | investigations-data-api-operators | clipboardFormatter', hooks => {
    setupTest(hooks);

    test('formats single object json values correctly', function (assert) {
      let value = { name: 'test', id: 123 };
      let type = 'json';
      let expected = JSON.stringify(value);
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });

    test('formats array with single object json values correctly', function (assert) {
      let value = [{ name: 'test', id: 123 }];
      let type = 'json';
      let expected = JSON.stringify(value[0]);
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });

    test('formats array with single primitive json values correctly', function (assert) {
      let value = ['test'];
      let type = 'json';
      let expected = 'test';
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });

    test('formats array with multiple elements json values correctly', function (assert) {
      let value = ['test', 'test2'];
      let type = 'json';
      let expected = JSON.stringify(value);
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });

    test('formats timestamp values correctly', function (assert) {
      let date = new Date('2023-01-01T12:00:00Z');
      let value = date.toISOString();
      let type = 'timestamp';
      let expected = date.toISOString();
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });

    test('returns the value directly for any other type', function (assert) {
      let value = 42;
      let type = 'number';
      let expected = 42;
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });

    test('handles string value with json type', function (assert) {
      let value = 'not json';
      let type = 'json';
      let expected = 'not json';
      let result = clipboardFormatter(value, type);
      assert.strictEqual(result, expected);
    });
  });
});
