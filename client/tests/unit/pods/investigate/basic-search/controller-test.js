import { module, test } from 'qunit';
import { mock } from 'ember-data-factory-guy';
import { setupTest } from 'ember-qunit';
import sinon from 'sinon';
import { waitUntil } from '@ember/test-helpers';
import { PILL_QUERY_TYPE } from 'vectra/reference-data/advanced-investigations/investigations-constants';

module('Unit | Controller | investigate | basic-search', function (hooks) {
  setupTest(hooks);

  let controller;
  let clock;
  let pendoStub;
  let advInvestigationsService;
  let settingsService;
  let metricsService;
  let routerService;

  hooks.beforeEach(function () {
    window.localStorage.clear();
    controller = this.owner.lookup('controller:investigate/basic-search');
    controller.dateTo = 1641297600000;
    controller.dateFrom = 1641276000000;

    let collectionService = this.owner.lookup('service:collection');
    pendoStub = sinon.stub(collectionService, 'pendoTrack');

    advInvestigationsService = this.owner.lookup('service:advancedInvestigations');
    settingsService = this.owner.lookup('service:settings');
    metricsService = this.owner.lookup('service:investigationsMetrics');
    routerService = this.owner.lookup('service:router');

    sinon.stub(routerService, 'transitionTo');

    clock = sinon.useFakeTimers(new Date('2022-01-04T12:00'));

    controller.set('columns', ['firstname', 'isValid']);
  });

  hooks.afterEach(function () {
    window.localStorage.clear();
    advInvestigationsService.destroy();
    settingsService.destroy();
    metricsService.destroy();
    routerService.destroy();
    clock.restore();
    sinon.restore();
  });

  test('onSearchButtonClick should set the request trigger correctly', async function (assert) {
    sinon.stub(controller.postQueryTask, 'perform');
    sinon.stub(advInvestigationsService, 'getSchema').returns([
      {
        name: 'event_source',
        type: 'string',
        underscoredId: 'event_source',
      },
      {
        name: 'timestamp',
        type: 'timestamp',
        underscoredId: 'timestamp',
      },
    ]);
    sinon.stub(advInvestigationsService, 'deleteQueryCache');
    sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
      connectorsWithData: ['123'],
    });
    sinon.stub(controller.getSearchQueryPayload);

    await controller.send('onSearchButtonClick', {
      dataSource: 'aws',
      dataStream: 'cloudtrail',
      multiConnector: ['123'],
      query: '',
      dateFrom: new Date('2022-01-04T06:00').valueOf(),
      dateTo: new Date('2022-01-04T12:00').valueOf(),
      columns: ['event_source', 'timestamp'],
      requestTrigger: 'searchClick',
    });

    assert.deepEqual(controller.requestTrigger, 'searchClick');
  });

  test('Builds the correct payload when there are multiple connectors', async function (assert) {
    settingsService.featureEnabled = () => true;
    let columns = ['event_source', 'timestamp'];
    let connectors = ['123', '456'];
    sinon.stub(controller.postQueryTask, 'perform');
    sinon.stub(advInvestigationsService, 'getSchema').returns([
      {
        name: 'event_source',
        type: 'string',
        underscoredId: 'event_source',
      },
      {
        name: 'timestamp',
        type: 'timestamp',
        underscoredId: 'timestamp',
      },
    ]);
    sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
      connectorsWithData: connectors,
    });

    await controller.setProperties({
      dataSource: 'aws',
      dataStream: 'cloudtrail',
      multiConnector: connectors,
      query: '',
      dateFrom: new Date('2022-01-04T06:00').valueOf(),
      dateTo: new Date('2022-01-04T12:00').valueOf(),
      columns,
      requestTrigger: 'searchClick',
    });

    let expectedPayload = {
      query_type: 'union_all',
      subqueries: [
        {
          source: {
            name: 'cloudtrail',
            sid: '123',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event_source',
              as: 'event_source',
            },
            {
              name: 'timestamp',
              as: 'timestamp',
            },
          ],
        },
        {
          source: {
            name: 'cloudtrail',
            sid: '456',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event_source',
              as: 'event_source',
            },
            {
              name: 'timestamp',
              as: 'timestamp',
            },
          ],
        },
      ],
      sort: [
        {
          column: 'timestamp',
          direction: 'Descending',
        },
      ],
    };

    let payload = await controller.getSearchQueryPayload();

    assert.deepEqual(payload, expectedPayload);
  });

  test('Builds the payload correctly when sort column is not included in selected columns', async function (assert) {
    settingsService.featureEnabled = () => true;
    let columns = ['event_source'];
    let connectors = ['123', '456'];
    sinon.stub(controller.postQueryTask, 'perform');
    sinon.stub(advInvestigationsService, 'getSchema').returns([
      {
        name: 'event_source',
        type: 'string',
        underscoredId: 'event_source',
      },
      {
        name: 'timestamp',
        type: 'timestamp',
        underscoredId: 'timestamp',
      },
    ]);
    sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
      connectorsWithData: connectors,
    });

    await controller.setProperties({
      dataSource: 'aws',
      dataStream: 'cloudtrail',
      multiConnector: connectors,
      query: '',
      dateFrom: new Date('2022-01-04T06:00').valueOf(),
      dateTo: new Date('2022-01-04T12:00').valueOf(),
      columns,
      requestTrigger: 'searchClick',
    });

    let expectedPayload = {
      query_type: 'union_all',
      subqueries: [
        {
          source: {
            name: 'cloudtrail',
            sid: '123',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event_source',
              as: 'event_source',
            },
            {
              name: 'timestamp',
              as: 'timestamp',
            },
          ],
        },
        {
          source: {
            name: 'cloudtrail',
            sid: '456',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event_source',
              as: 'event_source',
            },
            {
              name: 'timestamp',
              as: 'timestamp',
            },
          ],
        },
      ],
      sort: [
        {
          column: 'timestamp',
          direction: 'Descending',
        },
      ],
    };

    let payload = await controller.getSearchQueryPayload();

    assert.deepEqual(payload, expectedPayload);
  });

  test('Builds the payload correctly when the sort column contains a period', async function (assert) {
    settingsService.featureEnabled = () => true;
    let columns = ['event.source'];
    let connectors = ['123', '456'];
    sinon.stub(controller.postQueryTask, 'perform');
    sinon.stub(advInvestigationsService, 'getSchema').returns([
      {
        name: 'event.source',
        type: 'string',
        underscoredId: 'event_source',
      },
    ]);
    sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
      connectorsWithData: connectors,
    });

    await controller.setProperties({
      dataSource: 'aws',
      dataStream: 'cloudtrail',
      multiConnector: connectors,
      query: '',
      dateFrom: new Date('2022-01-04T06:00').valueOf(),
      dateTo: new Date('2022-01-04T12:00').valueOf(),
      columns,
      requestTrigger: 'searchClick',
      sortColumn: 'event.source',
      sortDirectionAscending: false,
    });

    let expectedPayload = {
      query_type: 'union_all',
      subqueries: [
        {
          source: {
            name: 'cloudtrail',
            sid: '123',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event.source',
              as: 'event_source',
            },
          ],
        },
        {
          source: {
            name: 'cloudtrail',
            sid: '456',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event.source',
              as: 'event_source',
            },
          ],
        },
      ],
      sort: [
        {
          column: 'event_source',
          direction: 'Descending',
        },
      ],
    };

    let payload = await controller.getSearchQueryPayload();

    assert.deepEqual(payload, expectedPayload);
  });

  test('Builds the payload correctly when sort column is host and not in selected columns', async function (assert) {
    settingsService.featureEnabled = () => true;
    let columns = ['event_source'];
    let connectors = ['123'];
    sinon.stub(controller.postQueryTask, 'perform');
    sinon.stub(advInvestigationsService, 'getSchema').returns([
      {
        name: 'orig_hostname',
        type: 'host',
        underscoredId: 'orig_hostname',
      },
      {
        name: 'event_source',
        type: 'string',
        underscoredId: 'event_source',
      },
    ]);
    sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
      connectorsWithData: connectors,
    });

    await controller.setProperties({
      dataSource: 'network',
      dataStream: 'beacon',
      multiConnector: connectors,
      query: '',
      dateFrom: new Date('2022-01-04T06:00').valueOf(),
      dateTo: new Date('2022-01-04T12:00').valueOf(),
      columns,
      requestTrigger: 'searchClick',
      sortColumn: 'orig_hostname',
    });

    let expectedPayload = {
      query_type: 'union_all',
      subqueries: [
        {
          source: {
            name: 'beacon',
            sid: '123',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event_source',
              as: 'event_source',
            },
            {
              name: 'orig_hostname',
              as: 'orig_hostname',
            },
          ],
        },
      ],
      sort: [
        {
          column: 'orig_hostname.name',
          direction: 'Descending',
        },
      ],
    };

    let payload = await controller.getSearchQueryPayload();

    assert.deepEqual(payload, expectedPayload);
  });
  test('Builds the correct payload when there is one connector', async function (assert) {
    settingsService.featureEnabled = () => true;
    let columns = ['event_source', 'timestamp'];
    sinon.stub(controller.postQueryTask, 'perform');
    sinon.stub(advInvestigationsService, 'getSchema').returns([
      {
        name: 'event_source',
        type: 'string',
        underscoredId: 'event_source',
      },
      {
        name: 'timestamp',
        type: 'timestamp',
        underscoredId: 'timestamp',
      },
    ]);
    sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
      connectorsWithData: ['123'],
    });

    await controller.setProperties({
      dataSource: 'aws',
      dataStream: 'cloudtrail',
      multiConnector: ['123'],
      query: '',
      dateFrom: new Date('2022-01-04T06:00').valueOf(),
      dateTo: new Date('2022-01-04T12:00').valueOf(),
      columns,
      requestTrigger: 'searchClick',
    });

    let expectedPayload = {
      query_type: 'union_all',
      subqueries: [
        {
          source: {
            name: 'cloudtrail',
            sid: '123',
          },
          timeRange: {
            from: '2022-01-04T06:00:00.000Z',
            to: '2022-01-04T12:00:00.000Z',
          },
          columns: [
            {
              name: 'event_source',
              as: 'event_source',
            },
            {
              name: 'timestamp',
              as: 'timestamp',
            },
          ],
        },
      ],
      sort: [
        {
          column: 'timestamp',
          direction: 'Descending',
        },
      ],
    };

    let payload = await controller.getSearchQueryPayload();

    assert.deepEqual(payload, expectedPayload);
  });

  test('it maps string field to correct cell types', function (assert) {
    let schema = [
      {
        name: 'firstname',
        type: 'string',
      },
    ];
    controller.set('model', { schema });
    controller.refreshColumns();

    let columns = controller.get('columnsFormattedForTable');
    assert.equal(columns[0].cellComponent, 'vc/table/cells/text/cell-text');
  });

  test('it maps int field to correct cell types', function (assert) {
    let schema = [
      {
        name: 'firstname',
        type: 'int',
      },
    ];
    controller.set('model', { schema });
    controller.refreshColumns();

    let columns = controller.get('columnsFormattedForTable');
    assert.equal(columns[0].cellComponent, 'vc/table/cells/number/cell-number');
  });

  test('it maps timestamp field to correct cell type', function (assert) {
    let schema = [
      {
        name: 'time',
        type: 'timestamp',
      },
    ];
    controller.set('model', { schema });
    controller.set('columns', ['time']);
    controller.refreshColumns();

    let columns = controller.get('columnsFormattedForTable');
    assert.equal(columns[0].cellComponent, 'vc/table/cells/date/cell-datetime');
  });

  test('it maps boolean field to correct text cell type', function (assert) {
    let schema = [
      {
        name: 'isValid',
        type: 'bool',
      },
    ];
    controller.set('model', { schema });
    controller.refreshColumns();

    let columns = controller.get('columnsFormattedForTable');
    assert.equal(columns[0].cellComponent, 'vc/table/cells/boolean/cell-boolean-string');
  });

  test('default celltype is string if not found', function (assert) {
    let schema = [
      {
        name: 'firstname',
        type: 'bogus-type',
      },
    ];
    controller.set('model', { schema });
    controller.refreshColumns();

    let columns = controller.get('columnsFormattedForTable');
    assert.equal(columns[0].cellComponent, 'vc/table/cells/text/cell-text');
  });

  test('constructs not available sensors message correctly - some sensors unavailable', function (assert) {
    let connectorOptions = [
      {
        label: 'test sensor 1',
        value: '0000-0000-abc',
      },
      {
        label: 'test sensor 2',
        value: '0000-0000-cde',
      },
    ];
    controller.set('model', { connectorOptions });
    controller.set('connectorsWithoutData', ['00000000abc', '12345']);
    controller.refreshColumns();

    let expectedMessage =
      'Data not found for <strong>test sensor 1</strong>. Possible reasons include: no data has been received yet or we are experiencing an outage.';

    let notAvailableSensorsMessage = controller.get('notAvailableSensorsMessage');

    assert.equal(notAvailableSensorsMessage.string, expectedMessage);
  });

  test('constructs not available sensors message correctly - all sensors unavailable', function (assert) {
    let connectorOptions = [
      {
        label: 'test sensor 1',
        value: '0000-0000-abc',
      },
      {
        label: 'test sensor 2',
        value: '0000-0000-cde',
      },
    ];
    controller.set('model', { connectorOptions });
    controller.set('connectorsWithoutData', ['00000000abc', '00000000cde']);
    controller.refreshColumns();

    let expectedMessage =
      'Data not found for <strong>test sensor 1</strong> and <strong>test sensor 2</strong>. Possible reasons include: no data has been received yet or we are experiencing an outage.';

    let notAvailableSensorsMessage = controller.get('notAvailableSensorsMessage');

    assert.equal(notAvailableSensorsMessage.string, expectedMessage);
  });

  module('API query tasks', function (innerHooks) {
    let availableTablesMock;
    let postMock;
    let getMock;
    let statusMock;
    let schemaMock;

    innerHooks.beforeEach(function () {
      clock.restore();

      postMock = mock({
        url: '/api/app/investigate/query',
        type: 'POST',
        responseText: {
          requestId: 'request-id-1',
        },
      });

      getMock = mock({
        url: '/api/app/investigate/query',
        type: 'GET',
      });

      statusMock = mock({
        url: '/api/app/investigate/query/status',
        type: 'GET',
      });

      availableTablesMock = mock({
        url: '/api/app/investigate/availableTables',
        responseText: {
          cloudtrail: {
            abc: ['cloudtrail'],
          },
        },
      }).withParams({
        dataSource: 'cloudtrail',
      });

      schemaMock = mock({
        url: '/api/app/investigate/schema',
        type: 'GET',
        responseText: [{}],
      });

      sinon.stub(advInvestigationsService, 'checkAvailableTablesForConnectors').returns({
        connectorsWithData: ['123'],
      });
    });

    innerHooks.afterEach(function () {
      availableTablesMock.destroy();
      postMock.destroy();
      getMock.destroy();
      statusMock.destroy();
      schemaMock.destroy();
      sinon.restore();
    });

    test('postQueryTask updates the requestId on successful POST', async function (assert) {
      sinon.stub(controller.fetchTableRowsTask, 'perform');
      controller.set('pendingColumns', ['col1', 'col2', 'col3']);
      controller.set('multiConnector', ['123']);
      await controller.postQueryTask.perform();
      assert.equal(postMock.timesCalled, 1);
      assert.equal(controller.requestId, 'request-id-1');
    });

    test('it posts correct status to rtt metrics service on successful query, when fetchTableRowsTask is called', async function (assert) {
      let postRoundTripTimeStub = sinon.stub(metricsService, 'postRoundTripTimeMetric');
      let metricsStub = sinon.stub(this.owner.lookup('service:collection'), 'postAvailabilityMetrics');

      getMock.returns({
        data: [],
      });
      let payload = await controller.getSearchQueryPayload();
      await controller.fetchTableRowsTask.perform(new Date().valueOf(), payload);

      assert.equal(postRoundTripTimeStub.getCall(0).args[0], 'advanced_investigation_query_rtt');
      assert.equal(postRoundTripTimeStub.getCall(0).args[1], 'SUCCESS');
      assert.equal(postRoundTripTimeStub.getCall(0).args[3].query_type, PILL_QUERY_TYPE);
      assert.equal(postRoundTripTimeStub.getCall(1).args[0], 'advanced_investigation_query_complete_rtt');
      assert.equal(postRoundTripTimeStub.getCall(1).args[1], 'SUCCESS');

      assert.ok(metricsStub.calledOnce);

      assert.equal(metricsStub.getCall(0).args[0], 'adv_inv_query_initial');
      assert.equal(metricsStub.getCall(0).args[1], 200);
    });

    test('it posts correct status to rtt metrics service when a query is restarted and fetchTableRowsTask is called', async function (assert) {
      let postRoundTripTimeStub = sinon.stub(metricsService, 'postRoundTripTimeMetric');
      let metricsStub = sinon.stub(this.owner.lookup('service:collection'), 'postAvailabilityMetrics');

      getMock.returns({
        data: [],
      });
      let payload = await controller.getSearchQueryPayload();
      controller.fetchTableRowsTask.perform(new Date().valueOf(), payload);
      await controller.fetchTableRowsTask.perform(new Date().valueOf(), payload);

      assert.ok(postRoundTripTimeStub.callCount === 4);

      assert.equal(postRoundTripTimeStub.getCall(0).args[0], 'advanced_investigation_query_rtt');
      assert.equal(postRoundTripTimeStub.getCall(0).args[1], 'ABORT');
      assert.equal(postRoundTripTimeStub.getCall(0).args[3].query_type, PILL_QUERY_TYPE);
      assert.equal(postRoundTripTimeStub.getCall(1).args[0], 'advanced_investigation_query_complete_rtt');
      assert.equal(postRoundTripTimeStub.getCall(1).args[1], 'ABORT');

      assert.equal(postRoundTripTimeStub.getCall(2).args[0], 'advanced_investigation_query_rtt');
      assert.equal(postRoundTripTimeStub.getCall(2).args[1], 'SUCCESS');
      assert.equal(postRoundTripTimeStub.getCall(2).args[3].query_type, PILL_QUERY_TYPE);
      assert.equal(postRoundTripTimeStub.getCall(3).args[0], 'advanced_investigation_query_complete_rtt');
      assert.equal(postRoundTripTimeStub.getCall(3).args[1], 'SUCCESS');

      assert.ok(metricsStub.calledOnce);

      assert.equal(metricsStub.getCall(0).args[0], 'adv_inv_query_initial');
      assert.equal(metricsStub.getCall(0).args[1], 200);
    });

    test('it posts correct status to rtt metrics service on failed query and fetchTableRowsTask is called', async function (assert) {
      let postRoundTripTimeStub = sinon.stub(metricsService, 'postRoundTripTimeMetric');
      let metricsStub = sinon.stub(this.owner.lookup('service:collection'), 'postAvailabilityMetrics');
      getMock.fails();

      let payload = await controller.getSearchQueryPayload();
      await controller.fetchTableRowsTask.perform(new Date().valueOf(), payload);

      assert.equal(postRoundTripTimeStub.getCall(0).args[0], 'advanced_investigation_query_rtt');
      assert.equal(postRoundTripTimeStub.getCall(0).args[1], 'FAIL');
      assert.equal(postRoundTripTimeStub.getCall(0).args[3].query_type, PILL_QUERY_TYPE);
      assert.equal(postRoundTripTimeStub.getCall(1).args[0], 'advanced_investigation_query_complete_rtt');
      assert.equal(postRoundTripTimeStub.getCall(1).args[1], 'FAIL');

      assert.ok(metricsStub.calledOnce);

      assert.equal(metricsStub.getCall(0).args[0], 'adv_inv_query_initial');
      assert.equal(metricsStub.getCall(0).args[1], 500);
    });

    test('postQueryTask performs the fetchTableRowsTask', async function (assert) {
      let fetchTaskStub = sinon.stub(controller.fetchTableRowsTask, 'perform');
      controller.set('pendingColumns', ['col1', 'col2', 'col3']);
      await controller.postQueryTask.perform();
      assert.ok(fetchTaskStub.calledOnce);
    });

    test('postQueryTask sets the error to GENERIC in case of unrecognised error', async function (assert) {
      postMock.fails({
        status: 502,
        convertErrors: false,
      });
      controller.set('pendingColumns', ['col1', 'col2', 'col3']);

      let payload = await controller.getSearchQueryPayload();
      await controller.postQueryTask.perform(new Date().valueOf(), payload);
      assert.equal(controller.errorPage, 'GENERIC');
    });

    test('fetchTableRowsTask sets the error page and searchable days correctly if the query is outside the searchable range', async function (assert) {
      getMock.fails({
        status: 400,
        convertErrors: false,
        response: {
          searchableRange: {
            queryPartiallyExceededSearchableRange: true,
            queryCompletelyExceededSearchableRange: true,
            searchableDaysAllowed: 4,
          },
        },
      });
      let payload = await controller.getSearchQueryPayload();
      await controller.fetchTableRowsTask.perform(new Date().valueOf(), payload);
      assert.equal(controller.errorPage, 'TIME_OUT_OF_RANGE');
    });

    test('fetchTableRowsTask sets the error to GENERIC in case of unrecognised error', async function (assert) {
      getMock.fails({
        status: 502,
        convertErrors: false,
      });
      let payload = await controller.getSearchQueryPayload();
      await controller.fetchTableRowsTask.perform(new Date().valueOf(), payload);
      assert.equal(getMock.timesCalled, 1);
      assert.equal(controller.errorPage, 'GENERIC');
    });

    test('it sends tracking information correctly on encountering a syntax error on POST', async function (assert) {
      let testQuery = [
        {
          key: 'test-1',
          operator: 'c',
          value: 'test',
        },
        {
          key: 'test-2',
          operator: 'c',
          value: 'test',
        },
        {
          key: 'test-3',
          operator: 'c',
          value: 'test',
        },
      ];

      let schema = {
        columns: [
          {
            name: 'timestamp',
            type: 'timestamp',
          },
          {
            name: 'event_source',
            type: 'string',
          },
        ],
      };

      let pendoObject = {
        keys: 'test-1,test-2,test-3',
      };

      postMock.fails({
        status: 500,
        convertErrors: false,
        response: {
          error: {
            errorCode: 'SYNTAX_ERROR',
          },
        },
      });
      controller.set('model', { schema: schema.columns });
      controller.set('_query', testQuery);
      await controller.postQueryTask.perform();

      assert.ok(pendoStub.calledWith('investigationsOnSyntaxError', pendoObject));
    });

    test('it sends Pendo tracking event onSearch with numberOfColumns as a string, filter key and operators and total results', async function (assert) {
      getMock.returns({
        data: [],
        meta: {
          numRowsAvailable: 1,
          queryStatus: 'SUCCESS',
        },
      });
      let schema = {
        columns: [
          {
            name: 'timestamp',
            type: 'timestamp',
          },
          {
            name: 'event_source',
            type: 'string',
          },
        ],
      };
      schemaMock = mock({
        url: '/api/app/investigate/schema',
        type: 'GET',
        responseText: schema.columns,
      });

      sinon.stub(advInvestigationsService, 'getSchema').returns(schema.columns);
      controller.set('model', { schema: schema.columns });
      controller.set('requestId', 'request-id-1');
      controller.set('columns', ['event_source', 'timestamp']);
      controller.set('_query', [
        { key: 'event_source', operator: 'is', value: { value: 'test', caseSensitive: false } },
      ]);
      controller.set('dateFrom', new Date('2022-01-03T12:00').valueOf());
      controller.set('dataSource', 'aws');
      controller.set('dataStream', 'cloudtrail');
      controller.set('dateTo', new Date('2022-01-03T13:01').valueOf());
      controller.set('queryMetaData', { queryStatus: 'SUCCESS' });
      controller.set('requestTrigger', 'searchClick');

      await controller.postQueryTask.perform();
      await waitUntil(() => !controller.fetchTableRowsTask.isRunning, { timeout: 1000 });
      assert.ok(
        pendoStub.calledWith('investigationsOnSearch', {
          timeRangeInHours: 1,
          timeStartEnd: '03 Jan 2022 12:00 - 03 Jan 2022 13:01',
          numberOfColumns: '2',
          dataSource: 'aws',
          dataStream: 'cloudtrail',
          key1: 'event_source',
          operator1: 'is (case-insensitive)',
          column1: 'event_source',
          column2: 'timestamp',
          resultsCount: 1,
          requestTrigger: 'searchClick',
        }),
      );
    });

    test('it sends Pendo tracking event on results download with filesize', async function (assert) {
      controller.set('queryMetaData', { status: 'SUCCESS', estimatedFileSizeBytes: 500, numRowsAvailable: 5000 });
      controller.set('searchResults');

      await controller.trackDownload();

      assert.ok(pendoStub.calledWith('investigationsOnDownload', { fileSizeBytes: '500', rows: '5000' }));
    });
  });

  module('Pagination functions', function () {
    test('onPageSizeChange page 1 is selected', function (assert) {
      controller.send('onPageSizeChange', 20);
      assert.equal(controller.get('pageNumber'), 1);
    });

    test('onPageSizeChange request trigger is set correctly', function (assert) {
      controller.send('onPageSizeChange', 20);
      assert.equal(controller.get('requestTrigger'), 'pageSizeChange');
    });

    test('onPageSizeChange new page size is set', function (assert) {
      controller.send('onPageSizeChange', 200);
      assert.equal(controller.get('pageSize'), 200);
    });

    test('onPageSizeChange scrolls to top of results correctly', function (assert) {
      controller._scrollToTopOfResults = sinon.spy();
      controller.send('onPageSelect', 5);

      assert.ok(controller._scrollToTopOfResults.calledOnce);
    });

    test('onPageSelect updates page number correctly', function (assert) {
      controller.send('onPageSelect', 5);
      assert.equal(controller.get('pageNumber'), 5);
    });

    test('onPageSelect request reason is set correctly', function (assert) {
      controller.send('onPageSelect', 20);
      assert.equal(controller.get('requestTrigger'), 'pageChange');
    });

    test('onPageSelect updates scrolls to top of results correctly', function (assert) {
      controller._scrollToTopOfResults = sinon.spy();
      controller.send('onPageSelect', 5);

      assert.ok(controller._scrollToTopOfResults.calledOnce);
    });

    test('on Search Button Click page number should reset to page 1', async function (assert) {
      sinon.stub(advInvestigationsService, 'getSchema').returns([{ name: 'field01' }]);
      controller.set('pageNumber', 20);
      await controller.send('onSearchButtonClick', {
        dataSource: 'aws',
        dataStream: 'cloudtrail',
        connector: '123',
        query: '',
        dateFrom: new Date('2022-01-03T12:00').valueOf(),
        dateTo: new Date('2022-01-04T12:00').valueOf(),
        columns: [],
      });

      assert.equal(controller.get('pageNumber'), 1);
    });
  });

  module('Column configuration functions', function () {
    test('Columns are removed correctly and setStoredColumnSelection is called when column is removed', function (assert) {
      controller.set('dataSource', 'aws');
      controller.set('dataStream', 'cloudtrail');
      controller.set('columns', ['test1', 'test2']);
      let setColumnsSelected = sinon.stub(advInvestigationsService, 'setStoredColumnSelection');

      controller.send('onRemoveColumnClick', { schemaName: 'test1' });

      assert.deepEqual(controller.get('columns'), ['test2']);
      assert.ok(setColumnsSelected.calledOnce);
    });

    test('When last column is removed splash screen shows', function (assert) {
      controller.set('dataSource', 'aws');
      controller.set('dataStream', 'cloudtrail');
      controller.set('columns', ['test1']);
      controller.send('onRemoveColumnClick', { schemaName: 'test1' });
      assert.equal(controller.get('errorPage'), ['NO_COLUMNS_SELECTED']);
    });

    test('Columns reorder correctly when reorderColumns is called', function (assert) {
      let columns = [{ schemaName: 'test1' }, { schemaName: 'test2' }];
      controller.send('reorderColumns', columns[0], columns);
      let expectedColumns = ['test1', 'test2'];
      assert.deepEqual(controller.get('columns'), expectedColumns);
    });

    test('Pendo event is sent correctly and column cache is set on column reorder ', function (assert) {
      let originalColumns = ['test1', 'test2'];
      let reorderedColumns = [{ schemaName: 'test2' }, { schemaName: 'test1' }];
      let pendoObject = {
        dataSource: 'aws',
        dataStream: 'cloudtrail',
        columnName: 'test1',
        oldPosition: '0',
        newPosition: '1',
        newOrderOfColumns: 'test2,test1',
      };
      let setColumnsSelected = sinon.stub(advInvestigationsService, 'setStoredColumnSelection');

      controller.set('dataSource', 'aws');
      controller.set('dataStream', 'cloudtrail');
      controller.set('columns', originalColumns);
      controller.send('reorderColumns', reorderedColumns[1], reorderedColumns);

      assert.ok(setColumnsSelected.calledOnce);
      assert.ok(pendoStub.calledWith('investigationsOnColumnReorder', pendoObject));
    });

    test('Pendo event is sent correctly on column resize', function (assert) {
      let dataSource = 'aws';
      let dataStream = 'cloudtrail';
      let resizedColumn = {
        schemaName: 'test1',
      };
      let columnName = resizedColumn.schemaName;
      let setColumnsResized = sinon.stub(advInvestigationsService, 'setStoredColumnSize');

      let pendoObject = {
        dataSource,
        dataStream,
        columnName,
        oldWidthInPx: '200',
        newWidthInPx: '320',
      };

      controller.set('dataSource', dataSource);
      controller.set('dataStream', dataStream);
      controller.send('onColumnResized', resizedColumn, '320px');

      assert.ok(setColumnsResized.calledOnce);
      assert.ok(pendoStub.calledWith('investigationsOnColumnResized', pendoObject));
    });
  });

  module('Column Sorting', function () {
    test('it should sort by timestamp descending by default', function (assert) {
      assert.equal(controller.sortColumn, 'timestamp');
      assert.equal(controller.sortDirectionAscending, false);
    });
    test('onSortChange updates sort column correctly', function (assert) {
      let sortColumn = {
        schemaName: 'test',
        label: 'test',
        ascending: false,
      };
      sinon.stub(controller.postQueryTask, 'perform');
      controller.send('onSortChange', 'test.desc', sortColumn);
      assert.equal(controller.get('sortColumn'), 'test');
    });

    test('onSortChange updates sort column correctly when valuePath contains punctuation', function (assert) {
      let sortColumn = {
        schemaName: 'test.test',
        label: 'test.test',
        ascending: false,
      };
      sinon.stub(controller.postQueryTask, 'perform');
      controller.send('onSortChange', 'test.test.desc', sortColumn);
      assert.equal(controller.get('sortColumn'), 'test.test');
    });

    test('onSortChange updates sort column correctly when column label different to schemaName', function (assert) {
      let sortColumn = {
        schemaName: 'timestamp',
        label: 'Timestamp (IST)',
        ascending: false,
      };
      sinon.stub(controller.postQueryTask, 'perform');
      controller.send('onSortChange', 'timestamp.desc', sortColumn);
      assert.equal(controller.get('sortColumn'), 'timestamp');
    });

    test('onSortChange updates sort direction correctly when ascending is true', function (assert) {
      let sortColumn = {
        fullValuePath: 'test.test',
        ascending: true,
      };
      sinon.stub(controller.postQueryTask, 'perform');
      controller.send('onSortChange', 'test.test.asc', sortColumn);
      assert.equal(controller.get('sortDirectionAscending'), true);
    });

    test('onSortChange updates sort direction correctly when ascending is false', function (assert) {
      let sortColumn = {
        fullValuePath: 'test.test',
        ascending: false,
      };
      sinon.stub(controller.postQueryTask, 'perform');
      controller.send('onSortChange', 'test.test.desc', sortColumn);
      assert.equal(controller.get('sortDirectionAscending'), false);
    });

    test('onSortChange resets page number to 1', function (assert) {
      controller.set('pageNumber', 3);
      let sortColumn = {
        fullValuePath: 'test.test',
        ascending: false,
      };
      sinon.stub(controller.postQueryTask, 'perform');
      controller.send('onSortChange', 'test.test.desc', sortColumn);
      assert.equal(controller.get('pageNumber'), 1);
    });
  });

  module('Direct data interaction', () => {
    test('it creates direct data intraction for string type', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();

      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].extra.directDataInteraction.isEnabled('firstname'), true);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(''), true);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(null), true);
    });

    test('it creates direct data interaction for boolean type', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'bool',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();

      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(true), true);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(false), true);
    });

    test('it creates direct data interaction for timestamp type', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'timestamp',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].extra.directDataInteraction.isEnabled('time'), true);
    });

    test('it creates direct data interaction for int type if int is not 0', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'int',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(''), false);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(null), false);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(0), false);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(1), true);
    });

    test('it creates direct data interaction for json type if the value is not empty', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'json',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].extra.directDataInteraction.isEnabled([{ test: 1 }]), true);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled([]), false);
    });

    test('it does not create direct data interaction for json type if the value is undefined', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'json',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(undefined), false);
      assert.equal(columns[0].extra.directDataInteraction.isEnabled(null), false);
    });

    test('Direct data interaction has telemetryAction that calls pendo', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      columns[0].extra.directDataInteraction.telemetryAction({}, { valuePath: 'test' }, { title: 'test' });

      let pendoObject = {
        key: 'test',
        operator: 'test',
      };
      assert.ok(pendoStub.calledOnce);
      pendoStub.calledWith('investigationsOnSyntaxError', pendoObject);
    });
  });

  module('Column widths', function () {
    test('column should have 320px fallback width if no other config set', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].width, '320px');
    });

    test('column should have 140px when data source is network', function (assert) {
      let schema = [
        {
          name: 'firstname',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.set('dataSource', 'network');
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].width, '140px');
    });

    test('column width should be 240px for network -> beacon -> beacon_uid', function (assert) {
      let schema = [
        {
          name: 'beacon_uid',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.set('dataSource', 'network');
      controller.set('dataStream', 'beacon');
      controller.set('columns', ['beacon_uid']);
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].width, '240px');
    });

    test('column width fall back to data source if column name not found and doesnt break', function (assert) {
      let schema = [
        {
          name: 'beacon_uid',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.set('dataSource', 'network');
      controller.set('dataStream', 'stream_does_not_exist');
      controller.set('columns', ['beacon_uid']);
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].width, '140px');
    });

    test('column width should prioritize from columnSizeCache cache', function (assert) {
      let schema = [
        {
          name: 'beacon_uid',
          type: 'string',
        },
      ];
      controller.set('model', { schema });
      controller.set('dataSource', 'network');
      controller.set('dataStream', 'beacon');
      controller.set('columns', ['beacon_uid']);
      localStorage.setItem(
        'advanced_investigations.column_width',
        JSON.stringify({ 'network.beacon.beacon_uid': '500px' }),
      );
      controller.refreshColumns();
      let columns = controller.get('columnsFormattedForTable');
      assert.equal(columns[0].width, '500px');
    });
  });

  module('Permissions test', () => {
    test('When an AWS datasource exists, hasAwsConnections is true', async function (assert) {
      controller.set('model', { dataSourceOptions: [{ value: 'aws' }] });

      assert.ok(controller.hasAwsConnections);
    });

    test('When an o365 datasource exists, hasAwsConnections is true', async function (assert) {
      controller.set('model', { dataSourceOptions: [{ value: 'o365' }] });

      assert.ok(controller.hasM365Connections);
    });

    test('When an AWS datasource exists, hasAwsConnections is true', async function (assert) {
      controller.set('model', { dataSourceOptions: [{ value: 'network' }] });

      assert.ok(controller.hasNetworkConnections);
    });
  });
});
