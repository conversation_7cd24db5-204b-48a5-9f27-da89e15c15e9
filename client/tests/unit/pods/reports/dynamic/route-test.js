import { module, test } from 'qunit';
import { setupTest } from 'ember-qunit';
import { setupMirage } from 'ember-cli-mirage/test-support';
import sinon from 'sinon';

module('Unit | Route | Reports | Report', function (hooks) {
  setupTest(hooks);
  setupMirage(hooks);

  let route;

  hooks.beforeEach(function () {
    route = this.owner.lookup('route:reports.dynamic');
  });

  hooks.afterEach(function () {
    sinon.restore();
  });

  test('it loads the correct RUX report config from the report slug', async function (assert) {
    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'cloud_only';
    });

    let model = await route.model({ report_slug: 'executive-overview' });
    assert.equal(model.report.name, 'Executive Overview');
    assert.ok(model.report.sections.every(section => section.name !== 'Key Assets'));
  });

  test('it loads the correct QUX report config from the report slug', async function (assert) {
    const settingsService = this.owner.lookup('service:settings');
    sinon.stub(settingsService, 'featureEnabled').callsFake(feature => {
      return feature === 'appliance_only';
    });

    let model = await route.model({ report_slug: 'executive-overview-qux' });
    assert.equal(model.report.name, 'Executive Overview');
    const threatsSection = model.report.sections.find(section => section.name === 'Threats');
    assert.ok(threatsSection.widgets.some(widget => widget.id === 'key-assets')); // only in QUX
  });
});
